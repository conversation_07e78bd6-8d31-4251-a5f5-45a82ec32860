package company

import (
	"fmt"
	"time"

	"vlab/app/common/dbs"
)

type StatusStr string

const (
	StatusStrEnable   StatusStr = "enable"
	StatusStrDisable  StatusStr = "disable"
	StatusStrAuditIng StatusStr = "ing"
)

var StatusMap = map[uint32]string{
	uint32(dbs.StatusEnable):   string(StatusStrEnable),
	uint32(dbs.StatusDisable):  string(StatusStrDisable),
	uint32(dbs.StatusAuditIng): string(StatusStrAuditIng),
}

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	ChannelID uint64    `json:"channel_id,omitempty"` // 关联渠道ID
	Name      string    `json:"name,omitempty"`
	Version   string    `json:"version,omitempty"`
	Sort      uint32    `json:"sort,omitempty"`
	Status    uint32    `json:"status,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "resource_version"
}

func (m *Model) IsValidVersion() bool {
	return m.Status == uint32(dbs.StatusEnable) || m.Status == uint32(dbs.StatusAuditIng)
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetVersionStr() string {
	if v, ok := StatusMap[m.Status]; ok {
		return v
	}
	return string(StatusStrDisable)
}

type Filter struct {
	ID      uint64
	NotID   uint64
	IDS     []uint64
	Name    string
	Version string
	Status  uint32

	ChannelID uint64
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetChannelVersionMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		key := GetChannelVersionKey(val.ChannelID, val.Version)
		if _, ok := retMap[key]; !ok {
			retMap[key] = val
		}
	}
	return retMap
}

func GetChannelVersionKey(channelID uint64, version string) string {
	return fmt.Sprintf("%v_%v", channelID, version)
}

func (ml ModelList) GetChannelIDs() []uint64 {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ChannelID]; !ok {
			retMap[val.ChannelID] = struct{}{}
		}
	}
	retList := make([]uint64, 0, len(retMap))
	for k := range retMap {
		retList = append(retList, k)
	}
	return retList
}
