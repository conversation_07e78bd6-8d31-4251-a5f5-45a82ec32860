package company

import (
	"encoding/json"
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableVersionList .
func (e *Entry) RedisEnableVersionList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisVersionList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.IsValidVersion() {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisVersionList .
func (e *Entry) RedisVersionList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.VersionListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadVersionList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisVersionIDMap .
func (e *Entry) RedisVersionIDMap(ctx *gin.Context) (map[uint64]*Model, error) {
	dataList, err := e.RedisVersionList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetIDMap(), nil
}

// RedisEnableChannelVersionMap .
func (e *Entry) RedisEnableChannelVersionMap(ctx *gin.Context) (map[string]*Model, error) {
	dataList, err := e.RedisEnableVersionList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetChannelVersionMap(), nil
}

// RedisReloadVersionList redis重载数据列表
func (e *Entry) RedisReloadVersionList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.VersionListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearVersionList .
func (e *Entry) RedisClearVersionList(ctx *gin.Context) error {
	cacheKey := redisPkg.VersionListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
