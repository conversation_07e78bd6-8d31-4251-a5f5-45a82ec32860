package company

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	VersionRepo
	VersionRedis
}

type VersionRepo interface {
	BatchCreate(ctx *gin.Context, m ModelList) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	FetchByUdx(*gin.Context, string) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type VersionRedis interface {
	RedisEnableVersionList(*gin.Context) (ModelList, error)
	RedisVersionList(*gin.Context) (ModelList, error)
	RedisVersionIDMap(*gin.Context) (map[uint64]*Model, error)
	RedisEnableChannelVersionMap(ctx *gin.Context) (map[string]*Model, error)
	RedisReloadVersionList(*gin.Context) (ModelList, error)
	RedisClearVersionList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
