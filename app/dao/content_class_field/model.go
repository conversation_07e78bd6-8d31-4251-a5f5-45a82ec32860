package content_class_field

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

type Model struct {
	dbs.ModelWithDel

	ClassID uint64

	Name string

	NameKey string

	ParentID uint64

	Status dbs.StatusType
}

func (m Model) TableName() string {
	return "content_class_field"
}

type Filter struct {
	ID uint64 `json:"id,omitempty"`

	IDs []uint64 `json:"ids,omitempty"`

	ClassID uint64 `json:"class_id,omitempty"`

	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`

	Status dbs.StatusType `json:"status,omitempty"`

	Sort []clause.OrderByColumn
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_class_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ClassID <= 0 {
			return db
		}
		return db.Where("class_id = ?", f.ClassID)
	}
}

func (f Filter) _eq_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Name == "" {
			return db
		}
		return db.Where("name = ?", f.Name)
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_class_id(),
		f._eq_status(),
		f._eq_name(),
	}
}

type ModelList []*Model

func (l ModelList) IDs() []uint64 {
	ids := make([]uint64, len(l))
	for i, v := range l {
		ids[i] = v.ID
	}
	return ids
}

func (l ModelList) ClassIDs() []uint64 {
	ids := make([]uint64, len(l))
	for i, v := range l {
		ids[i] = v.ClassID
	}
	return lo.Uniq(ids)
}

func (l ModelList) GetNameKeys() []string {
	keys := make([]string, len(l))
	for i, v := range l {
		keys[i] = v.NameKey
	}
	return lo.Uniq(keys)
}

func (l ModelList) GetClassIDMap() map[uint64]ModelList {
	m := make(map[uint64]ModelList)
	for _, v := range l {
		if _, ok := m[v.ClassID]; !ok {
			m[v.ClassID] = make(ModelList, 0)
		}
		m[v.ClassID] = append(m[v.ClassID], v)
	}
	return m
}

func (l ModelList) GetIDMap() map[uint64]ModelList {
	m := make(map[uint64]ModelList)
	for _, v := range l {
		if _, ok := m[v.ID]; !ok {
			m[v.ID] = make(ModelList, 0)
		}
		m[v.ID] = append(m[v.ID], v)
	}
	return m
}

func (l ModelList) GetMap() map[uint64]*Model {
	m := make(map[uint64]*Model)
	for _, v := range l {
		m[v.ID] = v
	}
	return m
}

func (l ModelList) GetGenreList() ModelList {
	genreList := make(ModelList, 0)
	for _, v := range l {
		if v.ClassID == 5 {
			genreList = append(genreList, v)
		}
	}
	return genreList
}
