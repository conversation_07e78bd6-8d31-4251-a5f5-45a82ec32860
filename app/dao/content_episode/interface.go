package episode

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	EpisodeRepo
}

type EpisodeRepo interface {
	FindByFilter(*gin.Context, *Filter) (EpisodeList, error)

	FindByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter) (EpisodeList, error)

	FetchByID(*gin.Context, uint64) (*Episode, error)

	CreateOrUpdate(*gin.Context, *Episode) (uint64, error)

	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Episode) (uint64, error)

	Create(*gin.Context, *Episode) (uint64, error)

	CreateWithTx(*gin.Context, *gorm.DB, *Episode) (uint64, error)

	BatchCreate(*gin.Context, []*Episode) error

	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Episode) error

	UpdateModelByID(*gin.Context, uint64, *Episode) error

	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Episode) error

	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error

	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error

	DataPageList(*gin.Context, *Filter, int, int) (total int64, list EpisodeList, err error)

	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)

	FeatchByFilterSort(*gin.Context, *Filter) (*Episode, error)

	CountByFilter(*gin.Context, *Filter) (int64, error)

	BatchCountByFilter(ctx *gin.Context, filter *Filter) (map[uint64]int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
