package episode

import (
	"fmt"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (EpisodeList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := EpisodeList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FindByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter) (EpisodeList, error) {
	if tx == nil {
		tx = e.MysqlEngine.UseWithGinCtx(ctx, true)
	}
	query := tx.Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := EpisodeList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*Episode, error) {
	ret := &Episode{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CreateOrUpdate(context *gin.Context, episode *Episode) (uint64, error) {
	panic("implement me")
}

func (e Entry) CreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, episode *Episode) (uint64, error) {
	panic("implement me")
}

func (e Entry) Create(ctx *gin.Context, model *Episode) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, db *gorm.DB, model *Episode) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*Episode) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*Episode) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) BatchCreateOrUpdate(ctx *gin.Context, models []*Episode) error {
	return e.BatchCreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateOrUpdateWithTx(ctx *gin.Context, db *gorm.DB, models []*Episode) error {
	if len(models) == 0 {
		return nil
	}
	if err := db.Model(&Episode{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"title",
				"status",
				"sort",
			}),
		}).CreateInBatches(models, 3000).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *Episode) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *Episode) error {
	if err := db.Model(&Episode{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)

}

func (e Entry) UpdateMapByIDWithTx(context *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&Episode{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list EpisodeList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Episode, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &Episode{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) BatchCountByFilter(ctx *gin.Context, filter *Filter) (map[uint64]int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Episode{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	type (
		ret struct {
			ShowID uint64
			Cnt    int64
		}
	)
	var (
		retList []ret

		res = make(map[uint64]int64)
	)

	// SELECT show_id, count(*) as cnt FROM content_episode WHERE show_id IN (?) AND is_deleted = 0 GROUP BY show_id;
	if err := query.Select("show_id, count(*) as cnt").Group("show_id").Find(&retList).Error; err != nil {
		return nil, err
	}

	for _, ret := range retList {
		res[ret.ShowID] = ret.Cnt
	}

	return res, nil
}
