package episode

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
)

type Episode struct {
	dbs.ModelWithDel

	Status        uint32 `json:"status,omitempty"`         // 状态
	ShowID        uint64 `json:"show_id,omitempty"`        // 剧ID
	SeasonID      uint64 `json:"season_id,omitempty"`      // 季ID
	Name          string `json:"name,omitempty"`           // 集名
	NameKey       string `json:"name_key,omitempty"`       // 集名多语言key
	EpisodeNumber uint32 `json:"episode_number,omitempty"` // 集序号

	MappingType uint32 `json:"mapping_type,omitempty"` // 映射类型
	MappingID   uint64 `json:"mapping_id,omitempty"`   // 映射ID
}

func (m *Episode) TableName() string {
	return "content_episode"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`

	SeasonID  uint64   `json:"season_id,omitempty"`
	SeasonIDs []uint64 `json:"season_ids,omitempty"`

	Sort []clause.OrderByColumn // 排序

	EpisodeNumber uint32 `json:"episode_number,omitempty"`

	NameLike string `json:"name_like,omitempty"`

	Status uint32 `json:"status,omitempty"`

	MappingType uint32 `json:"mapping_type,omitempty"`

	MappingID uint64 `json:"mapping_id,omitempty"`

	MappingIDs []uint64 `json:"mapping_ids,omitempty"`
}

func (f Filter) _eq_episode_number() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.EpisodeNumber <= 0 {
			return db
		}
		return db.Where("episode_number = ?", f.EpisodeNumber)
	}
}

func (f Filter) _eq_mapping_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.MappingType <= 0 {
			return db
		}
		return db.Where("mapping_type = ?", f.MappingType)
	}
}

func (f Filter) _eq_mapping_id() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if f.MappingID <= 0 {
			return db
		}
		return db.Where("mapping_id = ?", f.MappingID)
	}
}

func (f Filter) _in_mapping_ids() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if len(f.MappingIDs) <= 0 {
			return db
		}
		return db.Where("mapping_id in (?)", f.MappingIDs)
	}
}

func (f Filter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NameLike == "" {
			return db
		}
		return db.Where("name like ? OR name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_episode' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%", "%"+f.NameLike+"%")
		//	return db.Where("name like ?", "%"+f.NameLike+"%").
		//		Or("name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_episode' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%")
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _eq_season_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.SeasonID <= 0 {
			return db
		}
		return db.Where("season_id = ?", f.SeasonID)
	}
}

func (f Filter) _in_season_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.SeasonIDs) <= 0 {
			return db
		}
		return db.Where("season_id in (?)", f.SeasonIDs)
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			db = db.Order("id desc")
			return db
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}
		return db
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_season_id(),
		f._in_season_ids(),
		f._like_name(),
		f._sort(),
		f._eq_episode_number(),
		f._eq_status(),

		f._eq_mapping_type(),
		f._eq_mapping_id(),
		f._in_mapping_ids(),
	}
}

type EpisodeList []*Episode

func (el EpisodeList) GetIDs() []uint64 {
	var ids []uint64
	for _, e := range el {
		ids = append(ids, e.ID)
	}
	return ids
}

func (el EpisodeList) GetMap() map[uint64]*Episode {
	m := make(map[uint64]*Episode)
	for _, e := range el {
		m[e.ID] = e
	}
	return m
}

func (el EpisodeList) GetSeasonIDs() []uint64 {
	var ids []uint64
	for _, e := range el {
		ids = append(ids, e.SeasonID)
	}
	return ids
}

func (el EpisodeList) GetSeasonKeyMap() map[uint64][]*Episode {
	m := make(map[uint64][]*Episode)
	for _, e := range el {
		if _, ok := m[e.SeasonID]; !ok {
			m[e.SeasonID] = make([]*Episode, 0)
		}
		m[e.SeasonID] = append(m[e.SeasonID], e)
	}
	return m
}

func (el EpisodeList) GetShowKeyMap() map[uint64][]*Episode {
	m := make(map[uint64][]*Episode)
	for _, e := range el {
		if _, ok := m[e.ShowID]; !ok {
			m[e.ShowID] = make([]*Episode, 0)
		}
		m[e.ShowID] = append(m[e.ShowID], e)
	}
	return m
}

type Mapping struct {
	MappingType uint32 `json:"mapping_type,omitempty"` // 映射类型
	MappingID   uint64 `json:"mapping_id,omitempty"`   // 映射ID
}

func (el EpisodeList) GetMappingKeyMap() map[Mapping]*Episode {
	m := make(map[Mapping]*Episode)
	for _, e := range el {
		key := Mapping{
			MappingType: e.MappingType,
			MappingID:   e.MappingID,
		}
		m[key] = e
	}
	return m
}
