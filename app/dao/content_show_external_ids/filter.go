package content_show_external_ids

import (
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Filter 查询过滤器
type Filter struct {
	ID      uint64   `json:"id,omitempty"`
	IDs     []uint64 `json:"ids,omitempty"`
	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`

	// 外部ID查询
	ImdbID  string   `json:"imdb_id,omitempty"`
	ImdbIDs []string `json:"imdb_ids,omitempty"`
	TmdbID  uint32   `json:"tmdb_id,omitempty"`
	TmdbIDs []uint32 `json:"tmdb_ids,omitempty"`
	TraktID uint32   `json:"trakt_id,omitempty"`
	Slug    string   `json:"slug,omitempty"`

	// 匹配相关
	MatchType     string   `json:"match_type,omitempty"`      // movie/show
	MinMatchScore *float64 `json:"min_match_score,omitempty"` // 最小匹配分数
	IsMatch       *uint8   `json:"is_match,omitempty"`        // 是否有效匹配
	Source        string   `json:"source,omitempty"`          // 数据来源

	// 时间范围
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	UpdatedAfter  *time.Time `json:"updated_after,omitempty"`
	UpdatedBefore *time.Time `json:"updated_before,omitempty"`

	// 分页和排序
	Sort []clause.OrderByColumn `json:"sort,omitempty"`
}

// BuildQuery 构建查询条件
func (f *Filter) BuildQuery(db *gorm.DB) *gorm.DB {
	query := db.Model(&Model{})

	// ID条件
	if f.ID > 0 {
		query = query.Where("id = ?", f.ID)
	}
	if len(f.IDs) > 0 {
		query = query.Where("id IN ?", f.IDs)
	}

	// ShowID条件
	if f.ShowID > 0 {
		query = query.Where("show_id = ?", f.ShowID)
	}
	if len(f.ShowIDs) > 0 {
		query = query.Where("show_id IN ?", f.ShowIDs)
	}

	// 外部ID条件
	if f.ImdbID != "" {
		query = query.Where("imdb_id = ?", f.ImdbID)
	}
	if len(f.ImdbIDs) > 0 {
		query = query.Where("imdb_id IN ?", f.ImdbIDs)
	}
	if f.TmdbID > 0 {
		query = query.Where("tmdb_id = ?", f.TmdbID)
	}
	if len(f.TmdbIDs) > 0 {
		query = query.Where("tmdb_id IN ?", f.TmdbIDs)
	}
	if f.TraktID > 0 {
		query = query.Where("trakt_id = ?", f.TraktID)
	}
	if f.Slug != "" {
		query = query.Where("slug = ?", f.Slug)
	}

	// 匹配相关条件
	if f.MatchType != "" {
		query = query.Where("match_type = ?", f.MatchType)
	}
	if f.MinMatchScore != nil {
		query = query.Where("match_score >= ?", *f.MinMatchScore)
	}
	if f.IsMatch != nil {
		query = query.Where("is_match = ?", *f.IsMatch)
	}
	if f.Source != "" {
		query = query.Where("source = ?", f.Source)
	}

	// 时间范围条件
	if f.CreatedAfter != nil {
		query = query.Where("created_at >= ?", f.CreatedAfter)
	}
	if f.CreatedBefore != nil {
		query = query.Where("created_at <= ?", f.CreatedBefore)
	}
	if f.UpdatedAfter != nil {
		query = query.Where("updated_at >= ?", f.UpdatedAfter)
	}
	if f.UpdatedBefore != nil {
		query = query.Where("updated_at <= ?", f.UpdatedBefore)
	}

	// 软删除条件（默认不查询已删除的记录）
	query = query.Where("is_deleted = ?", 0)

	// 排序
	if len(f.Sort) > 0 {
		query = query.Clauses(clause.OrderBy{Columns: f.Sort})
	} else {
		// 默认按ID倒序
		query = query.Order("id DESC")
	}

	return query
}

// WithDeleted 包含已删除的记录
func (f *Filter) WithDeleted() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		// 不添加 is_deleted = 0 的条件
		return db
	}
}

// OnlyDeleted 只查询已删除的记录
func (f *Filter) OnlyDeleted() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted = ?", 1)
	}
}
