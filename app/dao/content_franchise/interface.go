package franchise

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	FranchiseRepo
}

type FranchiseRepo interface {
	FindByFilter(*gin.Context, *Filter) (FranchiseList, error)

	CreateOrUpdate(*gin.Context, *Franchise) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Franchise) (uint64, error)
	Create(*gin.Context, *Franchise) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Franchise) (uint64, error)
	BatchCreate(*gin.Context, []*Franchise) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Franchise) error
	UpdateModelByID(*gin.Context, uint64, *Franchise) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Franchise) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list FranchiseList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Franchise, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Franchise, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
