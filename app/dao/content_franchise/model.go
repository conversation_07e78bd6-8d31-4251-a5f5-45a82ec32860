package franchise

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type Franchise struct {
	dbs.ModelWithDel

	Status  uint32 `json:"status,omitempty"`   // 状态
	Name    string `json:"name,omitempty"`     // 系列名
	NameKey string `json:"name_key,omitempty"` // 系列名多语言key
}

func (m *Franchise) TableName() string {
	return "content_franchise"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	NameLike string `json:"name_like,omitempty"` // 系列名模糊查询
	Name     string `json:"name,omitempty"`      // 系列名

	Status uint32 `json:"status,omitempty"` // 状态
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NameLike == "" {
			return db
		}
		return db.Where("name like ? OR name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_franchise' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%", "%"+f.NameLike+"%")
		//return db.Where("name like ?", "%"+f.NameLike+"%").
		//	Or("name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_franchise' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%")

	}
}

func (f Filter) _eq_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Name == "" {
			return db
		}
		return db.Where("name = ?", f.Name)
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._like_name(),
		f._eq_name(),
		f._eq_status(),
	}
}

type FranchiseList []*Franchise

func (pl FranchiseList) GetIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ID)
	}
	return lo.Uniq(ids)
}

func (pl FranchiseList) GetMap() map[uint64]*Franchise {
	m := make(map[uint64]*Franchise)
	for _, p := range pl {
		m[p.ID] = p
	}
	return m
}

func (pl FranchiseList) GetNameKeys() []string {
	var nameKeys []string
	for _, p := range pl {
		nameKeys = append(nameKeys, p.NameKey)
	}
	return lo.Uniq(nameKeys)
}
