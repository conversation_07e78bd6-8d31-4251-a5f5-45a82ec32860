package resource_ip_disallow

import (
	"fmt"

	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "channel_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"ip_list", "status", "is_deleted"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	e.RedisClearIPDisallowList(ctx)
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, updateMap map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, updateMap)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, updateMap map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(updateMap).Error; err != nil {
		return err
	}
	e.RedisClearIPDisallowList(ctx)
	return nil
}

// buildQuery 构建查询条件
func (e *Entry) buildQuery(db *gorm.DB, f *Filter) *gorm.DB {
	query := db.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	if f.ID > 0 {
		query = query.Where("id = ?", f.ID)
	}
	if f.ChannelID > 0 {
		query = query.Where("channel_id = ?", f.ChannelID)
	}
	if f.Status > 0 {
		query = query.Where("status = ?", f.Status)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	if err = query.Count(&total).Error; err != nil {
		return
	}
	if total == 0 {
		return
	}

	orderStr := "id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	offset := (page - 1) * limit
	if err = query.Order(orderStr).Offset(offset).Limit(limit).Find(&list).Error; err != nil {
		return
	}
	return
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	orderStr := "id desc"
	if f.Sort.Field != "" && f.Sort.Method != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.Sort.Field, f.Sort.Method, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("id = ? AND is_deleted = 0", id).First(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByChannelID 根据渠道ID获取IP黑名单配置
func (e *Entry) FetchByChannelID(ctx *gin.Context, channelID uint64) (*Model, error) {
	ret := &Model{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("channel_id = ? AND is_deleted = 0", channelID).First(&ret).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果没有找到记录，返回空的Model而不是错误
			return &Model{}, nil
		}
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (int64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	var count int64
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
