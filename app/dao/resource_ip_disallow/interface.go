package resource_ip_disallow

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	IPDisallowRepo
	IPDisallowRedis
}

type IPDisallowRepo interface {
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	FetchByChannelID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type IPDisallowRedis interface {
	RedisIPDisallowList(*gin.Context) (ModelList, error)
	RedisIPDisallowMap(*gin.Context) (map[uint64]*Model, error)
	RedisIPDisallowChannelMap(*gin.Context) (map[uint64]*Model, error)
	RedisReloadIPDisallowList(*gin.Context) (ModelList, error)
	RedisClearIPDisallowList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
