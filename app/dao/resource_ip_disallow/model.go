package resource_ip_disallow

import (
	"net"
	"strings"

	"vlab/app/common/dbs"

	"github.com/samber/lo"
)

type Model struct {
	dbs.ModelWithDel
	ChannelID uint64 `json:"channel_id,omitempty"` // 渠道ID
	IPList    string `json:"ip_list,omitempty"`    // IP地址列表，CIDR格式，逗号分隔
	Status    uint32 `json:"status,omitempty"`     // 状态：1-启用，0-禁用
}

func (m *Model) TableName() string {
	return "resource_ip_disallow"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

// GetIPCIDRList 获取IP CIDR列表
func (m *Model) GetIPCIDRList() []string {
	if m.IPList == "" {
		return []string{}
	}
	ipList := strings.Split(m.IPList, ",")
	return lo.Map(ipList, func(ip string, _ int) string {
		return strings.TrimSpace(ip)
	})
}

// IsIPInBlacklist 检查IP是否在黑名单中
func (m *Model) IsIPInBlacklist(clientIP string) bool {
	if m.Status != uint32(dbs.StatusEnable) {
		return false
	}

	ipList := m.GetIPCIDRList()
	if len(ipList) == 0 {
		return false
	}

	// 解析客户端IP
	clientIPAddr := net.ParseIP(clientIP)
	if clientIPAddr == nil {
		return false
	}

	// 检查每个CIDR
	for _, cidr := range ipList {
		if cidr == "" {
			continue
		}

		// 如果不包含/，则认为是单个IP地址
		if !strings.Contains(cidr, "/") {
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 解析CIDR
		_, ipNet, err := net.ParseCIDR(cidr)
		if err != nil {
			// 如果解析失败，尝试作为单个IP处理
			if cidr == clientIP {
				return true
			}
			continue
		}

		// 检查IP是否在CIDR范围内
		if ipNet.Contains(clientIPAddr) {
			return true
		}
	}

	return false
}

type Filter struct {
	ID        uint64
	ChannelID uint64
	Status    uint32
	Sort      dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDs() []uint64 {
	return lo.Map(ml, func(item *Model, _ int) uint64 {
		return item.ID
	})
}

func (ml ModelList) GetChannelIDs() []uint64 {
	return lo.Uniq(lo.Map(ml, func(item *Model, _ int) uint64 {
		return item.ChannelID
	}))
}

func (ml ModelList) GetChannelMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		retMap[val.ChannelID] = val
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		retMap[val.ID] = val
	}
	return retMap
}
