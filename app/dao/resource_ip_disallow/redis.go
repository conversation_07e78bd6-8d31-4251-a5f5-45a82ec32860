package resource_ip_disallow

import (
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisIPDisallowList 获取IP黑名单列表
func (e *Entry) RedisIPDisallowList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.IPDisallowListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadIPDisallowList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisIPDisallowMap 获取IP黑名单ID映射
func (e *Entry) RedisIPDisallowMap(ctx *gin.Context) (map[uint64]*Model, error) {
	list, err := e.RedisIPDisallowList(ctx)
	if err != nil {
		return nil, err
	}
	return list.GetIDMap(), nil
}

// RedisIPDisallowChannelMap 获取IP黑名单渠道映射
func (e *Entry) RedisIPDisallowChannelMap(ctx *gin.Context) (map[uint64]*Model, error) {
	list, err := e.RedisIPDisallowList(ctx)
	if err != nil {
		return nil, err
	}
	return list.GetChannelMap(), nil
}

// RedisReloadIPDisallowList 重新加载IP黑名单列表到缓存
func (e *Entry) RedisReloadIPDisallowList(ctx *gin.Context) (ModelList, error) {
	list, err := e.FindByFilter(ctx, &Filter{
		Status: uint32(dbs.StatusEnable), // 只缓存启用的记录
	})
	if err != nil {
		return nil, err
	}

	cacheKey := redisPkg.IPDisallowListKey
	cacheData, err := json.Marshal(list)
	if err != nil {
		return nil, err
	}

	// 缓存5分钟
	if err = e.RedisCli.Set(ctx.Request.Context(), cacheKey, cacheData, redisPkg.LockTimeFiveMinute).Err(); err != nil {
		return nil, err
	}
	return list, nil
}

// RedisClearIPDisallowList 清除IP黑名单缓存
func (e *Entry) RedisClearIPDisallowList(ctx *gin.Context) error {
	cacheKey := redisPkg.IPDisallowListKey
	return e.RedisCli.Del(ctx.Request.Context(), cacheKey).Err()
}
