package content_tmdb

/*
-- auto-generated definition
create table detail_all
(
    id               int auto_increment
        primary key,
    vod_id           varchar(255)  not null,
    type_id          varchar(255)  null,
    type_id_1        varchar(255)  null,
    pt_id            varchar(255)  not null,
    vod_name         varchar(255)  null,
    vod_sub          varchar(255)  null,
    vod_en           varchar(255)  null,
    vod_status       varchar(255)  null,
    vod_letter       varchar(255)  null,
    vod_color        varchar(255)  null,
    vod_tag          varchar(255)  null,
    vod_class        varchar(255)  null,
    vod_pic          text          null,
    vod_pic_thumb    varchar(255)  null,
    vod_pic_slide    varchar(255)  null,
    vod_actor        varchar(255)  null,
    vod_director     varchar(255)  null,
    vod_writer       varchar(255)  null,
    vod_blurb        text          null,
    vod_remarks      varchar(255)  null,
    vod_total        int           null,
    vod_serial       varchar(255)  null,
    vod_tv           varchar(255)  null,
    vod_weekday      varchar(255)  null,
    vod_area         varchar(255)  null,
    vod_lang         varchar(255)  null,
    vod_year         varchar(255)  null,
    vod_version      varchar(255)  null,
    vod_state        varchar(255)  null,
    vod_author       varchar(255)  null,
    vod_copyright    varchar(255)  null,
    vod_time         varchar(255)  null,
    vod_time_add     varchar(255)  null,
    vod_douban_id    varchar(255)  null,
    vod_douban_score decimal(3, 1) null,
    vod_content      text          null,
    vod_play_from    varchar(255)  null,
    vod_play_server  varchar(255)  null,
    vod_play_url     longtext      null,
    vod_plot         text          null,
    type_name        varchar(255)  null,
    sys_add_time     bigint        null,
    sys_from         varchar(255)  null,
    sys_status       int default 0 null,
    sys_update_time  bigint        null,
    constraint pt_id
        unique (pt_id)
)
    charset = utf8mb4
    row_format = DYNAMIC;
*/

type Model struct {
	ID             uint64
	VodID          string
	TypeID         string
	TypeID1        string
	PtID           string
	VodName        string
	VodSub         string
	VodEn          string
	VodStatus      string
	VodLetter      string
	VodColor       string
	VodTag         string
	VodClass       string
	VodPic         string
	VodPicThumb    string
	VodPicSlide    string
	VodActor       string
	VodDirector    string
	VodWriter      string
	VodBlurb       string
	VodRemarks     string
	VodTotal       uint64
	VodSerial      string
	VodTv          string
	VodWeekday     string
	VodArea        string
	VodLang        string
	VodYear        string
	VodVersion     string
	VodState       string
	VodAuthor      string
	VodCopyright   string
	VodTime        string
	VodTimeAdd     string
	VodDoubanID    string
	VodDoubanScore float64
	VodContent     string
	VodPlayFrom    string
	VodPlayServer  string
	VodPlayURL     string
	VodPlot        string
	TypeName       string
	SysAddTime     int64
	SysFrom        string
	SysStatus      int
	SysUpdateTime  int64
}

func (m *Model) TableName() string {
	return "content_tmdb"
}
