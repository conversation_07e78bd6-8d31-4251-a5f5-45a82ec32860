package season

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type Season struct {
	dbs.ModelWithDel

	ShowID       uint64 `json:"show_id,omitempty"`       // 剧ID
	Name         string `json:"name,omitempty"`          // 季名
	NameKey      string `json:"name_key,omitempty"`      // 季名多语言key
	Overview     string `json:"overview,omitempty"`      // 季概述
	OverviewKey  string `json:"overview_key,omitempty"`  // 季概述多语言key
	SeasonNumber uint32 `json:"season_number,omitempty"` // 季序号
	AirDate      string `json:"air_date,omitempty"`      // 首播日期

	PosterID uint64 `json:"poster_id,omitempty"` // 海报ID
}

func (m *Season) TableName() string {
	return "content_season"
}

type Filter struct {
	ID      uint64   `json:"id,omitempty"`
	IDs     []uint64 `json:"ids,omitempty"`
	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
	}
}

type SeasonList []*Season
