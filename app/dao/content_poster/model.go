package poster

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type Poster struct {
	dbs.ModelWithDel

	ShowID    uint64 `json:"show_id,omitempty"`   // 剧ID
	SeasonID  uint64 `json:"season_id,omitempty"` // 季ID
	ImageID   uint64 `json:"image_id,omitempty"`  // 图片ID
	ISO_639_1 string `json:"iso_639_1,omitempty"` // 语言标识
}

func (m *Poster) TableName() string {
	return "content_poster"
}

type Filter struct {
	ID        uint64   `json:"id,omitempty"`
	IDs       []uint64 `json:"ids,omitempty"`
	ShowID    uint64   `json:"show_id,omitempty"`
	ShowIDs   []uint64 `json:"show_ids,omitempty"`
	SeasonID  uint64   `json:"season_id,omitempty"`
	ImageID   uint64   `json:"image_id,omitempty"`
	ImageIDs  []uint64 `json:"image_ids,omitempty"`
	ISO_639_1 string   `json:"iso_639_1,omitempty"` // 语言标识
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _eq_iso_639_1() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ISO_639_1 == "" {
			return db
		}
		return db.Where("iso_639_1 = ?", f.ISO_639_1)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _eq_season_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.SeasonID <= 0 {
			return db
		}
		return db.Where("season_id = ?", f.SeasonID)
	}
}

func (f Filter) _eq_image_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ImageID <= 0 {
			return db
		}
		return db.Where("image_id = ?", f.ImageID)
	}
}

func (f Filter) _in_image_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ImageIDs) <= 0 {
			return db
		}
		return db.Where("image_id in (?)", f.ImageIDs)
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_season_id(),
		f._eq_image_id(),
		f._in_image_ids(),
		f._eq_iso_639_1(),
	}
}

type PosterList []*Poster

func (pl PosterList) GetImageIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ImageID)
	}
	return ids
}

func (pl PosterList) GetShowMap() map[uint64]PosterList {
	m := make(map[uint64]PosterList)
	for _, p := range pl {
		m[p.ShowID] = append(m[p.ShowID], p)
	}
	return m
}
