package poster

import (
	"fmt"

	"gorm.io/gorm"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (PosterList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := PosterList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CreateOrUpdate(context *gin.Context, Poster *Poster) (uint64, error) {
	panic("implement me")
}

func (e Entry) CreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, Poster *Poster) (uint64, error) {
	panic("implement me")
}

func (e Entry) Create(ctx *gin.Context, model *Poster) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, db *gorm.DB, model *Poster) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*Poster) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*Poster) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *Poster) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *Poster) error {
	if err := db.Model(&Poster{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) UpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&Poster{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list PosterList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*Poster, error) {
	ret := &Poster{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Poster, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &Poster{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Poster{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) DeleteByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter) (int64, error) {
	query := tx.Model(&Poster{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	result := query.Delete(&Poster{})
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (e Entry) DeleteByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	return e.DeleteByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter)
}
