package errorReport

import (
	"time"
	"vlab/app/common/dbs"
)

type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	EntityID   uint64    `json:"entity_id,omitempty"`
	EntityType uint32    `json:"entity_type,omitempty"`
	ShowID     uint64    `json:"show_id,omitempty"`
	EpisodeID  uint64    `json:"episode_id,omitempty"`
	VideoID    uint64    `json:"video_id,omitempty"`
	Resolution uint32    `json:"resolution,omitempty"`
	ErrCode    string    `json:"err_code,omitempty"`
	ErrMsg     string    `json:"err_msg,omitempty"`
	Date       string    `json:"date,omitempty"`
	Lang       string    `json:"lang,omitempty"`
	ClientType uint32    `json:"client_type,omitempty"`
	VersionID  uint64    `json:"version_id,omitempty"`
	ChannelID  uint64    `json:"channel_id,omitempty"`
	TraceID    string    `json:"trace_id,omitempty"`
	Header     string    `json:"header,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "monitor_error_report"
}

type Filter struct {
	ID   uint64
	Sort dbs.CommonSort
}

type ModelList []*Model
