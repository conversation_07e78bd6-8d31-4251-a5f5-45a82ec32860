package common

type PlatformType uint32
type VipPlat uint32
type Region string
type Channel uint64

var (
	PlatformSkybox  PlatformType = 1 // skybox
	PlatformViBox   PlatformType = 2 // vibox
	PlatformHitv    PlatformType = 3 // hitv - 已废弃
	PlatformAhatv   PlatformType = 4 // ahatv - 已废弃
	PlatformHeytv   PlatformType = 5 // heytv
	PlatformPikcube PlatformType = 6 // pikcube
	PlatformLomlom  PlatformType = 7 // lomlom

	RegionCn Region = "cn" // 中国大陆
	RegionUs Region = "us" // 美国

	VipPlatApple  VipPlat = 1 // 苹果平台
	VipPlatGoogle VipPlat = 2 // 谷歌平台
	VipPlatPayPal VipPlat = 3 // PayPal平台
	VipPlatStripe VipPlat = 4 // Stripe 平台

	ChannelSkyBoxAndroid Channel = 1
	ChannelSkyBoxIos     Channel = 2

	ChannelViBoxAndroid Channel = 4
	ChannelViBoxIos     Channel = 5

	ChannelHitvBoxAndroid Channel = 6
)
