package dao

import (
	"fmt"
	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
	"vlab/app/dto"
)

// VikingDBSearchParams VikingDB搜索参数配置
type VikingDBSearchParams struct {
	// 基础搜索参数
	Query      string `json:"query"`       // 搜索关键词
	SearchMode string `json:"search_mode"` // 搜索模式：vector, hybrid, filter_only
	Language   string `json:"language"`    // 语言偏好

	// 分页参数（VikingDB在应用层处理分页）
	Page     int `json:"page"`      // 页码，从1开始
	PageSize int `json:"page_size"` // 每页大小

	// VikingDB核心参数
	Limit        int64                  `json:"limit"`         // 搜索结果数量限制（对应TopK）
	OutputFields []string               `json:"output_fields"` // 输出字段列表
	Filter       map[string]interface{} `json:"filter"`        // 筛选条件

	// 向量搜索参数
	DenseWeight  float64 `json:"dense_weight"`  // 稠密向量权重（0.0-1.0）
	SparseWeight float64 `json:"sparse_weight"` // 稀疏向量权重（0.0-1.0）
}

// BuildVikingDBSearchOptions 构建VikingDB搜索选项
func (dao *SearchDao) BuildVikingDBSearchOptions(req *dto.SearchRequest) *vikingdb.SearchOptions {
	// 计算需要获取的结果数量
	// 对于分页，我们需要获取足够的结果以支持当前页面
	neededResults := req.Page * req.PageSize
	maxResults := 1000 // VikingDB建议的最大结果数
	if neededResults > maxResults {
		neededResults = maxResults
	}

	// 确保至少获取TopK个结果（向后兼容）
	if neededResults < req.VectorParams.TopK {
		neededResults = req.VectorParams.TopK
	}

	// 构建筛选条件
	filter := dao.buildFilterConditions(req)

	// 创建搜索选项
	searchOptions := vikingdb.NewSearchOptions()

	// 设置基础参数
	searchOptions.SetLimit(int64(neededResults))
	searchOptions.SetOutputFields(dao.getOutputFields())

	// 设置筛选条件（如果有）
	if len(filter) > 0 {
		searchOptions.SetFilter(filter)
	}

	// 根据搜索模式设置特定参数
	switch req.SearchMode {
	case "hybrid":
		// 混合搜索：设置向量权重
		if req.VectorParams.DenseWeight > 0 {
			searchOptions.SetDenseWeight(req.VectorParams.DenseWeight)
		}
		// 注意：如果SetSparseWeight方法不存在，则省略该设置
		// VikingDB可能通过其他方式处理稀疏向量权重
	case "vector":
		// 纯向量搜索：可以设置向量相关参数
		// 注意：纯向量搜索通常不需要额外的权重设置
	case "filter_only":
		// 纯筛选搜索：主要依赖filter条件
		// 这种模式下向量权重不重要
	}

	return searchOptions
}

// ValidateSearchRequest 验证搜索请求参数
func (dao *SearchDao) ValidateSearchRequest(req *dto.SearchRequest) error {
	// 验证搜索模式
	validModes := map[string]bool{
		"vector":      true,
		"hybrid":      true,
		"filter_only": true,
	}
	if !validModes[req.SearchMode] {
		return fmt.Errorf("不支持的搜索模式: %s", req.SearchMode)
	}

	// 验证向量搜索参数
	if req.SearchMode == "vector" || req.SearchMode == "hybrid" {
		if req.Query == "" {
			return fmt.Errorf("%s搜索模式需要查询文本", req.SearchMode)
		}
	}

	// 验证筛选搜索参数
	if req.SearchMode == "filter_only" {
		filter := dao.buildFilterConditions(req)
		if len(filter) == 0 {
			return fmt.Errorf("筛选搜索需要至少一个筛选条件")
		}
	}

	// 验证权重参数
	if req.VectorParams.DenseWeight < 0 || req.VectorParams.DenseWeight > 1 {
		return fmt.Errorf("DenseWeight必须在0.0-1.0之间")
	}
	if req.VectorParams.SparseWeight < 0 || req.VectorParams.SparseWeight > 1 {
		return fmt.Errorf("SparseWeight必须在0.0-1.0之间")
	}

	// 验证分页参数
	if req.Page <= 0 {
		return fmt.Errorf("页码必须大于0")
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		return fmt.Errorf("每页大小必须在1-100之间")
	}

	return nil
}

// GetRecommendedSearchParams 获取推荐的搜索参数配置
func GetRecommendedSearchParams() VikingDBSearchParams {
	return VikingDBSearchParams{
		// 基础参数
		SearchMode: "hybrid", // 推荐使用混合搜索
		Language:   "zh",     // 默认中文

		// 分页参数
		Page:     1,  // 从第一页开始
		PageSize: 20, // 每页20条结果

		// VikingDB参数
		Limit: 100, // 默认获取100个候选结果

		// 向量权重（用于混合搜索）
		DenseWeight:  0.5, // 稠密向量权重50%
		SparseWeight: 0.5, // 稀疏向量权重50%
	}
}

// BuildSearchRequest 根据业务参数构建标准搜索请求
func BuildSearchRequest(params struct {
	Name         string            // 搜索关键词
	ISO_639_1    string            // 语言代码
	VectorTopK   int               // 向量搜索TopK
	DenseWeight  float32           // 稠密向量权重
	SparseWeight float32           // 稀疏向量权重
	Filters      dto.SearchFilters // 筛选条件
}) *dto.SearchRequest {

	// 使用推荐的默认配置
	recommended := GetRecommendedSearchParams()

	return &dto.SearchRequest{
		// 基础搜索参数
		Query:      params.Name,
		SearchMode: recommended.SearchMode, // 使用推荐的混合搜索模式
		Language:   params.ISO_639_1,

		// 分页参数（根据VikingDB的实际处理方式）
		Page:     1,                 // 向量搜索时通常获取所有结果，后续在应用层分页
		PageSize: params.VectorTopK, // 使用TopK作为页面大小

		// 排序参数
		SortBy:    "relevance", // 按相关度排序
		SortOrder: "desc",      // 降序排列

		// 筛选条件
		Filters: params.Filters,

		// 向量搜索参数（确保权重在有效范围内）
		VectorParams: dto.VectorSearchParams{
			DenseWeight:  normalizeWeight(float64(params.DenseWeight)),
			SparseWeight: normalizeWeight(float64(params.SparseWeight)),
			TopK:         params.VectorTopK,
		},
	}
}

// normalizeWeight 规范化权重值到0.0-1.0范围
func normalizeWeight(weight float64) float64 {
	if weight < 0 {
		return 0.0
	}
	if weight > 1 {
		return 1.0
	}
	return weight
}

// 搜索参数配置常量
const (
	// 默认搜索参数
	DefaultSearchMode   = "hybrid"
	DefaultPage         = 1
	DefaultPageSize     = 20
	DefaultTopK         = 100
	DefaultDenseWeight  = 0.5
	DefaultSparseWeight = 0.5

	// 限制参数
	MaxPageSize      = 100
	MaxTopK          = 1000
	MaxSearchResults = 1000
)
