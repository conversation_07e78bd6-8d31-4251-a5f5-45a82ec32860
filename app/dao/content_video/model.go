package video

import (
	"github.com/samber/lo"
	"vlab/app/common/dbs"

	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type PathType uint32

const (
	PathTypeUrl       PathType = 1 // 链接上传
	PathTypeUpload    PathType = 2 // vod上传
	PathTypePrototype PathType = 3 // 原始上传方式
)

type Video struct {
	dbs.ModelWithDel

	EpisodeID uint64 `json:"episode_id,omitempty"` // 集ID

	Name      string `json:"name,omitempty"`       // 视频名
	Runtime   int64  `json:"runtime,omitempty"`    // 视频时长
	ImageID   uint64 `json:"image_id,omitempty"`   // 图片ID
	VideoPath string `json:"video_path,omitempty"` // 文件路径
	//VideoEtag string `json:"video_etag,omitempty"` // 视频etag

	PathType uint32 `json:"path_type,omitempty"` // 路径类型 1: 直接路径

	VideoParam datatypes.JSON `gorm:"column:video_param;serializer:json" mapstructure:"video_param"` // 视频请求参数
}

func (m *Video) TableName() string {
	return "content_video"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	EpisodeID  uint64   `json:"episode_id,omitempty"`
	EpisodeIDs []uint64 `json:"episode_ids,omitempty"`

	Name     string `json:"name,omitempty"`
	NameLike string `json:"name_like,omitempty"`

	Sort []clause.OrderByColumn // 排序

}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID > 0 {
			return db.Where("id = ?", f.ID)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id IN ?", f.IDs)
		}
		return db
	}
}

func (f Filter) _eq_episode_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.EpisodeID > 0 {
			return db.Where("episode_id = ?", f.EpisodeID)
		}
		return db
	}
}

func (f Filter) _in_episode_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.EpisodeIDs) > 0 {
			return db.Where("episode_id IN ?", f.EpisodeIDs)
		}
		return db
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_episode_id(),
		f._in_episode_ids(),
	}
}

type VideoList []*Video

func (vl *VideoList) GetImageIDs() []uint64 {
	ret := make([]uint64, 0, len(*vl))
	for _, v := range *vl {
		ret = append(ret, v.ImageID)
	}
	return lo.Uniq(ret)
}

func (vl *VideoList) GetIDs() []uint64 {
	ret := make([]uint64, 0, len(*vl))
	for _, v := range *vl {
		ret = append(ret, v.ID)
	}
	return lo.Uniq(ret)
}

func (vl VideoList) GetIDMap() map[uint64]*Video {
	retMap := make(map[uint64]*Video, len(vl))
	for _, val := range vl {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (vl VideoList) GetEpisodeIDMap() map[uint64]VideoList {
	retMap := map[uint64]VideoList{}
	for _, val := range vl {
		retMap[val.EpisodeID] = append(retMap[val.EpisodeID], val)
	}
	return retMap
}
