package video

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	VideoRepo
}

type VideoRepo interface {
	FindByFilter(*gin.Context, *Filter) (VideoList, error)
	FindByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter) (VideoList, error)
	CreateOrUpdate(*gin.Context, *Video) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Video) (uint64, error)
	Create(*gin.Context, *Video) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Video) (uint64, error)
	BatchCreate(*gin.Context, []*Video) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Video) error
	UpdateModelByID(*gin.Context, uint64, *Video) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Video) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error

	UpdateMapByFilter(*gin.Context, *Filter, map[string]interface{}) error
	UpdateMapByFilterWithTx(*gin.Context, *gorm.DB, *Filter, map[string]interface{}) error

	DataPageList(*gin.Context, *Filter, int, int) (total int64, list VideoList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Video, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Video, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
