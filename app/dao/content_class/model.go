package content_class

import (
	"context"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
)

type Model struct {
	dbs.ModelWithDel

	Name string

	NameKey string

	Status dbs.StatusType
}

func (m Model) TableName() string {
	return "content_class"
}

type Filter struct {
	ID uint64 `json:"id,omitempty"`

	IDs []uint64 `json:"ids,omitempty"`

	NotID uint64 `json:"not_id,omitempty"`

	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`

	Sort []clause.OrderByColumn
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _not_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NotID <= 0 {
			return db
		}
		return db.Where("id != ?", f.NotID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			db = db.Order("id desc")
			return db
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._sort(),
	}
}

type ModelList []*Model

func (l ModelList) IDs() []uint64 {
	ids := make([]uint64, len(l))
	for i, v := range l {
		ids[i] = v.ID
	}
	return lo.Uniq(ids)
}

func (l ModelList) GetNameKeys() []string {
	keys := make([]string, len(l))
	for i, v := range l {
		keys[i] = v.NameKey
	}
	keys = lo.Uniq(keys)
	return keys
}

func (l ModelList) GetIDMap() map[uint64]ModelList {
	m := make(map[uint64]ModelList)
	for _, v := range l {
		if _, ok := m[v.ID]; !ok {
			m[v.ID] = make(ModelList, 0)
		}
		m[v.ID] = append(m[v.ID], v)
	}
	return m
}

func (l ModelList) GetMap() map[uint64]*Model {
	m := make(map[uint64]*Model)
	for _, v := range l {
		m[v.ID] = v
	}
	return m
}
