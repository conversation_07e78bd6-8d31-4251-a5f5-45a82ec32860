package adminAccount

import (
	"time"
	"vlab/app/common/dbs"
	adminDto "vlab/app/dto/admin"
	"vlab/pkg/helper"

	"github.com/samber/lo"
)

type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	Account   string    `json:"account,omitempty"`
	Name      string    `json:"name,omitempty"`
	Password  string    `json:"password,omitempty"`
	Mobile    string    `json:"mobile,omitempty"`
	Avatar    string    `json:"avatar,omitempty"`
	RoleID    uint64    `json:"role_id,omitempty"`
	Status    uint32    `json:"status,omitempty"`
	Sort      uint32    `json:"sort,omitempty"`
	IsDeleted uint32    `json:"is_deleted,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "admin_account"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID      uint64
	NotID   uint64
	NotIds  []uint64
	IDS     []uint64
	Account string
	Name    string
	Mobile  string
	RoleID  uint64
	RoleIds []uint64
	NotRids []uint64
	Unique  bool
	Status  uint32
	Sort
}

type Sort struct {
	SortField  dbs.SortField
	SortMethod dbs.SortMethod
}

type ModelList []*Model

func (ml ModelList) GetRids() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.RoleID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetNameMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Name]; !ok {
			retMap[val.Name] = val
		}
	}
	return retMap
}

// SetAccountReqToModel .
func SetAccountReqToModel(req *adminDto.AdminSetAccountReq) *Model {
	m := &Model{
		ID:      req.ID,
		Account: req.Account,
		Name:    req.Name,
		Mobile:  req.Mobile,
		RoleID:  req.RoleID,
		Status:  req.Status,
	}
	if req.ID == 0 {
		m.Password, _ = helper.AesCbcEncrypt(req.Pwd, helper.AesKey, helper.AesIV)
	}
	return m
}
