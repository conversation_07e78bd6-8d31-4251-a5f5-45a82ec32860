package adminAccount

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model, clearToken bool) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m, clearToken)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model, clearToken bool) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"name", "mobile", "avatar", "role_id", "status"}),
		}).Create(&m).Error; err != nil {
		return err
	}

	if clearToken {
		e.RedisClearAccountToken(ctx, m.ID)
	}
	e.RedisClearAccountList(ctx)
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}, clearToken bool) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data, clearToken)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}, clearToken bool) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	if clearToken {
		e.RedisClearAccountToken(ctx, id)
	}
	return nil
}

// UpdateMapByFilter .
func (e *Entry) UpdateMapByFilter(ctx *gin.Context, f *Filter, data map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), f, data)
}

// UpdateMapByFilterWithTx .
func (e *Entry) UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, f *Filter, data map[string]interface{}) error {
	if f.ID == 0 {
		return nil
	}
	query := tx.Model(&Model{})
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}

	if err := query.Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.IDS) > 0 {
		query.Where("id in (?)", f.IDS)
	}
	if f.NotID != 0 {
		query.Where("id <> ?", f.NotID)
	}
	if len(f.NotIds) != 0 {
		query.Where("id not in ?", f.NotIds)
	}
	if f.Unique {
		if f.Account != "" && f.Name != "" {
			query.Where("account = ? or name = ? ", f.Account, f.Name)
		}
	} else {
		if f.Account != "" {
			query.Where("account = ?", f.Account)
		}
		if f.Name != "" {
			query.Where("name like ?", "%"+f.Name+"%")
		}
	}
	if f.Mobile != "" {
		query.Where("mobile like ?", "%"+f.Mobile+"%")
	}
	if f.RoleID != 0 {
		query.Where("role_id = ?", f.RoleID)
	}
	if len(f.RoleIds) > 0 {
		query.Where("role_id in (?)", f.RoleIds)
	}
	if len(f.NotRids) != 0 {
		query.Where("role_id not in ?", f.NotRids)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "id desc"
	if f.SortField != "" && f.SortMethod != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.SortField, f.SortMethod, orderStr)
	}

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Order(orderStr).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "id desc"
	if f.SortField != "" && f.SortMethod != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.SortField, f.SortMethod, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindAll .
func (e *Entry) FindAll(ctx *gin.Context) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{})
	orderStr := "id desc"
	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	ret := &Model{}
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{})
	if err := query.Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FeatchByFilterSort .
func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
	ret := &Model{}
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "id desc"
	if f.SortField != "" && f.SortMethod != "" {
		orderStr = fmt.Sprintf("%s %s, id desc", f.SortField, f.SortMethod)
	}
	if err := query.Order(orderStr).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Select("id").Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// DelByID .
func (e *Entry) DelByID(ctx *gin.Context, id uint64) error {
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id)
	if err := query.Updates(map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
		"status":                  dbs.StatusDisable,
	}).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DelByID err")
		return err
	}
	e.RedisClearAccountToken(ctx, id)
	e.RedisClearAccountList(ctx)
	return nil
}
