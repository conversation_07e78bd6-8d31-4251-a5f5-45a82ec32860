package adminConfig

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
)

type Repo interface {
	ConfigRepo
	ConfigRedisRepo
}

type ConfigRepo interface {
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type ConfigRedisRepo interface {
	RedisEnableConfigList(*gin.Context) (ModelList, error)
	RedisConfigList(*gin.Context) (ModelList, error)
	RedisConfigMap(*gin.Context) (map[string]string, error)
	RedisReloadConfigList(*gin.Context) (ModelList, error)
	RedisClearConfigList(*gin.Context) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
