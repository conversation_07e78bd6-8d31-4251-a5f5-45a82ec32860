package user

import (
	"fmt"

	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid dbs.PluckField = "x_id"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model, clearCache bool) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m, clearCache)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model, clearCache bool) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"nickname",
				"mobile",
				"email",
				"password",
				"avatar",
				"status",
				"watch_ad_time",
				"last_login_time"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	if clearCache {
		e.RedisClearUserInfo(ctx, m.ID)
	}
	return nil
}

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m []*Model) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}

	e.RedisClearUserInfo(ctx, id)
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.IDS) > 0 {
		query.Where("id in (?)", f.IDS)
	}
	if f.UUID != "" {
		query.Where("uuid = ?", f.UUID)
	}
	if f.EmptyUUID {
		query.Where("uuid = ''")
	}
	if f.Nickname != "" {
		query.Where("nickname like ?", "%"+f.Nickname+"%")
	}
	if f.Mobile != "" {
		query.Where("mobile like ?", "%"+f.Mobile+"%")
	}
	if f.EmailLike != "" {
		query.Where("email like ?", "%"+f.EmailLike+"%")
	}
	if f.Email != "" {
		query.Where("email = ?", f.Email)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.PlatformID != 0 {
		query.Where("platform_id = ?", f.PlatformID)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList error")
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindXidsByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{ID: id})
	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByID error")
		return ret, err
	}
	return ret, nil
}

func (e *Entry) FetchByEmail(ctx *gin.Context, email string) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{Email: email})
	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByEmail error")
		return ret, err
	}
	return ret, nil
}

func (e *Entry) FetchByUUID(ctx *gin.Context, uuid string) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{UUID: uuid})
	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByUUID error")
		return ret, err
	}
	return ret, nil
}

// FeatchByFilterSort .
func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchByFilterSort error")
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}
