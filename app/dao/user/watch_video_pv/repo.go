package watchVideoPv

import (
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{})
	if f.ShowID != 0 {
		query.Where("show_id = ?", f.ShowID)
	}
	if f.ChannelID != 0 {
		query.Where("channel_id = ?", f.ChannelID)
	}
	if f.Lang != "" {
		query.Where("lang = ?", f.Lang)
	}
	if f.LogType != 0 {
		query.Where("log_type = ?", f.LogType)
	}
	if f.Date != "" {
		query.Where("date = ?", f.Date)
	}
	if f.StartDate != "" {
		query.Where("date >= ?", f.StartDate)
	}
	if f.EndDate != "" {
		query.Where("date <= ?", f.EndDate)
	}

	return query
}

// ShowDailyActive 剧日活统计
func (e *Entry) ShowDailyActive(ctx *gin.Context, f *Filter) (ShowDailyActiveList, error) {
	subQuery := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Select("entity_id, entity_type,date").
		Group("entity_id, entity_type,date")

	ret := ShowDailyActiveList{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).
		Table("(?) as t", subQuery).
		Select("date, count(*) num").Group("date").Order("date desc").Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// ShowDailyPlaynum 剧 日播放量/日下载量 统计
func (e *Entry) ShowDailyCountByFilter(ctx *gin.Context, f *Filter) (ShowDailyActiveList, error) {
	ret := ShowDailyActiveList{}
	if err := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Select("date, count(id) num").Group("date").Order("date desc").Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// ShowCountTopByFilter 剧 播放量/下载量 排行统计
func (e *Entry) ShowCountTopByFilter(ctx *gin.Context, f *Filter) (ShowPlaynumTopList, error) {
	ret := ShowPlaynumTopList{}
	if err := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Select("show_id, count(id) num").Group("show_id").Order("num desc").Limit(f.Limit).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
