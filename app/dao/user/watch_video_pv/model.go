package watchVideoPv

import (
	"time"
	"vlab/app/common/dbs"

	"github.com/samber/lo"
)

type LogType uint32

const (
	LogTypePlay     LogType = 1 // 播放
	LogTypeDownload LogType = 2 // 下载
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	EntityID   uint64    `json:"entity_id,omitempty"`
	EntityType uint32    `json:"entity_type,omitempty"`
	ShowID     uint64    `json:"show_id,omitempty"`
	EpisodeID  uint64    `json:"episode_id,omitempty"`
	Num        uint32    `json:"num,omitempty"`
	Date       string    `json:"date,omitempty"`
	Lang       string    `json:"lang,omitempty"`
	ClientType uint32    `json:"client_type,omitempty"`
	LogType    LogType   `json:"log_type,omitempty"`
	Resolution uint32    `json:"resolution,omitempty"`
	VersionID  uint64    `json:"version_id,omitempty"`
	ChannelID  uint64    `json:"channel_id,omitempty"`
	TraceID    string    `json:"trace_id,omitempty"`
	Header     string    `json:"header,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_watch_video_pv"
}

type Filter struct {
	ID        uint64
	ShowID    uint64
	ChannelID uint64
	Lang      string
	Date      string
	StartDate string
	EndDate   string
	LogType   LogType
	Limit     int
	Sort      dbs.CommonSort
}

type ModelList []*Model

type ShowDailyActive struct {
	Date time.Time `json:"date"`
	Num  int64     `json:"num"`
}

func (m *ShowDailyActive) GetDate() string {
	return m.Date.Format(dbs.TimeDateFormat)
}

type ShowDailyActiveList []*ShowDailyActive

type ShowPlaynumTop struct {
	ShowID uint64 `json:"show_id"`
	Num    int64  `json:"num"`
}

type ShowPlaynumTopList []*ShowPlaynumTop

func (ml ShowPlaynumTopList) GetShowIds() []uint64 {
	ret := lo.Map(ml, func(item *ShowPlaynumTop, idx int) uint64 {
		return item.ShowID
	})
	ret = lo.Uniq(ret)
	return ret
}
