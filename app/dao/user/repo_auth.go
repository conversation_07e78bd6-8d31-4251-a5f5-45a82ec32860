package user

import (
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// Create .
func (e *Entry) CreateAuth(ctx *gin.Context, m *Auth) error {
	return e.CreateAuthWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateAuthWithTx(ctx *gin.Context, tx *gorm.DB, m *Auth) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// CreateOrUpdateAuthWithTx .
func (e *Entry) CreateOrUpdateAuthWithTx(ctx *gin.Context, tx *gorm.DB, m *Auth) error {
	if err := tx.Model(&Auth{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "auth_type"}, {Name: "auth_uid"}},
			DoUpdates: clause.AssignmentColumns([]string{"auth_token"}),
		}).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateAuthMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Auth{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

// UpdateAuthMapByUdx .
func (e *Entry) UpdateAuthMapByUdx(ctx *gin.Context, authType, platformID uint32, authUid string, data map[string]interface{}) error {
	return e.UpdateAuthMapByUdxWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), authType, platformID, authUid, data)
}

// UpdateAuthMapByUdx .
func (e *Entry) UpdateAuthMapByUdxWithTx(ctx *gin.Context, tx *gorm.DB, authType, platformID uint32, authUid string, data map[string]interface{}) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Auth{}).
		Where("auth_type = ?", authType).Where("platform_id = ?", platformID).Where("auth_uid = ?", authUid).
		Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e *Entry) buildAuthQuery(tx *gorm.DB, f *AuthFilter, sel bool) *gorm.DB {
	query := tx.Table(_Auth.TableName() + " a").
		Joins("LEFT JOIN " + _Model.TableName() + " u ON a.user_id = u.id").Where("u.is_deleted = 0")

	if f.UserID > 0 {
		query.Where("a.user_id = ?", f.UserID)
	}
	if f.AuthType > 0 {
		query.Where("a.auth_type = ?", f.AuthType)
	}
	if f.PlatformID > 0 {
		query.Where("a.platform_id = ?", f.PlatformID)
	}

	if f.AuthUid != "" {
		query.Where("a.auth_uid = ?", f.AuthUid)
	}
	if f.AuthToken != "" {
		query.Where("a.auth_token = ?", f.AuthToken)
	}
	if f.AuthEmail != "" {
		query.Where("a.auth_email = ?", f.AuthEmail)
	}
	if f.Status != 0 {
		query.Where("u.status = ? ", f.Status)
	}

	if sel {
		return query.Select("u.*, a.id as auth_id, a.auth_type, a.auth_uid, a.auth_token")
	}
	return query
}

// AuthPageList .
func (e *Entry) AuthPageList(ctx *gin.Context, f *AuthFilter, page int, limit int) (total int64, list AuthList, err error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false)
	query.Select("a.id").Count(&total)
	if err = query.Select("u.*, a.id as auth_id, a.auth_type, a.auth_uid, a.auth_token").Offset((page - 1) * limit).Limit(int(limit)).
		Order("a.id desc").Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

// FindAuthByFilter .
func (e *Entry) FindAuthByFilter(ctx *gin.Context, f *AuthFilter) (AuthList, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, true)
	ret := AuthList{}
	if err := query.Order("a.id desc").Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

// FindAuthRecordsByFilter 查找纯Auth记录列表，不包含用户信息
func (e *Entry) FindAuthRecordsByFilter(ctx *gin.Context, f *AuthFilter) ([]*Auth, error) {

	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false)

	ret := []*Auth{}
	if err := query.Order("id desc").Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindAuthRecordsByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchAuthRecordByFilter 查找单个纯Auth记录
func (e *Entry) FetchAuthRecordByFilter(ctx *gin.Context, f *AuthFilter) (*Auth, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false)

	ret := &Auth{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchAuthRecordByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchAuthByUdx .
func (e *Entry) FetchAuthByUdx(ctx *gin.Context, authType, platformID uint32, authUid string) (*UserAuth, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &AuthFilter{AuthType: authType, PlatformID: platformID, AuthUid: authUid}, true)
	ret := &UserAuth{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchAuthByUdx error")
		return ret, err
	}
	return ret, nil
}

// FetchAuthByUdx .
func (e *Entry) FetchAuthByUid(ctx *gin.Context, uid uint64) (*UserAuth, error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &AuthFilter{UserID: uid}, true)
	ret := &UserAuth{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchAuthByUdx error")
		return ret, err
	}
	return ret, nil
}

// CountAuthByFilter .
func (e *Entry) CountAuthByFilter(ctx *gin.Context, f *AuthFilter) (num int64, err error) {
	query := e.buildAuthQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f, false).Select("a.id")
	if err := query.Count(&num).Error; err != nil {
		return 0, err
	}
	return
}

// FetchAuthByUserAndType 根据用户ID和认证类型查找认证记录，使用Filter统一查询
func (e *Entry) FetchAuthByUserAndType(ctx *gin.Context, userID uint64, authType uint32, platformID uint32) (*Auth, error) {
	return e.FetchAuthRecordByFilter(ctx, &AuthFilter{
		UserID:     userID,
		AuthType:   authType,
		PlatformID: platformID,
	})
}

// FetchAuthByTypeAndUid 根据认证类型和认证ID查找认证记录，使用Filter统一查询
func (e *Entry) FetchAuthByTypeAndUid(ctx *gin.Context, authType uint32, authUid string, platformID uint32) (*Auth, error) {
	return e.FetchAuthRecordByFilter(ctx, &AuthFilter{
		AuthType:   authType,
		AuthUid:    authUid,
		PlatformID: platformID,
	})
}

// FindAuthsByUserID 获取用户所有认证方式，使用Filter统一查询
func (e *Entry) FindAuthsByUserID(ctx *gin.Context, userID uint64, platformID uint32) ([]*Auth, error) {
	return e.FindAuthRecordsByFilter(ctx, &AuthFilter{
		UserID:     userID,
		PlatformID: platformID,
	})
}

// DeleteAuth 删除认证记录
func (e *Entry) DeleteAuth(ctx *gin.Context, authID uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).
		Table(_Auth.TableName()).
		Where("id = ?", authID).
		Delete(&Auth{}).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DeleteAuth error")
		return err
	}
	return nil
}
