package watchVideoLog

import (
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m ModelList) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m ModelList) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(m).Error; err != nil {
		return err
	}
	return nil
}
