package watchVideoLog

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/app/dao/user"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
)

// RedisVerifyWatchVideo .
func (e *Entry) RedisVerifyWatchVideo(ctx *gin.Context, showID, episodeID, eid uint64, etype user.EntityType) bool {
	var (
		date     = timeUtil.NowToDateStringByZone(ctx)
		cacheKey = redisPkg.GetWatchVideoSetKey(date, showID, episodeID)
		member   = fmt.Sprintf("etype_eid_%d_%d", etype, eid)
	)

	exists, err := e.RedisCli.SIsMember(ctx, cacheKey, member).Result()
	if err != nil {
		fmt.Printf("Error verifying nonce: %v\n", err)
		return false
	}
	if !exists {
		e.RedisStoreWatchVideo(ctx, showID, episodeID, eid, etype)
	}
	return exists
}

// RedisStoreWatchVideo .
func (e *Entry) RedisStoreWatchVideo(ctx *gin.Context, showID, episodeID, eid uint64, etype user.EntityType) bool {
	var (
		date     = timeUtil.NowToDateStringByZone(ctx)
		cacheKey = redisPkg.GetWatchVideoSetKey(date, showID, episodeID)
		member   = fmt.Sprintf("etype_eid_%d_%d", etype, eid)
	)

	pipe := e.RedisCli.Pipeline()
	defer pipe.Close()

	saddCmd := pipe.SAdd(ctx.Request.Context(), cacheKey, member)
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.GetRedisExpireTime(dbs.ResidExpireTime))

	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RedisStoreWatchVideo")
		return false
	}
	return saddCmd.Val() > 0
}
