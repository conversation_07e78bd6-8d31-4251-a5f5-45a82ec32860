package watchVideoLog

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

// TODO 替换
var (
	// defaultEntry         Repo
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetRepo() *Entry {
	// func GetApi() Repo {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
