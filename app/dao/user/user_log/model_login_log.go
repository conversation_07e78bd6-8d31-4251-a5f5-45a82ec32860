package userLog

import (
	"time"
	"vlab/app/common/dbs"
)

// TODO 分表 or kafka异步写入es
type LoginLog struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	UserID     uint64    `json:"user_id,omitempty"`
	ClientIp   string    `json:"client_ip,omitempty"`
	ClientType uint32    `json:"client_type,omitempty"`
	Device     string    `json:"device,omitempty"`
	UserAgent  string    `json:"user_agent,omitempty"`
	Header     string    `json:"header,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *LoginLog) TableName() string {
	return "user_login_log"
}

func (m *LoginLog) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type LoginLogFilter struct {
	ID     uint64
	UserID uint64
	Sort   dbs.CommonSort
}

type LoginLogList []*LoginLog
