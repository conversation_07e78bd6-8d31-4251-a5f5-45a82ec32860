package user

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	UserRepo
	AuthRepo

	RedisUser
	RedisReq
}

type UserRepo interface {
	CreateOrUpdate(ctx *gin.Context, m *Model, clearToken bool) error
	CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model, clearToken bool) error
	Create(ctx *gin.Context, m *Model) error
	CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error
	BatchCreate(ctx *gin.Context, m []*Model) error
	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error
	UpdateModelByID(ctx *gin.Context, id uint64, m *Model) error
	UpdateModelByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, m *Model) error
	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error
	DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error)
	FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
	FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error)
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	FetchByEmail(*gin.Context, string) (*Model, error)
	FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error)
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
}

type AuthRepo interface {
	CreateOrUpdateAuthWithTx(ctx *gin.Context, tx *gorm.DB, m *Auth) error
	CreateAuthWithTx(ctx *gin.Context, tx *gorm.DB, m *Auth) error
	UpdateAuthMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	AuthPageList(ctx *gin.Context, f *AuthFilter, page int, limit int) (total int64, list AuthList, err error)
	FindAuthByFilter(ctx *gin.Context, f *AuthFilter) (AuthList, error)
	FetchAuthByUdx(ctx *gin.Context, authType uint32, platformID uint32, authUid string) (*UserAuth, error)
	FetchAuthByUid(ctx *gin.Context, uid uint64) (*UserAuth, error)
	FetchAuthByUserAndType(ctx *gin.Context, userID uint64, authType uint32) (*Auth, error)
	FetchAuthByTypeAndUid(ctx *gin.Context, authType uint32, authUid string) (*Auth, error)
	FindAuthsByUserID(ctx *gin.Context, userID uint64) ([]*Auth, error)
	DeleteAuth(ctx *gin.Context, authID uint64) error
	CountAuthByFilter(ctx *gin.Context, f *AuthFilter) (num int64, err error)

	// 基础Filter查询方法
	FindAuthRecordsByFilter(ctx *gin.Context, f *AuthFilter) ([]*Auth, error)
	FetchAuthRecordByFilter(ctx *gin.Context, f *AuthFilter) (*Auth, error)
}

type RedisUser interface {
	RedisUserInfo(ctx *gin.Context, uid uint64) (*Model, error)
	RedisReloadUserInfo(ctx *gin.Context, uid uint64) (*Model, error)
	RedisClearUserInfo(ctx *gin.Context, uid uint64) error

	RedisGetUserToken(ctx *gin.Context, uid uint64) (string, error)
	RedisSetUserToken(ctx *gin.Context, uid uint64, token string) error
	RedisClearUserToken(ctx *gin.Context, uid uint64) error
	RedisClearUserAllToken(ctx *gin.Context, uid uint64) error
}

type RedisReq interface {
	RedisVerifyReqID(ctx *gin.Context, reqID string) bool
	RedisStoreClientReqID(ctx *gin.Context, reqID string) bool
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	// defaultRepo         Repo
	defaultRepo         *Entry
	defaultRepoInitOnce sync.Once
)

// func GetRepo() Repo {
func GetRepo() *Entry {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
