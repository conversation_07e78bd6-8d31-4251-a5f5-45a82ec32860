package user

import "time"

type AuthType uint32

const (
	AtApple    AuthType = 1 // app ID
	AtGoogle   AuthType = 2 // google
	AtFacebook AuthType = 3 // facebook
	AtEmail    AuthType = 4 // email
	AtMobile   AuthType = 5 // mobile
)

type Auth struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	UserID     uint64    `json:"user_id,omitempty"`
	ChannelID  uint32    `json:"channel_id,omitempty"`
	PlatformID uint32    `json:"platform_id,omitempty"`
	AuthType   uint32    `json:"auth_type,omitempty"`
	AuthUid    string    `json:"auth_uid,omitempty"`
	AuthToken  string    `json:"auth_token,omitempty"`
	AuthEmail  string    `json:"auth_email,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

type UserAuth struct {
	*Model
	AuthID    uint64 `json:"auth_id,omitempty"`
	AuthType  uint32 `json:"auth_type,omitempty"`
	AuthUid   string `json:"auth_uid,omitempty"`
	AuthToken string `json:"auth_token,omitempty"`
	AuthEmail string `json:"auth_email,omitempty"`
}

func (m *Auth) TableName() string {
	return "user_auth"
}

type AuthFilter struct {
	UserID     uint64
	AuthType   uint32
	PlatformID uint32
	AuthUid    string
	AuthToken  string
	AuthEmail  string
	Status     uint32
}

type AuthList []*Auth

func (ml AuthList) GetIDMap() map[uint64]*Auth {
	retMap := make(map[uint64]*Auth, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
