package user

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
)

func (e *Entry) RedisVerifyReqID(ctx *gin.Context, reqID string) bool {
	cacheKey := redisPkg.GetClientReqSlidingWindowKey(timeUtil.GetSlidingWindowTime(ctx, dbs.SlidingWindowTimeInterval))
	exists, err := e.RedisCli.SIsMember(ctx, cacheKey, reqID).Result()
	if err != nil {
		fmt.Printf("Error verifying nonce: %v\n", err)
		return false
	}
	if !exists {
		e.RedisStoreClientReqID(ctx, reqID)
	}
	return exists
}

// RedisStoreClientReqID .
func (e *Entry) RedisStoreClientReqID(ctx *gin.Context, reqID string) bool {
	cacheKey := redisPkg.GetClientReqSlidingWindowKey(timeUtil.GetSlidingWindowTime(ctx, dbs.SlidingWindowTimeInterval))

	pipe := e.RedisCli.Pipeline()
	defer pipe.Close()

	saddCmd := pipe.SAdd(ctx.Request.Context(), cacheKey, reqID)
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.SlidingWindowTimeInterval)

	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RedisStoreClientReqIDErr")
		return false
	}
	return saddCmd.Val() > 0
}
