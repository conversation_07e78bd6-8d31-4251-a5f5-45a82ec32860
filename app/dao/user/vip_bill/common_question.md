## 常见问题
#### 如何防止掉单
在支付系统中，最重要的是用户权益。很多时候用户明明已经下单并且付钱了，但是 VIP 没有充上，钻石没有到账，是用户无法接受的。
一个很常见的 Case 是：用户付钱后，断开网络连接，这时后台系统没有收到消息，该怎么处理？
这里我们做了两步来保证：
 - 客户端补偿策略：采用本地事务日志+断点续传设计。客户端在划账请求的 ACK 之前先调用后台接口生成订单，如果用户在支付后突然断网，重新打开客户端后会检查当前是否存在未确认的划账请求，如果有就再调用一次后台订单再 ACK。同时后台通过幂等性来保证用户不会多次支付同一笔订单；
 - 业务系统双保险告警处理：业务平台接收到支付网关回调时，发现已有订单就更新订单状态；没有订单就发告警，进行人工处理；

#### 如何保证账单和订单正确性
在传统的公司交易中，都会需要会计来对账，将每月的账单和收支金额总额对比，防止出现账不对钱的坏账。
所以，一方面为了保证订单的有序性，我们在业务系统禁止随意扭转订单状态；另一方面我们在支付网关进行每天的定时对账：
- 状态机检查装置：每次触发业务回调时，业务后台都会判断数据库状态和支付平台的后台状态一致性，
  - 若是不一致，则判断状态是否可以扭转，若是不能扭转则告警出来；
  - 若是可以扭转则更新 DB 里订单的状态；
- 服务端哨兵系统：每小时扫描未完结订单与支付平台对账。
  - 支付网关每天定时比较昨日数据库和支付平台后台的交易状态差异，有差错的部分进行告警。