package device

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisDeviceInfo .
func (e *Entry) RedisDeviceInfo(ctx *gin.Context, deviceNo string) (*Model, error) {
	var (
		cacheKey  = redisPkg.GetDeviceInfoKey(deviceNo)
		ret       = &Model{}
		cacheData string
		err       error
	)
	if cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result(); err != nil {
		if err == redis.Nil {
			return e.RedisReloadDeviceInfo(ctx, deviceNo)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadDeviceInfo redis重载数据列表
func (e *Entry) RedisReloadDeviceInfo(ctx *gin.Context, deviceNo string) (*Model, error) {
	var (
		cacheKey   = redisPkg.GetDeviceInfoKey(deviceNo)
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		cacheData  []byte
		uInfo      = &Model{}
		err        error
	)
	if uInfo, err = e.FetchByDeviceID(ctx, deviceNo); err != nil {
		return nil, err
	}
	if uInfo.ID > dbs.False {
		if cacheData, err = json.Marshal(uInfo); err != nil {
			return nil, err
		}

		if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
			return nil, err
		}
	}

	return uInfo, nil
}

// RedisClearDeviceInfo .
func (e *Entry) RedisClearDeviceInfo(ctx *gin.Context, deviceNo string) error {
	cacheKey := redisPkg.GetDeviceInfoKey(deviceNo)
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}

func (e *Entry) RedisVerifyDeviceID(ctx *gin.Context, deviceNo string) bool {
	cacheKey := redisPkg.GetDeviceNoSlidingWindowKey(timeUtil.GetWeekSlidingWindowTime(ctx))
	exists, err := e.RedisCli.SIsMember(ctx, cacheKey, deviceNo).Result()
	if err != nil {
		fmt.Printf("Error verifying nonce: %v\n", err)
		return false
	}
	if !exists {
		e.RedisStoreClientDeviceNo(ctx, deviceNo)
	}
	return exists
}

// RedisStoreClientDeviceID .
func (e *Entry) RedisStoreClientDeviceNo(ctx *gin.Context, deviceNo string) bool {
	cacheKey := redisPkg.GetDeviceNoSlidingWindowKey(timeUtil.GetWeekSlidingWindowTime(ctx))

	pipe := e.RedisCli.Pipeline()
	defer pipe.Close()

	saddCmd := pipe.SAdd(ctx.Request.Context(), cacheKey, deviceNo)
	pipe.Expire(ctx.Request.Context(), cacheKey, dbs.GetRedisExpireTime(dbs.RedisExpireTimeWeek))

	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RedisStoreClientDeviceIDErr")
		return false
	}
	return saddCmd.Val() > 0
}
