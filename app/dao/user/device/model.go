package device

import (
	"time"
	"vlab/app/common/dbs"

	"github.com/samber/lo"
)

type Model struct {
	ID          uint64    `gorm:"primarykey" json:"id"`
	UserID      uint64    `json:"user_id,omitempty"`
	DeviceID    string    `json:"device_id,omitempty"`
	Status      uint32    `json:"status,omitempty"`
	WatchAdTime int64     `json:"watch_ad_time,omitempty"`
	IsDeleted   uint32    `json:"is_deleted,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_device"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID       uint64
	UID      uint64
	DeviceID string
	Status   uint32
	Sort     dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDS() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
