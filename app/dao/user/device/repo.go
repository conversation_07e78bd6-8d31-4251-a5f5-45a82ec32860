package device

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid dbs.PluckField = "x_id"
)

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "device_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"status"}),
		}).Create(&m).Error; err != nil {
		return err
	}

	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, deviceNo string, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, deviceNo, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, deviceNo string, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	e.RedisClearDeviceInfo(ctx, deviceNo)
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if f.UID != 0 {
		query.Where("user_id = ?", f.UID)
	}
	if f.DeviceID != "" {
		query.Where("device_id = ?", f.DeviceID)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}

	return query
}

// DataPageList .
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList error")
		return 0, nil, err
	}
	return total, list, nil
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// FindXidsByFilter .
func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindXidsByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchByDeviceID .
func (e *Entry) FetchByDeviceID(ctx *gin.Context, deviceID string) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{DeviceID: deviceID})
	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByDeviceID error")
		return ret, err
	}
	return ret, nil
}

// FeatchByFilterSort .
func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchByFilterSort error")
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}
