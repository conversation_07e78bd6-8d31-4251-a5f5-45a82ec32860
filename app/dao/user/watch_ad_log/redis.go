package watchAdLog

import (
	"vlab/app/common/dbs"
	"vlab/app/dao/user"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gogf/gf/v2/util/gconv"
)

// RedisGetWatchAd .
func (e *Entry) RedisGetWatchAd(ctx *gin.Context, eid uint64, etype user.EntityType) (uint32, error) {
	var (
		date           = timeUtil.NowToDateStringByZone(ctx)
		cacheKey       = redisPkg.GetWatchAdKey(date, eid, int(etype))
		cacheData, err = e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	)
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return 0, nil
		}
		return 0, err
	}

	return gconv.Uint32(cacheData), nil
}

// RedisSetWatchAd .
func (e *Entry) RedisSetWatchAd(ctx *gin.Context, eid uint64, etype user.EntityType) error {
	var (
		date       = timeUtil.NowToDateStringByZone(ctx)
		cacheKey   = redisPkg.GetWatchAdKey(date, eid, int(etype))
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		pipe       = e.RedisCli.Pipeline()
	)
	defer pipe.Close()

	pipe.Incr(ctx.Request.Context(), cacheKey)
	pipe.Expire(ctx.Request.Context(), cacheKey, expireTime)

	if _, err := pipe.Exec(ctx.Request.Context()); err != nil {
		log.Ctx(ctx).WithError(err).Warn("RedisSetWatchAd")
		return err
	}

	return nil
}
