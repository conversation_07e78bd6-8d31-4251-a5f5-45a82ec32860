package watchAdLog

import (
	"time"
	"vlab/app/common/dbs"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID         uint64    `gorm:"primarykey" json:"id"`
	EntityID   uint64    `json:"entity_id,omitempty"`
	EntityType uint32    `json:"entity_type,omitempty"`
	BeforeTime int64     `json:"before_time,omitempty"`
	AddTime    int64     `json:"add_time,omitempty"`
	AfterTime  int64     `json:"after_time,omitempty"`
	ClientType uint32    `json:"client_type,omitempty"`
	VersionID  uint64    `json:"version_id,omitempty"`
	ChannelID  uint64    `json:"channel_id,omitempty"`
	TraceID    string    `json:"trace_id,omitempty"`
	Header     string    `json:"header,omitempty"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "user_watch_ad_log"
}

type Filter struct {
	ID   uint64
	Sort dbs.CommonSort
}

type ModelList []*Model
