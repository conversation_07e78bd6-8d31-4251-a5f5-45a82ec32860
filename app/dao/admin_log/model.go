package adminLog

import (
	"time"
	"vlab/app/common/dbs"
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID        uint64    `gorm:"primarykey" json:"id"`
	AccountID uint64    `json:"account_id,omitempty"`
	RoleID    uint64    `json:"role_id,omitempty"`
	Method    string    `json:"method,omitempty"`
	TraceID   string    `json:"trace_id,omitempty"`
	Url       string    `json:"url,omitempty"`
	ClientIp  string    `json:"client_ip,omitempty"`
	Header    string    `json:"header,omitempty"`
	Body      string    `json:"body,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "admin_log"
}

type Filter struct {
	ID        uint64
	AccountID uint64
	RoleID    uint64
	Sort      dbs.CommonSort
}

type ModelList []*Model
