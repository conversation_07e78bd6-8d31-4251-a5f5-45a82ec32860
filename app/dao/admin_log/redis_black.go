package adminLog

import (
	"encoding/json"
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisLogBlackList .
func (e *Entry) RedisLogBlackList(ctx *gin.Context) (LogBlackList, error) {
	cacheKey := redisPkg.LogBlackListKey
	ret := LogBlackList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadLogBlackList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisLogBlackMap .
func (e *Entry) RedisLogBlackMap(ctx *gin.Context) (map[string]struct{}, error) {
	list, err := e.RedisLogBlackList(ctx)
	if err != nil {
		return nil, err
	}
	return list.GetUrlEmptyMap(), nil
}

// RedisReloadLogBlackList redis重载数据列表
func (e *Entry) RedisReloadLogBlackList(ctx *gin.Context) (LogBlackList, error) {
	var (
		cacheKey   = redisPkg.LogBlackListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = LogBlackList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindBlackByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearLogBlackList .
func (e *Entry) RedisClearLogBlackList(ctx *gin.Context) error {
	cacheKey := redisPkg.LogBlackListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
