package channelkey

import (
	"encoding/json"
	"vlab/app/common/dbs"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableChannelKeyList .
func (e *Entry) RedisEnableChannelKeyList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisChannelKeyList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisChannelKeyList .
func (e *Entry) RedisChannelKeyList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.ChannelKeyListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err == redis.Nil {
			return e.RedisReloadChannelKeyList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisChannelKeyMap .
func (e *Entry) RedisChannelKeyMap(ctx *gin.Context) (map[uint64]*Model, error) {
	dataList, err := e.RedisChannelKeyList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetChannelIDMap(), nil
}

func (e *Entry) RedisEnableChannelKeyInfo(ctx *gin.Context, channelID uint64) (*Model, error) {
	channelKeyInfo := &Model{}
	channelKeyMap, err := e.RedisEnableChannelKeyMap(ctx)
	if err != nil {
		log.Ctx(ctx).Warn("GetCtxChannelKeyInfo channel key invalid")
		return channelKeyInfo, ecode.ChannelIDInvalidErr
	}

	if info, ok := channelKeyMap[channelID]; ok {
		return info, nil
	}
	return channelKeyInfo, ecode.ChannelIDInvalidErr
}

// RedisChannelKeyMap .
func (e *Entry) RedisEnableChannelKeyMap(ctx *gin.Context) (map[uint64]*Model, error) {
	dataList, err := e.RedisEnableChannelKeyList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetChannelIDMap(), nil
}

// RedisReloadChannelKeyList redis重载数据列表
func (e *Entry) RedisReloadChannelKeyList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.ChannelKeyListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearChannelKeyList .
func (e *Entry) RedisClearChannelKeyList(ctx *gin.Context) error {
	cacheKey := redisPkg.ChannelKeyListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
