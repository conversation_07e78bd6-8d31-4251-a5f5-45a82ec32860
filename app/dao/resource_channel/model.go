package company

import (
	"time"

	"vlab/app/common/dbs"
)

type UpdateType uint32

const (
	UpdateTypeNone UpdateType = iota + 1
	UpdateTypeNotice
	UpdateTypeForce
)

type Model struct {
	ID              uint64     `gorm:"primarykey" json:"id"`
	PlatformID      uint32     `json:"platform_id,omitempty"`
	Name            string     `json:"name,omitempty"`
	Sort            uint32     `json:"sort,omitempty"`
	Status          uint32     `json:"status,omitempty"`
	UpdateVersionID uint64     `json:"update_version_id,omitempty"`
	UpdateType      UpdateType `json:"update_type,omitempty"`
	Url             string     `json:"url,omitempty"`
	Desc            string     `json:"desc,omitempty"`
	IsDeleted       uint32     `json:"is_deleted,omitempty"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "resource_channel"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type UpdateInfo struct {
	UpdateType UpdateType `json:"update_type,omitempty"`
	Url        string     `json:"url,omitempty"`
	Desc       string     `json:"desc,omitempty"`
}

func (m *Model) GetUpdateInfo() *UpdateInfo {
	return &UpdateInfo{
		UpdateType: m.UpdateType,
		Url:        m.Url,
		Desc:       m.Desc,
	}
}

func (m *Model) NeedUpdate() bool {
	return m.UpdateType != UpdateTypeNone
}

func (m *Model) NeedForceUpdate() bool {
	return m.UpdateType == UpdateTypeForce
}

func (m *Model) NeedUpdateNone() bool {
	return m.UpdateType == 0 || m.UpdateType == UpdateTypeNone
}

type Filter struct {
	ID     uint64
	NotID  uint64
	IDS    []uint64
	Name   string
	Status uint32
	Sort   dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetNameMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.Name]; !ok {
			retMap[val.Name] = val
		}
	}
	return retMap
}

func (ml ModelList) GetVersionIDs() []uint64 {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.UpdateVersionID]; !ok {
			retMap[val.UpdateVersionID] = struct{}{}
		}
	}
	retList := make([]uint64, 0, len(retMap))
	for k := range retMap {
		retList = append(retList, k)
	}
	return retList
}

