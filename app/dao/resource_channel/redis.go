package company

import (
	"encoding/json"
	"vlab/app/common/dbs"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableChannelList .
func (e *Entry) RedisEnableChannelList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisChannelList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisChannelList .
func (e *Entry) RedisChannelList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.ChannelListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadChannelList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisChannelMap .
func (e *Entry) RedisChannelMap(ctx *gin.Context) (map[uint64]*Model, error) {
	dataList, err := e.RedisChannelList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetIDMap(), nil
}

func (e *Entry) RedisEnableChannelInfo(ctx *gin.Context, channelID uint64) (*Model, error) {
	channelInfo := &Model{}
	channelMap, err := e.RedisEnableChannelMap(ctx)
	if err != nil {
		log.Ctx(ctx).Warn("RedisEnableChannelInfo channel invalid")
		return channelInfo, ecode.ChannelIDInvalidErr
	}

	if info, ok := channelMap[channelID]; ok {
		return info, nil
	}
	return channelInfo, ecode.ChannelIDInvalidErr
}

// RedisChannelMap .
func (e *Entry) RedisEnableChannelMap(ctx *gin.Context) (map[uint64]*Model, error) {
	dataList, err := e.RedisEnableChannelList(ctx)
	if err != nil {
		return nil, err
	}
	return dataList.GetIDMap(), nil
}

// RedisReloadChannelList redis重载数据列表
func (e *Entry) RedisReloadChannelList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.ChannelListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearChannelList .
func (e *Entry) RedisClearChannelList(ctx *gin.Context) error {
	cacheKey := redisPkg.ChannelListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
