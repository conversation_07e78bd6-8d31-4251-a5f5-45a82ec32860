package content_recommend

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
)

type Recommend struct {
	dbs.ModelWithDel

	ShowID    uint64 `json:"show_id,omitempty"`    // 剧集ID
	Order     uint32 `json:"order,omitempty"`      // 排序
	ImageID   uint64 `json:"image_id,omitempty"`   // 图片ID
	ChannelID uint64 `json:"channel_id,omitempty"` //

	Name    string `json:"name,omitempty"`     // 名字
	NameKey string `json:"name_key,omitempty"` // 名字多语言key
	Status  uint32 `json:"status,omitempty"`   // 状态
}

func (m *Recommend) TableName() string {
	return "content_recommend"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID  uint64   `json:"show_id,omitempty"`  // 剧集ID
	ShowIDs []uint64 `json:"show_ids,omitempty"` // 剧集ID

	ChannelID uint64 `json:"channel_id,omitempty"` //

	NameLike string `json:"name_like,omitempty"` // 名字模糊查询

	Sort []clause.OrderByColumn

	Status uint32 `json:"status,omitempty"` // 状态
}

type RecommendList []*Recommend

func (pl RecommendList) GetIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ID)
	}
	return ids
}

func (pl RecommendList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ShowID)
	}
	return ids
}

func (pl RecommendList) GetKeys() []string {
	var keys []string
	for _, v := range pl {
		keys = append(keys, v.NameKey)
	}
	return keys
}

func (pl RecommendList) GetImageIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ImageID)
	}
	return ids
}

func (pl RecommendList) GetChannelIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		if v.ChannelID > 0 {
			ids = append(ids, v.ChannelID)
		}
	}
	return ids
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _eq_channel_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ChannelID <= 0 {
			return db
		}
		return db.Where("channel_id = ?", f.ChannelID)
	}
}

func (f Filter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NameLike == "" {
			return db
		}
		return db.Where("name like ? OR name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_recommend' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%", "%"+f.NameLike+"%")
		//return db.Where("name like ?", "%"+f.NameLike+"%").
		//	Or("name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_recommend' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%")
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id DESC")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._like_name(),
		f._eq_status(),
		f._eq_channel_id(),
		f._sort(),
	}
}
