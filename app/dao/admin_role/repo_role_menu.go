package adminRole

import (
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DelByRidWithTx .
func (e *Entry) DelByRidWithTx(ctx *gin.Context, tx *gorm.DB, rid uint64) error {
	if err := tx.Model(&RoleMenu{}).Where("role_id = ?", rid).Delete(&RoleMenu{}).Error; err != nil {
		return err
	}
	e.RedisClearRoleMenuIds(ctx, rid)
	return nil
}

// BatchCreateRoleMenuWithTx .
func (e *Entry) BatchCreateRoleMenuWithTx(tx *gorm.DB, m []*RoleMenu) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// FindMenuIdsByRole .
func (e *Entry) FindMenuIdsByRole(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&RoleMenu{}).Where("role_id = ?", rid)
	ret := RoleAuthMenuIds{}
	if err := query.Distinct().Pluck("menu_id", &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
