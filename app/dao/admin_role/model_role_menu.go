package adminRole

type RoleMenu struct {
	RoleID uint64 `json:"role_id"`
	MenuID uint64 `json:"menu_id"`
}

func (m *RoleMenu) TableName() string {
	return "admin_role_menu"
}

type RoleMenuFilter struct {
	RoleID uint64
	MenuID uint64
}

type RoleAuthMenuIds []uint64

func (am RoleAuthMenuIds) GetAuthMenuEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(am))
	for _, menuId := range am {
		if _, ok := retMap[menuId]; !ok {
			retMap[menuId] = struct{}{}
		}
	}
	return retMap
}
