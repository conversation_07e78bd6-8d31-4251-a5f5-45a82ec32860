package adminRole

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Repo interface {
	RoleRepo
	RoleMenuRepo
	RoleRedis
	RoleMenuRedis
}

type RoleRepo interface {
	CreateOrUpdate(ctx *gin.Context, m *Model) error
	CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error
	DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error)
	FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
	FindAll(ctx *gin.Context) (ModelList, error)
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
	DelByID(ctx *gin.Context, tx *gorm.DB, id uint64) error
}

type RoleMenuRepo interface {
	DelByRidWithTx(ctx *gin.Context, tx *gorm.DB, rid uint64) error
	BatchCreateRoleMenuWithTx(tx *gorm.DB, m []*RoleMenu) error
	FindMenuIdsByRole(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error)
}

type RoleRedis interface {
	JudgeRoleIsValid(ctx *gin.Context, rid uint64) bool
	RedisEnableRoleList(ctx *gin.Context) (ModelList, error)
	RedisRoleList(ctx *gin.Context) (ModelList, error)
	RedisReloadRoleList(ctx *gin.Context) (ModelList, error)
	RedisClearRoleList(ctx *gin.Context) error
}

type RoleMenuRedis interface {
	RedisRoleMenuIds(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error)
	RedisReloadRoleMenuIds(ctx *gin.Context, rid uint64) (RoleAuthMenuIds, error)
	RedisClearRoleMenuIds(ctx *gin.Context, rid uint64) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
