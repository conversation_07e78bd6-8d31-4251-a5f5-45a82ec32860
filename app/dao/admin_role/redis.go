package adminRole

import (
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// JudgeRoleIsValid .
func (e *Entry) JudgeRoleIsValid(ctx *gin.Context, rid uint64) bool {
	validList, err := e.RedisRoleList(ctx)
	if err != nil {
		return false
	}

	validMap := validList.GetIDEmptyMap()
	_, ok := validMap[rid]

	return ok
}

// RedisEnableRoleList .
func (e *Entry) RedisEnableRoleList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisRoleList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisRoleList .
func (e *Entry) RedisRoleList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.RoleListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadRoleList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadRoleList redis重载数据列表
func (e *Entry) RedisReloadRoleList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.RoleListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindAll(ctx)
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearRoleList .
func (e *Entry) RedisClearRoleList(ctx *gin.Context) error {
	cacheKey := redisPkg.RoleListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
