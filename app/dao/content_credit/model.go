package credit

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/pkg/log"
)

type CreditJob uint32

/*
导演
演员
编剧
制片人
音乐
摄影
剪辑
选角
服装
化妆
副导演
视觉特效
其它
*/
const (
	CreditJobDirector CreditJob = iota + 1
	CreditJobActor
	CreditJobWriter
	CreditJobProducer
	CreditJobMusic
	CreditJobPhotography
	CreditJobEditing
	CreditJobCasting
	CreditJobCostume
	CreditJobMakeup
	CreditJobAssistantDirector
	CreditJobVisualEffects
	CreditJobOther
)

type Credit struct {
	dbs.ModelWithDel

	PersonID   uint64    `json:"person_id,omitempty"`  // 人物ID
	ShowID     uint64    `json:"show_id,omitempty"`    // 剧ID
	Job        CreditJob `json:"job,omitempty"`        // 工作内容
	Department string    `json:"department,omitempty"` // 部门
	Character  string    `json:"character,omitempty"`  // 角色
	Order      uint32    `json:"order,omitempty"`      // 排序
}

func (m *Credit) TableName() string {
	return "content_credit"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`

	Sort []clause.OrderByColumn // 排序
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			db = db.Order("id desc")
			return db
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}
		return db
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),

		f._sort(),

		f._eq_show_id(),
		f._in_show_ids(),
	}
}

type CreditList []*Credit

func (cl CreditList) GetPersonIDs() []uint64 {
	var personIDs []uint64
	for _, credit := range cl {
		personIDs = append(personIDs, credit.PersonID)
	}
	return personIDs
}
