package adminArea

import (
	"fmt"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if len(f.IDS) > 0 {
		query.Where("id in (?)", f.IDS)
	}
	orderStr := "sort desc, id asc"
	if f.SortField != "" && f.SortMethod != "" {
		orderStr = fmt.Sprintf("%s %s, %s", f.<PERSON>, f.SortMethod, orderStr)
	}

	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}
