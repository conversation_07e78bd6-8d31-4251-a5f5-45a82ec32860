package adminArea

import (
	"vlab/app/common/dbs"
)

type Model struct {
	ID           uint64 `gorm:"primarykey" json:"id"`
	Pid          uint64 `json:"pid,omitempty"`
	CityCode     string `json:"city_code,omitempty"`
	DistrictCode string `json:"district_code,omitempty"`
	Name         string `json:"name,omitempty"`
	Level        string `json:"level,omitempty"`
	Sort         uint32 `json:"sort,omitempty"`
	IsDeleted    uint32 `json:"is_deleted,omitempty"`
}

func (m *Model) TableName() string {
	return "admin_area"
}

type Filter struct {
	ID   uint64
	IDS  []uint64
	Name string
	Sort
}

type Sort struct {
	SortField  dbs.SortField
	SortMethod dbs.SortMethod
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}
