package subtitle

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (SubtitleList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := SubtitleList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CreateOrUpdate(context *gin.Context, Subtitle *Subtitle) (uint64, error) {
	return e.CreateOrUpdateWithTx(context, e.MysqlEngine.UseWithGinCtx(context, true), Subtitle)
}

func (e Entry) CreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, m *Subtitle) (uint64, error) {
	if err := db.Model(&Subtitle{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "video_id"}, {Name: "iso_639_1"}, {Name: "episode_id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"file_path",
			}),
			Where: clause.Where{
				Exprs: []clause.Expression{clause.Expr{SQL: fmt.Sprintf("%s = 0", dbs.SoftDelField)}},
			},
		}).Create(m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil
}

func (e Entry) Create(ctx *gin.Context, model *Subtitle) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, db *gorm.DB, model *Subtitle) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*Subtitle) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*Subtitle) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) BatchCreateOrUpdate(ctx *gin.Context, models []*Subtitle) error {
	return e.BatchCreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateOrUpdateWithTx(ctx *gin.Context, db *gorm.DB, models []*Subtitle) error {
	if len(models) <= 0 {
		return nil
	}
	if err := db.Model(&Subtitle{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "video_id"}, {Name: "iso_639_1"}, {Name: "episode_id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"file_path",
			}),
			Where: clause.Where{
				Exprs: []clause.Expression{clause.Expr{SQL: fmt.Sprintf("%s = 0", dbs.SoftDelField)}},
			},
		}).CreateInBatches(models, 2000).Error; err != nil {
		return err
	}
	return nil

}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *Subtitle) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *Subtitle) error {
	if err := db.Model(&Subtitle{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) UpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&Subtitle{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list SubtitleList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*Subtitle, error) {
	ret := &Subtitle{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Subtitle, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &Subtitle{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Subtitle{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) UpdateMapByFilterWithTx(ctx *gin.Context, db *gorm.DB, filter *Filter, m map[string]interface{}) error {
	query := db.Model(&Subtitle{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	if err := query.Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DeleteByID(ctx *gin.Context, id uint64) error {
	return e.DeleteByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id)
}

func (e Entry) DeleteByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64) error {
	if err := db.Model(&Subtitle{}).Where("id = ?", id).Delete(&Subtitle{}).Error; err != nil {
		return err
	}
	return nil
}
