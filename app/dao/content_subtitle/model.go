package subtitle

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/config"
)

type Subtitle struct {
	dbs.ModelWithDel

	EpisodeID uint64          `json:"episode_id,omitempty"` // 剧集ID
	VideoID   uint64          `json:"video_id,omitempty"`   // 视频ID
	ISO_639_1 string          `json:"iso_639_1,omitempty"`  // 语言标识
	FilePath  dbs.OssFilePath `json:"file_path,omitempty"`  // 文件路径
}

func (m *Subtitle) TableName() string {
	return "content_subtitle"
}

func (m *Subtitle) GetFilePath(host string) string {
	if m.FilePath == "" {
		return ""
	}
	if host == "" {
		host = config.BytePlusCfg.OssSubtitleHost
	}
	return m.FilePath.String(host)
}

type Filter struct {
	ID         uint64   `json:"id,omitempty"`
	IDs        []uint64 `json:"ids,omitempty"`
	EpisodeID  uint64   `json:"episode_id,omitempty"`
	EpisodeIDs []uint64 `json:"episode_ids,omitempty"`

	VideoID  uint64   `json:"video_id,omitempty"`
	VideoIDs []uint64 `json:"video_ids,omitempty"`

	ISO_639_1 string `json:"iso_639_1,omitempty"`

	Sort []clause.OrderByColumn // 排序

}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID > 0 {
			return db.Where("id = ?", f.ID)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) > 0 {
			return db.Where("id IN ?", f.IDs)
		}
		return db
	}
}

func (F Filter) _eq_episode_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if F.EpisodeID > 0 {
			return db.Where("episode_id = ?", F.EpisodeID)
		}
		return db
	}
}

func (f Filter) _in_episode_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.EpisodeIDs) > 0 {
			return db.Where("episode_id IN ?", f.EpisodeIDs)
		}
		return db
	}
}

func (f Filter) _eq_video_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.VideoID > 0 {
			return db.Where("video_id = ?", f.VideoID)
		}
		return db
	}
}

func (f Filter) _in_video_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.VideoIDs) > 0 {
			return db.Where("video_id IN ?", f.VideoIDs)
		}
		return db
	}
}

func (f Filter) _eq_iso_639_1() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ISO_639_1 != "" {
			return db.Where("iso_639_1 = ?", f.ISO_639_1)
		}
		return db
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_video_id(),
		f._in_video_ids(),
		f._eq_iso_639_1(),
		f._eq_episode_id(),
		f._in_episode_ids(),
	}
}

type SubtitleList []*Subtitle

func (sl SubtitleList) GetVideoKeyMap() map[uint64]SubtitleList {
	ret := map[uint64]SubtitleList{}
	for _, v := range sl {
		if _, ok := ret[v.VideoID]; !ok {
			ret[v.VideoID] = SubtitleList{}
		}
		ret[v.VideoID] = append(ret[v.VideoID], v)
	}
	return ret
}
