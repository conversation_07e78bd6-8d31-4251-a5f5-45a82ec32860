package subtitle

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	SubtitleRepo
}

type SubtitleRepo interface {
	FindByFilter(*gin.Context, *Filter) (SubtitleList, error)

	CreateOrUpdate(*gin.Context, *Subtitle) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Subtitle) (uint64, error)
	Create(*gin.Context, *Subtitle) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Subtitle) (uint64, error)
	BatchCreate(*gin.Context, []*Subtitle) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Subtitle) error

	BatchCreateOrUpdate(*gin.Context, []*Subtitle) error
	BatchCreateOrUpdateWithTx(*gin.Context, *gorm.DB, []*Subtitle) error
	UpdateModelByID(*gin.Context, uint64, *Subtitle) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Subtitle) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list SubtitleList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Subtitle, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Subtitle, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)

	DeleteByID(*gin.Context, uint64) error

	DeleteByIDWithTx(*gin.Context, *gorm.DB, uint64) error

	UpdateMapByFilterWithTx(ctx *gin.Context, db *gorm.DB, filter *Filter, m map[string]interface{}) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
