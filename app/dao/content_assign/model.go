package content_popular

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

type Assign struct {
	dbs.ModelWithDel

	ShowID uint64 `json:"show_id,omitempty"` // 剧集ID

	Status uint32 `json:"status,omitempty"` // 状态

	ChannelID uint64 `json:"channel_id,omitempty"` //
}

func (m *Assign) TableName() string {
	return "content_assign"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID  uint64   `json:"show_id,omitempty"`  // 剧集ID
	ShowIDs []uint64 `json:"show_ids,omitempty"` // 剧集ID

	ChannelID uint64 `json:"channel_id,omitempty"` //

	Sort []clause.OrderByColumn

	Status uint32 `json:"status,omitempty"` // 状态
}

type AssignList []*Assign

func (pl AssignList) GetIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ID)
	}
	return ids
}

func (pl AssignList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ShowID)
	}
	return ids
}

func (pl AssignList) GetChannelIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		if v.ChannelID > 0 {
			ids = append(ids, v.ChannelID)
		}
	}
	return ids
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _eq_channel_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ChannelID <= 0 {
			return db
		}
		return db.Where("channel_id = ?", f.ChannelID)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_status(),
		f._eq_channel_id(),
	}
}
