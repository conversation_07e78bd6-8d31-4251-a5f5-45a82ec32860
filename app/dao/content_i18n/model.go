package i18n

import (
	"vlab/app/common/dbs"

	"gorm.io/gorm"
)

type I18n struct {
	dbs.ModelWithDel

	Key       string `json:"key,omitempty"`       // 多语言key
	ISO_639_1 string `json:"iso_639_1,omitempty"` // 语言标识
	Table     string `json:"table,omitempty"`     // 表名
	Column    string `json:"column,omitempty"`    // 列名
	Value     string `json:"value,omitempty"`     // 多语言值
}

func (m *I18n) TableName() string {
	return "content_i18n"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	Key string `json:"key,omitempty"`

	Keys []string `json:"keys,omitempty"`

	ISO_639_1 string `json:"iso_639_1,omitempty"`
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_key() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Key == "" {
			return db
		}
		return db.Where("`key` = ?", f.Key)
	}
}

func (f Filter) _in_keys() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Keys) <= 0 {
			return db
		}
		return db.Where("`key` in (?)", f.Keys)
	}
}

func (f Filter) _eq_iso_639_1() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ISO_639_1 == "" {
			return db
		}
		return db.Where("iso_639_1 = ?", f.ISO_639_1)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_key(),
		f._in_keys(),
		f._eq_iso_639_1(),
	}
}

type I18nList []*I18n

func (il I18nList) GetI18nKeyMap() map[string]string {
	m := make(map[string]string)
	for _, i := range il {
		m[i.Key] = i.Value
	}
	return m
}

func (il I18nList) GetI18nKey() map[string][]*I18n {
	m := make(map[string][]*I18n)
	for _, i := range il {
		if _, ok := m[i.Key]; !ok {
			m[i.Key] = make([]*I18n, 0)
		}
		m[i.Key] = append(m[i.Key], i)
	}
	return m
}

// GetI18nValueArrays 获取按key分组的所有翻译值数组
func (il I18nList) GetI18nValueArrays(includeEmpty bool) map[string][]string {
	keyGroups := il.GetI18nKey()
	result := make(map[string][]string)

	for key, i18nList := range keyGroups {
		values := make([]string, 0)
		seen := make(map[string]bool)

		// 按原始顺序去重
		for _, i18n := range i18nList {
			if (includeEmpty || i18n.Value != "") && !seen[i18n.Value] {
				values = append(values, i18n.Value)
				seen[i18n.Value] = true
			}
		}

		result[key] = values
	}

	return result
}
