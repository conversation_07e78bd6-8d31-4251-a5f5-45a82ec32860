package i18n

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	I18nRepo
}

type I18nRepo interface {
	FindByFilter(*gin.Context, *Filter) (I18nList, error)
	FindByFilterWithFallback(*gin.Context, *Filter, string) (I18nList, error)

	CreateOrUpdate(*gin.Context, *I18n) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *I18n) (uint64, error)
	BatchCreateOrUpdate(*gin.Context, []*I18n) error
	BatchCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, I18nList []*I18n) error
	Create(*gin.Context, *I18n) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *I18n) (uint64, error)
	BatchCreate(*gin.Context, []*I18n) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*I18n) error
	UpdateModelByID(*gin.Context, uint64, *I18n) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *I18n) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list I18nList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*I18n, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*I18n, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)

	UpdateMapByFilter(*gin.Context, *Filter, map[string]interface{}) error
	UpdateMapByFilterWithTx(*gin.Context, *gorm.DB, *Filter, map[string]interface{}) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
