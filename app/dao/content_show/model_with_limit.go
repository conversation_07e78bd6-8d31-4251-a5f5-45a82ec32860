package show

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type LimitType uint32

const (
	LimitTypeNone LimitType = iota
	LimitTypeChannel
	LimitTypeVersion
	LimitTypeRegion
	LimitTypeAudit // 1 审核中 2 过审
)

type LimitTypeAuditStatus uint32

const (
	LimitTypeAuditStatusNone LimitTypeAuditStatus = iota
	LimitTypeAuditStatusProcessing
	LimitTypeAuditStatusPass
)

type ShowWithLimit struct {
	dbs.ModelWithDel

	ShowID  uint64    `json:"show_id,omitempty"`  // 剧ID
	LimitID uint64    `json:"limit_id,omitempty"` // 限制ID
	Type    LimitType `json:"type,omitempty"`     // 类型

}

func (m *ShowWithLimit) TableName() string {
	return "content_show_with_limit"
}

type LimitFilter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID   uint64   `json:"show_id,omitempty"`   // 剧ID
	ShowIDs  []uint64 `json:"show_ids,omitempty"`  // 剧ID
	LimitID  uint64   `json:"limit_id,omitempty"`  // 限制ID
	LimitIDs []uint64 `json:"limit_ids,omitempty"` // 限制ID

	Type uint32 `json:"type,omitempty"` // 类型
}

func (f LimitFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f LimitFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f LimitFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f LimitFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f LimitFilter) _eq_limit_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.LimitID <= 0 {
			return db
		}
		return db.Where("limit_id = ?", f.LimitID)
	}
}

func (f LimitFilter) _in_limit_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.LimitIDs) <= 0 {
			return db
		}
		return db.Where("limit_id in (?)", f.LimitIDs)
	}
}

func (f LimitFilter) _eq_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Type <= 0 {
			return db
		}
		return db.Where("type = ?", f.Type)
	}
}

func (f LimitFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_limit_id(),
		f._in_limit_ids(),
		f._eq_type(),
	}
}

type ShowWithLimitList []*ShowWithLimit

func (swll ShowWithLimitList) GetShowIDs() []uint64 {
	var showIDs []uint64
	for _, swl := range swll {
		showIDs = append(showIDs, swl.ShowID)
	}
	return showIDs
}

func (swll ShowWithLimitList) GetLimitIDs() []uint64 {
	var limitIDs []uint64
	for _, swl := range swll {
		limitIDs = append(limitIDs, swl.LimitID)
	}
	return limitIDs
}

func (swll ShowWithLimitList) GetChannelIDs() []uint64 {
	var channelIDs []uint64
	for _, swl := range swll {
		if swl.Type == LimitTypeChannel {
			channelIDs = append(channelIDs, swl.LimitID)
		}
	}
	return channelIDs
}

func (swll ShowWithLimitList) GetVersionIDs() []uint64 {
	var versionIDs []uint64
	for _, swl := range swll {
		if swl.Type == LimitTypeVersion {
			versionIDs = append(versionIDs, swl.LimitID)
		}
	}
	return versionIDs
}

func (swll ShowWithLimitList) GetShowIDMap() map[uint64]ShowWithLimitList {
	retMap := make(map[uint64]ShowWithLimitList, len(swll))
	for _, val := range swll {
		if _, ok := retMap[val.ShowID]; !ok {
			retMap[val.ShowID] = make(ShowWithLimitList, 0)
		}
		retMap[val.ShowID] = append(retMap[val.ShowID], val)
	}
	return retMap
}
