package show

import (
	"fmt"

	"gorm.io/gorm"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindFranchiseByFilter(ctx *gin.Context, filter *FranchiseFilter) (ShowWithFranchiseList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowWithFranchiseList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FranchiseCreateOrUpdate(context *gin.Context, model *ShowWithFranchise) (uint64, error) {
	panic("implement me")
}

func (e Entry) FranchiseCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, model *ShowWithFranchise) (uint64, error) {
	panic("implement me")
}

func (e Entry) FranchiseCreate(ctx *gin.Context, model *ShowWithFranchise) (uint64, error) {
	return e.FranchiseCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) FranchiseCreateWithTx(ctx *gin.Context, db *gorm.DB, model *ShowWithFranchise) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) FranchiseBatchCreate(ctx *gin.Context, models []*ShowWithFranchise) error {
	return e.FranchiseBatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) FranchiseBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithFranchise) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) FranchiseUpdateModelByID(ctx *gin.Context, u uint64, model *ShowWithFranchise) error {
	return e.FranchiseUpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) FranchiseUpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *ShowWithFranchise) error {
	if err := db.Model(&ShowWithFranchise{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) FranchiseUpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.FranchiseUpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) FranchiseUpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&ShowWithFranchise{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) FranchiseDataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list ShowWithFranchiseList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindFranchiseXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchFranchiseByID(ctx *gin.Context, id uint64) (*ShowWithFranchise, error) {
	ret := &ShowWithFranchise{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchFranchiseByFilterSort(ctx *gin.Context, filter *Filter) (*ShowWithFranchise, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &ShowWithFranchise{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountFranchiseByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithFranchise{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}
