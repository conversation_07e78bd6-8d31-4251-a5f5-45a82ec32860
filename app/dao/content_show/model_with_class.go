package show

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type ShowWithClass struct {
	dbs.ModelWithDel

	ShowID uint64

	ClassID uint64

	FieldID uint64
}

func (m *ShowWithClass) TableName() string {
	return "content_show_with_class"
}

type ClassFilter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID  uint64   `json:"show_id,omitempty"`  // 剧ID
	ShowIDs []uint64 `json:"show_ids,omitempty"` // 剧ID

	ClassID uint64 `json:"class_id,omitempty"` // 类型

	FieldID uint64 `json:"field_id,omitempty"` // 类型

}

func (f ClassFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f ClassFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f ClassFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f ClassFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f ClassFilter) _eq_class_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ClassID <= 0 {
			return db
		}
		return db.Where("class_id = ?", f.ClassID)
	}
}

func (f ClassFilter) _eq_field_id() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if f.FieldID <= 0 {
			return db
		}
		return db.Where("field_id = ?", f.FieldID)
	}
}

func (f ClassFilter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_class_id(),
		f._eq_field_id(),
	}
}

type ShowWithClassList []*ShowWithClass

func (cl ShowWithClassList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, c := range cl {
		ids = append(ids, c.ShowID)
	}
	return ids
}

func (cl ShowWithClassList) GetClassIDs() []uint64 {
	var ids []uint64
	for _, c := range cl {
		ids = append(ids, c.ClassID)
	}
	return ids
}

func (cl ShowWithClassList) GetFieldIDs() []uint64 {
	var ids []uint64
	for _, c := range cl {
		ids = append(ids, c.FieldID)
	}
	return ids
}

func (cl ShowWithClassList) GetShowIDMap() map[uint64]ShowWithClassList {
	m := make(map[uint64]ShowWithClassList)
	for _, c := range cl {
		if _, ok := m[c.ShowID]; !ok {
			m[c.ShowID] = make(ShowWithClassList, 0)
		}
		m[c.ShowID] = append(m[c.ShowID], c)
	}
	return m
}

func (cl ShowWithClassList) GetGenreList() ShowWithClassList {
	ret := make(ShowWithClassList, 0)
	for _, c := range cl {
		if c.ClassID == 5 {
			ret = append(ret, c)
		}
	}
	return ret
}
