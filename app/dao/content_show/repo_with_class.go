package show

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

func (e Entry) FindClassByFilter(ctx *gin.Context, filter *ClassFilter) (ShowWithClassList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithClass{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowWithClassList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) ClassBatchCreate(ctx *gin.Context, models []*ShowWithClass) error {
	return e.ClassBatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) ClassBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithClass) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DeleteClassByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *ClassFilter) (int64, error) {
	query := tx.Model(&ShowWithClass{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	result := query.UpdateColumn(string(dbs.SoftDelField), 1)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
