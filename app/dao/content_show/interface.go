package show

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	ShowRepo

	GenreWithRepo

	FranchiseWithRepo

	LimitWithRepo

	ClassWithRepo

	MappingWithRepo

	SearchHistoryRepo

	RelationRepo
}

type ShowRepo interface {
	CreateOrUpdate(*gin.Context, *Show) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Show) (uint64, error)
	Create(*gin.Context, *Show) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Show) (uint64, error)
	BatchCreate(*gin.Context, []*Show) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Show) error
	UpdateModelByID(*gin.Context, uint64, *Show) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Show) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ShowList, err error)
	FindByFilter(*gin.Context, *Filter) (ShowList, error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Show, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Show, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)

	UpdateMapByFilter(ctx *gin.Context, filter *Filter, model map[string]interface{}) error
	UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter, model map[string]interface{}) error
}

type GenreWithRepo interface {
	FindGenreByFilter(*gin.Context, *GenreFilter) (ShowWithGenreList, error)

	GenreCreateOrUpdate(*gin.Context, *ShowWithGenre) (uint64, error)
	GenreCreateOrUpdateWithTx(*gin.Context, *gorm.DB, *ShowWithGenre) (uint64, error)
	GenreCreate(*gin.Context, *ShowWithGenre) (uint64, error)
	GenreCreateWithTx(*gin.Context, *gorm.DB, *ShowWithGenre) (uint64, error)
	GenreBatchCreate(*gin.Context, []*ShowWithGenre) error
	GenreBatchCreateWithTx(*gin.Context, *gorm.DB, []*ShowWithGenre) error
	GenreUpdateModelByID(*gin.Context, uint64, *ShowWithGenre) error
	GenreUpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *ShowWithGenre) error
	GenreUpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	GenreUpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	GenreDataPageList(*gin.Context, *GenreFilter, int, int) (total int64, list ShowWithGenreList, err error)
	FindGenreXidsByFilter(*gin.Context, *GenreFilter, dbs.PluckField) ([]uint64, error)
	FetchGenreByID(*gin.Context, uint64) (*ShowWithGenre, error)
	FeatchGenreByFilterSort(*gin.Context, *GenreFilter) (*ShowWithGenre, error)
	CountGenreByFilter(*gin.Context, *GenreFilter) (int64, error)

	DeleteGenreByFilter(ctx *gin.Context, filter *GenreFilter) (int64, error)

	DeleteGenreByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *GenreFilter) (int64, error)
}

type FranchiseWithRepo interface {
	FindFranchiseByFilter(*gin.Context, *FranchiseFilter) (ShowWithFranchiseList, error)

	FranchiseCreateOrUpdate(*gin.Context, *ShowWithFranchise) (uint64, error)
	FranchiseCreateOrUpdateWithTx(*gin.Context, *gorm.DB, *ShowWithFranchise) (uint64, error)
	FranchiseCreate(*gin.Context, *ShowWithFranchise) (uint64, error)
	FranchiseCreateWithTx(*gin.Context, *gorm.DB, *ShowWithFranchise) (uint64, error)
	FranchiseBatchCreate(*gin.Context, []*ShowWithFranchise) error
	FranchiseBatchCreateWithTx(*gin.Context, *gorm.DB, []*ShowWithFranchise) error
	FranchiseUpdateModelByID(*gin.Context, uint64, *ShowWithFranchise) error
	FranchiseUpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *ShowWithFranchise) error
	FranchiseUpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	FranchiseUpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	FranchiseDataPageList(*gin.Context, *Filter, int, int) (total int64, list ShowWithFranchiseList, err error)
	FindFranchiseXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchFranchiseByID(*gin.Context, uint64) (*ShowWithFranchise, error)
	FeatchFranchiseByFilterSort(*gin.Context, *Filter) (*ShowWithFranchise, error)
	CountFranchiseByFilter(*gin.Context, *Filter) (int64, error)
}

type LimitWithRepo interface {
	FindLimitByFilter(*gin.Context, *LimitFilter) (ShowWithLimitList, error)

	LimitCreateOrUpdate(*gin.Context, *ShowWithLimit) (uint64, error)
	LimitCreateOrUpdateWithTx(*gin.Context, *gorm.DB, *ShowWithLimit) (uint64, error)
	LimitCreate(*gin.Context, *ShowWithLimit) (uint64, error)
	LimitCreateWithTx(*gin.Context, *gorm.DB, *ShowWithLimit) (uint64, error)
	LimitBatchCreate(*gin.Context, []*ShowWithLimit) error
	LimitBatchCreateWithTx(*gin.Context, *gorm.DB, []*ShowWithLimit) error
	LimitUpdateModelByID(*gin.Context, uint64, *ShowWithLimit) error
	LimitUpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *ShowWithLimit) error
	LimitUpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	LimitUpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	LimitDataPageList(*gin.Context, *LimitFilter, int, int) (total int64, list ShowWithLimitList, err error)
	FindLimitXidsByFilter(*gin.Context, *LimitFilter, dbs.PluckField) ([]uint64, error)
	FetchLimitByID(*gin.Context, uint64) (*ShowWithLimit, error)
	FeatchLimitByFilterSort(*gin.Context, *LimitFilter) (*ShowWithLimit, error)
	CountLimitByFilter(*gin.Context, *LimitFilter) (int64, error)

	DeleteLimitByFilter(ctx *gin.Context, filter *LimitFilter) (int64, error)

	DeleteLimitByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *LimitFilter) (int64, error)
}

type ClassWithRepo interface {
	FindClassByFilter(*gin.Context, *ClassFilter) (ShowWithClassList, error)

	ClassBatchCreate(ctx *gin.Context, models []*ShowWithClass) error

	ClassBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithClass) error

	DeleteClassByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *ClassFilter) (int64, error)
}

type MappingWithRepo interface {
	FindMappingByFilter(*gin.Context, *MappingFilter) (ShowWithMappingList, error)

	FindMappingXidsByFilter(ctx *gin.Context, filter *MappingFilter, field dbs.PluckField) ([]uint64, error)

	MappingCreateOrUpdate(*gin.Context, *ShowWithMapping) (uint64, error)

	MappingCreateOrUpdateWithTx(*gin.Context, *gorm.DB, *ShowWithMapping) (uint64, error)

	MappingCreate(*gin.Context, *ShowWithMapping) (uint64, error)

	MappingCreateWithTx(*gin.Context, *gorm.DB, *ShowWithMapping) (uint64, error)
}

type SearchHistoryRepo interface {
	SearchHistoryCreate(ctx *gin.Context, model *ShowSearchHistory) (uint64, error)

	SearchHistoryGroup(ctx *gin.Context, filter *SearchHistoryFilter, page, limit int) (list GroupList, err error)
}

type RelationRepo interface {
	// 关联组相关
	FindRelationGroupByFilter(ctx *gin.Context, filter *RelationGroupFilter) (list RelationGroupList, err error)
	FetchRelationGroupByID(ctx *gin.Context, id uint64) (group *ShowRelationGroup, err error)
	CreateRelationGroup(ctx *gin.Context, group *ShowRelationGroup) error
	UpdateRelationGroup(ctx *gin.Context, group *ShowRelationGroup) error
	DeleteRelationGroup(ctx *gin.Context, id uint64) error
	CountRelationGroupByFilter(ctx *gin.Context, filter *RelationGroupFilter) (int64, error)
	RelationGroupPageList(ctx *gin.Context, filter *RelationGroupFilter, page int, limit int) (total int64, list RelationGroupList, err error)

	// 关联组成员相关
	FindRelationMemberByFilter(ctx *gin.Context, filter *RelationMemberFilter) (list RelationMemberList, err error)
	CreateRelationMembers(ctx *gin.Context, members RelationMemberList) error
	DeleteRelationMembers(ctx *gin.Context, filter *RelationMemberFilter) error

	// 事务操作
	CreateRelationGroupWithMembers(ctx *gin.Context, group *ShowRelationGroup, members RelationMemberList) error
	DeleteRelationGroupWithMembers(ctx *gin.Context, groupID uint64) error
	BatchSetRelationMembers(ctx *gin.Context, groupID uint64, members RelationMemberList) error

	// 组合查询
	FindRelationGroupsByShowID(ctx *gin.Context, showID uint64) (groupIDs []uint64, err error)
	FindRelatedShowsByGroupIDs(ctx *gin.Context, groupIDs []uint64, excludeShowID uint64) (showIDs []uint64, err error)
	GetRelatedShows(ctx *gin.Context, showID uint64, limit int) (relatedShowIDs []uint64, err error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
