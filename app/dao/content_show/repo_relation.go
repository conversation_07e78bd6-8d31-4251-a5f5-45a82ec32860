package show

import (
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ============ 关联组相关方法 ============

// FindRelationGroupByFilter 根据条件查询关联组
func (e Entry) FindRelationGroupByFilter(ctx *gin.Context, filter *RelationGroupFilter) (list RelationGroupList, err error) {
	list = make(RelationGroupList, 0)
	scopes := filter.fullScopes()

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowRelationGroup{}).Scopes(scopes...)
	if len(filter.Sort) > 0 {
		query = query.Clauses(clause.OrderBy{Columns: filter.Sort})
	} else {
		query = query.Order("id desc")
	}

	if err = query.Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindRelationGroupByFilter error")
		return
	}
	return
}

// FetchRelationGroupByID 根据ID获取关联组
func (e Entry) FetchRelationGroupByID(ctx *gin.Context, id uint64) (group *ShowRelationGroup, err error) {
	group = &ShowRelationGroup{}
	err = e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowRelationGroup{}).
		Where("id = ? AND is_deleted = 0", id).
		First(group).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		log.Ctx(ctx).WithError(err).Error("FetchRelationGroupByID error")
	}
	return
}

// CreateRelationGroup 创建关联组
func (e Entry) CreateRelationGroup(ctx *gin.Context, group *ShowRelationGroup) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Create(group).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateRelationGroup error")
		return err
	}
	return nil
}

// CreateRelationGroupWithMembers 创建关联组及其成员（事务）
func (e Entry) CreateRelationGroupWithMembers(ctx *gin.Context, group *ShowRelationGroup, members RelationMemberList) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		// 创建关联组
		if err := tx.Create(group).Error; err != nil {
			log.Ctx(ctx).WithError(err).Error("CreateRelationGroup in tx error")
			return err
		}

		// 如果有成员，批量创建
		if len(members) > 0 {
			// 设置组ID
			for _, member := range members {
				member.GroupID = group.ID
			}
			if err := tx.Create(&members).Error; err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateRelationMembers in tx error")
				return err
			}
		}

		return nil
	})
}

// UpdateRelationGroup 更新关联组
func (e Entry) UpdateRelationGroup(ctx *gin.Context, group *ShowRelationGroup) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&ShowRelationGroup{}).
		Where("id = ?", group.ID).
		Updates(group).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateRelationGroup error")
		return err
	}
	return nil
}

// DeleteRelationGroup 删除关联组（软删除）
func (e Entry) DeleteRelationGroup(ctx *gin.Context, id uint64) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&ShowRelationGroup{}).
		Where("id = ?", id).
		Update("is_deleted", 1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DeleteRelationGroup error")
		return err
	}
	return nil
}

// RelationGroupPageList 分页查询关联组列表
func (e Entry) RelationGroupPageList(ctx *gin.Context, filter *RelationGroupFilter, page int, limit int) (total int64, list RelationGroupList, err error) {
	list = make(RelationGroupList, 0)
	scopes := filter.fullScopes()

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).
		Where("is_deleted = 0").
		Model(&ShowRelationGroup{}).Scopes(scopes...)

	// 先获取总数
	if err = query.Count(&total).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("RelationGroupPageList count error")
		return 0, nil, err
	}

	if total == 0 {
		return 0, list, nil
	}

	// 重新构建查询，添加排序和分页
	query = e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowRelationGroup{}).Scopes(scopes...)
	if len(filter.Sort) > 0 {
		query = query.Clauses(clause.OrderBy{Columns: filter.Sort})
	} else {
		query = query.Order("id desc")
	}

	// 添加分页
	if err = query.Offset((page - 1) * limit).Limit(limit).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("RelationGroupPageList find error")
		return 0, nil, err
	}

	return total, list, nil
}

// ============ 关联组成员相关方法 ============

// FindRelationMemberByFilter 根据条件查询关联组成员
func (e Entry) FindRelationMemberByFilter(ctx *gin.Context, filter *RelationMemberFilter) (list RelationMemberList, err error) {
	list = make(RelationMemberList, 0)
	scopes := filter.fullScopes()

	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowRelationMember{}).Scopes(scopes...)
	if len(filter.Sort) > 0 {
		query = query.Clauses(clause.OrderBy{Columns: filter.Sort})
	} else {
		query = query.Order("`order` asc, id asc")
	}

	if err = query.Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindRelationMemberByFilter error")
		return
	}
	return
}

// CreateRelationMembers 批量创建关联组成员
func (e Entry) CreateRelationMembers(ctx *gin.Context, members RelationMemberList) error {
	if len(members) == 0 {
		return nil
	}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Create(&members).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateRelationMembers error")
		return err
	}
	return nil
}

// DeleteRelationMembers 批量删除关联组成员
func (e Entry) DeleteRelationMembers(ctx *gin.Context, filter *RelationMemberFilter) error {
	scopes := filter.fullScopes()
	query := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&ShowRelationMember{}).Scopes(scopes...)
	if err := query.Update("is_deleted", dbs.True).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DeleteRelationMembers error")
		return err
	}
	return nil
}

// DeleteRelationGroupWithMembers 删除关联组及其成员（事务）
func (e Entry) DeleteRelationGroupWithMembers(ctx *gin.Context, groupID uint64) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		// 删除组内所有成员关系
		memberQuery := tx.Model(&ShowRelationMember{}).Where("group_id = ?", groupID)
		if err := memberQuery.Update("is_deleted", dbs.True).Error; err != nil {
			log.Ctx(ctx).WithError(err).Error("DeleteRelationMembers in tx error")
			return err
		}

		// 删除关联组
		groupQuery := tx.Model(&ShowRelationGroup{}).Where("id = ?", groupID)
		if err := groupQuery.Update("is_deleted", dbs.True).Error; err != nil {
			log.Ctx(ctx).WithError(err).Error("DeleteRelationGroup in tx error")
			return err
		}

		return nil
	})
}

// BatchSetRelationMembers 批量设置关联组成员（事务）
func (e Entry) BatchSetRelationMembers(ctx *gin.Context, groupID uint64, members RelationMemberList) error {
	return e.MysqlEngine.UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		// 删除原有成员
		memberQuery := tx.Model(&ShowRelationMember{}).Where("group_id = ? AND is_deleted = 0", groupID)
		if err := memberQuery.Update("is_deleted", dbs.True).Error; err != nil {
			log.Ctx(ctx).WithError(err).Error("DeleteRelationMembers in tx error")
			return err
		}

		// 添加新成员
		if len(members) > 0 {
			if err := tx.Create(&members).Error; err != nil {
				log.Ctx(ctx).WithError(err).Error("CreateRelationMembers in tx error")
				return err
			}
		}

		return nil
	})
}

// ============ 组合查询方法 ============

// FindRelationGroupsByShowID 查找剧集所属的所有关联组
func (e Entry) FindRelationGroupsByShowID(ctx *gin.Context, showID uint64) (groupIDs []uint64, err error) {
	// 先查找该剧所属的所有组
	memberFilter := &RelationMemberFilter{
		ShowID: showID,
	}
	members, err := e.FindRelationMemberByFilter(ctx, memberFilter)
	if err != nil {
		return nil, err
	}

	// 获取组ID列表
	groupIDs = members.GetGroupIDs()
	return
}

// FindRelatedShowsByGroupIDs 获取同组的其他剧集
func (e Entry) FindRelatedShowsByGroupIDs(ctx *gin.Context, groupIDs []uint64, excludeShowID uint64) (showIDs []uint64, err error) {
	if len(groupIDs) == 0 {
		return []uint64{}, nil
	}

	// 查找这些组中的所有成员，排除当前剧
	memberFilter := &RelationMemberFilter{
		GroupIDs:      groupIDs,
		ExcludeShowID: excludeShowID,
		Sort: []clause.OrderByColumn{
			{Column: clause.Column{Name: "`order`"}, Desc: false},
			{Column: clause.Column{Name: "id"}, Desc: false},
		},
	}
	members, err := e.FindRelationMemberByFilter(ctx, memberFilter)
	if err != nil {
		return nil, err
	}

	// 获取剧ID列表（已去重）
	showIDs = members.GetShowIDs()
	return
}

// GetRelatedShows 获取剧的关联剧（一步到位的方法）
func (e Entry) GetRelatedShows(ctx *gin.Context, showID uint64, limit int) (relatedShowIDs []uint64, err error) {
	// 1. 查找该剧所属的所有关联组
	groupIDs, err := e.FindRelationGroupsByShowID(ctx, showID)
	if err != nil || len(groupIDs) == 0 {
		return []uint64{}, err
	}

	// 2. 查找启用状态的关联组
	groupFilter := &RelationGroupFilter{
		IDs:    groupIDs,
		Status: uint32(dbs.StatusEnable),
	}
	groups, err := e.FindRelationGroupByFilter(ctx, groupFilter)
	if err != nil || len(groups) == 0 {
		return []uint64{}, err
	}
	activeGroupIDs := groups.GetIDs()

	// 3. 获取这些组中的其他剧集
	relatedShowIDs, err = e.FindRelatedShowsByGroupIDs(ctx, activeGroupIDs, showID)
	if err != nil {
		return nil, err
	}

	// 4. 限制返回数量
	if limit > 0 && len(relatedShowIDs) > limit {
		relatedShowIDs = relatedShowIDs[:limit]
	}

	return
}

// CountRelationGroupByFilter 计算关联组数量
func (e Entry) CountRelationGroupByFilter(ctx *gin.Context, filter *RelationGroupFilter) (int64, error) {
	var count int64
	scopes := filter.fullScopes()

	err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowRelationGroup{}).Scopes(scopes...).Count(&count).Error
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("CountRelationGroupByFilter error")
		return 0, err
	}

	return count, nil
}
