package show

import (
	"context"
	"time"

	"vlab/app/common/dbs"
	"vlab/pkg/log"
	sliceUtil "vlab/pkg/util/slice_util"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var (
	_Model = &Show{}
)

type ContentType uint32

const (
	ContentTypeMovie ContentType = 1
	ContentTypeTV    ContentType = 2
	ContentTypeComic ContentType = 3
)

type InProduction uint32

const (
	InProductionYes InProduction = 1
	InProductionNo  InProduction = 2
)

func (ip InProduction) Uint32() uint32 {
	return uint32(ip)
}

type Show struct {
	dbs.ModelWithDel

	Status       uint32       `json:"status,omitempty"`        // 状态
	Name         string       `json:"name,omitempty"`          // 剧名
	NameKey      string       `json:"name_key,omitempty"`      // 剧名多语言key
	Overview     string       `json:"overview,omitempty"`      // 剧概述
	OverviewKey  string       `json:"overview_key,omitempty"`  // 剧概述多语言key
	AirDate      string       `json:"air_date,omitempty"`      // 首播日期
	AirDateKey   string       `json:"air_date_key,omitempty"`  // 首播日期多语言key
	ContentType  ContentType  `json:"content_type,omitempty"`  // 剧内容类型
	Score        uint32       `json:"score,omitempty"`         // 剧评分
	InProduction InProduction `json:"in_production,omitempty"` // 是否正在制作
	Homepage     string       `json:"homepage,omitempty"`      // 官网

	AirDateTs *time.Time `json:"air_date_ts,omitempty"` // 首播日期时间戳

	FranchiseID uint64 `json:"franchise_id,omitempty"` // 系列ID

	Langs string `json:"langs,omitempty"` // 语言,ISO_639_1,逗号分割

	PresentationTime uint32 `json:"presentation_time,omitempty"` // 播放年份
}

func (m *Show) TableName() string {
	return "content_show"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	NameLike    string
	Keyword     string   `json:"keyword,omitempty"`
	FranchiseID uint64   `json:"franchise_id,omitempty"`
	GenreID     uint64   `json:"genre_id,omitempty"`
	GenreIDs    []uint64 `json:"genre_ids,omitempty"`
	ContentType uint32   `json:"content_type,omitempty"`

	Status uint32 `json:"status,omitempty"`

	ChannelID uint64 `json:"channel_id,omitempty"`
	VersionID uint64 `json:"version_id,omitempty"`

	//AuditType uint32 `json:"audit_type,omitempty"` // 是否为审核剧

	FieldIDs []uint64 `json:"field_ids,omitempty"`

	UpdatedAfter *time.Time `json:"updated_after,omitempty"` // 查询更新时间大于此时间的数据

	Sort []clause.OrderByColumn `json:"sort,omitempty"`
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("content_show.status = ?", f.Status)
	}
}

func (f Filter) _eq_content_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ContentType <= 0 {
			return db
		}
		return db.Where("content_show.content_type = ?", f.ContentType)
	}
}

func (f Filter) _in_genre_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.GenreIDs) <= 0 {
			return db
		}
		var genreIDs []uint64
		genreIDs = sliceUtil.FilterZero(f.GenreIDs)
		if len(genreIDs) <= 0 {
			return db
		}
		return db.Where("content_show.id IN (SELECT show_id FROM content_show_with_genre WHERE genre_id IN (?) AND is_deleted = 0)", genreIDs)
	}
}

func (f Filter) _eq_franchise_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.FranchiseID <= 0 {
			return db
		}
		return db.Where("content_show.franchise_id = ?", f.FranchiseID)
	}
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("content_show.id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("content_show.id in (?)", f.IDs)
	}
}

func (f Filter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NameLike == "" {
			return db
		}
		// keyword := "%" + f.NameLike + "%"

		return db.Where("content_show.name LIKE ?", "%"+f.NameLike+"%")
		// return db.Where("(content_show.name LIKE ?) OR (content_show.name_key IN (SELECT DISTINCT `key` FROM content_i18n WHERE `table` = 'content_show' AND `column` = 'name' AND value LIKE ?))", keyword, keyword)
	}
}

// TODO: 多语言 多字段 模糊
func (f Filter) _like_keyword() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Keyword == "" {
			return db
		}

		return db.Where("content_show.name LIKE ?", "%"+f.Keyword+"%")

		// keyword := "%" + f.Keyword + "%"

		// return db.Where(
		// 	"((content_show.name LIKE ? OR content_show.overview LIKE ?) OR (content_show.name_key IN (SELECT DISTINCT `key` FROM content_i18n WHERE `table` = 'content_show' AND `column` = 'name' AND value LIKE ?)) OR (content_show.overview_key IN (SELECT DISTINCT `key` FROM content_i18n WHERE `table` = 'content_show' AND `column` = 'overview' AND value LIKE ?)))",
		// 	keyword, keyword, keyword, keyword,
		// )

		//return db.Where(
		//	db.Where("content_show.name like ?", "%"+f.Keyword+"%").
		//		Or("content_show.overview like ?", "%"+f.Keyword+"%").
		//		Or("content_show.name_key IN (SELECT DISTINCT `key` FROM content_i18n WHERE `table` = 'content_show' AND `column` = 'name' AND value LIKE ?)", "%"+f.Keyword+"%").
		//		Or("content_show.overview_key IN (SELECT DISTINCT `key` FROM content_i18n WHERE `table` = 'content_show' AND `column` = 'overview' AND value LIKE ?)", "%"+f.Keyword+"%"),
		//)
	}
}

func (f Filter) _filter_channel_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ChannelID <= 0 {
			return db
		}
		return db.Joins("JOIN content_show_with_limit as swla ON swla.show_id = content_show.id").
			Where("swla.is_deleted = 0").
			Where("swla.type = ?", LimitTypeChannel).
			Where("swla.limit_id = ?", f.ChannelID)
	}
}

func (f Filter) _filter_version_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.VersionID <= 0 {
			return db
		}
		return db.Joins("JOIN content_show_with_limit as swlb ON swlb.show_id = content_show.id").
			Where("swlb.is_deleted = 0").
			Where("swlb.type = ?", LimitTypeVersion).
			Where("swlb.limit_id = ?", f.VersionID)
	}
}

//func (f Filter) _filter_audit_type() func(db *gorm.DB) *gorm.DB {
//	return func(db *gorm.DB) *gorm.DB {
//		if f.AuditType <= 0 {
//			return db
//		}
//		return db.Joins("JOIN content_show_with_limit as swlc ON swlc.show_id = content_show.id").
//			Where("swlc.is_deleted = 0").
//			Where("swlc.type = ?", LimitTypeAudit).
//			Where("swlc.limit_id = ?", f.AuditType)
//	}
//}

func (f Filter) _filter_field_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.FieldIDs) <= 0 {
			return db
		}
		// 去除列表中的零值
		var fieldIDs []uint64
		fieldIDs = sliceUtil.FilterZero(f.FieldIDs)
		if len(fieldIDs) <= 0 {
			return db
		}

		return db.Joins("JOIN content_show_with_class as swf ON swf.show_id = content_show.id").
			Where("swf.is_deleted = 0").
			Where("swf.field_id IN (?)", fieldIDs)
	}
}

func (f Filter) _gt_updated_after() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.UpdatedAfter == nil {
			return db
		}
		return db.Where("content_show.updated_at > ?", f.UpdatedAfter).Where("content_show.updated_at <= ?", time.Now().Format(dbs.TimeDateFormatFull)) // 限制在24小时内更新的数据
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			db = db.Order("content_show.id desc")
			return db
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}

		return db
	}
}

func (f Filter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._like_keyword(),
		f._eq_franchise_id(),
		f._in_genre_ids(),
		f._eq_status(),
		f._gt_updated_after(),
		f._sort(),
		f._filter_channel_id(),
		f._eq_content_type(),
		f._filter_field_ids(),
		f._filter_version_id(),
		f._like_name(),
		//f._filter_audit_type(),
	}
}

type ShowList []*Show

func (sl ShowList) GetIDs() []uint64 {
	var ids []uint64
	for _, s := range sl {
		ids = append(ids, s.ID)
	}
	return lo.Uniq(ids)
}

func (sl ShowList) GetMap() map[uint64]*Show {
	m := make(map[uint64]*Show)
	for _, s := range sl {
		m[s.ID] = s
	}
	return m
}
