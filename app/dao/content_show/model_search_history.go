package show

import (
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type ShowSearchHistory struct {
	dbs.ModelWithDel

	UserID        uint64         `json:"user_id"`        // 用户ID
	SearchContent datatypes.JSON `json:"search_content"` // 搜索内容

	Keyword string `json:"keyword"`                           // 关键词
	Iso6391 string `json:"iso_639_1" gorm:"column:iso_639_1"` // 语言
}

func (m *ShowSearchHistory) TableName() string {
	return "content_show_search_history"
}

type ShowSearchHistoryList []*ShowSearchHistory

type SearchHistoryFilter struct {
	ID          uint64 `json:"id"`        // ID
	UserID      uint64 `json:"user_id"`   // 用户ID
	Keyword     string `json:"keyword"`   // 关键词
	Iso6391     string `json:"iso_639_1"` // 语言
	CreateStart string `json:"create_start"`
	CreateEnd   string `json:"create_end"` // 创建时间范围
}

func (f *SearchHistoryFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID > 0 {
			db = db.Where("id = ?", f.ID)
		}
		return db
	}
}

func (f *SearchHistoryFilter) _eq_user_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.UserID > 0 {
			db = db.Where("user_id = ?", f.UserID)
		}
		return db
	}
}

func (f *SearchHistoryFilter) _eq_keyword() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Keyword != "" {
			db = db.Where("keyword = ?", f.Keyword)
		}
		return db
	}
}

func (f *SearchHistoryFilter) _eq_iso_639_1() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Iso6391 != "" {
			db = db.Where("iso_639_1 = ?", f.Iso6391)
		}
		return db
	}
}

func (f *SearchHistoryFilter) _between_create_time() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.CreateStart != "" && f.CreateEnd != "" {
			db = db.Where("created_at >= ? AND created_at < ?", f.CreateStart, f.CreateEnd)
		}
		return db
	}
}

func (f SearchHistoryFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._eq_user_id(),
		f._eq_keyword(),
		f._eq_iso_639_1(),
		f._between_create_time(),
	}
}
