package show

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

// ShowRelationMember 剧集关联组成员
type ShowRelationMember struct {
	dbs.ModelWithDel

	GroupID uint64 `json:"group_id,omitempty"` // 关联组ID
	ShowID  uint64 `json:"show_id,omitempty"`  // 剧ID
	Order   uint32 `json:"order,omitempty"`    // 在组内的排序
}

func (m *ShowRelationMember) TableName() string {
	return "content_show_relation_member"
}

// RelationMemberFilter 关联组成员过滤条件
type RelationMemberFilter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	GroupID  uint64   `json:"group_id,omitempty"`
	GroupIDs []uint64 `json:"group_ids,omitempty"`

	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`

	ExcludeShowID uint64 `json:"exclude_show_id,omitempty"` // 排除的剧ID

	Sort []clause.OrderByColumn `json:"sort,omitempty"`
}

func (f RelationMemberFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f RelationMemberFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f RelationMemberFilter) _eq_group_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.GroupID <= 0 {
			return db
		}
		return db.Where("group_id = ?", f.GroupID)
	}
}

func (f RelationMemberFilter) _in_group_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.GroupIDs) <= 0 {
			return db
		}
		return db.Where("group_id in (?)", f.GroupIDs)
	}
}

func (f RelationMemberFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f RelationMemberFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f RelationMemberFilter) _ne_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ExcludeShowID <= 0 {
			return db
		}
		return db.Where("show_id != ?", f.ExcludeShowID)
	}
}

func (f RelationMemberFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_group_id(),
		f._in_group_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._ne_show_id(),
	}
}

// RelationMemberList 关联组成员列表
type RelationMemberList []*ShowRelationMember

// GetIDs 获取ID列表
func (list RelationMemberList) GetIDs() []uint64 {
	var ids []uint64
	for _, item := range list {
		if item.ID > 0 {
			ids = append(ids, item.ID)
		}
	}
	return ids
}

// GetGroupIDs 获取关联组ID列表
func (list RelationMemberList) GetGroupIDs() []uint64 {
	var ids []uint64
	seen := make(map[uint64]bool)
	for _, item := range list {
		if item.GroupID > 0 && !seen[item.GroupID] {
			ids = append(ids, item.GroupID)
			seen[item.GroupID] = true
		}
	}
	return ids
}

// GetShowIDs 获取剧ID列表
func (list RelationMemberList) GetShowIDs() []uint64 {
	var ids []uint64
	seen := make(map[uint64]bool)
	for _, item := range list {
		if item.ShowID > 0 && !seen[item.ShowID] {
			ids = append(ids, item.ShowID)
			seen[item.ShowID] = true
		}
	}
	return ids
}

// GetGroupShowMap 获取组ID到剧ID列表的映射
func (list RelationMemberList) GetGroupShowMap() map[uint64][]uint64 {
	m := make(map[uint64][]uint64)
	for _, item := range list {
		if item.GroupID > 0 && item.ShowID > 0 {
			m[item.GroupID] = append(m[item.GroupID], item.ShowID)
		}
	}
	return m
}

// GetShowGroupMap 获取剧ID到组ID列表的映射
func (list RelationMemberList) GetShowGroupMap() map[uint64][]uint64 {
	m := make(map[uint64][]uint64)
	for _, item := range list {
		if item.ShowID > 0 && item.GroupID > 0 {
			m[item.ShowID] = append(m[item.ShowID], item.GroupID)
		}
	}
	return m
}
