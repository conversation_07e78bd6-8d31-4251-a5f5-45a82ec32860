package show

import (
	"vlab/app/common/dbs"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// RelationType 关联类型
type RelationType uint32

const (
	RelationTypeRecommend RelationType = 1 // 相关推荐
	RelationTypeSeries    RelationType = 2 // 同系列
	RelationTypeSimilar   RelationType = 3 // 相似题材
	RelationTypeI18n      RelationType = 4 // 多语言组合
)

// ShowRelationGroup 剧集关联组
type ShowRelationGroup struct {
	dbs.ModelWithDel

	Name         string       `json:"name,omitempty"`          // 关联组名称
	Description  string       `json:"description,omitempty"`   // 关联组描述
	RelationType RelationType `json:"relation_type,omitempty"` // 关联类型
	Status       uint32       `json:"status,omitempty"`        // 状态: 1-启用,2-禁用
}

func (m *ShowRelationGroup) TableName() string {
	return "content_show_relation_group"
}

// RelationGroupFilter 关联组过滤条件
type RelationGroupFilter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	Name         string       `json:"name,omitempty"`          // 名称模糊查询
	RelationType RelationType `json:"relation_type,omitempty"` // 关联类型
	Status       uint32       `json:"status,omitempty"`        // 状态

	Sort []clause.OrderByColumn `json:"sort,omitempty"`
}

func (f RelationGroupFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f RelationGroupFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f RelationGroupFilter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Name == "" {
			return db
		}
		return db.Where("name like ?", "%"+f.Name+"%")
	}
}

func (f RelationGroupFilter) _eq_relation_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.RelationType <= 0 {
			return db
		}
		return db.Where("relation_type = ?", f.RelationType)
	}
}

func (f RelationGroupFilter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f RelationGroupFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._like_name(),
		f._eq_relation_type(),
		f._eq_status(),
	}
}

// RelationGroupList 关联组列表
type RelationGroupList []*ShowRelationGroup

// GetIDs 获取ID列表
func (list RelationGroupList) GetIDs() []uint64 {
	var ids []uint64
	for _, item := range list {
		if item.ID > 0 {
			ids = append(ids, item.ID)
		}
	}
	return ids
}

// GetIDMap 获取ID映射
func (list RelationGroupList) GetIDMap() map[uint64]*ShowRelationGroup {
	m := make(map[uint64]*ShowRelationGroup, len(list))
	for _, item := range list {
		if item.ID > 0 {
			m[item.ID] = item
		}
	}
	return m
}
