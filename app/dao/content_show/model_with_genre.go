package show

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type ShowWithGenre struct {
	dbs.ModelWithDel

	ShowID  uint64 `json:"show_id,omitempty"`  // 剧ID
	GenreID uint64 `json:"genre_id,omitempty"` // 类型ID
	Type    uint32 `json:"type,omitempty"`     // 类型
}

func (m *ShowWithGenre) TableName() string {
	return "content_show_with_genre"
}

type GenreFilter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID  uint64   `json:"show_id,omitempty"`  // 剧ID
	ShowIDs []uint64 `json:"show_ids,omitempty"` // 剧ID
	GenreID uint64   `json:"genre_id,omitempty"` // 类型ID

	Type uint32 `json:"type,omitempty"` // 类型
}

func (f GenreFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f GenreFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f GenreFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f GenreFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f GenreFilter) _eq_genre_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.GenreID <= 0 {
			return db
		}
		return db.Where("genre_id = ?", f.GenreID)
	}
}

func (f GenreFilter) _eq_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Type <= 0 {
			return db
		}
		return db.Where("type = ?", f.Type)
	}
}

func (f GenreFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_genre_id(),
		f._eq_type(),
	}
}

type ShowWithGenreList []*ShowWithGenre

func (pl ShowWithGenreList) GetGenreIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.GenreID)
	}
	return ids
}

func (pl ShowWithGenreList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ShowID)
	}
	return ids
}

func (pl ShowWithGenreList) GetShowIDMap() map[uint64]ShowWithGenreList {
	m := make(map[uint64]ShowWithGenreList)
	for _, p := range pl {
		if _, ok := m[p.ShowID]; !ok {
			m[p.ShowID] = ShowWithGenreList{}
		}
		m[p.ShowID] = append(m[p.ShowID], p)
	}
	return m
}
