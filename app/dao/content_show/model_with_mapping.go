package show

import (
	"github.com/samber/lo"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type MappingType uint32

const (
	MappingLok MappingType = 1
)

type ShowWithMapping struct {
	dbs.ModelWithDel

	ShowID uint64

	MappingType MappingType

	MappingID uint64

	MappingStash datatypes.JSON
}

func (m *ShowWithMapping) TableName() string {
	return "content_show_with_mapping"
}

type MappingFilter struct {
	ID         uint64   `json:"id,omitempty"`
	IDs        []uint64 `json:"ids,omitempty"`
	MappingID  uint64   `json:"mapping_id,omitempty"`
	MappingIDs []uint64 `json:"mapping_ids,omitempty"`

	ShowID  uint64   `json:"show_id,omitempty"`
	ShowIDs []uint64 `json:"show_ids,omitempty"`

	MappingType uint32 `json:"mapping_type,omitempty"`
}

func (f MappingFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f MappingFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f MappingFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f MappingFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f MappingFilter) _eq_mapping_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.MappingID <= 0 {
			return db
		}
		return db.Where("mapping_id = ?", f.MappingID)
	}
}

func (f MappingFilter) _in_mapping_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.MappingIDs) <= 0 {
			return db
		}
		return db.Where("mapping_id in (?)", f.MappingIDs)
	}
}

func (f MappingFilter) _eq_mapping_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.MappingType <= 0 {
			return db
		}
		return db.Where("mapping_type = ?", f.MappingType)
	}
}

func (f MappingFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_mapping_id(),
		f._in_mapping_ids(),
		f._eq_mapping_type(),
	}
}

type ShowWithMappingList []*ShowWithMapping

func (ml ShowWithMappingList) GetShowIDMap() map[uint64]*ShowWithMapping {
	retMap := make(map[uint64]*ShowWithMapping, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ShowID]; !ok {
			retMap[val.ShowID] = val
		}
	}
	return retMap
}

func (ml ShowWithMappingList) GetMappingIDMap() map[uint64]*ShowWithMapping {
	retMap := make(map[uint64]*ShowWithMapping, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.MappingID]; !ok {
			retMap[val.MappingID] = val
		}
	}
	return retMap
}

func (ml ShowWithMappingList) GetShowIDs() []uint64 {
	ret := []uint64{}
	for _, val := range ml {
		ret = append(ret, val.ShowID)
	}
	ret = lo.Uniq(ret)
	return ret
}

func (ml ShowWithMappingList) GetMappingIDs() []uint64 {
	ret := []uint64{}
	for _, val := range ml {
		ret = append(ret, val.MappingID)
	}
	ret = lo.Uniq(ret)
	return ret
}
