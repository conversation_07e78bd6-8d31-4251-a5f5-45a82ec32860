package show

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

func (e Entry) FindMappingXidsByFilter(ctx *gin.Context, filter *MappingFilter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithMapping{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FindMappingByFilter(ctx *gin.Context, filter *MappingFilter) (ShowWithMappingList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithMapping{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowWithMappingList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) MappingCreateOrUpdate(context *gin.Context, model *ShowWithMapping) (uint64, error) {
	panic("implement me")
}

func (e Entry) MappingCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, m *ShowWithMapping) (uint64, error) {
	if err := db.Model(&ShowWithMapping{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "show_id"}, {Name: "mapping_type"}, {Name: "mapping_id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"mapping_stash",
			}),
			Where: clause.Where{
				Exprs: []clause.Expression{clause.Expr{SQL: fmt.Sprintf("%s = 0", dbs.SoftDelField)}},
			},
		}).Create(m).Error; err != nil {
		return 0, err
	}
	return m.ID, nil

}

func (e Entry) MappingCreate(ctx *gin.Context, model *ShowWithMapping) (uint64, error) {
	return e.MappingCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) MappingCreateWithTx(ctx *gin.Context, db *gorm.DB, model *ShowWithMapping) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) MappingBatchCreate(ctx *gin.Context, models []*ShowWithMapping) error {
	return e.MappingBatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) MappingBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithMapping) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}
