package show

import (
	"fmt"

	"gorm.io/gorm"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindGenreByFilter(ctx *gin.Context, filter *GenreFilter) (ShowWithGenreList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowWithGenreList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) GenreCreateOrUpdate(context *gin.Context, model *ShowWithGenre) (uint64, error) {
	panic("implement me")
}

func (e Entry) GenreCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, model *ShowWithGenre) (uint64, error) {
	panic("implement me")
}

func (e Entry) GenreCreate(ctx *gin.Context, model *ShowWithGenre) (uint64, error) {
	return e.GenreCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) GenreCreateWithTx(ctx *gin.Context, db *gorm.DB, model *ShowWithGenre) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) GenreBatchCreate(ctx *gin.Context, models []*ShowWithGenre) error {
	return e.GenreBatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) GenreBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithGenre) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) GenreUpdateModelByID(ctx *gin.Context, u uint64, model *ShowWithGenre) error {
	return e.GenreUpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) GenreUpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *ShowWithGenre) error {
	if err := db.Model(&ShowWithGenre{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) GenreUpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.GenreUpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) GenreUpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&ShowWithGenre{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) GenreDataPageList(ctx *gin.Context, filter *GenreFilter, page int, limit int) (total int64, list ShowWithGenreList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindGenreXidsByFilter(ctx *gin.Context, filter *GenreFilter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchGenreByID(ctx *gin.Context, id uint64) (*ShowWithGenre, error) {
	ret := &ShowWithGenre{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchGenreByFilterSort(ctx *gin.Context, filter *GenreFilter) (*ShowWithGenre, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &ShowWithGenre{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountGenreByFilter(ctx *gin.Context, filter *GenreFilter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithGenre{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) DeleteGenreByFilter(ctx *gin.Context, filter *GenreFilter) (int64, error) {
	return e.DeleteGenreByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter)
}

func (e Entry) DeleteGenreByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *GenreFilter) (int64, error) {
	query := tx.Model(&ShowWithGenre{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	result := query.UpdateColumn(string(dbs.SoftDelField), 1)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
