package show

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type ShowWithFranchise struct {
	dbs.ModelWithDel

	ShowID      uint64 `json:"show_id,omitempty"`      // 剧ID
	FranchiseID uint64 `json:"franchise_id,omitempty"` // 剧集ID
	Type        uint32 `json:"type,omitempty"`         // 类型
}

func (m *ShowWithFranchise) TableName() string {
	return "content_show_with_franchise"
}

type FranchiseFilter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID      uint64   `json:"show_id,omitempty"`      // 剧ID
	ShowIDs     []uint64 `json:"show_ids,omitempty"`     // 剧ID
	FranchiseID uint64   `json:"franchise_id,omitempty"` // 剧集ID

	Type  uint32   `json:"type,omitempty"` // 类型
	Types []uint32 `json:"types,omitempty"`
}

func (f FranchiseFilter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f FranchiseFilter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f FranchiseFilter) _eq_show_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f FranchiseFilter) _in_show_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f FranchiseFilter) _eq_franchise_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.FranchiseID <= 0 {
			return db
		}
		return db.Where("franchise_id = ?", f.FranchiseID)
	}
}

func (f FranchiseFilter) _eq_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Type <= 0 {
			return db
		}
		return db.Where("type = ?", f.Type)
	}
}

func (f FranchiseFilter) _in_types() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Types) <= 0 {
			return db
		}
		return db.Where("type in (?)", f.Types)
	}
}

func (f FranchiseFilter) fullScopes() []func(*gorm.DB) *gorm.DB {
	return []func(*gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_franchise_id(),
		f._eq_type(),
		f._in_types(),
	}
}

type ShowWithFranchiseList []*ShowWithFranchise

func (pl ShowWithFranchiseList) GetFranchiseIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.FranchiseID)
	}
	return ids
}

func (pl ShowWithFranchiseList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ShowID)
	}
	return ids
}

func (pl ShowWithFranchiseList) GetShowIDMap() map[uint64]ShowWithFranchiseList {
	m := make(map[uint64]ShowWithFranchiseList)
	for _, p := range pl {
		if _, ok := m[p.ShowID]; !ok {
			m[p.ShowID] = ShowWithFranchiseList{}
		}
		m[p.ShowID] = append(m[p.ShowID], p)
	}
	return m
}
