package show

import (
	"fmt"

	"gorm.io/gorm"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
)

func (e Entry) FindLimitByFilter(ctx *gin.Context, filter *LimitFilter) (ShowWithLimitList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowWithLimitList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) LimitCreateOrUpdate(context *gin.Context, model *ShowWithLimit) (uint64, error) {
	panic("implement me")
}

func (e Entry) LimitCreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, model *ShowWithLimit) (uint64, error) {
	panic("implement me")
}

func (e Entry) LimitCreate(ctx *gin.Context, model *ShowWithLimit) (uint64, error) {
	return e.LimitCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) LimitCreateWithTx(ctx *gin.Context, db *gorm.DB, model *ShowWithLimit) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) LimitBatchCreate(ctx *gin.Context, models []*ShowWithLimit) error {
	return e.LimitBatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) LimitBatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*ShowWithLimit) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) LimitUpdateModelByID(ctx *gin.Context, u uint64, model *ShowWithLimit) error {
	return e.LimitUpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) LimitUpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *ShowWithLimit) error {
	if err := db.Model(&ShowWithLimit{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) LimitUpdateMapByID(ctx *gin.Context, u uint64, m map[string]interface{}) error {
	return e.LimitUpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, m)
}

func (e Entry) LimitUpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&ShowWithLimit{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) LimitDataPageList(ctx *gin.Context, filter *LimitFilter, page int, limit int) (total int64, list ShowWithLimitList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindLimitXidsByFilter(ctx *gin.Context, filter *LimitFilter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchLimitByID(ctx *gin.Context, id uint64) (*ShowWithLimit, error) {
	ret := &ShowWithLimit{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchLimitByFilterSort(ctx *gin.Context, filter *LimitFilter) (*ShowWithLimit, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &ShowWithLimit{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountLimitByFilter(ctx *gin.Context, filter *LimitFilter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&ShowWithLimit{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) DeleteLimitByFilter(ctx *gin.Context, filter *LimitFilter) (int64, error) {
	return e.DeleteLimitByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter)
}

func (e Entry) DeleteLimitByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *LimitFilter) (int64, error) {
	query := tx.Model(&ShowWithLimit{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	result := query.UpdateColumn(string(dbs.SoftDelField), 1)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}
