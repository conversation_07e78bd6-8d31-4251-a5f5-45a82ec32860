package show

import (
	"fmt"

	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func (e Entry) CreateOrUpdate(context *gin.Context, m *Show) (uint64, error) {
	panic("implement me")
}

func (e Entry) CreateOrUpdateWithTx(context *gin.Context, db *gorm.DB, m *Show) (uint64, error) {
	panic("implement me")
}

func (e Entry) Create(ctx *gin.Context, model *Show) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, db *gorm.DB, model *Show) (uint64, error) {
	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*Show) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, db *gorm.DB, models []*Show) error {
	if len(models) == 0 {
		return nil
	}

	if err := db.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *Show) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, model *Show) error {
	if err := db.Model(&Show{}).Where("id = ?", id).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, u uint64, model map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateMapByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64, m map[string]interface{}) error {
	if err := db.Model(&Show{}).Where("id = ?", id).Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByFilter(ctx *gin.Context, filter *Filter, model map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter, model)
}

func (e Entry) UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter, model map[string]interface{}) error {
	query := tx.Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	if err := query.Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list ShowList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("content_show.id").Count(&total)
	if err = query.Select("content_show.*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (ShowList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ShowList{}
	if err := query.Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query.Scopes(f.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*Show, error) {
	ret := &Show{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Show, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &Show{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Show{}).
		Where(fmt.Sprintf("%s.%s = 0", _Model.TableName(), dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}
