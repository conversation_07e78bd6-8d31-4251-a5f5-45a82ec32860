package content_class_mapping

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	ClassMappingRepo
}

type ClassMappingRepo interface {
	CreateOrUpdate(*gin.Context, *Model) (uint64, error)

	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) (uint64, error)

	Create(*gin.Context, *Model) (uint64, error)

	CreateWithTx(*gin.Context, *gorm.DB, *Model) (uint64, error)

	BatchCreate(*gin.Context, []*Model) error

	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Model) error

	UpdateModelByID(*gin.Context, uint64, *Model) error

	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Model) error

	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error

	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error

	DataPageList(*gin.Context, *Filter, int, int) (total int64, list ModelList, err error)

	FindByFilter(*gin.Context, *Filter) (ModelList, error)

	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)

	FetchByID(*gin.Context, uint64) (*Model, error)

	FeatchByFilterSort(*gin.Context, *Filter) (*Model, error)

	CountByFilter(*gin.Context, *Filter) (int64, error)

	DeleteByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter) (int64, error)

	DeleteByFilter(ctx *gin.Context, filter *Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
