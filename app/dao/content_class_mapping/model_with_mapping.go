package content_class_mapping

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type ClassMappingType uint32

const (
	ClassMappingZone  ClassMappingType = 1 // 区域
	ClassMappingGenre ClassMappingType = 2 // 类型
)

type Model struct {
	dbs.ModelWithDel

	ClassID uint64

	MappingType ClassMappingType

	MappingID uint64
}

func (m *Model) TableName() string {
	return "content_class_field_with_mapping"
}

type ModelList []*Model

func (ml ModelList) GetClassIDs() []uint64 {
	classIDs := make([]uint64, 0, len(ml))
	for _, m := range ml {
		classIDs = append(classIDs, m.ClassID)
	}
	return lo.Uniq(classIDs)
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`
	IDs []uint64 `json:"ids,omitempty"`

	ClassID     uint64           `json:"class_id,omitempty"`
	ClassIDs    []uint64         `json:"class_ids,omitempty"`
	MappingID   uint64           `json:"mapping_id,omitempty"`
	MappingIDs  []uint64         `json:"mapping_ids,omitempty"`
	MappingType ClassMappingType `json:"mapping_type,omitempty"`
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_class_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ClassID <= 0 {
			return db
		}
		return db.Where("class_id = ?", f.ClassID)
	}
}

func (f Filter) _in_class_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.ClassIDs) <= 0 {
			return db
		}
		return db.Where("class_id in (?)", f.ClassIDs)
	}
}

func (f Filter) _eq_mapping_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.MappingID <= 0 {
			return db
		}
		return db.Where("mapping_id = ?", f.MappingID)
	}
}
func (f Filter) _in_mapping_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.MappingIDs) <= 0 {
			return db
		}
		return db.Where("mapping_id in (?)", f.MappingIDs)
	}
}

func (f Filter) _eq_mapping_type() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.MappingType <= 0 {
			return db
		}
		return db.Where("mapping_type = ?", f.MappingType)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_class_id(),
		f._in_class_ids(),
		f._eq_mapping_id(),
		f._in_mapping_ids(),
		f._eq_mapping_type(),
	}
}
