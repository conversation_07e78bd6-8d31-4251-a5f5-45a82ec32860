package admin_banner

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	BannerRepo
}

type BannerRepo interface {
	CreateOrUpdate(ctx *gin.Context, model *Banner) (uint64, error)

	CreateOrUpdateWithTx(ctx *gin.Context, db *gorm.DB, model *Banner) (uint64, error)

	Create(ctx *gin.Context, model *Banner) (uint64, error)

	CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Banner) (uint64, error)

	BatchCreate(ctx *gin.Context, models []*Banner) error

	BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Banner) error

	UpdateModelByID(ctx *gin.Context, u uint64, model *Banner) error

	UpdateModelByIDWithTx(ctx *gin.Context, tx *gorm.DB, u uint64, model *Banner) error

	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error

	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error

	UpdateMapByFilter(ctx *gin.Context, filter *Filter, data map[string]interface{}) error

	UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, filter *Filter, data map[string]interface{}) error

	DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list ModelList, err error)

	FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)

	//FindByXidsByFilter(ctx *gin.Context, filter *Filter) (ModelList, error)

	FetchByID(ctx *gin.Context, id uint64) (*Banner, error)

	FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Banner, error)

	CountByFilter(ctx *gin.Context, filter *Filter) (int64, error)

	DeleteByIDWithTx(ctx *gin.Context, db *gorm.DB, id uint64) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
