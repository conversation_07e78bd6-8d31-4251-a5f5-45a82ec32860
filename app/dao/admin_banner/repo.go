package admin_banner

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

func (e Entry) CreateOrUpdate(ctx *gin.Context, model *Banner) (uint64, error) {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateOrUpdateWithTx(ctx *gin.Context, db *gorm.DB, model *Banner) (uint64, error) {
	// implement me
	panic("implement me")
}

func (e Entry) Create(ctx *gin.Context, model *Banner) (uint64, error) {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

func (e Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, model *Banner) (uint64, error) {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&model).Error; err != nil {
		return 0, err
	}
	return model.ID, nil
}

func (e Entry) BatchCreate(ctx *gin.Context, models []*Banner) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
}

func (e Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Banner) error {
	if len(models) == 0 {
		return nil
	}

	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(models).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateModelByID(ctx *gin.Context, u uint64, model *Banner) error {
	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), u, model)
}

func (e Entry) UpdateModelByIDWithTx(ctx *gin.Context, tx *gorm.DB, u uint64, model *Banner) error {
	if err := tx.Model(&Banner{}).Where("id = ?", u).Updates(model).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

func (e Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Banner{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) UpdateMapByFilter(ctx *gin.Context, filter *Filter, m map[string]interface{}) error {
	return e.UpdateMapByFilterWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), filter, m)
}

func (e Entry) UpdateMapByFilterWithTx(ctx *gin.Context, tx *gorm.DB, f *Filter, m map[string]interface{}) error {
	query := tx.Model(&Banner{})

	query.Scopes(f.fullScopes()...)

	if err := query.Updates(m).Error; err != nil {
		return err
	}
	return nil
}

func (e Entry) DataPageList(ctx *gin.Context, filter *Filter, page int, limit int) (total int64, list ModelList, err error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query = query.Scopes(filter.fullScopes()...)

	query.Select("id").Count(&total)
	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).
		Find(&list).Error; err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (e Entry) FindByFilter(ctx *gin.Context, filter *Filter) (ModelList, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)) // 未删除

	query.Scopes(
		filter.fullScopes()...,
	)

	ret := ModelList{}
	if err := query.Debug().Find(&ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FindXidsByFilter(ctx *gin.Context, filter *Filter, field dbs.PluckField) ([]uint64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := []uint64{}
	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FetchByID(ctx *gin.Context, id uint64) (*Banner, error) {
	ret := &Banner{}
	if err := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).
		Where(fmt.Sprintf("%s = 0", dbs.SoftDelField)).
		Where("id = ?", id).Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) FeatchByFilterSort(ctx *gin.Context, filter *Filter) (*Banner, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(filter.fullScopes()...)

	ret := &Banner{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		return ret, err
	}
	return ret, nil
}

func (e Entry) CountByFilter(ctx *gin.Context, filter *Filter) (int64, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Banner{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))

	query.Scopes(
		filter.fullScopes()...,
	)

	var total int64
	if err := query.Count(&total).Error; err != nil {
		return 0, err
	}
	return total, nil
}

func (e Entry) DeleteByIDWithTx(ctx *gin.Context, db *gorm.DB, u uint64) error {
	if err := db.Model(&Banner{}).Where("id = ?", u).Update(string(dbs.SoftDelField), 1).Error; err != nil {
		return err
	}

	return nil
}
