package admin_banner

import (
	"context"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	"vlab/config"
	"vlab/pkg/log"
)

type JumpType uint32

const (
	JumpTypeNone  JumpType = iota
	JumpTypeGoods          // 商品
	JumpTypeUrl            // 链接
)

type Position uint32

const (
	PositionHome Position = iota + 1
	PositionCard          // 卡片
)

type Banner struct {
	dbs.ModelWithDel

	ChannelID uint64          `gorm:"column:channel_id;NOT NULL"` //
	Title     string          `gorm:"column:title;NOT NULL"`      // 标题
	TitleKey  string          `gorm:"column:title_key;NOT NULL"`  // 标题key
	Cover     dbs.OssFilePath `gorm:"column:cover;NOT NULL"`      // 封面
	JumpType  JumpType        `gorm:"column:jump_type;NOT NULL"`  // 跳转类型
	Jump      string          `gorm:"column:jump;NOT NULL"`       // 跳转链接
	Position  Position        `gorm:"column:position;NOT NULL"`   // 位置
	Sort      uint64          `gorm:"column:sort;NOT NULL"`       // 排序

	Status dbs.StatusType `gorm:"column:status;default:1"` // 启用禁用
}

func (m *Banner) TableName() string {
	return "content_banner"
}

type Filter struct {
	ID        uint64
	IDS       []uint64
	Position  Position
	Title     string
	Status    dbs.StatusType
	ChannelID uint64 //

	Clauses []clause.Expression
	Sort    []clause.OrderByColumn
}

func (m *Banner) GetFilePath(host string) string {
	if m.Cover == "" {
		return ""
	}
	if host == "" {
		host = config.BytePlusCfg.OssImageHost
	}
	return m.Cover.String(host)
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID > 0 {
			return db.Where("id = ?", f.ID)
		}
		return db
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDS) > 0 {
			return db.Where("id IN ?", f.IDS)
		}
		return db
	}
}

func (f Filter) _like_title() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Title != "" {
			return db.Where("title LIKE ?", "%"+f.Title+"%")
		}
		return db
	}
}

func (f Filter) _eq_position() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if t := f.Position; t > 0 {
			return db.Where("position = ?", t)
		}
		return db
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status > 0 {
			return db.Where("status = ?", f.Status)
		}
		return db
	}
}

func (f Filter) _clauses() func(db *gorm.DB) *gorm.DB {
	return func(tx *gorm.DB) *gorm.DB {
		if t := f.Clauses; len(t) > 0 {
			return tx.Clauses(t...)
		}
		return tx
	}
}

func (f Filter) _eq_channel_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ChannelID > 0 {
			return db.Where("channel_id = ?", f.ChannelID)
		}
		return db
	}
}

func (f Filter) _sort() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.Sort) <= 0 {
			return db.Order("id desc")
		}
		for _, val := range f.Sort {
			if val.Column.Name == "" {
				log.WithField(context.Background(), "column", val).Error("sort column name is empty")
				continue
			}
			db = db.Order(val)
		}
		return db
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._like_title(),
		f._eq_status(),
		f._clauses(),
		f._eq_position(),
		f._eq_channel_id(),
		f._sort(),
	}
}

type ModelList []*Banner

func (ml ModelList) GetIDs() []uint64 {
	ret := lo.Map(ml, func(item *Banner, idx int) uint64 {
		return item.ID
	})
	return lo.Uniq(ret)
}

func (ml ModelList) GetIDMap() map[uint64]*Banner {
	retMap := make(map[uint64]*Banner)
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}

	return retMap
}

func (ml ModelList) GetKeys() []string {
	ret := lo.Map(ml, func(item *Banner, idx int) string {
		return item.TitleKey
	})
	return lo.Uniq(ret)
}

func (ml ModelList) GetChannelIDs() []uint64 {
	ret := lo.FilterMap(ml, func(item *Banner, idx int) (uint64, bool) {
		return item.ChannelID, item.ChannelID > 0
	})
	return lo.Uniq(ret)
}
