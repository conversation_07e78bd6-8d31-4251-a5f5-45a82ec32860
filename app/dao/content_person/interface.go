package person

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	PersonRepo
}

type PersonRepo interface {
	FindByFilter(*gin.Context, *Filter) (PersonList, error)

	CreateOrUpdate(*gin.Context, *Person) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Person) (uint64, error)
	Create(*gin.Context, *Person) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Person) (uint64, error)
	BatchCreate(*gin.Context, []*Person) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Person) error
	UpdateModelByID(*gin.Context, uint64, *Person) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Person) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list PersonList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Person, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Person, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
