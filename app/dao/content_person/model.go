package person

import (
	"gorm.io/gorm"
	"vlab/app/common/dbs"
)

type Person struct {
	dbs.ModelWithDel

	Status    uint32 `json:"status,omitempty"`     // 状态
	Name      string `json:"name,omitempty"`       // 名字
	NameKey   string `json:"name_key,omitempty"`   // 名字多语言key
	Gender    uint32 `json:"gender,omitempty"`     // 性别
	ProfileID uint64 `json:"profile_id,omitempty"` // 头像ID
}

func (m *Person) TableName() string {
	return "content_person"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	NameLike string `json:"name_like,omitempty"` // 名字模糊查询

	Status uint32 `json:"status,omitempty"` // 状态
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _like_name() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.NameLike == "" {
			return db
		}
		return db.Where("name like ? OR name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_person' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%", "%"+f.NameLike+"%")

		//return db.Where(
		//	db.Where("name like ?", "%"+f.NameLike+"%").
		//	Or("name_key IN (SELECT `key` FROM content_i18n WHERE `table` = 'content_person' AND `column` = 'name' AND value LIKE ?)", "%"+f.NameLike+"%")
		//	)
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._like_name(),
		f._eq_status(),
	}
}

type PersonList []*Person

func (pl PersonList) GetImageIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ProfileID)
	}
	return ids
}

func (pl PersonList) GetIDs() []uint64 {
	var ids []uint64
	for _, p := range pl {
		ids = append(ids, p.ID)
	}
	return ids
}

func (pl PersonList) GetMap() map[uint64]*Person {
	m := make(map[uint64]*Person)
	for _, p := range pl {
		m[p.ID] = p
	}
	return m
}

func (pl PersonList) GetNameKeys() []string {
	var keys []string
	for _, p := range pl {
		keys = append(keys, p.NameKey)
	}
	return keys
}
