package videoMps

import (
	"time"
	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"

	"github.com/samber/lo"
)

type MpsStatus uint32
type OperateType uint32

const (
	MpsStatusIng      MpsStatus = 1 // 转码中
	MpsStatusSuccess  MpsStatus = 2 // 转码成功
	MpsStatusFailed   MpsStatus = 3 // 转码失败
	MpsStatusInvalid  MpsStatus = 4 // url或vido id无效
	MpsStatusSrvError MpsStatus = 5 // 第三方服务错误

	OtVodUpload   OperateType = 1 // vod上传
	OtVodPull     OperateType = 2 // vod拉取
	OtOssUpload   OperateType = 3 // oss上传
	OtByteVodPull OperateType = 4 // byte vod拉取

	RetryNum uint32 = 3
)

type Model struct {
	ID              uint64                   `json:"id,omitempty"`
	VideoID         uint64                   `json:"video_id,omitempty"`
	Resolution      common.Resolution        `json:"resolution,omitempty"`
	TranscodeFormat common.TranscodingFormat `json:"transcode_format,omitempty"`
	Status          MpsStatus                `json:"status,omitempty"`
	WaitMigrate     uint32                   `json:"wait_migrate,omitempty"`
	VideoKey        string                   `json:"video_key,omitempty"`
	MpsVideoKey     string                   `json:"mps_video_key,omitempty"`
	JobID           string                   `json:"job_id,omitempty"`
	TempID          string                   `json:"temp_id,omitempty"`
	Retry           uint32                   `json:"retry,omitempty"`
	OperateType     uint32                   `json:"operate_type,omitempty"`
	Runtime         int64                    `json:"runtime,omitempty"`    // 视频时长
	IsDeleted       uint32                   `json:"is_deleted,omitempty"` // 软删
	CreatedAt       time.Time                `json:"created_at"`           // 创建时间
	UpdatedAt       time.Time                `json:"updated_at"`           // 修改时间
}

func (m *Model) TableName() string {
	return "content_video_mps"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

func (m *Model) GetVodDefinition(resolution common.Resolution) common.VodDefinition {
	switch resolution {
	case common.Resolution_640:
		return common.VodDefinitionFD
	case common.Resolution_848:
		return common.VodDefinitionLD
	case common.Resolution_1280:
		return common.VodDefinitionSD
	default:
		return common.VodDefinitionOD
	}
}

func (m *Model) GetBytesVodDefinition(resolution common.Resolution) common.VodDefinition {
	switch resolution {
	case common.Resolution_640:
		return common.BytesVodDefinition480
	case common.Resolution_848:
		return common.BytesVodDefinition540
	case common.Resolution_1280:
		return common.BytesVodDefinition720
	case common.Resolution_1920:
		return common.BytesVodDefinition1080
	default:
		return common.BytesVodDefinitionOD
	}
}

type Filter struct {
	LtID           uint64
	GtID           uint64
	VideoID        uint64
	LetVideoID     uint64
	GetVideoID     uint64
	VideoKey       string
	Resolution     common.Resolution
	ResolutionZero bool
	Status         uint32
	OperateType    uint32
	NotOperateType uint32
	StatusNot      uint32
	WaitMigrate    uint32
	LtRetry        uint32
	RuntimeType    uint32 // 1: 视频时长为0
	Limit          int
	Sort           dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.ID
	})
	return ret
}

func (ml ModelList) GetVideoIds() []uint64 {
	ret := lo.Map(ml, func(item *Model, idx int) uint64 {
		return item.VideoID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetJobIds() []string {
	ret := lo.Map(ml, func(item *Model, idx int) string {
		return item.JobID
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetVideoKeys() []string {
	ret := lo.Map(ml, func(item *Model, idx int) string {
		return item.VideoKey
	})
	ret = lo.Uniq(ret)
	return ret
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}
