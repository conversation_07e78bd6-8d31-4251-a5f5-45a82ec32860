package videoMps

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// const (
// 	SortFieldResolution dbs.SortField = "resolution"
// )

// CreateOrUpdate .
func (e *Entry) CreateOrUpdate(ctx *gin.Context, m *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateOrUpdateWithTx .
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Model(&Model{}).Omit("created_time", "modified_time").
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "id"}},
			DoUpdates: clause.AssignmentColumns([]string{"job_id", "status"}),
		}).Create(&m).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateOrUpdateWithTx error")
		return err
	}
	return nil
}

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Omit("created_time", "modified_time").Create(&m).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateWithTx error")
		return err
	}
	return nil
}

// BatchCreate .
func (e *Entry) BatchCreate(ctx *gin.Context, m []*Model) error {
	return e.BatchCreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// BatchCreateWithTx .
func (e *Entry) BatchCreateWithTx(ctx *gin.Context, tx *gorm.DB, m []*Model) error {
	if len(m) == dbs.False {
		return nil
	}
	if err := tx.Omit("created_time", "modified_time").Create(m).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("BatchCreateWithTx error")
		return err
	}
	return nil
}

// func (e *Entry) BatchCreateOrUpdate(ctx *gin.Context, models []*Model) error {
// 	return e.BatchCreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), models)
// }

// func (e *Entry) BatchCreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, models []*Model) error {
// 	if len(models) == dbs.False {
// 		return nil
// 	}
// 	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
// 		Clauses(clause.OnConflict{
// 			Columns:   []clause.Column{{Name: "video_id"}, {Name: "resolution"}},
// 			DoUpdates: clause.AssignmentColumns([]string{"video_key", "mps_video_key", "job_id", "temp_id", "status", "retry", "operate_type"}),
// 		}).Create(&models).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// // UpdateModelByID .
// func (e *Entry) UpdateModelByID(ctx *gin.Context, id uint64, m *Model) error {
// 	return e.UpdateModelByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, m)
// }

// // UpdateModelByIDWithTx .
// func (e *Entry) UpdateModelByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, m *Model) error {
// 	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(m).Error; err != nil {
// 		log.Ctx(ctx).WithError(err).Error("UpdateModelByIDWithTx error")
// 		return err
// 	}
// 	return nil
// }

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByIDWithTx error")
		return err
	}
	return nil
}

func (e *Entry) UpdateMapByIds(ctx *gin.Context, ids []uint64, data map[string]interface{}) error {
	return e.UpdateMapByIdsWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), ids, data)
}

func (e *Entry) UpdateMapByIdsWithTx(ctx *gin.Context, tx *gorm.DB, ids []uint64, data map[string]interface{}) error {
	if len(ids) == dbs.False {
		return nil
	}
	if err := tx.Model(&Model{}).Where("id IN (?)", ids).Updates(data).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByIdsWithTx error")
		return err
	}
	return nil
}

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.LtID != 0 {
		query.Where("id < ?", f.LtID)
	}
	if f.LtRetry != 0 {
		query.Where("retry < ?", f.LtRetry)
	}
	if f.VideoID != 0 {
		query.Where("video_id = ?", f.VideoID)
	}
	if f.VideoKey != "" {
		query.Where("video_key = ?", f.VideoKey)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.OperateType != 0 {
		query.Where("operate_type = ?", f.OperateType)
	}
	if f.Resolution != 0 {
		query.Where("resolution = ?", f.Resolution)
	}
	if f.ResolutionZero {
		query.Where("resolution = 0")
	}
	if f.StatusNot != 0 {
		query.Where("status <> ?", f.StatusNot)
	}
	if f.GetVideoID != 0 {
		query.Where("video_id >= ?", f.GetVideoID)
	}
	if f.LetVideoID != 0 {
		query.Where("video_id <= ?", f.LetVideoID)
	}
	if f.WaitMigrate > 0 {
		query.Where("wait_migrate = ?", f.WaitMigrate)
	}
	if f.Limit != 0 {
		query.Limit(f.Limit)
	}
	return query
}

// // DataPageList .
// func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int) (total int64, list ModelList, err error) {
// 	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
// 		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

// 	query.Select("id").Count(&total)
// 	if err = query.Select("*").Offset((page - 1) * limit).Limit(int(limit)).Find(&list).Error; err != nil {
// 		log.Ctx(ctx).WithError(err).Error("DataPageList error")
// 		return 0, nil, err
// 	}
// 	return total, list, nil
// }

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)
	orderStr := "id desc" // 涉及定时脚本, 勿修改
	ret := ModelList{}
	if err := query.Order(orderStr).Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// // FindXidsByFilter .
// func (e *Entry) FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error) {
// 	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

// 	ret := []uint64{}
// 	if err := query.Distinct().Pluck(string(field), &ret).Error; err != nil {
// 		log.Ctx(ctx).WithError(err).Error("FindXidsByFilter error")
// 		return ret, err
// 	}
// 	return ret, nil
// }

// // FetchByFilter .
// func (e *Entry) FetchByFilter(ctx *gin.Context, f *Filter) (*Model, error) {
// 	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

// 	ret := &Model{}
// 	if err := query.Find(&ret).Limit(1).Error; err != nil {
// 		log.Ctx(ctx).WithError(err).Error("FetchByFilter error")
// 		return ret, err
// 	}
// 	return ret, nil
// }

// // FeatchByFilterSort .
// func (e *Entry) FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error) {
// 	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).
// 		Order(dbs.GetDefaultSort(f.Sort.Field, f.Sort.Method))

// 	ret := &Model{}
// 	if err := query.Find(&ret).Limit(1).Error; err != nil {
// 		log.Ctx(ctx).WithError(err).Error("FeatchByFilterSort error")
// 		return ret, err
// 	}
// 	return ret, nil
// }

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}
