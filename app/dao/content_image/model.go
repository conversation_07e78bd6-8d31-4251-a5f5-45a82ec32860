package image

import (
	"github.com/samber/lo"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/config"
)

type Image struct {
	dbs.ModelWithDel

	AspectRatio float32         `json:"aspect_ratio,omitempty"` // 宽高比
	Width       uint32          `json:"width,omitempty"`        // 宽度
	Height      uint32          `json:"height,omitempty"`       // 高度
	FilePath    dbs.OssFilePath `json:"file_path,omitempty"`    // 文件路径
	ISO_639_1   string          `json:"iso_639_1,omitempty"`    // 语言标识

	AspectType uint32 `json:"aspect_type,omitempty"` // 宽高比类型
}

func (m *Image) TableName() string {
	return "content_image"
}

func (m *Image) SetAspectType() uint32 {
	if m.AspectType > 0 {
		return m.AspectType
	}

	if m.AspectRatio == 0 {
		return 0
	}

	if m.AspectRatio > 1 {
		return 1
	} else if m.AspectRatio < 1 {
		return 2
	}
	return 0
}

func (m *Image) GetFilePath(host string) string {
	if m.FilePath == "" {
		return ""
	}
	if host == "" {
		host = config.BytePlusCfg.OssImageHost
	}
	return m.FilePath.String(host)
}

func aspectRatio(width, height uint32) float32 {
	if height == 0 {
		return 0
	}
	return float32(width) / float32(height)
}

func (m *Image) BeforeCreate(tx *gorm.DB) (err error) {
	m.AspectRatio = aspectRatio(m.Width, m.Height)
	m.AspectType = m.SetAspectType()
	return
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ISO_639_1 string `json:"iso_639_1,omitempty"` // 语言标识
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_iso_639_1() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ISO_639_1 == "" {
			return db
		}
		return db.Where("iso_639_1 = ?", f.ISO_639_1)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_iso_639_1(),
	}
}

type ImageList []*Image

func (il ImageList) GetIDs() []uint64 {
	var ids []uint64
	for _, i := range il {
		ids = append(ids, i.ID)
	}
	return lo.Uniq(ids)
}

func (il ImageList) GetMap() map[uint64]*Image {
	m := make(map[uint64]*Image)
	for _, i := range il {
		m[i.ID] = i
	}
	return m
}
