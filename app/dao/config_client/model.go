package config

import (
	"time"

	"vlab/app/common/dbs"
)

type ConfigKey string

const (
	KeyCurrentEnv           ConfigKey = "currentEnv"           // 当前环境变量
	KeyBackendDomain        ConfigKey = "backendDomain"        // 后端服务地址
	KeyWxAppletPayNotify    ConfigKey = "wxAppletPayNotify"    // 小程序支付回调通知地址
	KeyWxAppletRefundNotify ConfigKey = "wxAppletRefundNotify" // 小程序退款回调通知地址
	KeyImageCdnDomain       ConfigKey = "imageCdnDomain"       // 图片CDN域名
)

// Box ...
const (
	KeyYaoNum             ConfigKey = "yaoNum"
	KeyPostFee            ConfigKey = "postFee"
	KeyPostCnt            ConfigKey = "postCnt"
	KeyAnnouncement       ConfigKey = "announcement" // 公告
	KeyUnderConstruct     ConfigKey = "underConstruct"
	KeyUnderConstructTime ConfigKey = "underConstructTime"
)

// 快递费配置相关常量
const (
	// 快递费配置开关
	ConfigKeyFreightFeeEnable = "freight_fee_enable"
	// 快递费配置规则 (JSON格式)
	ConfigKeyFreightFeeRule = "freight_fee_rule"
)

// 积分抵扣配置相关常量
const (
	// 积分抵扣功能开关
	ConfigKeyPointsDiscountEnable = "points_discount_enable"
	// 积分抵扣比例配置 (JSON格式)
	ConfigKeyPointsDiscountRule = "points_discount_rule"
	// 积分抵扣最大比例 (订单金额的百分比)
	ConfigKeyPointsDiscountMaxPercent = "points_discount_max_percent"
)

type Model struct {
	ID          uint64    `gorm:"primarykey" json:"id"`
	Name        string    `json:"name,omitempty"`
	ConfigKey   string    `json:"config_key,omitempty"`
	ConfigValue string    `json:"config_value,omitempty"`
	ConfigType  uint32    `json:"config_type,omitempty"`
	ChannelID   uint64    `json:"channel_id,omitempty"` // 渠道ID，0表示全局配置
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Status      uint32    `json:"status,omitempty"` // 状态,,1-启用 2-禁用
}

func (m *Model) TableName() string {
	return "config_client"
}

func (m *Model) GetCreatedTime() string {
	return m.CreatedAt.Format(dbs.TimeDateFormatFull)
}

type Filter struct {
	ID     uint64
	IDS    []uint64
	Status uint32

	ConfigType uint32 // 配置类型
	ConfigKey  ConfigKey
	ConfigKeys []ConfigKey
	ChannelID  uint64 // 渠道ID筛选，0表示全局配置

	Sort dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetConfigMap() map[string]string {
	retMap := make(map[string]string, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ConfigKey]; !ok {
			retMap[val.ConfigKey] = val.ConfigValue
		}
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDEmptyMap() map[uint64]struct{} {
	retMap := make(map[uint64]struct{}, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = struct{}{}
		}
	}
	return retMap
}

func (ml ModelList) GetChannelList() []uint64 {
	channelSet := make(map[uint64]struct{})
	for _, val := range ml {
		if val.ChannelID > 0 {
			channelSet[val.ChannelID] = struct{}{}
		}
	}
	channels := make([]uint64, 0, len(channelSet))
	for channelID := range channelSet {
		channels = append(channels, channelID)
	}
	return channels
}
