package config

import (
	"sync"

	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	ConfigRepo
	ConfigRedisRepo
}

type ConfigRepo interface {
	FindByFilter(*gin.Context, *Filter) (ModelList, error)
	FetchByID(*gin.Context, uint64) (*Model, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
	CreateOrUpdate(*gin.Context, *Model) error
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Model) error
	DataPageList(*gin.Context, *Filter, int32, int32) (int64, ModelList, error)
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	FindConfigWithFallback(*gin.Context, string, uint64) (*Model, error)
}

type ConfigRedisRepo interface {
	RedisEnableConfigList(*gin.Context) (ModelList, error)
	RedisConfigList(*gin.Context) (ModelList, error)
	RedisConfigMap(*gin.Context) (map[string]string, error)
	RedisReloadConfigList(*gin.Context) (ModelList, error)
	RedisClearConfigList(*gin.Context) error
	// 页面配置相关缓存方法
	RedisPageConfigList(*gin.Context) (ModelList, error)
	RedisPageConfigMap(*gin.Context) (map[string]string, error)
	RedisReloadPageConfigList(*gin.Context) (ModelList, error)
	RedisClearPageConfigList(*gin.Context) error
	RedisGetPageConfig(*gin.Context, string) (string, error)
	RedisSetPageConfig(*gin.Context, string, string) error
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
