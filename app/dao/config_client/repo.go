package config

import (
	"fmt"

	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	PluckXid     dbs.PluckField = "xid"
	SortFieldXid dbs.SortField  = "xid"
)

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.ID != 0 {
		query.Where("id = ?", f.ID)
	}
	if len(f.IDS) > 0 {
		query.Where("id in (?)", f.IDS)
	}
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if t := f.ConfigKey; t != "" {
		query.Where("config_key = ?", t)
	}
	if t := f.Config<PERSON>ey<PERSON>; len(t) > 0 {
		query.Where("config_key in (?)", t)
	}
	if t := f.ConfigType; t != 0 {
		query.Where("config_type = ?", t)
	}
	// 支持按渠道ID筛选，如果不指定则查询所有渠道
	if f.ChannelID != 0 {
		query.Where("channel_id = ?", f.ChannelID)
	}
	return query
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}

// FetchByID .
func (e *Entry) FetchByID(ctx *gin.Context, id uint64) (*Model, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), &Filter{ID: id})

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FetchByID error")
		return ret, err
	}
	return ret, nil
}

// CountByFilter .
func (e *Entry) CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	if err := query.Count(&num).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CountByFilter error")
		return 0, err
	}
	return
}

// CreateOrUpdate 创建或更新配置项
func (e *Entry) CreateOrUpdate(ctx *gin.Context, model *Model) error {
	return e.CreateOrUpdateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), model)
}

// CreateOrUpdateWithTx 使用事务创建或更新配置项
func (e *Entry) CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, model *Model) error {
	if err := tx.Model(&Model{}).Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{
				{Name: "config_key"}, {Name: "channel_id"}},
			DoUpdates: clause.AssignmentColumns([]string{
				//"name",
				"config_type",
				"config_value",
				//"desc",
				"status",
			}),
		}).Create(&model).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("CreateOrUpdate error")
		return err
	}
	return nil
}

// DataPageList 分页查询配置列表
func (e *Entry) DataPageList(ctx *gin.Context, f *Filter, page, limit int32) (total int64, list ModelList, err error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f).Model(&Model{})

	// 获取总数
	if err = query.Count(&total).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList count error")
		return 0, nil, err
	}

	if total == 0 {
		return 0, ModelList{}, nil
	}

	// 分页查询
	offset := (page - 1) * limit
	if err = query.Order("created_at DESC").Offset(int(offset)).Limit(int(limit)).Find(&list).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("DataPageList find error")
		return 0, nil, err
	}

	return total, list, nil
}

// UpdateMapByID 根据ID更新字段
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, updateMap map[string]interface{}) error {
	if err := e.MysqlEngine.UseWithGinCtx(ctx, true).Model(&Model{}).Where("id = ?", id).Updates(updateMap).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByID error")
		return err
	}
	return nil
}

// FindConfigWithFallback 查找配置（支持渠道配置回退到全局配置）
func (e *Entry) FindConfigWithFallback(ctx *gin.Context, configKey string, channelID uint64) (*Model, error) {
	// 如果指定了 channelID，先查询渠道特定配置
	if channelID > 0 {
		channelFilter := &Filter{
			ConfigType: 2, // 页面配置类型
			ConfigKey:  ConfigKey(configKey),
			ChannelID:  channelID,
			Status:     1, // 启用状态
		}

		channelConfigs, err := e.FindByFilter(ctx, channelFilter)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("FindConfigWithFallback channel config error")
			return nil, err
		}

		if len(channelConfigs) > 0 && channelConfigs[0] != nil {
			return channelConfigs[0], nil
		}
	}

	// 查询全局配置（channel_id = 0）
	globalFilter := &Filter{
		ConfigType: 2, // 页面配置类型
		ConfigKey:  ConfigKey(configKey),
		ChannelID:  0, // 全局配置
		Status:     1, // 启用状态
	}

	globalConfigs, err := e.FindByFilter(ctx, globalFilter)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("FindConfigWithFallback global config error")
		return nil, err
	}

	if len(globalConfigs) > 0 && globalConfigs[0] != nil {
		return globalConfigs[0], nil
	}

	return nil, nil // 未找到配置
}
