package dao

import (
	"context"
	"encoding/base64"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	"vlab/app/dto"
	"vlab/config"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"

	"github.com/sirupsen/logrus"
	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
)

// SearchDao 搜索DAO
type SearchDao struct {
	Service          *vikingdb.VikingDBService
	CollectionClient *vikingdb.CollectionClient
	EmbeddingClient  *EmbeddingClient
	IndexClient      *vikingdb.IndexClient
	config           *config.VikingDB
}

// EmbeddingClient 嵌入客户端接口
type EmbeddingClient struct {
	service *vikingdb.VikingDBService
	config  *config.VikingDB
}

// NewSearchDao 创建搜索DAO
func NewSearchDao() *SearchDao {
	cfg := config.VikingDBCfg

	// 检查配置是否有效
	if cfg == nil || cfg.Host == "" || cfg.AccessKeyID == "" || cfg.AccessKeySecret == "" {
		log.WithContext(context.Background()).Warn("VikingDB configuration is incomplete or missing, returning nil SearchDao")
		return nil
	}

	// 使用 defer 和 recover 来捕获 panic
	var searchDao *SearchDao
	var initError error

	func() {
		defer func() {
			if r := recover(); r != nil {
				initError = fmt.Errorf("panic during VikingDB initialization: %v", r)
				log.WithContext(context.Background()).WithError(initError).Error("Failed to initialize SearchDao")
			}
		}()

		// 尝试创建VikingDB客户端，设置超时
		ctx, cancel := context.WithTimeout(context.Background(), 8*time.Second)
		defer cancel()

		// 创建VikingDB Collection客户端
		collectionClient := vikingdb.NewCollectionClient(
			cfg.Collection,
			cfg.Host,
			cfg.Region,
			cfg.AccessKeyID,
			cfg.AccessKeySecret,
			cfg.Scheme,
		)

		// 创建嵌入客户端
		embeddingClient := &EmbeddingClient{
			service: vikingdb.NewVikingDBService(
				cfg.Host,
				cfg.Region,
				cfg.AccessKeyID,
				cfg.AccessKeySecret,
				cfg.Scheme,
			),
			config: cfg,
		}

		embeddingClient.service.SetConnectionTimeout(8) // 设置连接超时为8秒

		// 获取索引客户端
		var indexClient *vikingdb.IndexClient
		if cfg.Index != "" {
			// 创建索引客户端
			indexClient = vikingdb.NewIndexClient(cfg.Collection, cfg.Index, cfg.Host, cfg.Region, cfg.AccessKeyID, cfg.AccessKeySecret, cfg.Scheme)
		}

		searchDao = &SearchDao{
			CollectionClient: collectionClient,
			EmbeddingClient:  embeddingClient,
			IndexClient:      indexClient,
			config:           cfg,
			Service:          embeddingClient.service,
		}

		searchDao.IndexClient.VikingDBService.SetConnectionTimeout(8) // 设置索引客户端连接超时为8秒

		// 执行健康检查（如果context未超时）
		select {
		case <-ctx.Done():
			initError = fmt.Errorf("VikingDB initialization timeout")
			searchDao = nil
		default:
			// 继续正常流程
		}
	}()

	// 如果初始化失败，返回nil
	if initError != nil || searchDao == nil {
		log.WithContext(context.Background()).WithFields(logrus.Fields{
			"host":   cfg.Host,
			"region": cfg.Region,
			"error":  initError,
		}).Error("VikingDB initialization failed, vector search will be disabled")
		return nil
	}

	return searchDao
}

// IsAvailable 检查VikingDB是否可用
func (dao *SearchDao) IsAvailable() bool {
	return dao != nil && dao.CollectionClient != nil
}

// HealthCheck 执行健康检查
func (dao *SearchDao) HealthCheck(ctx context.Context) error {
	if dao == nil {
		return fmt.Errorf("SearchDao is not initialized")
	}

	// 设置超时上下文
	checkCtx, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	// 尝试一个简单的操作来验证连接
	// 注意：这里可能需要根据VikingDB SDK提供的方法调整
	select {
	case <-checkCtx.Done():
		return fmt.Errorf("health check timeout")
	default:
		// 如果VikingDB SDK没有提供健康检查方法，只能检查客户端是否存在
		if dao.CollectionClient == nil {
			return fmt.Errorf("collection client is nil")
		}
		return nil
	}
}

// Search 执行搜索
func (dao *SearchDao) Search(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, error) {
	// 检查dao是否已初始化
	if dao == nil {
		log.Ctx(ctx).Warn("SearchDao is nil, vector search is disabled")
		return nil, fmt.Errorf("vector search service is temporarily unavailable")
	}

	startTime := time.Now()

	// 获取请求ID
	requestID := ctxUtil.GetRequestID(ctx)

	log.Ctx(ctx).WithFields(logrus.Fields{
		"request_id":  requestID,
		"search_mode": req.SearchMode,
		"query":       req.Query,
		"page":        req.Page,
		"page_size":   req.PageSize,
	}).Info("开始执行向量搜索")

	// 验证和设置默认值
	dao.setSearchDefaults(req)

	// 检查索引客户端
	if dao.IndexClient == nil {
		log.WithField(ctx, "request_id", requestID).Error("索引客户端未初始化")
		return nil, fmt.Errorf("索引客户端未初始化")
	}

	var results []*vikingdb.Data
	var total int64
	var err error
	var vectorAPIUsed bool
	var vectorRequestID string

	// 根据搜索模式执行不同的搜索
	log.Ctx(ctx).WithField("search_mode", req.SearchMode).Info("开始执行具体搜索操作")

	switch req.SearchMode {
	case "vector":
		results, total, vectorAPIUsed, vectorRequestID, err = dao.vectorSearch(ctx, req)
	case "filter_only":
		results, total, vectorAPIUsed, vectorRequestID, err = dao.filterSearch(ctx, req)
	case "hybrid":
		results, total, vectorAPIUsed, vectorRequestID, err = dao.hybridSearch(ctx, req)
	default:
		log.Ctx(ctx).WithField("invalid_mode", req.SearchMode).Error("不支持的搜索模式")
		return nil, fmt.Errorf("不支持的搜索模式: %s", req.SearchMode)
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("搜索执行失败")
		return nil, err
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"results_count": len(results),
		"total":         total,
	}).Info("搜索执行完成")

	// 转换结果
	items := dao.convertToSearchResults(ctx, results, req.SearchMode)

	// 计算实际的总页数
	// 注意：对于向量搜索，total 可能是预估值或实际返回的数量
	actualTotal := total
	if len(items) < int(total) {
		actualTotal = int64(len(items))
	}
	totalPages := int(math.Ceil(float64(actualTotal) / float64(req.PageSize)))

	// 记录搜索完成日志
	processingTime := time.Since(startTime)
	log.Ctx(ctx).WithFields(logrus.Fields{
		"request_id":         requestID,
		"items_count":        len(items),
		"items":              items,
		"actual_total":       actualTotal,
		"total_pages":        totalPages,
		"processing_time_ms": processingTime.Milliseconds(),
	}).Info("向量搜索完成")

	return &dto.SearchResponse{
		Items:      items,
		Total:      actualTotal,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		SearchInfo: dto.SearchInfo{
			SearchMode:      req.SearchMode,
			ProcessingTime:  processingTime,
			Query:           req.Query,
			VectorUsed:      req.SearchMode == "vector" || req.SearchMode == "hybrid",
			FilterUsed:      req.SearchMode == "filter_only" || req.SearchMode == "hybrid",
			VectorAPIUsed:   vectorAPIUsed,
			VectorRequestID: vectorRequestID,
		},
	}, nil
}

// vectorSearch 纯向量搜索
func (dao *SearchDao) vectorSearch(ctx *gin.Context, req *dto.SearchRequest) ([]*vikingdb.Data, int64, bool, string, error) {
	requestID := ctxUtil.GetRequestID(ctx)

	if req.Query == "" {
		log.Ctx(ctx).WithField("request_id", requestID).Error("向量搜索缺少查询文本")
		return nil, 0, false, "", fmt.Errorf("向量搜索需要查询文本")
	}

	// 计算需要获取的结果数量
	// 为了支持分页，需要获取到当前页的所有数据
	// 注意：VikingDB 可能不支持 offset，所以我们需要获取前 N 页的所有数据
	neededResults := req.Page * req.PageSize
	// 限制最大获取数量，避免性能问题
	maxResults := 1000
	if neededResults > maxResults {
		neededResults = maxResults
	}
	// 确保至少获取 TopK 个结果（向后兼容）
	if neededResults < req.VectorParams.TopK {
		neededResults = req.VectorParams.TopK
	}

	// 构建搜索选项
	searchOptions := vikingdb.NewSearchOptions().
		SetLimit(int64(neededResults)).
		SetOutputFields(dao.getOutputFields()).SetRetry(true)

	// 使用文本搜索
	textObj := vikingdb.TextObject{
		Text: req.Query,
	}

	log.Ctx(ctx).WithField("needed_results", neededResults).Info("开始调用VikingDB向量搜索")
	startTime := time.Now()

	results, err := dao.IndexClient.SearchByText(textObj, searchOptions)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("VikingDB向量搜索失败")
		return nil, 0, true, "", fmt.Errorf("向量搜索失败: %v", err)
	}

	// 生成一个模拟的请求ID（因为SDK不返回实际的请求ID）
	// 格式：vdb_<timestamp>_<request_id>
	vectorRequestID := fmt.Sprintf("vdb_%d_%s", time.Now().UnixNano(), requestID)

	log.Ctx(ctx).WithFields(logrus.Fields{
		"vikingdb_results":  len(results),
		"vikingdb_time_ms":  time.Since(startTime).Milliseconds(),
		"vector_request_id": vectorRequestID,
	}).Info("VikingDB向量搜索完成")

	// 计算实际的偏移量和返回结果
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start >= len(results) {
		// 请求的页面超出结果范围
		return []*vikingdb.Data{}, int64(len(results)), false, vectorRequestID, nil
	}
	if end > len(results) {
		end = len(results)
	}

	// 返回当前页的结果
	pageResults := results[start:end]
	// 总数仍然返回所有匹配的结果数（用于计算总页数）
	total := int64(len(results))

	return pageResults, total, true, vectorRequestID, nil
}

// filterSearch 纯筛选搜索
func (dao *SearchDao) filterSearch(ctx *gin.Context, req *dto.SearchRequest) ([]*vikingdb.Data, int64, bool, string, error) {
	requestID := ctxUtil.GetRequestID(ctx)

	// 构建筛选条件
	filter := dao.buildFilterConditions(req)

	log.Ctx(ctx).WithField("filter_conditions", filter).Info("构建筛选条件")

	if len(filter) == 0 {
		log.Ctx(ctx).WithField("request_id", requestID).Error("筛选搜索缺少筛选条件")
		return nil, 0, false, "", fmt.Errorf("筛选搜索需要至少一个筛选条件")
	}

	// 计算需要获取的结果数量（支持分页）
	neededResults := req.Page * req.PageSize
	// 限制最大获取数量
	maxResults := 1000
	if neededResults > maxResults {
		neededResults = maxResults
	}

	// 构建搜索选项
	searchOptions := vikingdb.NewSearchOptions().
		SetFilter(filter).
		SetLimit(int64(neededResults)).
		SetOutputFields(dao.getOutputFields()).SetRetry(true)

	// 方案一：如果有默认查询文本，使用文本搜索
	// 这样可以避免使用零向量，同时仍然可以应用筛选条件
	defaultQuery := "*" // 使用通配符或空字符串
	textObj := vikingdb.TextObject{
		Text: defaultQuery,
	}

	log.Ctx(ctx).WithField("needed_results", neededResults).Info("开始调用VikingDB筛选搜索")
	startTime := time.Now()

	results, err := dao.IndexClient.SearchByText(textObj, searchOptions)
	vectorAPIUsed := false // 初始化为false
	vectorRequestID := fmt.Sprintf("vdb_%d_%s", time.Now().UnixNano(), requestID)
	if err == nil {
		vectorAPIUsed = true // 成功调用则标记为true
	}

	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("文本搜索失败，尝试回退到零向量方案")
		// 如果文本搜索失败，回退到零向量方案
		// 方案二：使用零向量进行筛选搜索
		vectorDim := dao.config.VectorDimension
		if vectorDim == 0 {
			vectorDim = 1024 // 默认维度
		}
		zeroVector := make([]float64, vectorDim)
		// 使用全零向量
		for i := range zeroVector {
			zeroVector[i] = 0.0
		}

		results, err = dao.IndexClient.SearchByVector(zeroVector, searchOptions)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("零向量搜索也失败")
			return nil, 0, false, vectorRequestID, fmt.Errorf("筛选搜索失败: %v", err)
		}
		vectorAPIUsed = true // 零向量搜索成功也算使用了向量API
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"vikingdb_results":  len(results),
		"vikingdb_time_ms":  time.Since(startTime).Milliseconds(),
		"vector_request_id": vectorRequestID,
		"filter":            filter,
	}).Info("VikingDB筛选搜索完成")

	// 计算实际的偏移量和返回结果
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start >= len(results) {
		// 请求的页面超出结果范围
		return []*vikingdb.Data{}, int64(len(results)), vectorAPIUsed, vectorRequestID, nil
	}
	if end > len(results) {
		end = len(results)
	}

	// 返回当前页的结果
	pageResults := results[start:end]
	total := int64(len(results))

	return pageResults, total, vectorAPIUsed, vectorRequestID, nil
}

// hybridSearch 混合搜索（向量 + 筛选）
func (dao *SearchDao) hybridSearch(ctx *gin.Context, req *dto.SearchRequest) ([]*vikingdb.Data, int64, bool, string, error) {
	requestID := ctxUtil.GetRequestID(ctx)

	if req.Query == "" {
		log.Ctx(ctx).WithField("request_id", requestID).Error("混合搜索缺少查询文本")
		return nil, 0, false, "", fmt.Errorf("混合搜索需要查询文本")
	}

	// 构建筛选条件
	filter := dao.buildFilterConditions(req)
	log.Ctx(ctx).WithField("filter_conditions", filter).Info("构建混合搜索筛选条件")

	// 计算需要获取的结果数量（支持分页）
	neededResults := req.Page * req.PageSize
	// 限制最大获取数量
	maxResults := 1000
	if neededResults > maxResults {
		neededResults = maxResults
	}
	// 确保至少获取 TopK 个结果（向后兼容）
	if neededResults < req.VectorParams.TopK {
		neededResults = req.VectorParams.TopK
	}

	// 构建搜索选项
	searchOptions := vikingdb.NewSearchOptions().
		SetLimit(int64(neededResults)).
		SetOutputFields(dao.getOutputFields()).SetRetry(true)

	// 如果有筛选条件，添加到搜索选项
	if len(filter) > 0 {
		searchOptions.SetFilter(filter)
	}

	// 设置密集向量权重
	if req.VectorParams.DenseWeight > 0 {
		searchOptions.SetDenseWeight(req.VectorParams.DenseWeight)
	}

	// 使用文本搜索
	textObj := vikingdb.TextObject{
		Text: req.Query,
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"needed_results": neededResults,
		"has_filter":     len(filter) > 0,
		"filter":         filter,
		"dense_weight":   req.VectorParams.DenseWeight,
		"textObj":        textObj.Text,
		"options":        searchOptions,
	}).Info("开始调用VikingDB混合搜索")
	startTime := time.Now()

	results, err := dao.IndexClient.SearchByText(textObj, searchOptions)
	vectorAPIUsed := err == nil // 根据是否成功设置
	vectorRequestID := fmt.Sprintf("vdb_%d_%s", time.Now().UnixNano(), requestID)

	if err != nil {
		log.Ctx(ctx).WithError(err).Error("VikingDB混合搜索失败")
		return nil, 0, false, vectorRequestID, fmt.Errorf("混合搜索失败: %v", err)
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"vikingdb_results":  results,
		"vikingdb_time_ms":  time.Since(startTime).Milliseconds(),
		"vector_request_id": vectorRequestID,
	}).Info("VikingDB混合搜索完成")

	// 计算实际的偏移量和返回结果
	start := (req.Page - 1) * req.PageSize
	end := start + req.PageSize
	if start >= len(results) {
		// 请求的页面超出结果范围
		return []*vikingdb.Data{}, int64(len(results)), vectorAPIUsed, vectorRequestID, nil
	}
	if end > len(results) {
		end = len(results)
	}

	// 返回当前页的结果
	pageResults := results[start:end]
	total := int64(len(results))

	return pageResults, total, vectorAPIUsed, vectorRequestID, nil
}

// getQueryVector 获取查询文本的向量
func (dao *SearchDao) getQueryVector(_ context.Context, query string) (map[string]interface{}, error) {
	// 构建向量化请求
	rawDataList := []vikingdb.RawData{
		{
			DataType: "text",
			Text:     query,
		},
	}

	// 设置模型参数
	params := map[string]interface{}{
		"return_token_usage": true,
	}

	if dao.config.UseSparse {
		params["return_sparse"] = true
	}

	embModel := vikingdb.EmbModel{
		ModelName: dao.config.ModelName,
		Params:    params,
	}

	// 调用向量化API
	results, err := dao.EmbeddingClient.service.EmbeddingV2(embModel, rawDataList)
	if err != nil {
		return nil, fmt.Errorf("向量化失败: %v", err)
	}

	// 解析结果
	dataInterface, ok := results["data"]
	if !ok {
		return nil, fmt.Errorf("向量化响应中缺少data字段")
	}

	dataList, ok := dataInterface.([]interface{})
	if !ok || len(dataList) == 0 {
		return nil, fmt.Errorf("向量化响应格式错误")
	}

	itemMap, ok := dataList[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("向量化响应项格式错误")
	}

	return itemMap, nil
}

// buildVectorSearchRequest 构建向量搜索请求
// TODO: 需要实现 HTTP API 调用，当前 SDK 不支持 SearchRequest 类型
/*
func (dao *SearchDao) buildVectorSearchRequest(req *dto.SearchRequest, queryVector map[string]interface{}) interface{} {
	// TODO: 待实现 HTTP API 调用
	return nil
}
*/

// buildFilterConditions 构建筛选条件
func (dao *SearchDao) buildFilterConditions(req *dto.SearchRequest) map[string]interface{} {
	// VikingDB 需要特定的筛选器格式，每个条件需要包含 "op" 操作符
	var conditions []map[string]interface{}

	// 分类筛选
	if len(req.Filters.Categories) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "category",
			"conds": req.Filters.Categories,
		})
	}

	//// 类型筛选（字符串）
	//if len(req.Filters.Genres) > 0 {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "must",
	//		"field": "genre",
	//		"conds": req.Filters.Genres,
	//	})
	//}

	// 类型ID筛选（精确匹配）
	if len(req.Filters.GenreIDs) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "genre_ids",
			"conds": req.Filters.GenreIDs,
		})
	}

	// 年份范围筛选
	//if req.Filters.YearFrom > 0 || req.Filters.YearTo > 0 {
	//	// VikingDB 使用 range 操作符进行范围筛选
	//	yearConditions := make(map[string]interface{})
	//	if req.Filters.YearFrom > 0 {
	//		yearConditions["gte"] = req.Filters.YearFrom
	//	}
	//	if req.Filters.YearTo > 0 {
	//		yearConditions["lte"] = req.Filters.YearTo
	//	}
	//	if len(yearConditions) > 0 {
	//		conditions = append(conditions, map[string]interface{}{
	//			"op":    "range",
	//			"field": "year",
	//			"conds": yearConditions,
	//		})
	//	}
	//}

	// 年份ID筛选（精确匹配）
	if len(req.Filters.YearIDs) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "year_ids",
			"conds": req.Filters.YearIDs,
		})
	}

	// 评分筛选（旧版 rating 0-10）
	if req.Filters.RatingFrom > 0 || req.Filters.RatingTo > 0 {
		ratingConditions := make(map[string]interface{})
		if req.Filters.RatingFrom > 0 {
			ratingConditions["gte"] = req.Filters.RatingFrom
		}
		if req.Filters.RatingTo > 0 {
			ratingConditions["lte"] = req.Filters.RatingTo
		}
		if len(ratingConditions) > 0 {
			conditions = append(conditions, map[string]interface{}{
				"op":    "range",
				"field": "rating",
				"conds": ratingConditions,
			})
		}
	}

	// 评分筛选（新版 score 0-100）
	if req.Filters.ScoreFrom > 0 || req.Filters.ScoreTo > 0 {
		scoreConditions := make(map[string]interface{})
		if req.Filters.ScoreFrom > 0 {
			scoreConditions["gte"] = req.Filters.ScoreFrom
		}
		if req.Filters.ScoreTo > 0 {
			scoreConditions["lte"] = req.Filters.ScoreTo
		}
		if len(scoreConditions) > 0 {
			conditions = append(conditions, map[string]interface{}{
				"op":    "range",
				"field": "score",
				"conds": scoreConditions,
			})
		}
	}

	// 时长筛选
	//if req.Filters.DurationFrom > 0 || req.Filters.DurationTo > 0 {
	//	durationConditions := make(map[string]interface{})
	//	if req.Filters.DurationFrom > 0 {
	//		durationConditions["gte"] = req.Filters.DurationFrom
	//	}
	//	if req.Filters.DurationTo > 0 {
	//		durationConditions["lte"] = req.Filters.DurationTo
	//	}
	//	if len(durationConditions) > 0 {
	//		conditions = append(conditions, map[string]interface{}{
	//			"op":    "range",
	//			"field": "duration",
	//			"conds": durationConditions,
	//		})
	//	}
	//}

	// 地区筛选（字符串）
	//if len(req.Filters.Regions) > 0 {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "must",
	//		"field": "region",
	//		"conds": req.Filters.Regions,
	//	})
	//}

	// 地区ID筛选（精确匹配）
	if len(req.Filters.RegionIDs) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "region_ids",
			"conds": req.Filters.RegionIDs,
		})
	}

	// 语言筛选
	//if len(req.Filters.Languages) > 0 {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "must",
	//		"field": "language",
	//		"conds": req.Filters.Languages,
	//	})
	//}

	// 标签筛选
	//if len(req.Filters.Tags) > 0 {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "must",
	//		"field": "tags",
	//		"conds": req.Filters.Tags,
	//	})
	//}

	// 状态筛选
	if len(req.Filters.Status) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "status",
			"conds": req.Filters.Status,
		})
	}

	// 内容类型筛选（int类型：1=Movie, 2=TV, 3=Comic）
	if len(req.Filters.ContentTypes) > 0 {
		conditions = append(conditions, map[string]interface{}{
			"op":    "must",
			"field": "content_type",
			"conds": req.Filters.ContentTypes,
		})
	}

	// 导演筛选
	//if req.Filters.Director != "" {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "match",
	//		"field": "director",
	//		"conds": []string{req.Filters.Director},
	//	})
	//}

	// 演员筛选
	//if len(req.Filters.Actors) > 0 {
	//	conditions = append(conditions, map[string]interface{}{
	//		"op":    "must",
	//		"field": "actors",
	//		"conds": req.Filters.Actors,
	//	})
	//}

	// 如果没有条件，返回空map
	if len(conditions) == 0 {
		return map[string]interface{}{}
	}

	// 如果只有一个条件，直接返回该条件
	if len(conditions) == 1 {
		return conditions[0]
	}

	// 如果有多个条件，需要使用 "and" 操作符组合
	return map[string]interface{}{
		"op":    "and",
		"conds": conditions,
	}
}

// getOutputFields 获取输出字段列表
func (dao *SearchDao) getOutputFields() []string {
	return []string{
		"id",
		//"show_id", "name", "name_i18n", "overview", "overview_i18n",
		//"category", "category_i18n", "genre", "genre_i18n", "genre_ids",
		//"year", "year_i18n", "year_ids",
		//"region", "region_i18n", "region_ids",
		//"rating", "score", "content_type", "duration",
		//"director", "actors", "tags", "language",
		//"season", "episodes", "status", "created_at",
	}
}

// convertToUint64 安全转换接口类型为 uint64
func convertToUint64(v interface{}) (uint64, bool) {
	switch val := v.(type) {
	case uint64:
		return val, true
	case int64:
		if val >= 0 {
			return uint64(val), true
		}
	case int:
		if val >= 0 {
			return uint64(val), true
		}
	case uint:
		return uint64(val), true
	case int32:
		if val >= 0 {
			return uint64(val), true
		}
	case uint32:
		return uint64(val), true
	case float64:
		if val >= 0 && val <= float64(^uint64(0)) {
			return uint64(val), true
		}
	case string:
		if idVal, err := strconv.ParseUint(val, 10, 64); err == nil {
			return idVal, true
		}
	case json.Number:
		// json.Number 是一个字符串类型，需要解析
		if idVal, err := val.Int64(); err == nil && idVal >= 0 {
			return uint64(idVal), true
		}
		// 如果 Int64() 失败，尝试作为字符串解析
		if idVal, err := strconv.ParseUint(string(val), 10, 64); err == nil {
			return idVal, true
		}
	}
	return 0, false
}

// convertToSearchResults 转换搜索结果
func (dao *SearchDao) convertToSearchResults(ctx *gin.Context, data []*vikingdb.Data, searchMode string) []dto.SearchResultItem {
	var items []dto.SearchResultItem

	for _, item := range data {
		searchItem := dto.SearchResultItem{
			MatchType: searchMode,
		}

		// 转换基础字段
		id, ok := convertToUint64(item.Id)
		if !ok {
			log.Ctx(ctx).WithField("id", item.Id).WithField("type", fmt.Sprintf("%T", item.Id)).Debug("failed to convert ID")
			continue
		}
		searchItem.ID = id

		if showID, ok := item.Fields["show_id"]; ok {
			if id, ok := convertToUint64(showID); ok {
				searchItem.ShowID = id
			}
		}

		if name, ok := item.Fields["name"].(string); ok {
			searchItem.Name = name
		}

		if overview, ok := item.Fields["overview"].(string); ok {
			searchItem.Overview = overview
		}

		// 转换多语言字段
		if nameI18n, ok := item.Fields["name_i18n"].([]interface{}); ok {
			searchItem.NameI18n = dao.convertInterfaceSliceToStringSlice(nameI18n)
		}

		if overviewI18n, ok := item.Fields["overview_i18n"].([]interface{}); ok {
			searchItem.OverviewI18n = dao.convertInterfaceSliceToStringSlice(overviewI18n)
		}

		// 转换其他字段
		if category, ok := item.Fields["category"].(string); ok {
			searchItem.Category = category
		}

		if categoryI18n, ok := item.Fields["category_i18n"].([]interface{}); ok {
			searchItem.CategoryI18n = dao.convertInterfaceSliceToStringSlice(categoryI18n)
		}

		if genre, ok := item.Fields["genre"].([]interface{}); ok {
			searchItem.Genre = dao.convertInterfaceSliceToStringSlice(genre)
		}

		if year, ok := item.Fields["year"].([]interface{}); ok {
			searchItem.Year = dao.convertInterfaceSliceToStringSlice(year)
		}

		if region, ok := item.Fields["region"].([]interface{}); ok {
			searchItem.Region = dao.convertInterfaceSliceToStringSlice(region)
		}

		if rating, ok := item.Fields["rating"].(float64); ok {
			searchItem.Rating = rating
		}

		if duration, ok := item.Fields["duration"].(int); ok {
			searchItem.Duration = duration
		}

		if director, ok := item.Fields["director"].(string); ok {
			searchItem.Director = director
		}

		if actors, ok := item.Fields["actors"].([]interface{}); ok {
			searchItem.Actors = dao.convertInterfaceSliceToStringSlice(actors)
		}

		if tags, ok := item.Fields["tags"].([]interface{}); ok {
			searchItem.Tags = dao.convertInterfaceSliceToStringSlice(tags)
		}

		if language, ok := item.Fields["language"].(string); ok {
			searchItem.Language = language
		}

		if season, ok := item.Fields["season"].(int); ok {
			searchItem.Season = season
		}

		if episodes, ok := item.Fields["episodes"].(int); ok {
			searchItem.Episodes = episodes
		}

		if status, ok := item.Fields["status"].(string); ok {
			searchItem.Status = status
		}

		// 设置相关度分数
		if item.Score > 0 {
			searchItem.Score = item.Score
		} else if score, ok := item.Fields["_score"]; ok {
			switch v := score.(type) {
			case float64:
				searchItem.Score = v
			case float32:
				searchItem.Score = float64(v)
			}
		} else {
			searchItem.Score = 0.0
		}

		log.Ctx(ctx).WithField("search_item", searchItem).Debug("search item")

		items = append(items, searchItem)
	}

	return items
}

// setSearchDefaults 设置搜索默认值
func (dao *SearchDao) setSearchDefaults(req *dto.SearchRequest) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}
	if req.SearchMode == "" {
		req.SearchMode = "hybrid"
	}
	if req.VectorParams.TopK <= 0 {
		req.VectorParams.TopK = 50
	}
	if req.VectorParams.DenseWeight <= 0 {
		req.VectorParams.DenseWeight = 0.5
	}
	if req.VectorParams.SparseWeight <= 0 {
		req.VectorParams.SparseWeight = 0.5
	}
	if req.SortBy == "" {
		req.SortBy = "relevance"
	}
	if req.SortOrder == "" {
		req.SortOrder = "desc"
	}
}

// 辅助方法

// convertToFloat32Array 转换为float32数组
func (dao *SearchDao) convertToFloat32Array(data interface{}) ([]float32, error) {
	if interfaceArray, ok := data.([]interface{}); ok {
		result := make([]float32, len(interfaceArray))
		for i, v := range interfaceArray {
			switch val := v.(type) {
			case float32:
				result[i] = val
			case float64:
				result[i] = float32(val)
			default:
				return nil, fmt.Errorf("无法转换索引 %d 的值: %T", i, v)
			}
		}
		return result, nil
	}
	return nil, fmt.Errorf("无法将 %T 类型转换为 float32 数组", data)
}

// convertToBase64Vector 转换为Base64向量
func (dao *SearchDao) convertToBase64Vector(embedding []float32) string {
	packedData := make([]byte, len(embedding)*4)
	for j, v := range embedding {
		binary.LittleEndian.PutUint32(packedData[j*4:], math.Float32bits(v))
	}
	return base64.StdEncoding.EncodeToString(packedData)
}

// convertToSparseVectorMap 转换为稀疏向量映射
func (dao *SearchDao) convertToSparseVectorMap(data interface{}) (map[string]float64, error) {
	if mapData, ok := data.(map[string]interface{}); ok {
		result := make(map[string]float64)
		for k, v := range mapData {
			if val, ok := v.(float64); ok {
				result[k] = val
			} else {
				return nil, fmt.Errorf("无法转换值 %v 为 float64", v)
			}
		}
		return result, nil
	}
	return nil, fmt.Errorf("无法将 %T 类型转换为稀疏向量映射", data)
}

// convertInterfaceSliceToStringSlice 转换interface{}切片为string切片
func (dao *SearchDao) convertInterfaceSliceToStringSlice(slice []interface{}) []string {
	result := make([]string, 0, len(slice))
	for _, item := range slice {
		if str, ok := item.(string); ok {
			result = append(result, str)
		}
	}
	return result
}
