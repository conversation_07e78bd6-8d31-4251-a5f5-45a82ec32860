package product

import (
	"encoding/json"
	"vlab/app/common/dbs"
	"vlab/app/dao/common"
	"vlab/pkg/ecode"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisRegionProductInfo .
func (e *Entry) RedisRegionProductInfo(ctx *gin.Context, region common.Region, product Product) (*Model, error) {
	list, err := e.RedisRegionProductList(ctx, region)
	if err != nil {
		return nil, err
	}
	ret := &Model{}
	for _, val := range list {
		if val.ProductID == string(product) {
			return val, nil
		}
	}
	return ret, ecode.UserVipRegionProductNotExistErr
}

// RedisRegionProductList .
func (e *Entry) RedisRegionProductList(ctx *gin.Context, region common.Region) (ModelList, error) {
	dataMap, err := e.RedisProductMap(ctx)
	if err != nil {
		return nil, err
	}

	dataList, ok := dataMap[string(region)]
	if !ok {
		return nil, ecode.UserVipProductRegionNotExistErr
	}
	return dataList, nil
}

// RedisProductRegionMap .
func (e *Entry) RedisProductMap(ctx *gin.Context) (map[string]ModelList, error) {
	productList, err := e.RedisProductList(ctx)
	if err != nil {
		return nil, err
	}
	return productList.GetProductRegionMap(), nil
}

// // RedisEnableProductList .
// func (e *Entry) RedisEnableProductList(ctx *gin.Context) (ModelList, error) {
// 	list, err := e.RedisProductList(ctx)
// 	if err != nil {
// 		return nil, err
// 	}
// 	ret := ModelList{}
// 	for _, val := range list {
// 		if val.Status == uint32(dbs.StatusEnable) {
// 			ret = append(ret, val)
// 		}
// 	}
// 	return ret, nil
// }

// RedisIDInfo .
func (e *Entry) RedisIDInfo(ctx *gin.Context, pid uint64) (*Model, error) {
	dataMap, err := e.RedisIDMap(ctx)
	if err != nil {
		return nil, err
	}

	if val, ok := dataMap[pid]; ok {
		if val.ID != 0 {
			return val, nil
		}
	}

	return nil, ecode.UserVipProductNotExistErr
}

// RedisIDMap .
func (e *Entry) RedisIDMap(ctx *gin.Context) (map[uint64]*Model, error) {
	productList, err := e.RedisProductList(ctx)
	if err != nil {
		return nil, err
	}
	return productList.GetIDMap(), nil
}

// RedisProductList .
func (e *Entry) RedisProductList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.VipProductListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadProductList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisReloadProductList redis重载数据列表
func (e *Entry) RedisReloadProductList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.VipProductListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearProductList .
func (e *Entry) RedisClearProductList(ctx *gin.Context) error {
	cacheKey := redisPkg.VipProductListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
