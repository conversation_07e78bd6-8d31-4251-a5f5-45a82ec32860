package product

import (
	"time"

	"vlab/app/common/dbs"
)

type Product string
type MapProduct string

const (
	ProductSingleMonth   Product = "skybox.vip.single.month"
	ProductSingleQuarter Product = "skybox.vip.single.quarter"
	ProductSingleYear    Product = "skybox.vip.single.year"
	ProductMonthly       Product = "skybox.vip.monthly"
	ProductQuarterly     Product = "skybox.vip.quarterly"
	ProductYearly        Product = "skybox.vip.yearly"

	ViProductSingleMonth   MapProduct = "vip.one.month.subscribe"
	ViProductSingleQuarter MapProduct = "vip.three.month.subscribe"
	ViProductSingleYear    MapProduct = "vip.one.year.subscribe"
	ViProductMonthly       MapProduct = "vip.monthly.subscribe"
	ViProductQuarterly     MapProduct = "vip.three.monthly.subscribe"
	ViProductYearly        MapProduct = "vip.yearly.subscribe"

	ViProductSingleMonthTest MapProduct = "vip.test.single"
	ViProductMonthlyTest     MapProduct = "test.aa.sub"
	ViProductMonthlyTest2    MapProduct = "test.subscribe"

	HitvProductSingleMonth   MapProduct = "vip.1.month.hitvplayer"
	HitvProductSingleQuarter MapProduct = "vip.3.month.hitvplayer"
	HitvProductSingleYear    MapProduct = "vip.1.year.hitvplayer"
	HitvProductMonthly       MapProduct = "vip.monthly.hitvplayer"
	HitvProductQuarterly     MapProduct = "vip.quarterly.hitvplayer"
	HitvProductYearly        MapProduct = "vip.yearly.hitvplayer"
)

var (
	GracePeriodMap = map[string]uint32{
		string(ProductSingleMonth):   0,
		string(ProductSingleQuarter): 0,
		string(ProductSingleYear):    0,
		string(ProductMonthly):       28,
		string(ProductQuarterly):     28,
		string(ProductYearly):        28,
	}
	TrialPeriodMap = map[string]uint32{
		string(ProductSingleMonth):   0,
		string(ProductSingleQuarter): 0,
		string(ProductSingleYear):    0,
		string(ProductMonthly):       3,
		string(ProductQuarterly):     3,
		string(ProductYearly):        3,
	}

	ToCommonProductMap = map[MapProduct]string{
		ViProductSingleMonth:   string(ProductSingleMonth),
		ViProductSingleQuarter: string(ProductSingleQuarter),
		ViProductSingleYear:    string(ProductSingleYear),
		ViProductMonthly:       string(ProductMonthly),
		ViProductQuarterly:     string(ProductQuarterly),
		ViProductYearly:        string(ProductYearly),

		ViProductSingleMonthTest: string(ProductSingleMonth),
		ViProductMonthlyTest:     string(ProductMonthly),
		ViProductMonthlyTest2:    string(ProductMonthly),

		HitvProductSingleMonth:   string(ProductSingleMonth),
		HitvProductSingleQuarter: string(ProductSingleQuarter),
		HitvProductSingleYear:    string(ProductSingleYear),
		HitvProductMonthly:       string(ProductMonthly),
		HitvProductQuarterly:     string(ProductQuarterly),
		HitvProductYearly:        string(ProductYearly),
	}
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID            uint64    `gorm:"primarykey" json:"id"`
	Name          string    `json:"name,omitempty"`
	ProductID     string    `json:"product_id,omitempty"`
	Region        string    `json:"region,omitempty"`
	Price         uint64    `json:"price,omitempty"`
	Currency      string    `json:"currency,omitempty"`
	GracePeriod   uint32    `json:"grace_period,omitempty"`   // 宽限期天数
	PaypalPlanID  string    `json:"paypal_plan_id,omitempty"` // Paypal计划ID
	IsSub         uint32    `json:"is_sub,omitempty"`         // 是否是订阅商品
	Status        uint32    `json:"status,omitempty"`
	IsDeleted     uint32    `json:"is_deleted,omitempty"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	StripePriceID string    `json:"stripe_price_id,omitempty"` // Stripe价格ID
}

func (m *Model) TableName() string {
	return "vip_product"
}

func IsSub(productID string) bool {
	return productID == string(ProductMonthly) || productID == string(ProductQuarterly) || productID == string(ProductYearly)
}

type Filter struct {
	Status uint32
	Region string
	Sort   dbs.CommonSort
}

type ModelList []*Model

func (ml ModelList) GetProductRegionMap() map[string]ModelList {
	productRegionMap := make(map[string]ModelList)
	for _, v := range ml {
		if _, ok := productRegionMap[v.Region]; !ok {
			productRegionMap[v.Region] = ModelList{}
		}
		productRegionMap[v.Region] = append(productRegionMap[v.Region], v)
	}
	return productRegionMap
}

func (ml ModelList) GetProductIDOnlyMap() map[string]*Model {
	retMap := make(map[string]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ProductID]; !ok {
			retMap[val.ProductID] = val
		}
	}
	return retMap
}

func (ml ModelList) GetIDMap() map[uint64]*Model {
	retMap := make(map[uint64]*Model, len(ml))
	for _, val := range ml {
		if _, ok := retMap[val.ID]; !ok {
			retMap[val.ID] = val
		}
	}
	return retMap
}

// type Product struct {
// 	ID                   uint   `gorm:"primaryKey NOT NULL AUTO_INCREMENT"`
// 	ProductId            string `json:"product_id" gorm:"index:product_id_app_id_deleted_at;type:varchar(64) DEFAULT ''"`
// 	ProductType          int    `json:"product_type" gorm:"comment:商品类型,0.未知类型/1.消耗型购买/2.非消耗型购买/3.自动订阅/4.非自动订阅;type:tinyint(4) DEFAULT 0"`
// 	Name                 string `json:"name" gorm:"comment:商品名称;type:varchar(64) DEFAULT ''"`
// 	Description          string `json:"description" gorm:"comment:商品描述;type:varchar(1024) DEFAULT ''"`
// 	Price                int    `json:"price" gorm:"comment:商品价格,精度2位小数,用100倍存储;type:bigint DEFAULT 0"`
// 	TokenType            int    `json:"token_type" gorm:"comment:虚拟币类型,0.钻石;1.金币;2.元宝;3.其它;type:tinyint(4) DEFAULT 0"`
// 	TokenQuantity        int    `json:"token_quantity" gorm:"comment:虚拟币数量;type:int(11) DEFAULT 0"`
// 	SubscribeDurationDay int    `json:"subscribe_duration_day" gorm:"comment:会员订阅时长(天);type:int(11) DEFAULT 0"`
// 	Weight               int    `json:"weight" gorm:"comment:权重，排序时从大到小，客户端根据此字段进行商品排序;type:int(11) DEFAULT 0"`
// 	ImageURL             string `json:"image_url" gorm:"comment:商品图片URL;type:varchar(1024) DEFAULT ''"`
// 	IsAutoSubscribe      int    `json:"is_auto_subscribe" gorm:"comment:是否是自动续费,0.未知/1.自动续费/2.非自动续费;type:tinyint(2) DEFAULT 0"`
// 	Platform             int    `json:"platform" gorm:"comment:支付平台, 0-海外,1-微信支付,2-国内支付渠道;type:tinyint(2) DEFAULT 0"`
// }
