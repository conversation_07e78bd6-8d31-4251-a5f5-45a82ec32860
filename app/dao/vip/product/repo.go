package product

import (
	"fmt"
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func (e *Entry) buildQuery(tx *gorm.DB, f *Filter) *gorm.DB {
	query := tx.Model(&Model{}).Where(fmt.Sprintf("%s = 0", dbs.SoftDelField))
	if f.Status != 0 {
		query.Where("status = ?", f.Status)
	}
	if f.Region != "" {
		query.Where("region = ?", f.Region)
	}

	return query
}

// FindByFilter .
func (e *Entry) FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error) {
	query := e.buildQuery(e.MysqlEngine.UseWithGinCtx(ctx, false), f)

	ret := ModelList{}
	if err := query.Find(&ret).Order("id asc").Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FindByFilter error")
		return ret, err
	}
	return ret, nil
}
