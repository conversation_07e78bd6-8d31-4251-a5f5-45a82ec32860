package payLog

import (
	"time"
	"vlab/app/common/dbs"
)

type Status uint32
type LogType uint32
type PayType uint32

const (
	StatusPaid    Status = 1 // 支付完成
	StatusRepeat  Status = 2 // 重复使用
	StatusIgnore  Status = 3 // 忽略通知
	StatusWaitPay Status = 4 // 等待支付
	StatusCancel  Status = 5 // 取消支付

	LogRequest LogType = 1 // 请求日志
	LogNotify  LogType = 2 // 通知日志

	PayTypePaid  PayType = 1 // 已支付
	PayTypeTrial PayType = 2 // 试用
)

// TODO 分表 or kafka异步写入es
type Model struct {
	ID                    uint64    `gorm:"primarykey" json:"id"`
	UserID                uint64    `json:"user_id,omitempty"`
	Platform              uint32    `json:"platform,omitempty"`
	LogType               uint32    `json:"log_type,omitempty"`
	Status                uint32    `json:"status,omitempty"`
	ProductID             string    `json:"product_id,omitempty"`
	TransactionID         string    `json:"transaction_id,omitempty"`
	OriginalTransactionID string    `json:"original_transaction_id,omitempty"`
	PayType               uint32    `json:"pay_type,omitempty"`
	PurchaseToken         string    `json:"purchase_token,omitempty"`
	Amount                int64     `json:"amount,omitempty"`
	Currency              string    `json:"currency,omitempty"`
	NotifyID              string    `json:"notify_id,omitempty"`
	NotifyType            string    `json:"notify_type,omitempty"`
	SubType               string    `json:"sub_type,omitempty"`
	ExpiresTime           int64     `json:"expires_time,omitempty"`
	Env                   string    `json:"env,omitempty"`
	ChannelID             uint64    `json:"channel_id,omitempty"`
	TraceID               string    `json:"trace_id,omitempty"`
	Func                  string    `json:"func,omitempty"`
	Date                  string    `json:"date,omitempty"`
	Header                string    `json:"header,omitempty"`
	ReqData               string    `json:"req_data,omitempty"`
	RespData              string    `json:"resp_data,omitempty"`
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

func (m *Model) TableName() string {
	return "vip_pay_log"
}

type Filter struct {
	ID   uint64
	Sort dbs.CommonSort
}

type ModelList []*Model
