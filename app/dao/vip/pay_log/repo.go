package payLog

import (
	"vlab/app/common/dbs"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Create .
func (e *Entry) Create(ctx *gin.Context, m *Model) error {
	return e.CreateWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), m)
}

// CreateWithTx .
func (e *Entry) CreateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) error {
	if err := tx.Omit(string(dbs.CreatedAtField), string(dbs.UpdatedAtField)).Create(&m).Error; err != nil {
		return err
	}
	return nil
}

// UpdateMapByID .
func (e *Entry) UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error {
	return e.UpdateMapByIDWithTx(ctx, e.MysqlEngine.UseWithGinCtx(ctx, true), id, data)
}

// UpdateMapByIDWithTx .
func (e *Entry) UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error {
	if err := tx.Model(&Model{}).Where("id = ?", id).Updates(data).Error; err != nil {
		return err
	}

	return nil
}

// FeatchByTransId .
func (e *Entry) FeatchByTransId(ctx *gin.Context, transId string, status Status) (*Model, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("transaction_id = ?", transId).
		Where("status = ?", status).
		Order("id desc")

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchByTransId error")
		return ret, err
	}
	return ret, nil
}

// FeatchByOriginalTransId .
func (e *Entry) FeatchByOriginalTransId(ctx *gin.Context, oGlTransId string, status Status) (*Model, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("original_transaction_id = ?", oGlTransId).
		Where("status = ?", status).
		Order("id desc")

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchByOriginalTransId error")
		return ret, err
	}
	return ret, nil
}

// FeatchByPurchaseToken .
func (e *Entry) FeatchByPurchaseToken(ctx *gin.Context, purchaseToken string) (*Model, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("purchase_token = ?", purchaseToken).
		Order("id desc")

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchByPurchaseToken error")
		return ret, err
	}
	return ret, nil
}

// FeatchThirdToCheck .
func (e *Entry) FeatchThirdToCheck(ctx *gin.Context, transId string, status uint32) (*Model, error) {
	query := e.MysqlEngine.UseWithGinCtx(ctx, false).Model(&Model{}).
		Where("transaction_id = ?", transId).
		Where("status = ?", status).
		Order("id asc")

	ret := &Model{}
	if err := query.Find(&ret).Limit(1).Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("FeatchThirdToCheck error")
		return ret, err
	}
	return ret, nil
}
