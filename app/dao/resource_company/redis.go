package company

import (
	"encoding/json"
	"vlab/app/common/dbs"
	redisPkg "vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
)

// RedisEnableCompanyList .
func (e *Entry) RedisEnableCompanyList(ctx *gin.Context) (ModelList, error) {
	list, err := e.RedisCompanyList(ctx)
	if err != nil {
		return nil, err
	}
	ret := ModelList{}
	for _, val := range list {
		if val.Status == uint32(dbs.StatusEnable) {
			ret = append(ret, val)
		}
	}
	return ret, nil
}

// RedisCompanyList .
func (e *Entry) RedisCompanyList(ctx *gin.Context) (ModelList, error) {
	cacheKey := redisPkg.CompanyListKey
	ret := ModelList{}
	cacheData, err := e.RedisCli.Get(ctx.Request.Context(), cacheKey).Result()
	if err != nil {
		if err.Error() == redis.Nil.Error() {
			return e.RedisReloadCompanyList(ctx)
		}
		return nil, err
	}
	if err = json.Unmarshal([]byte(cacheData), &ret); err != nil {
		return nil, err
	}
	return ret, nil
}

// RedisCompanyMap .
func (e *Entry) RedisCompanyMap(ctx *gin.Context) (map[uint64]*Model, error) {
	companyList, err := e.RedisCompanyList(ctx)
	if err != nil {
		return nil, err
	}
	return companyList.GetIDMap(), nil
}

// RedisCompanyNameMap .
func (e *Entry) RedisCompanyNameMap(ctx *gin.Context) (map[string]*Model, error) {
	companyList, err := e.RedisCompanyList(ctx)
	if err != nil {
		return nil, err
	}
	return companyList.GetNameMap(), nil
}

// RedisReloadCompanyList redis重载数据列表
func (e *Entry) RedisReloadCompanyList(ctx *gin.Context) (ModelList, error) {
	var (
		cacheKey   = redisPkg.CompanyListKey
		expireTime = dbs.GetRedisExpireTime(dbs.ResidExpireTime)
		tmp        = ModelList{}
		cacheData  []byte
		err        error
	)
	list, err := e.FindByFilter(ctx, &Filter{})
	if err != nil {
		return tmp, err
	}
	if cacheData, err = json.Marshal(list); err != nil {
		return tmp, err
	}

	if err := e.RedisCli.Set(ctx.Request.Context(), cacheKey, string(cacheData), expireTime).Err(); err != nil {
		return tmp, err
	}
	return list, nil
}

// RedisClearCompanyList .
func (e *Entry) RedisClearCompanyList(ctx *gin.Context) error {
	cacheKey := redisPkg.CompanyListKey
	e.RedisCli.Del(ctx.Request.Context(), cacheKey)
	return nil
}
