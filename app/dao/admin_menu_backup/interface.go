package adminMenuBackup

import (
	"sync"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type Repo interface {
	CreateOrUpdate(ctx *gin.Context, m *Model) (uint64, error)
	CreateOrUpdateWithTx(ctx *gin.Context, tx *gorm.DB, m *Model) (uint64, error)
	UpdateMapByID(ctx *gin.Context, id uint64, data map[string]interface{}) error
	UpdateMapByIDWithTx(ctx *gin.Context, tx *gorm.DB, id uint64, data map[string]interface{}) error
	FindByFilter(ctx *gin.Context, f *Filter) (ModelList, error)
	FindXidsByFilter(ctx *gin.Context, f *Filter, field dbs.PluckField) ([]uint64, error)
	FetchByID(ctx *gin.Context, id uint64) (*Model, error)
	FeatchByFilterSort(ctx *gin.Context, f *Filter) (*Model, error)
	CountByFilter(ctx *gin.Context, f *Filter) (num int64, err error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
