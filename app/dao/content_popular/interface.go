package content_popular

import (
	"sync"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	"vlab/pkg/redis"
)

type Repo interface {
	PopularRepo
}

type PopularRepo interface {
	FindByFilter(*gin.Context, *Filter) (PopularList, error)

	CreateOrUpdate(*gin.Context, *Popular) (uint64, error)
	CreateOrUpdateWithTx(*gin.Context, *gorm.DB, *Popular) (uint64, error)
	Create(*gin.Context, *Popular) (uint64, error)
	CreateWithTx(*gin.Context, *gorm.DB, *Popular) (uint64, error)
	BatchCreate(*gin.Context, []*Popular) error
	BatchCreateWithTx(*gin.Context, *gorm.DB, []*Popular) error
	UpdateModelByID(*gin.Context, uint64, *Popular) error
	UpdateModelByIDWithTx(*gin.Context, *gorm.DB, uint64, *Popular) error
	UpdateMapByID(*gin.Context, uint64, map[string]interface{}) error
	UpdateMapByIDWithTx(*gin.Context, *gorm.DB, uint64, map[string]interface{}) error
	DataPageList(*gin.Context, *Filter, int, int) (total int64, list PopularList, err error)
	FindXidsByFilter(*gin.Context, *Filter, dbs.PluckField) ([]uint64, error)
	FetchByID(*gin.Context, uint64) (*Popular, error)
	FeatchByFilterSort(*gin.Context, *Filter) (*Popular, error)
	CountByFilter(*gin.Context, *Filter) (int64, error)
}

type Entry struct {
	MysqlEngine *dbs.MysqlEngines
	RedisCli    *redis.RedisClient
}

var (
	defaultRepo         Repo
	defaultRepoInitOnce sync.Once
)

func GetRepo() Repo {
	if defaultRepo == nil {
		defaultRepoInitOnce.Do(func() {
			ret := newEntry()
			defaultRepo = ret
		})
	}
	return defaultRepo
}

func newEntry() *Entry {
	return &Entry{
		MysqlEngine: dbs.NewMysqlEngines(),
		RedisCli:    redis.GetRedisClient(),
	}
}
