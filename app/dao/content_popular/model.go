package content_popular

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
)

type Popular struct {
	dbs.ModelWithDel

	ShowID uint64 `json:"show_id,omitempty"` // 剧集ID

	Heat uint32 `json:"heat,omitempty"` // 热度

	Status uint32 `json:"status,omitempty"` // 状态

	ChannelID uint64 `json:"channel_id,omitempty"` // 渠道ID
}

func (m *Popular) TableName() string {
	return "content_popular"
}

type Filter struct {
	ID  uint64   `json:"id,omitempty"`  // ID
	IDs []uint64 `json:"ids,omitempty"` // ID

	ShowID  uint64   `json:"show_id,omitempty"`  // 剧集ID
	ShowIDs []uint64 `json:"show_ids,omitempty"` // 剧集ID

	ChannelID uint64 `json:"channel_id,omitempty"` // 渠道ID

	Heat uint32 `json:"heat,omitempty"` // 热度

	Sort []clause.OrderByColumn

	Status uint32 `json:"status,omitempty"` // 状态
}

type PopularList []*Popular

func (pl PopularList) GetIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ID)
	}
	return ids
}

func (pl PopularList) GetShowIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		ids = append(ids, v.ShowID)
	}
	return ids
}

func (pl PopularList) GetChannelIDs() []uint64 {
	var ids []uint64
	for _, v := range pl {
		if v.ChannelID > 0 {
			ids = append(ids, v.ChannelID)
		}
	}
	return ids
}

func (f Filter) _eq_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ID <= 0 {
			return db
		}
		return db.Where("id = ?", f.ID)
	}
}

func (f Filter) _in_ids() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if len(f.IDs) <= 0 {
			return db
		}
		return db.Where("id in (?)", f.IDs)
	}
}

func (f Filter) _eq_status() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.Status <= 0 {
			return db
		}
		return db.Where("status = ?", f.Status)
	}
}

func (f Filter) _eq_show_id() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if f.ShowID <= 0 {
			return db
		}
		return db.Where("show_id = ?", f.ShowID)
	}
}

func (f Filter) _in_show_ids() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if len(f.ShowIDs) <= 0 {
			return db
		}
		return db.Where("show_id in (?)", f.ShowIDs)
	}
}

func (f Filter) _eq_heat() func(db *gorm.DB) *gorm.DB {

	return func(db *gorm.DB) *gorm.DB {
		if f.Heat <= 0 {
			return db
		}
		return db.Where("heat = ?", f.Heat)
	}
}

func (f Filter) _eq_channel_id() func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if f.ChannelID <= 0 {
			return db
		}
		return db.Where("channel_id = ?", f.ChannelID)
	}
}

func (f Filter) fullScopes() []func(db *gorm.DB) *gorm.DB {
	return []func(db *gorm.DB) *gorm.DB{
		f._eq_id(),
		f._in_ids(),
		f._eq_show_id(),
		f._in_show_ids(),
		f._eq_heat(),
		f._eq_channel_id(),
		f._eq_status(),
	}
}
