package search

import (
	"strconv"

	"vlab/app/dto"
	"vlab/app/service/search"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// Search 搜索接口
func Search(c *gin.Context) {
	req := &dto.SearchRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	// 获取语言偏好（从header或参数中）
	if req.Language == "" {
		req.Language = c.GetHeader("Accept-Language")
		if req.Language == "" {
			req.Language = c.Query("lang")
		}
		if req.Language == "" {
			req.Language = "zh" // 默认中文
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// QuickSearch 快速搜索接口（简化版搜索）
func QuickSearch(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		helper.AppResp(c, ecode.ParamErr.Code(), "搜索关键词不能为空")
		return
	}

	// 构建搜索请求
	req := &dto.SearchRequest{
		Query:      query,
		SearchMode: "hybrid",
		Page:       1,
		PageSize:   10,
		Language:   c.GetHeader("Accept-Language"),
	}

	if req.Language == "" {
		req.Language = "zh"
	}

	// 解析页码参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	// 解析每页大小参数
	if sizeStr := c.Query("size"); sizeStr != "" {
		if size, err := strconv.Atoi(sizeStr); err == nil && size > 0 && size <= 50 {
			req.PageSize = size
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// VectorSearch 纯向量搜索接口
func VectorSearch(c *gin.Context) {
	req := &dto.SearchRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if req.Query == "" {
		helper.AppResp(c, ecode.ParamErr.Code(), "向量搜索需要查询文本")
		return
	}

	// 强制设置为向量搜索模式
	req.SearchMode = "vector"

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.VectorParams.TopK == 0 {
		req.VectorParams.TopK = 100
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// FilterSearch 纯筛选搜索接口
func FilterSearch(c *gin.Context) {
	req := &dto.SearchRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 强制设置为筛选搜索模式
	req.SearchMode = "filter_only"

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SearchSuggestions 搜索建议接口
func SearchSuggestions(c *gin.Context) {
	req := &dto.SearchSuggestionRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 支持通过query参数传递
	if req.Query == "" {
		req.Query = c.Query("q")
	}

	if req.Query == "" {
		// 返回空建议而不是错误
		helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), &dto.SearchSuggestionResponse{
			Suggestions: []dto.SearchSuggestion{},
		})
		return
	}

	// 设置默认值
	if req.Limit == 0 {
		req.Limit = 10
	}
	if req.Language == "" {
		req.Language = c.GetHeader("Accept-Language")
		if req.Language == "" {
			req.Language = "zh"
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.GetSearchSuggestions(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// Recommendations 推荐接口
func Recommendations(c *gin.Context) {
	req := &dto.RecommendationRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 设置默认值
	if req.Limit == 0 {
		req.Limit = 20
	}
	if req.Language == "" {
		req.Language = c.GetHeader("Accept-Language")
		if req.Language == "" {
			req.Language = "zh"
		}
	}

	// 支持通过query参数传递show_id
	if req.ShowID == 0 {
		if showIDStr := c.Query("show_id"); showIDStr != "" {
			if showID, err := strconv.ParseUint(showIDStr, 10, 64); err == nil {
				req.ShowID = showID
			}
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.GetRecommendations(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// SimilarShows 相似节目推荐接口
func SimilarShows(c *gin.Context) {
	showIDStr := c.Param("id")
	if showIDStr == "" {
		helper.AppResp(c, ecode.ParamErr.Code(), "节目ID不能为空")
		return
	}

	showID, err := strconv.ParseUint(showIDStr, 10, 64)
	if err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), "无效的节目ID")
		return
	}

	// 构建推荐请求
	req := &dto.RecommendationRequest{
		ShowID:   showID,
		Limit:    20,
		Language: c.GetHeader("Accept-Language"),
	}

	if req.Language == "" {
		req.Language = "zh"
	}

	// 解析限制数量参数
	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 50 {
			req.Limit = limit
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.GetRecommendations(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PopularShows 热门节目接口
func PopularShows(c *gin.Context) {
	// 构建热门内容搜索请求
	req := &dto.SearchRequest{
		SearchMode: "filter_only",
		Page:       1,
		PageSize:   20,
		SortBy:     "rating",
		SortOrder:  "desc",
		Language:   c.GetHeader("Accept-Language"),
		Filters: dto.SearchFilters{
			RatingFrom: 7.0, // 高评分内容
		},
	}

	if req.Language == "" {
		req.Language = "zh"
	}

	// 解析分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	if sizeStr := c.Query("size"); sizeStr != "" {
		if size, err := strconv.Atoi(sizeStr); err == nil && size > 0 && size <= 50 {
			req.PageSize = size
		}
	}

	// 分类筛选
	if category := c.Query("category"); category != "" {
		req.Filters.Categories = []string{category}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// CategoryShows 分类节目接口
func CategoryShows(c *gin.Context) {
	category := c.Param("category")
	if category == "" {
		helper.AppResp(c, ecode.ParamErr.Code(), "分类不能为空")
		return
	}

	// 构建分类搜索请求
	req := &dto.SearchRequest{
		SearchMode: "filter_only",
		Page:       1,
		PageSize:   20,
		SortBy:     "rating",
		SortOrder:  "desc",
		Language:   c.GetHeader("Accept-Language"),
		Filters: dto.SearchFilters{
			Categories: []string{category},
		},
	}

	if req.Language == "" {
		req.Language = "zh"
	}

	// 解析分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	if sizeStr := c.Query("size"); sizeStr != "" {
		if size, err := strconv.Atoi(sizeStr); err == nil && size > 0 && size <= 50 {
			req.PageSize = size
		}
	}

	// 其他筛选条件
	if genre := c.Query("genre"); genre != "" {
		req.Filters.Genres = []string{genre}
	}

	if year := c.Query("year"); year != "" {
		if yearInt, err := strconv.Atoi(year); err == nil {
			req.Filters.YearFrom = yearInt
			req.Filters.YearTo = yearInt
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdvancedSearch 高级搜索接口
func AdvancedSearch(c *gin.Context) {
	req := &dto.SearchRequest{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 高级搜索默认使用混合模式
	if req.SearchMode == "" {
		req.SearchMode = "hybrid"
	}

	// 设置默认值
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 20
	}
	if req.Language == "" {
		req.Language = c.GetHeader("Accept-Language")
		if req.Language == "" {
			req.Language = "zh"
		}
	}

	// 支持URL参数覆盖
	if query := c.Query("q"); query != "" {
		req.Query = query
	}

	// 分类参数
	if categories := c.QueryArray("categories"); len(categories) > 0 {
		req.Filters.Categories = categories
	}

	// 类型参数
	if genres := c.QueryArray("genres"); len(genres) > 0 {
		req.Filters.Genres = genres
	}

	// 年份范围
	if yearFrom := c.Query("year_from"); yearFrom != "" {
		if year, err := strconv.Atoi(yearFrom); err == nil {
			req.Filters.YearFrom = year
		}
	}
	if yearTo := c.Query("year_to"); yearTo != "" {
		if year, err := strconv.Atoi(yearTo); err == nil {
			req.Filters.YearTo = year
		}
	}

	// 评分范围
	if ratingFrom := c.Query("rating_from"); ratingFrom != "" {
		if rating, err := strconv.ParseFloat(ratingFrom, 64); err == nil {
			req.Filters.RatingFrom = rating
		}
	}
	if ratingTo := c.Query("rating_to"); ratingTo != "" {
		if rating, err := strconv.ParseFloat(ratingTo, 64); err == nil {
			req.Filters.RatingTo = rating
		}
	}

	searchService := search.NewSearchService()
	ret, err := searchService.Search(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
