package show

import (
	"github.com/gin-gonic/gin"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func AdminGenreList(c *gin.Context) {
	req := &showDto.AdminGenreListReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	ret, err := show.GetService().AdminGenreList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)

}

func AdminGenreDetail(c *gin.Context) {
	req := &showDto.AdminGenreDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminGenreDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)

}

func AdminGenreCreate(c *gin.Context) {
	req := &showDto.AdminGenreCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminGenreCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminGenreUpdate(c *gin.Context) {
	req := &showDto.AdminGenreUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminGenreUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminGenreUpdatePatch(c *gin.Context) {
	req := &showDto.AdminGenreUpdatePatchReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminGenreUpdatePatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminGenreDelete(c *gin.Context) {
	req := &showDto.AdminGenreDeleteReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminGenreDelete(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
