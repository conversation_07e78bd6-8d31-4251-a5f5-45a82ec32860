package show

import (
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func EpisodeDetail(c *gin.Context) {
	req := &showDto.EpisodeDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	req.Action = watchVideoPv.LogTypePlay

	ret, err := show.GetService().EpisodeDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func EpisodeDownload(c *gin.Context) {
	req := &showDto.EpisodeDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	req.Action = watchVideoPv.LogTypeDownload

	ret, err := show.GetService().EpisodeDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
