package show

import (
	"github.com/gin-gonic/gin"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func AdminShowList(c *gin.Context) {
	req := &showDto.AdminShowListReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	ret, err := show.GetService().AdminShowList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowDetail(c *gin.Context) {
	req := &showDto.AdminShowDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowCreate(c *gin.Context) {
	req := &showDto.AdminShowCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowUpdate(c *gin.Context) {
	req := &showDto.AdminShowUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowStatusBatch(c *gin.Context) {
	req := &showDto.AdminShowStatusBatchReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowStatusBatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowUpdatePatch(c *gin.Context) {
	req := &showDto.AdminShowUpdatePatchReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowUpdatePatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowDelete(c *gin.Context) {
	req := &showDto.AdminShowDeleteReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowDelete(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowTmdb(c *gin.Context) {
	req := &showDto.AdminShowTmdbReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowTmdb(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowTmdbDetail(c *gin.Context) {
	req := &showDto.AdminShowTmdbDetailReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowTmdbDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminShowTmdbUrl(c *gin.Context) {
	req := &showDto.AdminShowTmdbUrlReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowTmdbUrl(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
