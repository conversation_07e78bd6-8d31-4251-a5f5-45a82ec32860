package show

import (
	"github.com/gin-gonic/gin"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func AdminFranchiseList(c *gin.Context) {
	req := &showDto.AdminFranchiseListReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	ret, err := show.GetService().AdminFranchiseList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminFranchiseDetail(c *gin.Context) {
	req := &showDto.AdminFranchiseDetailReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminFranchiseDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminFranchiseCreate(c *gin.Context) {
	req := &showDto.AdminFranchiseCreateReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminFranchiseCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminFranchiseUpdate(c *gin.Context) {
	req := &showDto.AdminFranchiseUpdateReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminFranchiseUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminFranchiseUpdatePatch(c *gin.Context) {
	req := &showDto.AdminFranchiseUpdatePatchReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminFranchiseUpdatePatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminFranchiseDelete(c *gin.Context) {
	req := &showDto.AdminFranchiseDeleteReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminFranchiseDelete(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
