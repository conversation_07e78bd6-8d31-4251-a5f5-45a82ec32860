package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	showDto "vlab/app/dto/show"
)

func AdminMarketBannerList(c *gin.Context) {
	req := &showDto.AdminMarketBannerListReq{}
	if err := c.Should<PERSON>ind<PERSON>y(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 10
	}

	ret, err := show.GetService().AdminMarketBannerList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminMarketBannerDetail(c *gin.Context) {
	req := &showDto.AdminMarketBannerDetailReq{}
	if err := c.ShouldBindQuery(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminMarketBannerDetail(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminMarketBannerCreate(c *gin.Context) {
	req := &showDto.AdminMarketBannerCreateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminMarketBannerCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminMarketBannerUpdate(c *gin.Context) {
	req := &showDto.AdminMarketBannerUpdateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminMarketBannerUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminMarketBannerUpdatePatch(c *gin.Context) {
	req := &showDto.AdminMarketBannerUpdatePatchReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminMarketBannerUpdatePatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func AdminMarketBannerDelete(c *gin.Context) {
	req := &showDto.AdminMarketBannerDeleteReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminMarketBannerDelete(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
