package show

import (
	"vlab/app/api/auto"
	"vlab/app/job/lok"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

func AdminTestLokToShow(c *gin.Context) {
	req := &auto.DetailResp{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	showID, err := lok.GetJob().ImportLokToShow(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())

		log.WithContext(c).WithError(err).Error("import lok to show failed")
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), showID)
}

func AdminTestLokToEpisode(c *gin.Context) {
	req := &auto.PlayReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	err := lok.GetJob().ImportLokToEpisode(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())

		log.WithContext(c).WithError(err).Error("import lok to episode failed")
		return
	}
	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}
