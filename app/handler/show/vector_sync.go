package show

import (
	"strconv"

	"github.com/gin-gonic/gin"
	showDto "vlab/app/dto/show"
	"vlab/app/job/vector"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

// VectorSyncManual 手动触发向量同步
func VectorSyncManual(ctx *gin.Context) {
	// 获取时间窗口参数（默认8小时）
	hoursStr := ctx.DefaultQuery("hours", "8")
	hours, err := strconv.Atoi(hoursStr)
	if err != nil || hours <= 0 {
		hours = 8
	}

	req := &showDto.VectorSyncManualReq{
		Hours: hours,
	}

	// 调用service层的手动同步方法
	resp, err := show.GetService().SyncRecentUpdatedShowsManual(ctx, req.Hours)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, "0", "同步完成", resp)
}

// VectorSyncJob 触发定时任务（用于测试）
func VectorSyncJob(ctx *gin.Context) {
	// 直接调用job
	err := vector.GetJob().SyncRecentUpdatedShows()
	if err != nil {
		helper.AppResp(ctx, ecode.SystemErr.Code(), "同步失败: "+err.Error())
		return
	}

	helper.AppWithDataResp(ctx, "0", "同步任务执行成功", nil)
}
