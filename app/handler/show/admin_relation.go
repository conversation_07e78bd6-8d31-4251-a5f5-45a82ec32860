package show

import (
	showServ "vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminRelationGroupList 获取关联组列表
func AdminRelationGroupList(c *gin.Context) {
	req := &showServ.AdminRelationGroupListReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := showServ.GetService().AdminRelationGroupList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminRelationGroupCreate 创建关联组
func AdminRelationGroupCreate(c *gin.Context) {
	req := &showServ.AdminRelationGroupCreateReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := showServ.GetService().AdminRelationGroupCreate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), nil)
}

// AdminRelationGroupUpdate 更新关联组
func AdminRelationGroupUpdate(c *gin.Context) {
	req := &showServ.AdminRelationGroupUpdateReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := showServ.GetService().AdminRelationGroupUpdate(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), nil)
}

func AdminRelationGroupUpdatePatch(c *gin.Context) {
	req := &showServ.AdminRelationGroupUpdatePatchReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := showServ.GetService().AdminRelationGroupUpdatePatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), nil)
}

// AdminRelationGroupDelete 删除关联组
func AdminRelationGroupDelete(c *gin.Context) {
	req := &struct {
		ID uint64 `json:"id" form:"id" binding:"required"`
	}{}
	if err := c.ShouldBindJSON(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := showServ.GetService().AdminRelationGroupDelete(c, req.ID)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), nil)
}

// AdminRelationMemberBatch 批量设置关联组成员
func AdminRelationMemberBatch(c *gin.Context) {
	req := &showServ.AdminRelationMemberBatchReq{}
	if err := c.ShouldBindJSON(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := showServ.GetService().AdminRelationMemberBatch(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), nil)
}

// AdminShowRelationList 查看剧的关联关系
func AdminShowRelationList(c *gin.Context) {
	req := &showServ.AdminShowRelationListReq{}
	if err := c.ShouldBind(req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := showServ.GetService().AdminShowRelationList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
