package show

import (
	"strconv"
	"time"

	externalIdsDao "vlab/app/dao/content_show_external_ids"
	showDto "vlab/app/dto/show"
	showService "vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// ExternalIDsHandler 外部ID处理器
type ExternalIDsHandler struct {
	service *showService.ExternalIDsService
}

// NewExternalIDsHandler 创建外部ID处理器
func NewExternalIDsHandler() *ExternalIDsHandler {
	return &ExternalIDsHandler{
		service: showService.NewExternalIDsService(),
	}
}

// SyncExternalIDs 同步单个剧集的外部IDs
// @Summary 同步剧集外部IDs
// @Description 调用IMDB API同步指定剧集的外部IDs（IMDB、TMDB、Trakt等）
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Param id path int true "剧集ID"
// @Success 200 {object} showDto.SyncExternalIDsResponse
// @Router /admin/show/{id}/sync-external-ids [post]
func (h *ExternalIDsHandler) SyncExternalIDs(c *gin.Context) {
	// 获取路径参数
	showIDStr := c.Param("id")
	showID, err := strconv.ParseUint(showIDStr, 10, 64)
	if err != nil || showID == 0 {
		helper.AppResp(c, ecode.ParamErr.Code(), "invalid show id")
		return
	}

	// 执行同步
	err = h.service.SyncExternalIDs(c, showID)
	if err != nil {
		// Failed to sync external IDs
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	// 获取同步后的数据
	externalIDs, err := h.service.GetExternalIDsByShowID(c, showID)
	if err != nil {
		// Failed to get external IDs after sync
		helper.AppResp(c, ecode.SystemErr.Code(), "sync completed but failed to retrieve data")
		return
	}

	resp := &showDto.SyncExternalIDsResponse{
		ShowID:  showID,
		Success: true,
		Message: "External IDs synced successfully",
	}

	if externalIDs != nil {
		resp.Data = convertToDTO(externalIDs)
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// BatchSyncExternalIDs 批量同步剧集的外部IDs
// @Summary 批量同步剧集外部IDs
// @Description 批量调用IMDB API同步多个剧集的外部IDs
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Param request body showDto.BatchSyncExternalIDsRequest true "批量同步请求"
// @Success 200 {object} showDto.BatchSyncExternalIDsResponse
// @Router /admin/show/batch-sync-external-ids [post]
func (h *ExternalIDsHandler) BatchSyncExternalIDs(c *gin.Context) {
	var req showDto.BatchSyncExternalIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 执行批量同步
	results, err := h.service.BatchSyncExternalIDs(c, req.ShowIDs)
	if err != nil {
		// Batch sync failed
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	// 构建响应
	resp := &showDto.BatchSyncExternalIDsResponse{
		Results:      make([]*showDto.SyncResult, 0, len(req.ShowIDs)),
		TotalCount:   len(req.ShowIDs),
		SuccessCount: 0,
		FailedCount:  0,
	}

	for _, showID := range req.ShowIDs {
		result := &showDto.SyncResult{
			ShowID: showID,
		}

		if err, exists := results[showID]; exists && err != nil {
			result.Success = false
			result.Error = err.Error()
			resp.FailedCount++
		} else {
			result.Success = true
			resp.SuccessCount++

			// 获取同步后的数据
			if externalIDs, err := h.service.GetExternalIDsByShowID(c, showID); err == nil && externalIDs != nil {
				result.Data = convertToDTO(externalIDs)
			}
		}

		resp.Results = append(resp.Results, result)
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// GetExternalIDs 获取剧集的外部IDs
// @Summary 获取剧集外部IDs
// @Description 获取指定剧集已保存的外部IDs
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Param id path int true "剧集ID"
// @Success 200 {object} showDto.ExternalIDsResponse
// @Router /admin/show/{id}/external-ids [get]
func (h *ExternalIDsHandler) GetExternalIDs(c *gin.Context) {
	// 获取路径参数
	showIDStr := c.Param("id")
	showID, err := strconv.ParseUint(showIDStr, 10, 64)
	if err != nil || showID == 0 {
		helper.AppResp(c, ecode.ParamErr.Code(), "invalid show id")
		return
	}

	// 查询外部IDs
	externalIDs, err := h.service.GetExternalIDsByShowID(c, showID)
	if err != nil {
		// Failed to get external IDs
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	if externalIDs == nil {
		helper.AppResp(c, ecode.NotFoundErr.Code(), "external IDs not found")
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), convertToDTO(externalIDs))
}

// UpdateExternalIDs 更新剧集的外部IDs
// @Summary 更新剧集外部IDs
// @Description 手动更新指定剧集的外部IDs
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Param id path int true "剧集ID"
// @Param request body showDto.UpdateExternalIDsRequest true "更新请求"
// @Success 200 {object} response.Response
// @Router /admin/show/{id}/external-ids [put]
func (h *ExternalIDsHandler) UpdateExternalIDs(c *gin.Context) {
	// 获取路径参数
	showIDStr := c.Param("id")
	showID, err := strconv.ParseUint(showIDStr, 10, 64)
	if err != nil || showID == 0 {
		helper.AppResp(c, ecode.ParamErr.Code(), "invalid show id")
		return
	}

	var req showDto.UpdateExternalIDsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 构建更新参数
	updates := make(map[string]interface{})
	if req.ImdbID != nil {
		updates["imdb_id"] = *req.ImdbID
	}
	if req.TmdbID != nil {
		updates["tmdb_id"] = *req.TmdbID
	}
	if req.TraktID != nil {
		updates["trakt_id"] = *req.TraktID
	}
	if req.Slug != nil {
		updates["slug"] = *req.Slug
	}
	if req.MatchType != nil {
		updates["match_type"] = *req.MatchType
	}

	// 执行更新
	err = h.service.UpdateExternalIDs(c, showID, updates)
	if err != nil {
		// Failed to update external IDs
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), "External IDs updated successfully")
}

// SearchByExternalID 根据外部ID搜索剧集
// @Summary 根据外部ID搜索剧集
// @Description 根据IMDB ID、TMDB ID等外部ID查找对应的剧集
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Param imdb_id query string false "IMDB ID"
// @Param tmdb_id query int false "TMDB ID"
// @Param trakt_id query int false "Trakt ID"
// @Param slug query string false "Slug"
// @Success 200 {object} showDto.SearchByExternalIDResponse
// @Router /admin/show/search-by-external-id [get]
func (h *ExternalIDsHandler) SearchByExternalID(c *gin.Context) {
	var req showDto.SearchByExternalIDRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 至少需要一个搜索条件
	if req.ImdbID == "" && req.TmdbID == 0 && req.TraktID == 0 && req.Slug == "" {
		helper.AppResp(c, ecode.ParamErr.Code(), "at least one external ID is required")
		return
	}

	var showID uint64
	var err error

	// 根据不同的外部ID搜索
	if req.ImdbID != "" {
		showID, err = h.service.SearchByIMDBID(c, req.ImdbID)
	} else if req.TmdbID > 0 {
		showID, err = h.service.SearchByTMDBID(c, req.TmdbID)
	}
	// TODO: 添加其他外部ID的搜索

	if err != nil {
		// Failed to search by external ID
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	resp := &showDto.SearchByExternalIDResponse{
		ShowID: showID,
		Found:  showID > 0,
	}

	// 如果找到了，获取完整的外部ID信息
	if showID > 0 {
		if externalIDs, err := h.service.GetExternalIDsByShowID(c, showID); err == nil && externalIDs != nil {
			resp.ExternalIDs = convertToDTO(externalIDs)
		}
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// GetStatistics 获取外部ID统计信息
// @Summary 获取外部ID统计信息
// @Description 获取外部ID同步的统计数据
// @Tags Show External IDs
// @Accept json
// @Produce json
// @Success 200 {object} showDto.ExternalIDsStatisticsResponse
// @Router /admin/show/external-ids/statistics [get]
func (h *ExternalIDsHandler) GetStatistics(c *gin.Context) {
	stats, err := h.service.GetStatistics(c)
	if err != nil {
		// Failed to get statistics
		helper.AppResp(c, ecode.SystemErr.Code(), err.Error())
		return
	}

	// 计算匹配率
	total := stats["total"].(int64)
	matched := stats["matched"].(int64)
	matchRate := 0.0
	if total > 0 {
		matchRate = float64(matched) / float64(total) * 100
	}

	resp := &showDto.ExternalIDsStatisticsResponse{
		Total:      total,
		Matched:    matched,
		NotMatched: stats["not_matched"].(int64),
		MatchRate:  matchRate,
		ByType:     stats["by_type"].(map[string]int64),
		BySource:   stats["by_source"].(map[string]int64),
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// 辅助函数：将DAO模型转换为DTO
func convertToDTO(model *externalIdsDao.Model) *showDto.ExternalIDsResponse {
	resp := &showDto.ExternalIDsResponse{
		ID:          model.ID,
		ShowID:      model.ShowID,
		TraktID:     model.TraktID,
		Slug:        model.Slug,
		ImdbID:      model.ImdbID,
		TmdbID:      model.TmdbID,
		MatchType:   model.MatchType,
		MatchScore:  model.MatchScore,
		MatchReason: model.MatchReason,
		IsMatch:     model.IsMatch,
		Source:      model.Source,
	}

	// 转换时间
	if model.CreatedAt != nil {
		t := time.Time(*model.CreatedAt)
		resp.CreatedAt = &t
	}
	if model.UpdatedAt != nil {
		t := time.Time(*model.UpdatedAt)
		resp.UpdatedAt = &t
	}

	// 转换原始响应
	if model.RawResponse != nil {
		resp.RawResponse = map[string]interface{}(*model.RawResponse)
	}

	return resp
}
