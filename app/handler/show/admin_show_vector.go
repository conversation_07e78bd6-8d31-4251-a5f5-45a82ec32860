package show

import (
	"github.com/gin-gonic/gin"
	showDto "vlab/app/dto/show"
	"vlab/app/service/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

// AdminShowVectorUpload 单个剧集向量上传接口
// @Summary 单个剧集向量上传
// @Description 将单个剧集的数据导出并上传到向量数据库
// @Tags 后台-剧集管理
// @Accept json
// @Produce json
// @Param req body showDto.AdminShowVectorUploadReq true "请求参数"
// @Success 200 {object} showDto.AdminShowVectorUploadResp "上传结果"
// @Router /admin/show/vector/upload [post]
func AdminShowVectorUpload(c *gin.Context) {
	req := &showDto.AdminShowVectorUploadReq{}
	if err := c.ShouldBindJSON(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := show.GetService().AdminShowVectorUpload(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
