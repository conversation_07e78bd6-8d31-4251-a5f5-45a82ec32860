package client_config

import (
	"github.com/gin-gonic/gin"
	configDto "vlab/app/dto/client_config"
	configSrv "vlab/app/service/client_config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

// AdminClientConfig 管理后台 - 获取页面配置列表
func AdminClientConfig(ctx *gin.Context) {
	req := &configDto.AdminClientConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().AdminClientConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminUpdateClientConfig 管理后台 - 更新页面配置
func AdminUpdateClientConfig(ctx *gin.Context) {
	req := &configDto.AdminUpdateClientConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().AdminUpdateClientConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminDeleteClientConfig 管理后台 - 删除页面配置
func AdminDeleteClientConfig(ctx *gin.Context) {
	req := &configDto.AdminDeleteClientConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().AdminDeleteClientConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
