package client_config

import (
	"github.com/gin-gonic/gin"
	configDto "vlab/app/dto/client_config"
	configSrv "vlab/app/service/client_config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func ClientConfig(ctx *gin.Context) {
	req := &configDto.ClientConfigReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := configSrv.GetService().ClientConfig(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
