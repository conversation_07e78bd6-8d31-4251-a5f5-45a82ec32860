package admin

import (
	"net"
	"strings"

	"vlab/app/common/dbs"
	adminDto "vlab/app/dto/admin"
	"vlab/app/service/admin"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// validateIPList 验证IP列表格式
func validateIPList(ipList string) error {
	if ipList == "" {
		return ecode.New(ecode.ParamErr.Code(), "IP list cannot be empty")
	}

	ips := strings.Split(ipList, ",")
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 检查是否是CIDR格式
		if strings.Contains(ip, "/") {
			_, _, err := net.ParseCIDR(ip)
			if err != nil {
				return ecode.New(ecode.ParamErr.Code(), "Invalid CIDR format: "+ip)
			}
		} else {
			// 检查是否是有效IP地址
			if net.ParseIP(ip) == nil {
				return ecode.New(ecode.ParamErr.Code(), "Invalid IP address: "+ip)
			}
		}
	}
	return nil
}

// AdminIPBlacklistList 获取IP黑名单列表
func AdminIPBlacklistList(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistListReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = dbs.DefaultPage
	}
	if req.Limit <= 0 {
		req.Limit = dbs.DefaultLimit
	}

	ret, err := admin.GetService().AdminIPBlacklistList(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminIPBlacklistDetail 获取IP黑名单详情
func AdminIPBlacklistDetail(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistDetailReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := admin.GetService().AdminIPBlacklistDetail(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminIPBlacklistCreate 创建IP黑名单
func AdminIPBlacklistCreate(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistCreateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	// 设置默认状态
	if req.Status == 0 {
		req.Status = uint32(dbs.StatusEnable)
	}

	err := admin.GetService().AdminIPBlacklistCreate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminIPBlacklistUpdate 更新IP黑名单
func AdminIPBlacklistUpdate(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistUpdateReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := admin.GetService().AdminIPBlacklistUpdate(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminIPBlacklistDelete 删除IP黑名单
func AdminIPBlacklistDelete(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistDeleteReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	err := admin.GetService().AdminIPBlacklistDelete(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// AdminIPBlacklistTest 测试IP是否在黑名单中
func AdminIPBlacklistTest(ctx *gin.Context) {
	req := &adminDto.AdminIPBlacklistTestReq{}
	if err := ctx.ShouldBindQuery(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := admin.GetService().AdminIPBlacklistTest(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
