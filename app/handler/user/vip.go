package user

import (
	userDto "vlab/app/dto/user"
	"vlab/app/service/user/vip"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AppStoreTransaction .
func AppStoreTransaction(ctx *gin.Context) {
	req := userDto.AppStoreTransactionReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := vip.GetService().AppStoreTransaction(ctx, ctxUser.UID, req.TransactionID, req.Environment)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GooglePlayTransaction .
func GooglePlayTransaction(ctx *gin.Context) {
	req := userDto.GooglePlayTransactionReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := vip.GetService().GooglePlayTransaction(ctx, ctxUser.UID, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// ThirdTransactionCreate .
func ThirdTransactionCreate(ctx *gin.Context) {
	req := userDto.ThirdTransactionCreateReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	var err error
	resp := &userDto.ThirdTransactionCreateResp{
		Paypal: &userDto.PaypalTransactionCreateResp{},
		Stripe: &userDto.StripeTransactionCreateResp{},
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	switch req.PayType {
	case "paypal":
		resp.Paypal, err = vip.GetService().PaypalTransactionCreate(ctx, ctxUser.UID, req)
	case "stripe":
		resp.Stripe, err = vip.GetService().StripeTransactionCreate(ctx, ctxUser.UID, req)
	}
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// ThirdTransactionCheck .
func ThirdTransactionCheck(ctx *gin.Context) {
	req := userDto.ThirdTransactionCheckReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	var err error
	resp := userDto.ThirdTransactionCheckResp{
		UserInfo: &userDto.AdminUserListItem{},
		Stripe:   &userDto.StripeTransactionCheckResp{},
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	switch req.PayType {
	case "paypal":
		resp.UserInfo, err = vip.GetService().PaypalTransactionCheck(ctx, ctxUser.UID, req)
	case "stripe":
		resp.Stripe, err = vip.GetService().StripeTransactionCheck(ctx, ctxUser.UID, req)
	}
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}
