package user

import (
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	"vlab/app/service/user/login"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// TODO：绑定邮箱与密码

// UserLogin .
func UserLoginThird(ctx *gin.Context) {
	req := userDto.UserLoginThirdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := login.NewLoginManager().Login(ctx, userDao.AuthType(req.AuthType), req.AuthToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// // UserLogin .
// func UserLogin(ctx *gin.Context) {
// 	req := userDto.UserLoginReq{}
// 	if err := ctx.ShouldBind(&req); err != nil {
// 		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
// 		return
// 	}

// 	ret, err := user.GetService().UserLogin(ctx, req.Account, req.Pwd)
// 	if err != nil {
// 		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
// 		return
// 	}
// 	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
// }

// UserLoginAudit .
func UserLoginAudit(ctx *gin.Context) {
	ret, err := login.ScriptUserLogin(ctx, login.GetAuditLoginUid(ctx))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// ScriptUserLogin .
func ScriptUserLogin(ctx *gin.Context) {
	req := userDto.AdminUserInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := login.ScriptUserLogin(ctx, req.ID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UserLogout .
func UserLogout(ctx *gin.Context) {
	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	userDao.GetRepo().RedisClearUserToken(ctx, ctxUser.UID)
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
