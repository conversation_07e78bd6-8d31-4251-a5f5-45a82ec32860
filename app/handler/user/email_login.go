package user

import (
	userDto "vlab/app/dto/user"
	userSrv "vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// EmailSendCode 发送邮箱验证码
func EmailSendCode(ctx *gin.Context) {
	req := userDto.EmailSendCodeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := userSrv.GetService().SendVerificationCode(ctx, &req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// EmailVerifyCode 验证邮箱验证码
func EmailVerifyCode(ctx *gin.Context) {
	req := userDto.EmailVerifyCodeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	resp, err := userSrv.GetService().VerifyCode(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// EmailRegister 邮箱注册
func EmailRegister(ctx *gin.Context) {
	req := userDto.EmailRegisterReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	resp, err := userSrv.GetService().Register(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// EmailLogin 邮箱登录
func EmailLogin(ctx *gin.Context) {
	req := userDto.EmailLoginReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	resp, err := userSrv.GetService().Login(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// EmailResetPassword 邮箱重置密码
func EmailResetPassword(ctx *gin.Context) {
	req := userDto.EmailResetPasswordReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := userSrv.GetService().ResetPassword(ctx, &req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

func EmailCheck(ctx *gin.Context) {
	req := userDto.EmailCheckReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := userSrv.GetService().CheckEmailExists(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
