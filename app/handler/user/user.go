package user

import (
	userDto "vlab/app/dto/user"
	"vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GetUserInfo 获取用户信息
func GetUserInfo(ctx *gin.Context) {
	ctxUser, _ := helper.GetCtxUser(ctx)
	ret, err := user.GetService().AdminUserInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GetDeviceInfo 获取设备信息
func GetDeviceInfo(ctx *gin.Context) {
	deviceID, _ := helper.GetDeviceNo(ctx)
	ret, err := user.GetService().GetDeviceInfo(ctx, deviceID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UserEdit 设置
func UserEdit(ctx *gin.Context) {
	req := userDto.UserEditReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().UserEdit(ctx, ctxUser.UID, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

// UserEditPwd .
func UserEditPwd(ctx *gin.Context) {
	req := userDto.UserEditPwdReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, _ := helper.GetCtxUser(ctx)
	if err := user.GetService().UserEditPwd(ctx, ctxUser.UID, req.OldPwd, req.NewPwd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
