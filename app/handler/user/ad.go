package user

import (
	userDto "vlab/app/dto/user"
	"vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdWatch .
func AdWatch(ctx *gin.Context) {
	var (
		ctxUser, err = helper.GetCtxUser(ctx)
		resp         = &userDto.WatchAdResp{}
		deviceID, _  = helper.GetDeviceNo(ctx)
	)

	if ctxUser.UID > 0 {
		resp, err = user.GetService().UserWatchAd(ctx, ctxUser.UID, deviceID)
	} else {
		resp, err = user.GetService().VisitorWatchAd(ctx, deviceID)
	}
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}
