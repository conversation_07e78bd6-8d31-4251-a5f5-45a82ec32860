package user

import (
	userDto "vlab/app/dto/user"
	userService "vlab/app/service/user"
	"vlab/app/service/user/login"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// GetUserAuthMethods 获取用户已绑定的认证方式
func GetAuthMethods(ctx *gin.Context) {
	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}

	// 调用service层
	authList := userService.GetService().GetUserAuthMethods(ctx, ctxUser.UID)

	// 格式化响应数据
	var authMethods []map[string]interface{}
	for _, auth := range authList {
		authMethods = append(authMethods, map[string]interface{}{
			"auth_type":  auth.AuthType,
			"auth_email": auth.AuthEmail,
		})
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), map[string]interface{}{
		"auth_methods": authMethods,
	})
}

// BindAuth 统一绑定认证方式
func BindAuth(ctx *gin.Context) {
	var req userDto.BindAuthReq
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}

	// 调用service层
	switch req.AuthType {
	case 1: // 苹果
		if err := login.NewLoginManager().BindApple(ctx, ctxUser.UID, req.AuthToken); err != nil {
			helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
			return
		}
	case 2: // 谷歌
		if err := login.NewLoginManager().BindGoogle(ctx, ctxUser.UID, req.AuthToken); err != nil {
			helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
			return
		}
	case 4: // 邮箱
		if err := userService.GetService().BindEmail(ctx, ctxUser.UID, req.Email, req.Code, req.Password); err != nil {
			helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
			return
		}
	default:
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := userService.GetService().AdminUserInfo(ctx, ctxUser.UID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UnbindAuth 解绑认证方式
func UnbindAuth(ctx *gin.Context) {

	var req userDto.UnbindAuthReq
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ctxUser, err := helper.GetCtxUser(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
		return
	}

	// 调用service层
	authBindService := userService.GetService()
	if err := authBindService.UnbindAuth(ctx, ctxUser.UID, req.AuthType, req.Password); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), err.Error())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), &userDto.BindSuccessResp{Message: "Unbind successfully"})
}
