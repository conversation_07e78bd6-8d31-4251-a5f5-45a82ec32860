package user

import (
	"vlab/app/dao/vip/product"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// VipProductList 列表
func VipProductList(ctx *gin.Context) {
	region, err := helper.GetRegion(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	ret, err := product.GetRepo().RedisRegionProductList(ctx, region)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	retList := []userDto.VipProductListItem{}
	for _, val := range ret {
		retList = append(retList, userDto.VipProductListItem{
			ID:       val.ID,
			Price:    val.Price,
			Currency: val.Currency,
		})
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), retList)
}
