package user

import (
	"encoding/json"
	"fmt"
	"net/url"

	userDto "vlab/app/dto/user"
	"vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"
	"vlab/pkg/util/timeUtil"

	channelKeyDao "vlab/app/dao/resource_channel_key"

	"github.com/gin-gonic/gin"
)

const (
	AesKey = "196b9a0f7x3c6d9e"
)

// EncryptGet .
func EncryptGet(ctx *gin.Context) {
	encrypt := ctx.GetHeader("encrypt")
	if encrypt != AesKey {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	params := map[string]string{}
	for key, _ := range ctx.Request.URL.Query() {
		params[key] = ctx.Request.URL.Query().Get(key)
	}
	ret, _ := json.Marshal(params)
	log.Ctx(ctx).WithField("params", params).Info("EncryptGet params")

	enData, err := helper.AesCbcEncrypt(string(ret), helper.AesReqKey, helper.AesIV)
	if err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	resp := &userDto.EncryptResp{
		OriginalData: userDto.EncryptOriginalResp{
			Timestamp:  timeUtil.GetNowTimestampByZone(ctx),
			ReqId:      helper.RandCode(32),
			Param:      string(ret),
			EncryptKey: helper.AesReqKey,
		},
		OriginalEncryptData: enData,
		EncryptData:         url.QueryEscape(enData),
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// DecryptGet .
func DecryptGet(ctx *gin.Context) {
	_, channelKeyInfo, err := helper.CheckChannel(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	param := ctx.Query("p")
	log.Ctx(ctx).WithField("param", param).Info("DecryptGet param")
	if param == "" {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	deData, err := helper.AesCbcDecrypt(param, channelKeyInfo.ReqKey, channelKeyInfo.IV)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("http.GET helper.AesCbcDecrypt err")
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		ctx.Abort()
		return
	}

	var params map[string]interface{}
	if err := json.Unmarshal([]byte(deData), &params); err != nil {
		log.Ctx(ctx).WithField("params", params).WithField("deData", deData).Info("json.Unmarshal param")
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), params)
}

// DecryptResp .
func DecryptResp(ctx *gin.Context) {
	_, channelKeyInfo, err := helper.CheckChannel(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	type DecryptRespReq struct {
		Param string `json:"param" binding:"required"`
	}
	param := DecryptRespReq{}
	if err := ctx.ShouldBind(&param); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	log.Ctx(ctx).WithField("param", param).Info("DecryptGet param")
	if param.Param == "" {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	deData, err := helper.AesCbcDecrypt(param.Param, channelKeyInfo.RespKey, channelKeyInfo.IV)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("http.GET helper.AesCbcDecrypt err")
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	var params interface{}
	if err := json.Unmarshal([]byte(deData), &params); err != nil {
		log.Ctx(ctx).WithField("params", params).WithField("deData", deData).Info("json.Unmarshal param")
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), params)
}

// EncryptPost .
func EncryptPost(ctx *gin.Context) {
	encrypt := ctx.GetHeader("encrypt")
	if encrypt != AesKey {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	channelKeyMap, _ := channelKeyDao.GetRepo().RedisEnableChannelKeyMap(ctx)
	channelKeyInfo := channelKeyMap[3]

	params := map[string]interface{}{}
	if err := ctx.ShouldBind(&params); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	log.Ctx(ctx).WithField("params", params).Info("EncryptPost params")

	ts, reqID := timeUtil.GetNowTimestampByZone(ctx), ctxUtil.GenRequestID(ctxUtil.ReqTypeS)
	msg := fmt.Sprintf("%s-%v-%s", params["path"], ts, reqID)
	signStr := helper.HmacHashSignMsg(ctx, msg, channelKeyInfo.SignKey)

	ret, _ := json.Marshal(params)
	enData, err := helper.AesCbcEncrypt(string(ret), helper.AesReqKey, helper.AesIV)
	if err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	resp := map[string]interface{}{
		"msg":           msg,
		"signStr":       signStr,
		"time":          ts,
		"uuid":          reqID,
		"originalParam": params,
		"encryptData":   enData,
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), resp)
}

// DecryptPost .
func DecryptPost(ctx *gin.Context) {
	req := &userDto.UserLoginReq{}
	if err := ctx.ShouldBind(req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := user.GetService().UserLogin(ctx, req.Account, req.Pwd)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
