package statistic

import (
	"vlab/app/common/dbs"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	statisticDto "vlab/app/dto/statistic"
	"vlab/app/service/statistic"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
)

// AdminShowDailyActive 剧日活统计
func AdminShowDailyActive(ctx *gin.Context) {
	req := statisticDto.GetShowDailyActiveReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if !timeUtil.JudgeValidStartEndDate(req.StartDate, req.EndDate, dbs.StatisticDateInterval) {
		helper.AppResp(ctx, ecode.TimeInvalidErr.Code(), ecode.TimeInvalidErr.Message())
		return
	}

	ret, err := statistic.GetService().ShowDailyActive(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShowDailyPlayNum 剧日播放量统计
func AdminShowDailyPlayNum(ctx *gin.Context) {
	req := statisticDto.GetShowDailyActiveReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if !timeUtil.JudgeValidStartEndDate(req.StartDate, req.EndDate, dbs.StatisticDateInterval) {
		helper.AppResp(ctx, ecode.TimeInvalidErr.Code(), ecode.TimeInvalidErr.Message())
		return
	}

	req.LogType = watchVideoPv.LogTypePlay
	ret, err := statistic.GetService().ShowDailyCount(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShowPlayNumTop 剧播放量top
func AdminShowPlayNumTop(ctx *gin.Context) {
	req := statisticDto.GetShowPlaynumTopReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if !timeUtil.JudgeValidStartEndDate(req.StartDate, req.EndDate, dbs.StatisticDateInterval) {
		helper.AppResp(ctx, ecode.TimeInvalidErr.Code(), ecode.TimeInvalidErr.Message())
		return
	}

	req.LogType = watchVideoPv.LogTypePlay
	ret, err := statistic.GetService().ShowCountTop(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShowDailyDownloadNum 剧日下载量统计
func AdminShowDailyDownloadNum(ctx *gin.Context) {
	req := statisticDto.GetShowDailyActiveReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if !timeUtil.JudgeValidStartEndDate(req.StartDate, req.EndDate, dbs.StatisticDateInterval) {
		helper.AppResp(ctx, ecode.TimeInvalidErr.Code(), ecode.TimeInvalidErr.Message())
		return
	}

	req.LogType = watchVideoPv.LogTypeDownload
	ret, err := statistic.GetService().ShowDailyCount(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminShowDownloadNumTop 剧播放量top
func AdminShowDownloadNumTop(ctx *gin.Context) {
	req := statisticDto.GetShowPlaynumTopReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if !timeUtil.JudgeValidStartEndDate(req.StartDate, req.EndDate, dbs.StatisticDateInterval) {
		helper.AppResp(ctx, ecode.TimeInvalidErr.Code(), ecode.TimeInvalidErr.Message())
		return
	}

	req.LogType = watchVideoPv.LogTypeDownload
	ret, err := statistic.GetService().ShowCountTop(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
