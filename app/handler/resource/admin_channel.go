package resource

import (
	"vlab/app/common/dbs"
	resourceDto "vlab/app/dto/resource"
	"vlab/app/service/resource"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminChannelList .
func AdminChannelList(c *gin.Context) {
	req := resourceDto.AdminChannelListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	if req.Page <= 0 {
		req.Page = dbs.DefaultPage
	}
	if req.Limit <= 0 {
		req.Limit = dbs.DefaultLimit
	}

	ret, err := resource.GetService().AdminChannelList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminChannelSet .
func AdminChannelSet(c *gin.Context) {
	req := resourceDto.AdminChannelSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := resource.GetService().AdminChannelSet(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminChannelOperate .
func AdminChannelOperate(c *gin.Context) {
	req := resourceDto.AdminChannelOperateReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}
	if err := resource.GetService().AdminChannelOperate(c, req.ID, dbs.OperateAction(req.Action), req.Value); err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}

	helper.AppResp(c, ecode.OK.Code(), ecode.OK.Message())
}

// AdminChannelAll .
func AdminChannelAll(c *gin.Context) {
	req := resourceDto.AdminChannelAllReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := resource.GetService().AdminChannelAll(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
