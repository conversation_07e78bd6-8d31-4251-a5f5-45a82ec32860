package resource

import (
	resourceDto "vlab/app/dto/resource"
	"vlab/app/service/resource"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

func VersionCheck(c *gin.Context) {
	req := &resourceDto.VersionCheckReq{}

	if cID, ok := c.Get("channelID"); ok && cID != nil {
		req.ChannelID = gconv.Uint64(cID)
	} else {
		helper.AppResp(c, ecode.ParamErr.Code(), "channelID is required")
		return
	}
	if vID, ok := c.Get("versionID"); ok && vID != nil {
		req.VersionID = gconv.Uint64(vID)
	} else {
		helper.AppResp(c, ecode.ParamErr.Code(), "versionID is required")
		return
	}

	ret, err := resource.GetService().VersionCheck(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func GetConfig(ctx *gin.Context) {

	ret, err := resource.GetService().GetConfig(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func ErrorReport(ctx *gin.Context) {
	req := resourceDto.ErrorReportReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	if err := resource.GetService().ErrorReport(ctx, req); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
