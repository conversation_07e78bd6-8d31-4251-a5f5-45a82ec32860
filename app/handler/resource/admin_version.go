package resource

import (
	"vlab/app/common/dbs"
	resourceDto "vlab/app/dto/resource"
	"vlab/app/service/resource"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AdminVersionList .
func AdminVersionList(c *gin.Context) {
	req := resourceDto.AdminVersionListReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	if req.Page <= 0 {
		req.Page = dbs.DefaultPage
	}
	if req.Limit <= 0 {
		req.Limit = dbs.DefaultLimit
	}

	ret, err := resource.GetService().AdminVersionList(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminVersionSet .
func AdminVersionSet(c *gin.Context) {
	req := resourceDto.AdminVersionSetReq{}
	if err := c.ShouldBind(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), helper.CustomTranslate(err, &req))
		return
	}

	ret, err := resource.GetService().AdminVersionSet(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AdminVersionAll .
func AdminVersionAll(c *gin.Context) {
	req := resourceDto.AdminVersionAllReq{}
	if err := c.ShouldBindQuery(&req); err != nil {
		helper.AppResp(c, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	ret, err := resource.GetService().AdminVersionAll(c, req)
	if err != nil {
		helper.AppResp(c, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(c, ecode.OK.Code(), ecode.OK.Message(), ret)
}
