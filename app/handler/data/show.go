package data

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	dataDto "vlab/app/dto/data"
	"vlab/app/service/data"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func DataShowSearch(ctx *gin.Context) {
	req := dataDto.DataShowSearchReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}
	if req.Page <= 0 {
		req.Page = dbs.DefaultPage
	}
	if req.Limit <= 0 {
		req.Limit = dbs.DefaultLimit
	}

	ret, err := data.GetService().DataShowSearch(ctx, &req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
