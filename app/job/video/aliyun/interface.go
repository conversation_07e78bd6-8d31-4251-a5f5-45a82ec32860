package aliyun

import (
	"sync"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
)

type Job interface {
}

type Entry struct {
	VideoMpsRepo videoMpsDao.Repo
	VideoRepo    videoDao.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetJob() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		VideoMpsRepo: videoMpsDao.GetRepo(),
		VideoRepo:    videoDao.GetRepo(),
	}
}
