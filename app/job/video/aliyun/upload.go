package aliyun

import (
	"strings"
	mpsApi "vlab/app/api/aliyun/mps"
	vodApi "vlab/app/api/aliyun/vod"
	"vlab/app/common/dbs"
	videoMpsDao "vlab/app/dao/content_video_mps"
	"vlab/pkg/helper"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/vod"
)

// UpdateVodPullJobStatus .
func (e *Entry) UpdateVodPullJobStatus() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusIng), OperateType: uint32(videoMpsDao.OtVodPull)}
	)
	if total, err = e.VideoMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = int(mpsApi.QueryTransJobNum)
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}
	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		var (
			jobIds = tempList.GetJobIds()
			jobMap = map[string]vod.UrlUploadJobInfoDTO{}
		)

		uploadRet, err := vodApi.GetApi().GetURLUploadInfos(ctx, jobIds)
		if err != nil {
			return err
		}
		for _, jobInfo := range uploadRet.URLUploadInfoList {
			jobMap[jobInfo.JobId] = jobInfo
		}

		for _, val := range tempList {
			if jobInfo, ok := jobMap[val.JobID]; ok {
				if strings.ToLower(jobInfo.Status) == "pending" {
					continue
				}
				var status uint32
				if jobInfo.ErrorCode != "" {
					status = uint32(videoMpsDao.MpsStatusInvalid)
				}
				if strings.ToLower(jobInfo.Status) == "success" {
					status = uint32(videoMpsDao.MpsStatusSuccess)
				}

				if err = e.VideoMpsRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{
					"status":        status,
					"video_key":     jobInfo.MediaId,
					"mps_video_key": jobInfo.MediaId,
				}); err != nil {
					return err
				}
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
		// time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(total)))
	}

	return nil
}

// UpdateVodUploadJobStatus .
func (e *Entry) UpdateVodUploadJobStatus() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoMpsDao.Filter{
			Status:         uint32(videoMpsDao.MpsStatusIng),
			OperateType:    uint32(videoMpsDao.OtVodUpload),
			ResolutionZero: true,
		}
	)
	if total, err = e.VideoMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = int(mpsApi.QueryTransJobNum)
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}

	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		vodKeys := tempList.GetVideoKeys()
		vodMap, err := vodApi.GetApi().GetVideoInfos(ctx, vodKeys)
		if err != nil {
			return err
		}

		for _, val := range tempList {
			// Uploading：上传中，UploadFail：上传失败，UploadSucc：上传完成，Transcoding：转码中，TranscodeFail：转码失败，Blocked：屏蔽，Normal：正常。
			if vod, ok := vodMap[val.VideoKey]; ok {
				var (
					status    uint32
					vodStatus = strings.ToLower(vod.Status)
				)
				if vodStatus == "uploadfail" || vodStatus == "transcodefail" {
					status = uint32(videoMpsDao.MpsStatusFailed)
				}
				if vodStatus == "normal" {
					status = uint32(videoMpsDao.MpsStatusSuccess)
				}
				if status == dbs.False {
					continue
				}
				if err = e.VideoMpsRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{"status": status}); err != nil {
					return err
				}
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
		// time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(total)))
	}

	return nil
}
