package aliyun

import (
	"time"
	"vlab/app/api/aliyun/common"
	mpsApi "vlab/app/api/aliyun/mps"
	"vlab/app/common/dbs"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"
)

// UpdateTranscodeJobStatus .
func (e *Entry) UpdateTranscodeJobStatus() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusIng), OperateType: uint32(videoMpsDao.OtOssUpload)}
	)
	if total, err = e.VideoMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = int(mpsApi.QueryTransJobNum)
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}

	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		jobIds := tempList.GetJobIds()
		jobStatusMap, err := mpsApi.GetApi().QueryTranscodeJobStatus(ctx, jobIds)
		if err != nil {
			return err
		}

		for _, val := range tempList {
			if status, ok := jobStatusMap[val.JobID]; ok {
				if err = e.VideoMpsRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{"status": status}); err != nil {
					return err
				}
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
		// time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(total)))
	}

	return nil
}

// ResubmitTranscodeFailedJob .
func (e *Entry) ResubmitTranscodeFailedJob() error {
	var (
		ctx      = helper.GenGinCtx()
		dataList = videoMpsDao.ModelList{}
		err      error
		filter   = &videoMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusFailed), LtRetry: videoMpsDao.RetryNum}
	)
	if dataList, err = e.VideoMpsRepo.FindByFilter(ctx, filter); err != nil {
		return err
	}
	if len(dataList) == 0 {
		return nil
	}

	videoList, err := e.VideoRepo.FindByFilter(ctx, &videoDao.Filter{IDs: dataList.GetVideoIds()})
	if err != nil {
		return err
	}
	videoMap := videoList.GetIDMap()
	log.Ctx(ctx).WithField("dataList", dataList).WithField("videoMap", videoMap).Info("ResubmitTranscodeJobDataListAndVideoMap")

	for _, val := range dataList {
		if videoInfo, ok := videoMap[val.VideoID]; ok {
			val.VideoKey = videoInfo.VideoPath
			if _, err = mpsApi.GetApi().SubmitTranscodeJob(ctx, val, []common.Resolution{val.Resolution}); err != nil {
				return err
			}
			time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(dbs.True)))
		}
	}
	return nil
}
