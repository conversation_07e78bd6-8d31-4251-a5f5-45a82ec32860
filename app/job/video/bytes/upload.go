package bytes

import (
	"fmt"
	"strings"
	"vlab/app/api/bytes/vod"
	"vlab/app/common/dbs"
	videoMpsDao "vlab/app/dao/content_video_mps"
	videoCpMpsDao "vlab/app/dao/content_video_mps/cp"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod/models/business"
	"github.com/gogf/gf/v2/util/gconv"
)

// UpdateVodPullJobStatus .
func (e *Entry) UpdateVodPullJobStatus() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusIng), OperateType: uint32(videoMpsDao.OtByteVodPull)}
	)

	if total, err = e.VideoMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = 20 // 每次最多处理20个任务
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}

	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		var (
			jobIds = tempList.GetJobIds()
			jobMap = map[string]*business.VodURLSet{}
		)
		resp, err := vod.GetApi().QueryUploadTaskInfo(ctx, jobIds)
		if err != nil {
			return err
		}
		if len(resp.GetData().GetMediaInfoList()) == 0 {
			log.Ctx(ctx).Error("vod.GetApi().QueryUploadTaskInfo resp.GetData().GetMediaInfoList()==0")
			return fmt.Errorf("bytes QueryUploadTaskInfo error")
		}

		for _, jobInfo := range resp.Data.MediaInfoList {
			jobMap[jobInfo.JobId] = jobInfo
		}

		for _, val := range tempList {
			if jobInfo, ok := jobMap[val.JobID]; ok {
				var (
					status  uint32
					runtime int64
				)
				if strings.ToLower(jobInfo.State) == "success" {
					status = uint32(videoMpsDao.MpsStatusSuccess)
				} else if strings.ToLower(jobInfo.State) == "failed" {
					status = uint32(videoMpsDao.MpsStatusFailed)
				} else {
					continue
				}
				runtime = gconv.Int64(jobInfo.SourceInfo.Duration)

				if err = e.VideoMpsRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{
					"status":        status,
					"runtime":       runtime,
					"video_key":     jobInfo.Vid,
					"mps_video_key": jobInfo.Vid,
				}); err != nil {
					return err
				}
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
		// time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(total)))
	}

	return nil
}

// UpdateVodPullJobStatusCp .
func (e *Entry) UpdateVodPullJobStatusCp() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoCpMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusIng), OperateType: uint32(videoCpMpsDao.OtByteVodPull)}
	)

	if total, err = e.VideoCpMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = 20 // 每次最多处理20个任务
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}

	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoCpMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		var (
			jobIds = tempList.GetJobIds()
			jobMap = map[string]*business.VodURLSet{}
		)
		resp, err := vod.GetApi().QueryUploadTaskInfo(ctx, jobIds)
		if err != nil {
			return err
		}
		if len(resp.GetData().GetMediaInfoList()) == 0 {
			log.Ctx(ctx).Error("vod.GetApi().QueryUploadTaskInfo resp.GetData().GetMediaInfoList()==0")
			return fmt.Errorf("bytes QueryUploadTaskInfo error")
		}

		for _, jobInfo := range resp.Data.MediaInfoList {
			jobMap[jobInfo.JobId] = jobInfo
		}

		for _, val := range tempList {
			if jobInfo, ok := jobMap[val.JobID]; ok {
				var status uint32
				if strings.ToLower(jobInfo.State) == "success" {
					status = uint32(videoMpsDao.MpsStatusSuccess)
				} else if strings.ToLower(jobInfo.State) == "failed" {
					status = uint32(videoMpsDao.MpsStatusFailed)
				} else {
					continue
				}

				if err = e.VideoCpMpsRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{
					"status":    status,
					"video_key": jobInfo.Vid,
				}); err != nil {
					return err
				}
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
		// time.Sleep(timeUtil.GetLadderTime(timeUtil.LadderNum(total)))
	}

	return nil
}

// SyncMpsCpFinishToMps .
func (e *Entry) SyncMpsCpSuccessToMps() error {
	var (
		ctx    = helper.GenGinCtx()
		total  int64
		err    error
		filter = &videoCpMpsDao.Filter{Status: uint32(videoMpsDao.MpsStatusSuccess), WaitMigrate: dbs.True, OperateType: uint32(videoCpMpsDao.OtByteVodPull)}
	)

	if total, err = e.VideoCpMpsRepo.CountByFilter(ctx, filter); err != nil {
		return err
	}
	filter.Limit = 20 // 每次最多处理20个任务
	filter.Sort = dbs.CommonSort{
		Field:  dbs.SortFieldID,
		Method: dbs.SortMethodDesc,
	}

	for {
		if total <= 0 {
			break
		}

		tempList, err := e.VideoCpMpsRepo.FindByFilter(ctx, filter)
		if err != nil {
			return err
		}
		tempLen := len(tempList)
		if tempLen == 0 {
			break
		}

		for _, val := range tempList {

			tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
			if err := func() (err error) {
				if err = e.VideoMpsRepo.UpdateMapByUdxWithTx(ctx, tx, val.VideoID, uint32(val.Resolution), map[string]interface{}{
					"video_key":    val.VideoKey,
					"job_id":       val.JobID,
					"operate_type": uint32(videoMpsDao.OtByteVodPull),
				}); err != nil {
					return
				}

				if err = e.VideoCpMpsRepo.UpdateMapByIDWithTx(ctx, tx, val.ID, map[string]interface{}{
					"wait_migrate": dbs.StatusDisable,
				}); err != nil {
					return

				}

				return tx.Commit().Error
			}(); err != nil {
				tx.Rollback()
				log.Ctx(ctx).WithError(err).Error("txErr UploadAliyunVideoUrlToBytes")
				return err
			}
		}

		filter.LtID = tempList[tempLen-1].ID
		total -= int64(tempLen)
	}

	return nil
}
