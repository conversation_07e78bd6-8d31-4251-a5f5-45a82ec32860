package lok

import (
	"github.com/gin-gonic/gin"
	"vlab/app/api/auto"
)

func (e *Entry) ImportLokToShow(ctx *gin.Context, req *auto.DetailResp) (showID uint64, err error) {
	showID, err = ToShowVWXYZ(ctx, req)
	if err != nil {
		return 0, err
	}
	return showID, nil
}

func (e *Entry) ImportLokToEpisode(ctx *gin.Context, req *auto.PlayReq) (err error) {
	err = ToEpisode(ctx, req)
	if err != nil {
		return err
	}
	return nil
}
