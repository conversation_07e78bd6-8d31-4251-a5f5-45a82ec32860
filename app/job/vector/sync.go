package vector

import (
	"context"
	"fmt"
	"sync"
	"time"

	"vlab/app/common/dbs"
	showDao "vlab/app/dao/content_show"
	showService "vlab/app/service/show"
	"vlab/config"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

var (
	jobInit     sync.Once
	jobInstance *Job
)

// Job 向量同步任务
type Job struct {
	showService showService.Entry
	ctx         context.Context
}

// GetJob 获取Job实例
func GetJob() *Job {
	if jobInstance == nil {
		jobInit.Do(func() {
			// 获取show service
			service := showService.GetService()
			// 转换为Entry类型
			if entry, ok := service.(*showService.Entry); ok {
				jobInstance = &Job{
					showService: *entry,
					ctx:         context.Background(),
				}
			} else {
				// 如果转换失败，创建一个新的Entry
				return
			}
		})
	}
	return jobInstance
}

// SyncRecentUpdatedShows 同步最近更新的show数据到向量数据库
func (j *Job) SyncRecentUpdatedShows() error {
	// 创建gin context用于服务调用
	ginCtx := &gin.Context{}

	// 获取时间窗口（默认8小时）
	syncWindow := 8 * time.Hour
	if config.VikingDBCfg != nil && config.VikingDBCfg.SyncWindowHours > 0 {
		syncWindow = time.Duration(config.VikingDBCfg.SyncWindowHours) * time.Hour
	}

	updatedAfter := time.Now().Add(-syncWindow)

	log.WithContext(j.ctx).
		WithField("updated_after", updatedAfter.Format(dbs.TimeDateFormatFull)).
		Info("开始同步向量数据")

	// 查询最近更新的show数据
	filter := &showDao.Filter{
		UpdatedAfter: &updatedAfter,
		//Status:       1, // 只同步启用状态的数据
	}

	// 先查询总数
	totalCount, err := j.showService.ShowRepo.CountByFilter(ginCtx, filter)
	if err != nil {
		log.WithContext(j.ctx).WithError(err).Error("查询数据总数失败")
		return fmt.Errorf("查询数据总数失败: %w", err)
	}

	if totalCount == 0 {
		log.WithContext(j.ctx).Info("没有需要同步的数据")
		return nil
	}

	log.WithContext(j.ctx).
		WithField("total_count", totalCount).
		Info("找到需要同步的数据")

	// 查询批量大小（用于从数据库分批查询数据）
	queryBatchSize := 200
	if config.VikingDBCfg != nil && config.VikingDBCfg.QueryBatchSize > 0 {
		queryBatchSize = config.VikingDBCfg.QueryBatchSize
	}

	// 处理批量大小（用于批量同步到向量数据库）
	processBatchSize := 100
	if config.VikingDBCfg != nil && config.VikingDBCfg.BatchSize > 0 {
		processBatchSize = config.VikingDBCfg.BatchSize
	}

	successCount := 0
	failedCount := 0

	// 计算总页数
	totalPages := int((totalCount + int64(queryBatchSize) - 1) / int64(queryBatchSize))

	// 分页查询并处理
	for page := 1; page <= totalPages; page++ {
		// 分页查询数据
		_, shows, err := j.showService.ShowRepo.DataPageList(ginCtx, filter, page, queryBatchSize)
		if err != nil {
			log.WithContext(j.ctx).
				WithError(err).
				WithField("page", page).
				Error("分页查询数据失败")
			// 继续处理下一页，不中断整个流程
			failedCount += queryBatchSize
			continue
		}

		if len(shows) == 0 {
			continue
		}

		log.WithContext(j.ctx).
			WithField("page", page).
			WithField("page_total", totalPages).
			WithField("batch_size", len(shows)).
			Info("查询到一批数据")

		// 对查询到的数据进行分批处理
		for i := 0; i < len(shows); i += processBatchSize {
			end := i + processBatchSize
			if end > len(shows) {
				end = len(shows)
			}

			batch := shows[i:end]

			// 调用Service层的批量同步方法
			err := j.showService.BatchSyncShowsToVector(ginCtx, batch)
			if err != nil {
				log.WithContext(j.ctx).
					WithError(err).
					WithField("page", page).
					WithField("batch_start", i).
					WithField("batch_end", end).
					Error("批次同步失败")
				failedCount += len(batch)
				continue
			}

			successCount += len(batch)

			log.WithContext(j.ctx).
				WithField("page", page).
				WithField("batch_start", i).
				WithField("batch_end", end).
				WithField("progress", fmt.Sprintf("Page %d/%d, Batch %d-%d", page, totalPages, i, end)).
				Info("批次同步成功")
		}
	}

	// 记录同步结果
	log.WithContext(j.ctx).
		WithField("total", totalCount).
		WithField("success", successCount).
		WithField("failed", failedCount).
		Info("向量同步任务完成")

	return nil
}

// SyncSingleShow 同步单个show到向量数据库（用于实时更新）
func (j *Job) SyncSingleShow(showID uint64) error {
	ginCtx := &gin.Context{}

	log.WithContext(j.ctx).
		WithField("show_id", showID).
		Info("开始同步单个剧集向量")

	// 查询show数据
	filter := &showDao.Filter{
		ID: showID,
	}

	shows, err := j.showService.ShowRepo.FindByFilter(ginCtx, filter)
	if err != nil {
		log.WithContext(j.ctx).WithError(err).Error("查询剧集失败")
		return fmt.Errorf("查询剧集失败: %w", err)
	}

	if len(shows) == 0 {
		log.WithContext(j.ctx).Warn("剧集不存在")
		return fmt.Errorf("剧集不存在: %d", showID)
	}

	// 同步到向量数据库
	err = j.showService.BatchSyncShowsToVector(ginCtx, shows)
	if err != nil {
		log.WithContext(j.ctx).WithError(err).Error("同步失败")
		return fmt.Errorf("同步失败: %w", err)
	}

	log.WithContext(j.ctx).Info("单个剧集向量同步成功")
	return nil
}
