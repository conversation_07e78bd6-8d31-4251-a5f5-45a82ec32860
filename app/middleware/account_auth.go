package middleware

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"vlab/app/common/dbs"
	adminAccount "vlab/app/dao/admin_account"
	adminMenu "vlab/app/dao/admin_menu"
	adminRole "vlab/app/dao/admin_role"
	"vlab/app/service/admin"
	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// CheckAccountLogin .
func CheckAccountLogin() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountLogin ctx.IsAborted")
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		appClaims, err := util.ParseToken(authToken)
		if err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}
		log.WithField(ctx.Request.Context(), "appClaims", appClaims).Info("util.ParseToken ret")

		token, err := adminAccount.GetRepo().RedisGetAccountToken(ctx, appClaims.UserID)
		if token == "" || err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithField("token", token).Warn("RedisGetAccountToken err")
			ctx.Abort()
			return
		}

		if token != authToken {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}

		if !adminRole.GetRepo().JudgeRoleIsValid(ctx, appClaims.RoleID) {
			adminAccount.GetRepo().RedisClearAccountToken(ctx, appClaims.UserID)
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}

		ctx.Set("ctxAccount", &helper.CtxAccount{
			AccountID: appClaims.UserID,
			RoleID:    appClaims.RoleID,
		})

		ctx.Next()
	}
}

// CheckAccountAuth .
func CheckAccountAuth() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountAuth ctx.IsAborted")
			return
		}

		ctxAccount, err := helper.GetCtxAccount(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		if admin.GetService().JudgeIsAdmin(ctxAccount.RoleID) {
			return
		}
		var (
			path               = strings.TrimPrefix(ctx.Request.URL.Path, "/")
			matchMenuList      = adminMenu.ModelList{}
			matchMenuIds       = []uint64{}
			menuList           = adminMenu.ModelList{}
			roleMenuIds        = []uint64{}
			hasAuth       bool = false
		)

		if menuList, err = adminMenu.GetRepo().RedisMenuList(ctx); err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		for _, menu := range menuList {
			beAuth := strings.TrimPrefix(menu.BeAuth, "/")
			if fmt.Sprintf("%s/%s", config.AppCfg.AppName, beAuth) == path {
				matchMenuList = append(matchMenuList, menu)
				matchMenuIds = append(matchMenuIds, menu.ID)
				if menu.IsRoot == dbs.True {
					helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
					ctx.Abort()
					return
				}
			}
		}

		if len(matchMenuList) == 0 {
			helper.AppResp(ctx, ecode.MenuNotExistErr.Code(), ecode.MenuNotExistErr.Message())
			ctx.Abort()
			return
		}

		if roleMenuIds, err = adminRole.GetRepo().RedisRoleMenuIds(ctx, ctxAccount.RoleID); err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		for _, roleMenuId := range roleMenuIds {
			if lo.Contains[uint64](matchMenuIds, roleMenuId) {
				hasAuth = true
				break
			}
		}

		if !hasAuth {
			helper.AppResp(ctx, ecode.MenuHasNotAuthErr.Code(), ecode.MenuHasNotAuthErr.Message())
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}

// AdminOperationLog .
func AdminOperationLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.Method == http.MethodGet {
			ctx.Next()
			return
		}

		// 获取请求头与请求体
		reqHeaders := ctx.Request.Header
		headers, _ := json.Marshal(&reqHeaders)

		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		ctx.Next()

		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("AdminOperationLog ctx.IsAborted")
			return
		}

		isAbort, err := admin.GetAdminLogEntity().Producer(ctx, headers, body)
		if isAbort {
			ctx.Abort()
			return
		}

		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
	}
}
