// Copyright 2014 <PERSON><PERSON>.  All rights reserved.
// Use of this source code is governed by a MIT style
// license that can be found in the LICENSE file.

package middleware

import (
	"bytes"
	"fmt"
	"io/ioutil"
	"net"
	"net/http/httputil"
	"os"
	"runtime"
	"strings"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

const timeLimit = 5000 // 5s
var (
	dunno     = []byte("???")
	centerDot = []byte("·")
	dot       = []byte(".")
	slash     = []byte("/")
)

// RecoveryWithWriter returns a middleware for a given writer that recovers from any panics and writes a 500 if there was one.
func RecoveryWithWriter() gin.HandlerFunc {
	return func(c *gin.Context) {
		httpRequest, _ := httputil.DumpRequest(c.Request, true)
		defer func() {
			if err := recover(); err != nil {
				// Check for a broken connection, as it is not really a
				// condition that warrants a panic stack trace.
				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					if se, ok := ne.Err.(*os.SyscallError); ok {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
				}

				stack := stack(3)
				headers := strings.Split(string(httpRequest), "\r\n")

				var fields = logrus.Fields{}
				nowTime := timeUtil.NowToDateTimeStringByZone(c)
				if brokenPipe {
					fields = logrus.Fields{"error": err, "req": headers}
				} else if gin.IsDebugging() {
					fields = logrus.Fields{"error": err, "req": headers, "now": nowTime, "stack": string(stack)}
				} else {
					fields = logrus.Fields{"error": err, "now": nowTime, "stack": string(stack)}
				}
				log.Ctx(c).WithFields(fields).Error("RecoveryWithWriter")

				helper.AppResp(c, ecode.SystemErr.Code(), ecode.SystemErr.Message())
				if brokenPipe {
					c.Abort()
					return
				}
			}
		}()

		c.Next()
		consumeTime := helper.GetElapsedTime(c)
		if consumeTime >= timeLimit {
			// TODO 响应超过5s的接口报警,需要优化
			log.Ctx(c).WithFields(logrus.Fields{"consumeTime": consumeTime, "req": string(httpRequest)}).Warn("elapsed >= %d", timeLimit)
		}
		log.Ctx(c).WithField("requestPath", c.Request.URL.Path).Info("AppServerEnd")
	}
}

// stack returns a nicely formatted stack frame, skipping skip frames.
func stack(skip int) []byte {
	buf := new(bytes.Buffer) // the returned data
	// As we loop, we open files and read them. These variables record the currently
	// loaded file.
	var lines [][]byte
	var lastFile string
	for i := skip; ; i++ { // Skip the expected number of frames
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		// Print this much at least.  If we can't find the source, it won't show.
		fmt.Fprintf(buf, "%s:%d (0x%x)--", file, line, pc)
		if file != lastFile {
			data, err := ioutil.ReadFile(file)
			if err != nil {
				continue
			}
			lines = bytes.Split(data, []byte{'\n'})
			lastFile = file
		}
		fmt.Fprintf(buf, "--%s: %s--", function(pc), source(lines, line))
	}
	return buf.Bytes()
}

// source returns a space-trimmed slice of the n'th line.
func source(lines [][]byte, n int) []byte {
	n-- // in stack trace, lines are 1-indexed but our array is 0-indexed
	if n < 0 || n >= len(lines) {
		return dunno
	}
	return bytes.TrimSpace(lines[n])
}

// function returns, if possible, the name of the function containing the PC.
func function(pc uintptr) []byte {
	fn := runtime.FuncForPC(pc)
	if fn == nil {
		return dunno
	}
	name := []byte(fn.Name())
	// The name includes the path name to the package, which is unnecessary
	// since the file name is already included.  Plus, it has center dots.
	// That is, we see
	//	runtime/debug.*T·ptrmethod
	// and want
	//	*T.ptrmethod
	// Also the package path might contains dot (e.g. code.google.com/...),
	// so first eliminate the path prefix
	if lastSlash := bytes.LastIndex(name, slash); lastSlash >= 0 {
		name = name[lastSlash+1:]
	}
	if period := bytes.Index(name, dot); period >= 0 {
		name = name[period+1:]
	}
	name = bytes.Replace(name, centerDot, dot, -1)
	return name
}
