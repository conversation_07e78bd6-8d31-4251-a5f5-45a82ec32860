package middleware

import (
	"time"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
)

func CommonMiddleware() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Header("Access-Control-Allow-Origin", "*")
		ctx.Header("Access-Control-Allow-Credentials", "true")
		ctx.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Accept, Origin, Cache-Control, Pass-Check, X-Requested-With,"+
			"Authorization, Client-Type, Client-Source, Client-Version, X-Language, X-Timestamp, X-Reqid, X-Sign, X-Channel, X-Deviceid, X-Region")
		ctx.Header("Access-Control-Allow-Methods", "POST,HEAD,PATCH,OPTIONS,GET,PUT,DELETE")

		var (
			requestID = ctxUtil.GenRequestID(ctxUtil.ReqTypeH)
			startTime = time.Now()
			newCtx    = ctxUtil.WithRequestID(ctx.Request.Context(), requestID)
		)
		ctx.Set("reqTime", startTime)
		ctx.Set("request_id", requestID)
		ctx.Request = ctx.Request.WithContext(newCtx)

		log.Ctx(ctx).WithField("requestPath", ctx.Request.URL.Path).
			WithField("HeaderInfo", ctx.Request.Header).Info("AppServerStart")

		if ctx.Request.Method == "OPTIONS" {
			ctx.AbortWithStatus(204)
			return
		}

		ctx.Next()
	}
}
