package middleware

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	adminConfig "vlab/app/dao/admin_config"
	versionDao "vlab/app/dao/resource_version"
	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	userSrv "vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/sirupsen/logrus"
)

func ReplayProtection() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		_, err := helper.GetClientType(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
			ctx.Abort()
			return
		}

		channelInfo, channelKeyInfo, err := helper.CheckChannel(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		// 按周缓存
		deviceNo, _ := helper.GetDeviceNo(ctx)
		if deviceNo != "" && !deviceDao.GetRepo().RedisVerifyDeviceID(ctx, deviceNo) {
			userSrv.GetService().CreateOrUpdateDevice(ctx, deviceNo)
		}

		versionInfo, err := helper.CheckVersion(ctx, channelInfo.ID)
		if err != nil {
			helper.AppResp(ctx, ecode.VersionInvalidErr.Code(), ecode.VersionInvalidErr.Message())
			ctx.Abort()
			return
		}

		if channelInfo.NeedForceUpdate() {
			var updateVersion *versionDao.Model
			updateVersion, err = versionDao.GetRepo().FetchByID(ctx, channelInfo.UpdateVersionID)
			if err != nil {
				helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
				ctx.Abort()
				return
			}

			if helper.CheckVersionUpdate(updateVersion.Version, versionInfo.Version) {
				// 当前版本小于等于配置的版本号 需要更新
				helper.AppWithDataResp(ctx, ecode.VersionUpdateErr.Code(), ecode.VersionUpdateErr.Message(),
					channelInfo.GetUpdateInfo(),
				)
				ctx.Abort()
				return
			}
		}

		if helper.JudgePassCheck(ctx) {
			ctx.Next()
			return
		}

		var (
			msg     string
			urlPath = ctx.Request.URL.Path
			params  = map[string]interface{}{}
			p       = ctx.Query("p")
		)
		log.Ctx(ctx).WithField("channelKeyInfo", channelKeyInfo).WithField("param", p).Info("helper.CheckChannel channelKeyInfo")
		if ctx.Request.Method == http.MethodGet && p != "" {
			deData, err := helper.AesCbcDecrypt(p, channelKeyInfo.ReqKey, channelKeyInfo.IV)
			if err != nil {
				log.Ctx(ctx).WithError(err).Warn("http.GET helper.AesCbcDecrypt err")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}
			log.Ctx(ctx).WithField("deData", deData).Info("http.GET DecryptGetRet")

			if err := json.Unmarshal([]byte(deData), &params); err != nil {
				log.Ctx(ctx).WithField("params", params).WithField("deData", deData).Warn("http.GET json.Unmarshal param")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}

			query := ctx.Request.URL.Query()
			for key, val := range params {
				switch v := val.(type) {
				case string:
					query.Set(key, v)
				case []interface{}:
					for _, item := range v {
						//if strItem, ok := item.(string); ok {
						//	query.Add(key, strItem)
						//}
						if item == nil {
							continue
						}

						switch itemVal := item.(type) {
						case string:
							query.Add(key, itemVal)
						case int, int64, float64, uint64, uint32, uint:
							query.Add(key, fmt.Sprintf("%v", itemVal))
						case bool:
							query.Add(key, fmt.Sprintf("%t", itemVal))
						default:
							query.Add(key, fmt.Sprintf("%v", itemVal))
						}
					}
				default:
					query.Set(key, fmt.Sprintf("%v", v))
				}
			}
			ctx.Request.URL.RawQuery = query.Encode()
		} else if ctx.Request.Method == http.MethodPost {
			body, err := io.ReadAll(ctx.Request.Body)
			if err != nil {
				helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
				ctx.Abort()
				return
			}
			if len(body) > 0 {
				// 解析 JSON，提取加密数据
				var reqData map[string]string
				if err := json.Unmarshal(body, &reqData); err != nil {
					helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
					ctx.Abort()
					return
				}

				encryptedData, ok := reqData["p"]
				if !ok {
					log.Ctx(ctx).WithError(err).Warn("http.POST Missing encrypted parameter p")
					helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
					ctx.Abort()
					return
				}

				decryptedData, err := helper.AesCbcDecrypt(encryptedData, channelKeyInfo.ReqKey, channelKeyInfo.IV)
				if err != nil {
					log.Ctx(ctx).WithError(err).Warn("http.POST helper.AesCbcDecrypt err")
					helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
					ctx.Abort()
					return
				}

				// 验证解密后的数据是有效 JSON
				var jsonData map[string]interface{}
				if err := json.Unmarshal([]byte(decryptedData), &jsonData); err != nil {
					log.Ctx(ctx).WithError(err).Warn("http.POST Decrypted data is not valid JSON")
					helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
					ctx.Abort()
					return
				}

				log.Ctx(ctx).WithField("decryptedData", decryptedData).Info("http.POST DecryptGetRet")
				ctx.Request.Body = io.NopCloser(bytes.NewBuffer([]byte(decryptedData)))
			}
		}

		if adminConfig.RedisGetConfig[bool](ctx, adminConfig.KeyEnableReplayProtection) {
			ts, reqID, signStr := ctx.GetHeader("X-Timestamp"), ctx.GetHeader("X-Reqid"), ctx.GetHeader("X-Sign")
			if ts == "" || reqID == "" || signStr == "" {
				// TODO 记录刷数据的IP, 后续拉入黑名单
				log.Ctx(ctx).Warn("missing ts or reqID or signStr")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}

			reqTs := gconv.Int64(ts)
			nowTime, maxSkew := timeUtil.GetNowTimestampByZone(ctx), adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyReplayProtectionTtl)
			log.Ctx(ctx).WithFields(logrus.Fields{
				"Reqid":   reqID,
				"Sign":    signStr,
				"urlPath": ctx.Request.URL.Path,
				"reqTs":   reqTs,
				"nowTime": nowTime,
				"maxSkew": maxSkew,
			}).Info("HeaderAndRedisGetConfigInfo")

			if maxSkew == 0 || nowTime-reqTs > maxSkew || reqTs-nowTime > maxSkew {
				log.Ctx(ctx).Warn("timestamp out maxSkew")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}

			if ctx.Request.Method == http.MethodGet && p != "" {
				msg = fmt.Sprintf("%s-%s-%s-%s", urlPath, p, ts, reqID)
			} else {
				msg = fmt.Sprintf("%s-%s-%s", urlPath, ts, reqID)
			}

			if !helper.HmacHashVerifySignature(ctx, msg, signStr, channelKeyInfo.SignKey) {
				log.Ctx(ctx).WithFields(logrus.Fields{
					"msg":     msg,
					"signStr": signStr,
				}).Warn("verify signature field")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}

			if userDao.GetRepo().RedisVerifyReqID(ctx, reqID) {
				log.Ctx(ctx).WithField("reqID", reqID).Warn("verify req id repeat")
				helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
				ctx.Abort()
				return
			}
		}

		ctx.Next()
	}
}

func ReplayProtectionBackup() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		channelInfo, _, err := helper.CheckChannel(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		if _, err := helper.CheckVersion(ctx, channelInfo.ID); err != nil {
			helper.AppResp(ctx, ecode.VersionInvalidErr.Code(), ecode.VersionInvalidErr.Message())
			ctx.Abort()
			return
		}
		ctx.Next()
	}
}

func CheckAuditVersion() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckAccountAuth ctx.IsAborted")
			return
		}

		versionStatus, err := helper.GetCtxVersionStatus(ctx)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}

		if versionStatus != string(versionDao.StatusStrAuditIng) {
			helper.AppResp(ctx, ecode.ForbiddenErr.Code(), ecode.ForbiddenErr.Message())
			ctx.Abort()
			return
		}

		ctx.Next()
	}
}
