package middleware

import (
	"bytes"
	"io"
	"net/http"
	userDao "vlab/app/dao/user"
	userSrv "vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util"

	"github.com/gin-gonic/gin"
)

// CheckUser .
func CheckUser() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckUser ctx.IsAborted")
			ctx.Abort()
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		if authToken != "" {
			appClaims, err := util.ParseToken(authToken)
			if err != nil {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				ctx.Abort()
				return
			}
			log.Ctx(ctx).WithField("appClaims", appClaims).Info("util.ParseToken ret")

			token, err := userDao.GetRepo().RedisGetUserToken(ctx, appClaims.UserID)
			if token == "" || err != nil {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				log.Ctx(ctx).WithField("token", token).Warn("RedisGetUserToken err")
				ctx.Abort()
				return
			}

			if token != authToken {
				helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
				ctx.Abort()
				return
			}

			ctx.Set("ctxUser", &helper.CtxUser{UID: appClaims.UserID})
		}
		ctx.Next()
	}
}

// CheckUserLogin .
func CheckUserLogin() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("CheckUserLogin ctx.IsAborted")
			ctx.Abort()
			return
		}

		authToken := ctx.Request.Header.Get("Authorization")
		appClaims, err := util.ParseToken(authToken)
		if err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}
		log.Ctx(ctx).WithField("appClaims", appClaims).Info("util.ParseToken ret")

		token, err := userDao.GetRepo().RedisGetUserToken(ctx, appClaims.UserID)
		if token == "" || err != nil {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			log.Ctx(ctx).WithField("token", token).Warn("RedisGetUserToken err")
			ctx.Abort()
			return
		}

		if token != authToken {
			helper.AppResp(ctx, ecode.TokenErr.Code(), ecode.TokenErr.Message())
			ctx.Abort()
			return
		}

		ctx.Set("ctxUser", &helper.CtxUser{UID: appClaims.UserID})
		ctx.Next()
	}
}

// UserOperationLog .
func UserOperationLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		if ctx.Request.Method == http.MethodGet {
			ctx.Next()
			return
		}

		// 获取请求头与请求体
		reqHeaders := ctx.Request.Header
		headers, _ := json.Marshal(&reqHeaders)

		body, err := io.ReadAll(ctx.Request.Body)
		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		ctx.Next()

		if ctx.IsAborted() {
			log.Ctx(ctx).Debug("AdminOperationLog ctx.IsAborted")
			return
		}

		isAbort, err := userSrv.GetUserLogEntity().Producer(ctx, headers, body)
		if isAbort {
			ctx.Abort()
			return
		}

		if err != nil {
			helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
			ctx.Abort()
			return
		}
	}
}
