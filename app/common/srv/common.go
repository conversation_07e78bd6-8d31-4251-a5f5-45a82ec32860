package srv

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"vlab/pkg/helper"
	"vlab/pkg/log"
)

// GracefullyShutdown .
func GracefullyShutdown(ctx context.Context, appSrvMgr *helper.AppSrvMgr) {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGQUIT, syscall.SIGTERM)
	<-quit

	if err := appSrvMgr.Close(ctx); err != nil {
		fmt.Printf("Failed to close services: %v\n", err)
		return
	}

	log.WithContext(ctx).Info("Server exiting")
}
