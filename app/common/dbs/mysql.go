package dbs

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"

	"vlab/config"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

// MysqlEngines 应对一主多从数据库操作方案
type MysqlEngines struct {
	Master *gorm.DB
	Slave1 *gorm.DB
	Slave2 *gorm.DB
	Slave3 *gorm.DB

	SlaveOnly *gorm.DB
}

var (
	defaultEngines         *MysqlEngines
	defaultEnginesInitOnce sync.Once
)

// NewMysqlEngines .
func NewMysqlEngines() *MysqlEngines {
	if defaultEngines == nil {
		defaultEnginesInitOnce.Do(func() {
			defaultEngines = newEngines()
		})
	}
	return defaultEngines
}

func newEngines() *MysqlEngines {
	return &MysqlEngines{
		Master:    newMysql(config.DbCfg),
		Slave1:    newMysql(config.DbReadCfg),
		SlaveOnly: newMysql(config.DbReadOnlyCfg),
	}
}

func newMysql(dbConf *config.Database) *gorm.DB {
	dns := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=%v",
		dbConf.Username,
		dbConf.Password,
		dbConf.Host,
		dbConf.Port,
		dbConf.Database,
		dbConf.Loc, // 例如 "Local" 或 "Asia/Shanghai"
	)
	db, err := gorm.Open(mysql.Open(dns), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
			// TablePrefix: ,  // 表前缀
		},
		Logger: &CustomGormLogger{},
	})
	if err != nil {
		panic(fmt.Sprintf("mysql链接异常 %s", err.Error()))
	}
	sqlDb, _ := db.DB()
	sqlDb.SetMaxIdleConns(32)
	sqlDb.SetMaxOpenConns(100)
	sqlDb.SetConnMaxLifetime(time.Hour)
	return db
}

// Use master or slave
func (rw *MysqlEngines) Use(userMaster bool) *gorm.DB {
	if userMaster {
		return rw.Master
	} else {
		return rw.Slave1
		// return rw.Slave()
	}
}

func (rw *MysqlEngines) UseWithCtx(ctx context.Context, userMaster bool) *gorm.DB {
	return rw.Use(userMaster).WithContext(ctx)
}

func (rw *MysqlEngines) UseWithGinCtx(ctx *gin.Context, userMaster bool) *gorm.DB {
	if ctx == nil || ctx.Request == nil {
		return rw.Use(userMaster)
	}

	return rw.UseWithCtx(ctx.Request.Context(), userMaster)
}

func (rw *MysqlEngines) UseReadOnlyWithGinCtx(ctx *gin.Context) *gorm.DB {
	if rw.SlaveOnly == nil {
		return rw.Slave1
	}
	if ctx == nil || ctx.Request == nil {
		return rw.SlaveOnly
	}

	return rw.SlaveOnly.WithContext(ctx.Request.Context())
}

func (rw *MysqlEngines) Slave() *gorm.DB {
	rand.Seed(time.Now().UnixNano())
	n := rand.Intn(9000)
	if n < 3000 {
		return rw.Slave1
	} else if n < 6000 {
		return rw.Slave2
	} else {
		return rw.Slave3
	}
}

var (
	mysqlDb *gorm.DB
	// pgDb    *gorm.DB
)

func Setup() {
	//1、连接mysql
	mysql_dns := fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8mb4&parseTime=True&loc=%v",
		config.DbCfg.Username,
		config.DbCfg.Password,
		config.DbCfg.Host,
		config.DbCfg.Port,
		config.DbCfg.Database,
		config.DbCfg.Loc, // 例如 "Local" 或 "Asia/Shanghai"
	)
	var err error
	//mysql_dns := "user:pass@tcp(127.0.0.1:3306)/dbname?charset=utf8mb4&parseTime=True&loc=Local"
	mysqlDb, err = gorm.Open(mysql.Open(mysql_dns), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, //禁用表名复数模式
			// TablePrefix:   config.DbCfg.TablePrefix, //表前缀
		},
	})
	if err != nil {
		panic(fmt.Sprintf("mysql connect error:%v\n", err.Error()))
	}

	// 设置最大连接数 与 最大空闲连接数
	sqlDb, _ := mysqlDb.DB()
	// SetMaxIdleConns 设置空闲连接池中连接的最大数量
	sqlDb.SetMaxIdleConns(16)
	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDb.SetMaxOpenConns(100)
	// SetConnMaxLifetime 设置了连接可复用的最大时间。
	sqlDb.SetConnMaxLifetime(time.Hour)

	// mysqlDb.Callback().Create().Replace("gorm:update_time_stamp", updateTimeStampForCreateCallback)
	// mysqlDb.Callback().Update().Replace("gorm:update_time_stamp", updateTimeStampForUpdateCallback)

	//2、连接postgre
	// dns := fmt.Sprintf("host=%v user=%v password=%v dbname=%v port=%v sslmode=disable TimeZone=Asia/Shanghai",
	// 	config.PgCfg.Host,
	// 	config.PgCfg.Username,
	// 	config.PgCfg.Password,
	// 	config.PgCfg.Database,
	// 	config.PgCfg.Port,
	// )
	// pgDb, err = gorm.Open(postgres.Open(dns), &gorm.Config{})
	// if err != nil {
	// 	log.Fatalln("pgsql connect error:", err)
	// }

	// pg, _ := pgDb.DB()
	// pg.SetMaxIdleConns(16)
	// pg.SetMaxOpenConns(100)
}

// CloseDB closes database connection (unnecessary)
func CloseDB() {

}

func GetDB() *gorm.DB {
	return mysqlDb
}

type CustomGormLogger struct {
}

func (c *CustomGormLogger) LogMode(logger.LogLevel) logger.Interface {
	return c
}

func (c *CustomGormLogger) Info(ctx context.Context, msg string, data ...interface{}) {
	log.WithContext(ctx).Info(msg, data...)
}

func (c *CustomGormLogger) Warn(ctx context.Context, msg string, data ...interface{}) {
	log.WithContext(ctx).Warn(msg, data...)
}

func (c *CustomGormLogger) Error(ctx context.Context, msg string, data ...interface{}) {
	log.WithContext(ctx).Error(msg, data...)
}

func (c *CustomGormLogger) Trace(ctx context.Context, begin time.Time, fc func() (sql string, rowsAffected int64), err error) {
	elapsed := time.Since(begin)
	sql, rows := fc()
	fields := logrus.Fields{
		"sql":     sql,
		"rows":    rows,
		"elapsed": float64(elapsed.Nanoseconds()) / 1e6,
	}
	if err != nil {
		fields["error"] = err
		log.WithContext(ctx).WithError(err).WithFields(fields).Error("SQL execution error")
	} else {
		log.WithContext(ctx).WithFields(fields).Info("SQL executed")
	}
}
