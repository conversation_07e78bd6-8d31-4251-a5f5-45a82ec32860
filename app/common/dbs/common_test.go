package dbs

import (
	"testing"
)

func TestOssFilePath_String(t *testing.T) {
	type args struct {
		host string
	}
	tests := []struct {
		name string
		o    OssFilePath
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			o:    "1731378466750_d0f981937037a4d61a549f90bbaf7f41%E4%B8%80.png",
			args: args{
				host: "https://img1.deep-seek.xyz",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.o.String(tt.args.host); got != tt.want {
				t.Errorf("String() = %v, want %v", got, tt.want)
			}
		})
	}
}
