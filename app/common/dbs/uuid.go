package dbs

import (
	"net"
	"os"
	"strconv"
	"strings"

	"github.com/bwmarrin/snowflake"
)

var node *snowflake.Node

func init() {
	node, _ = snowflake.NewNode(getNodeId())
}

func Generate() uint64 {
	return uint64(node.Generate())
}

func getNodeId() int64 {
	ip := GetIP()
	idStr := strings.Replace(ip.String(), ".", "", -1) + strconv.Itoa(os.Getpid())
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return 1
	}
	return id % 1024
}

func GetIP() net.IP {
	addr, err := net.InterfaceAddrs()
	if err != nil {
		return net.IPv4(127, 0, 0, 1)
	}
	for _, address := range addr {
		// 检查ip地址判断是否回环地址
		if ipNet, ok := address.(*net.IPNet); ok && !ipNet.IP.IsLoopback() {
			if ipNet.IP.To4() != nil {
				return ipNet.IP
			}
		}
	}
	return net.IPv4(127, 0, 0, 1)
}
