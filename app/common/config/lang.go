package config

import "github.com/gin-gonic/gin"

type Lang string

const (
	EnUs Lang = "en_US"
	IdId Lang = "id_ID"
	ZhCn Lang = "zh_CN"

	DeDe = "de_DE"
	EsEs = "es_ES"
	FrFr = "fr_FR"
	ItIt = "it_IT"
	JaJp = "ja_JP"
	KoKr = "ko_KR"
	PtPt = "pt_PT"
	RuRu = "ru_RU"
	ThTh = "th_TH"
	TrTr = "tr_TR"
	ViVn = "vi_VN"
	ZhTw = "zh_TW"
	MsMy = "ms_MY"
	ArAr = "ar_AR"
)

func GetLang(ctx *gin.Context) Lang {
	ctxLang := ctx.GetHeader("X-Language")

	if ctxLang == "" {
		return EnUs
	}
	switch ctxLang {
	case string(ZhCn):
		return ZhCn
	case string(IdId):
		return IdId
	case string(DeDe):
		return DeDe
	case string(EsEs):
		return EsEs
	case string(FrFr):
		return FrFr
	case string(ItIt):
		return ItIt
	case string(JaJp):
		return JaJp
	case string(KoKr):
		return KoKr
	case string(PtPt):
		return PtPt
	case string(RuRu):
		return RuRu
	case string(ThTh):
		return ThTh
	case string(TrTr):
		return TrTr
	case string(ViVn):
		return ViVn
	case string(ZhTw):
		return ZhTw
	case string(EnUs):
		return EnUs
	case string(MsMy):
		return MsMy
	case string(ArAr):
		return ArAr
	default:
		return EnUs
	}
}
