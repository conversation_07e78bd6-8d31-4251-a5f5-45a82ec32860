package user

import (
	"sync"
	userDao "vlab/app/dao/user"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Script interface {
}

type Entry struct {
	UserRepo *userDao.Entry
	// StoreRepo storeDao.Repo
	// SkuRepo   skuDao.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetScript() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo: userDao.GetRepo(),
		// StoreRepo: storeDao.GetRepo(),
		// SkuRepo:   skuDao.GetRepo(),
	}
}
