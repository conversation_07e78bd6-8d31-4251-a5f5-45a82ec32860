package user

import (
	"time"
	userDao "vlab/app/dao/user"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

func (e *Entry) FlushUserUUID(ctx *gin.Context, uIds []uint64) error {
	userList, err := e.UserRepo.FindByFilter(ctx, &userDao.Filter{IDS: uIds, EmptyUUID: true})
	if err != nil {
		return err
	}
	for _, val := range userList {
		if err = e.UserRepo.UpdateMapByID(ctx, val.ID, map[string]interface{}{
			"uuid": uuid.New().String(),
		}); err != nil {
			return err
		}
		time.Sleep(time.Millisecond * 10)
	}
	return nil
}
