package script

import (
	videoScript "vlab/app/script/video"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// FlushVideoMapAliyunToBytes 刷新视频映射表aliyun到bytes的映射关系

func FlushVideoMapAliyunToBytes(ctx *gin.Context) {
	type FlushVideoMapAliyunToBytesReq struct {
		VideoStart uint64 `form:"video_start" json:"video_start" binding:"required,omitempty"`
		VideoEnd   uint64 `form:"video_end" json:"video_end" binding:"required,omitempty"`
	}
	req := FlushVideoMapAliyunToBytesReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := videoScript.GetScript().FlushVideoMapAliyunToBytes(ctx, req.VideoStart, req.VideoEnd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

func FlushVideoMapAliyunToBytesRetry(ctx *gin.Context) {
	type FlushVideoMapAliyunToBytesRetryReq struct {
		VideoStart uint64 `form:"video_start" json:"video_start" binding:"required,omitempty"`
		VideoEnd   uint64 `form:"video_end" json:"video_end" binding:"required,omitempty"`
	}
	req := FlushVideoMapAliyunToBytesRetryReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := videoScript.GetScript().FlushVideoMapAliyunToBytesRetry(ctx, req.VideoStart, req.VideoEnd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

func FlushVideoMapBytesRuntime(ctx *gin.Context) {
	type FlushVideoMapBytesRuntimeReq struct {
		IdStart uint64 `form:"id_start" json:"id_start" binding:"required,omitempty"`
		IdEnd   uint64 `form:"id_end" json:"id_end" binding:"required,omitempty"`
	}
	req := FlushVideoMapBytesRuntimeReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := videoScript.GetScript().FlushVideoMapBytesRuntime(ctx, req.IdStart, req.IdEnd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

func FlushVideoMapBytesCp(ctx *gin.Context) {
	type FlushVideoMapBytesCpReq struct {
		VideoStart uint64 `form:"video_start" json:"video_start" binding:"required,omitempty"`
		VideoEnd   uint64 `form:"video_end" json:"video_end" binding:"required,omitempty"`
	}
	req := FlushVideoMapBytesCpReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := videoScript.GetScript().FlushVideoMapBytesCp(ctx, req.VideoStart, req.VideoEnd); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
