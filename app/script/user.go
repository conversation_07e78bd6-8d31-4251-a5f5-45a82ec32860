package script

import (
	userScript "vlab/app/script/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func FlushUserUUID(ctx *gin.Context) {
	type FlushUserUUIDReq struct {
		UserIds []uint64 `json:"user_ids"`
	}
	req := FlushUserUUIDReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	if err := userScript.GetScript().FlushUserUUID(ctx, req.UserIds); err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
