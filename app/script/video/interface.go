package video

import (
	"sync"
	videoMpsDao "vlab/app/dao/content_video_mps"
	videoCpMpsDao "vlab/app/dao/content_video_mps/cp"
)

type Script interface {
}

type Entry struct {
	VideoMpsRepo   videoMpsDao.Repo
	VideoCpMpsRepo *videoCpMpsDao.Entry
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetScript() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		VideoMpsRepo:   videoMpsDao.GetRepo(),
		VideoCpMpsRepo: videoCpMpsDao.GetRepo(),
	}
}
