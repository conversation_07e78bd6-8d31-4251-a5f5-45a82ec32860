package resource

type AdminCompanyListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminCompanyListItem struct {
	ID     uint64 `json:"id"`
	Name   string `json:"name"`
	Cover  string `json:"cover"`
	Status uint32 `json:"status,omitempty"`
}

type AdminCompanyListResp struct {
	List  []*AdminCompanyListItem `json:"list"`
	Total int64                   `json:"total"`
}

type AdminCompanySetReq struct {
	ID    uint64 `form:"id" json:"id" binding:"omitempty"`
	Name  string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Cover string `form:"cover" json:"cover" binding:"omitempty,max=128"`
	// Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminCompanyOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminCompanyAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
