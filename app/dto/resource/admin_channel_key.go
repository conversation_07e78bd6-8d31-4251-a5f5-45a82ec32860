package resource

type AdminChannelKeyListReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id"`
	Status    uint32 `form:"status" json:"status"`
	Page      int    `form:"page" json:"page"`
	Limit     int    `form:"limit" json:"limit"`
}

type AdminChannelKeyListItem struct {
	ID        uint64 `json:"id"`
	ChannelID uint64 `json:"channel_id"`
	SignKey   string `json:"sign_key"`
	ReqKey    string `json:"req_key"`
	RespKey   string `json:"resp_key"`
	IV        string `json:"iv"`
	Status    uint32 `json:"status,omitempty"`
}

type AdminChannelKeyListResp struct {
	List  []*AdminChannelKeyListItem `json:"list"`
	Total int64                      `json:"total"`
}

type AdminChannelKeySetReq struct {
	ID        uint64 `form:"id" json:"id" binding:"omitempty"`
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required"`
	SignKey   string `form:"sign_key" json:"sign_key" binding:"required,omitempty,max=128"`
	ReqKey    string `form:"req_key" json:"req_key" binding:"required,omitempty,max=128"`
	RespKey   string `form:"resp_key" json:"resp_key" binding:"required,omitempty,max=128"`
	IV        string `form:"iv" json:"iv" binding:"required,omitempty,max=128"`
	// Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminChannelKeyOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminChannelKeyAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
