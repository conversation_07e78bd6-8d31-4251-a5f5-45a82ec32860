package resource

type AdminChannelListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminChannelListItem struct {
	ID     uint64 `json:"id"`
	Name   string `json:"name"`
	Status uint32 `json:"status,omitempty"`

	UpdateType      uint32 `json:"update_type,omitempty"` // 1:无更新 2:可选更新 3:强制更新
	Url             string `json:"url,omitempty"`
	Desc            string `json:"desc,omitempty"`
	UpdateVersionID uint64 `json:"update_version_id,omitempty"`
	Version         string `json:"version,omitempty"`
}

type AdminChannelListResp struct {
	List  []*AdminChannelListItem `json:"list"`
	Total int64                   `json:"total"`
}

type AdminChannelSetReq struct {
	ID   uint64 `form:"id" json:"id" binding:"omitempty"`
	Name string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	// Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
	VersionID  uint64 `form:"version_id" json:"version_id" binding:"omitempty"`
	UpdateType uint32 `form:"update_type" json:"update_type" binding:"oneof=1 2 3"` // 1:无更新 2:可选更新 3:强制更新
	Url        string `form:"url" json:"url" binding:"omitempty"`
	Desc       string `form:"desc" json:"desc" binding:"omitempty"`
}

type AdminChannelOperateReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminChannelAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
