package resource

import (
	channelDao "vlab/app/dao/resource_channel"
)

type VersionCheckReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id"`
	VersionID uint64 `form:"version_id" json:"version_id"`
}

type VersionCheckResp struct {
	*channelDao.UpdateInfo
}

type ConfigResp struct {
	ProductUrl string `json:"product_url,omitempty"` //
}

type ErrorReportReq struct {
	ErrCode    string `form:"err_code" json:"err_code" binding:"required,omitempty,max=128"`
	ErrMsg     string `form:"err_msg" json:"err_msg" binding:"required,omitempty,max=128"`
	ShowID     uint64 `form:"show_id" json:"show_id"`
	EpisodeID  uint64 `form:"episode_id" json:"episode_id"`
	VideoID    uint64 `form:"video_id" json:"video_id"`
	Resolution uint32 `form:"resolution" json:"resolution"`
}
