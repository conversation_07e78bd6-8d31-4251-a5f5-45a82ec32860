package resource

type AdminVersionListReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"omitempty"`
	Name      string `form:"name" json:"name"`
	Version   string `form:"version" json:"version"`
	Status    uint32 `form:"status" json:"status"`
	Page      int    `form:"page" json:"page"`
	Limit     int    `form:"limit" json:"limit"`
}

type AdminVersionListItem struct {
	ID      uint64               `json:"id"`
	Name    string               `json:"name"`
	Channel AdminChannelListItem `json:"channel"`
	Version string               `json:"version"`
	Status  uint32               `json:"status,omitempty"`
}

type AdminVersionListResp struct {
	List  []*AdminVersionListItem `json:"list"`
	Total int64                   `json:"total"`
}

type AdminVersionSetReq struct {
	ID        uint64 `form:"id" json:"id" binding:"omitempty"`
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required" msg:"需要设置版本的渠道"`
	Name      string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Version   string `form:"version" json:"version" binding:"checkVersion" msg:"版本号格式不正确"`
	Status    uint32 `form:"status" json:"status" binding:"oneof=1 2 3"`
}

// type AdminVersionOperateReq struct {
// 	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
// 	Action string `form:"action" json:"action" binding:"oneof=open close"`
// 	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
// }

type AdminVersionAllReq struct {
	Enable    uint32 `form:"enable" json:"enable"`
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required" msg:"需要筛选版本的渠道"`
}
