package data

import (
	"vlab/app/dto/common"
)

type DataShowSearchReq struct {
	common.DataListReq
	QueryStart int64 `form:"query_start" json:"query_start,omitempty"`
	QueryEnd   int64 `form:"query_end" json:"query_end,omitempty"`
}

type DataShowSearchResp struct {
	List  []*DataShowSearchItem `json:"list,omitempty"`
	Count int64                 `json:"count,omitempty"`
}

type DataShowSearchItem struct {
	Keyword string `json:"keyword,omitempty"` // 关键词
	Lang    string `json:"lang,omitempty"`    // 语言
	Num     int64  `json:"num,omitempty"`     // 数量
}
