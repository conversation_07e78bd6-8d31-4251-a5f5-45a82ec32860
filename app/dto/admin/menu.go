package admin

type AdminMenuListResp struct {
	ID        uint64               `json:"id"`
	Pid       uint64               `json:"pid"`
	Title     string               `json:"title"`
	Icon      string               `json:"icon"`
	MenuType  uint32               `json:"menu_type"`
	Component string               `json:"component"`
	BeAuth    string               `json:"be_auth"`
	IsShow    uint32               `json:"is_show"`
	Sort      uint32               `json:"sort"`
	Path      string               `json:"path"`
	PathName  string               `json:"path_name"`
	Redirect  string               `json:"redirect"`
	FeAuth    string               `json:"fe_auth"`
	Status    uint32               `json:"status"`
	KeepAlive uint32               `json:"keep_alive"`
	Child     []*AdminMenuListResp `json:"child"`
	ChildIds  []uint64             `json:"child_ids"`
}

type AdminMenuOptionResp struct {
	ID      uint64                 `json:"id"`
	Pid     uint64                 `json:"pid"`
	Title   string                 `json:"title"`
	Status  uint32                 `json:"status"`
	HasAuth uint32                 `json:"has_auth"`
	Child   []*AdminMenuOptionResp `json:"child"`
}

type AdminMenuRouterResp struct {
	ID        uint64                 `json:"id"`
	Pid       uint64                 `json:"pid"`
	Title     string                 `json:"title"`
	Icon      string                 `json:"icon"`
	MenuType  uint32                 `json:"menu_type"`
	Component string                 `json:"component"`
	Path      string                 `json:"path"`
	PathName  string                 `json:"path_name"`
	Redirect  string                 `json:"redirect"`
	FeAuth    string                 `json:"fe_auth"`
	IsShow    uint32                 `json:"is_show"`
	KeepAlive uint32                 `json:"keep_alive"`
	Child     []*AdminMenuRouterResp `json:"child"`
}

type AdminSetMenuReq struct {
	ID        uint64 `form:"id" json:"id" binding:"omitempty"`
	Pid       uint64 `form:"pid" json:"pid"`
	Title     string `form:"title" json:"title" binding:"required,omitempty,max=100"`
	Icon      string `form:"icon" json:"icon"`
	MenuType  uint32 `form:"menu_type" json:"menu_type" binding:"oneof=1 2 3"`
	Component string `form:"component" json:"component" binding:"max=100"`
	BeAuth    string `form:"be_auth" json:"be_auth" binding:"max=100"`
	IsShow    uint32 `form:"is_show" json:"is_show" binding:"oneof=1 2"`
	Sort      uint32 `form:"sort" json:"sort" binding:"min=0,max=100000000"`
	Status    uint32 `form:"status" json:"status" binding:"oneof=1 2"`
	KeepAlive uint32 `form:"keep_alive" json:"keep_alive" binding:"oneof=0 1"`
	Path      string `form:"path" json:"path" binding:"max=100"`
	PathName  string `form:"path_name" json:"path_name" binding:"max=100"`
	Redirect  string `form:"redirect" json:"redirect" binding:"max=100"`
	FeAuth    string `form:"fe_auth" json:"fe_auth" binding:"max=100"`
}

type AdminMenuOptionReq struct {
	RoleID uint64 `form:"role_id" json:"role_id"`
}

type AdminDelMenuReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}
