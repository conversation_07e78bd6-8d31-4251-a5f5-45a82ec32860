package admin

type AdminRoleListReq struct {
	Name    string `form:"name" json:"name"`
	Status  uint32 `form:"status" json:"status"`
	Page    int    `form:"page" json:"page"`
	Limit   int    `form:"limit" json:"limit"`
	NotRids []uint64
}

type AdminRoleListItem struct {
	ID        uint64 `json:"id"`
	Name      string `json:"name"`
	Status    uint32 `json:"status,omitempty"`
	IsAdmin   bool   `json:"is_admin"`
	CreatedAt string `json:"created_at,omitempty"`
}

type AdminRoleListResp struct {
	List  []*AdminRoleListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminSetRoleReq struct {
	ID     uint64 `form:"id" json:"id" binding:"omitempty"`
	Name   string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminSetRoleAuthReq struct {
	ID      uint64   `form:"id" json:"id" binding:"omitempty"`
	MenuIDS []uint64 `form:"menu_ids" json:"menu_ids" binding:"omitempty"`
}

type AdminDelRoleReq struct {
	ID uint64 `form:"id" json:"id" binding:"omitempty"`
}

type AdminRoleAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}
