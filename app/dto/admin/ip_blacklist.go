package admin

import (
	showDto "vlab/app/dto/show"
)

// AdminIPBlacklistListReq IP黑名单列表请求
type AdminIPBlacklistListReq struct {
	ID        uint64 `form:"id" json:"id"`
	ChannelID uint64 `form:"channel_id" json:"channel_id"`
	Status    uint32 `form:"status" json:"status"`
	Page      int    `form:"page" json:"page"`
	Limit     int    `form:"limit" json:"limit"`
}

// AdminIPBlacklistListItem IP黑名单列表项
type AdminIPBlacklistListItem struct {
	ID        uint64               `json:"id"`
	ChannelID uint64               `json:"channel_id"`        // 保持向后兼容
	Channel   *showDto.ChannelBase `json:"channel,omitempty"` // 新增渠道信息对象
	IPList    string               `json:"ip_list"`
	Status    uint32               `json:"status"`
	CreatedAt string               `json:"created_at"`
	UpdatedAt string               `json:"updated_at"`
}

// AdminIPBlacklistListResp IP黑名单列表响应
type AdminIPBlacklistListResp struct {
	List  []*AdminIPBlacklistListItem `json:"list"`
	Total int64                       `json:"total"`
}

// AdminIPBlacklistDetailReq IP黑名单详情请求
type AdminIPBlacklistDetailReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

// AdminIPBlacklistDetailResp IP黑名单详情响应
type AdminIPBlacklistDetailResp struct {
	ID        uint64               `json:"id"`
	ChannelID uint64               `json:"channel_id"`        // 保持向后兼容
	Channel   *showDto.ChannelBase `json:"channel,omitempty"` // 新增渠道信息对象
	IPList    string               `json:"ip_list"`
	IPCIDRs   []string             `json:"ip_cidrs"` // 解析后的IP/CIDR列表
	Status    uint32               `json:"status"`
	CreatedAt string               `json:"created_at"`
	UpdatedAt string               `json:"updated_at"`
}

// AdminIPBlacklistCreateReq 创建IP黑名单请求
type AdminIPBlacklistCreateReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required"`
	IPList    string `form:"ip_list" json:"ip_list" binding:"required,max=2000"`
	Status    uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

// AdminIPBlacklistUpdateReq 更新IP黑名单请求
type AdminIPBlacklistUpdateReq struct {
	ID        uint64 `form:"id" json:"id" binding:"required"`
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required"`
	IPList    string `form:"ip_list" json:"ip_list" binding:"required,max=2000"`
	Status    uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

// AdminIPBlacklistDeleteReq 删除IP黑名单请求
type AdminIPBlacklistDeleteReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

// AdminIPBlacklistTestReq 测试IP是否在黑名单中的请求
type AdminIPBlacklistTestReq struct {
	ChannelID uint64 `form:"channel_id" json:"channel_id" binding:"required"`
	IP        string `form:"ip" json:"ip" binding:"required,ip"`
}

// AdminIPBlacklistTestResp 测试IP黑名单响应
type AdminIPBlacklistTestResp struct {
	IsBlacklisted bool   `json:"is_blacklisted"`
	MatchedRule   string `json:"matched_rule,omitempty"` // 匹配的规则（IP或CIDR）
	Message       string `json:"message"`
}
