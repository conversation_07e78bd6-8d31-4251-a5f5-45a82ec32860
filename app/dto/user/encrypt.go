package user

type EncryptReq struct {
	EncryptKey string `form:"account" json:"account" binding:"required,omitempty"`
}

type EncryptOriginalResp struct {
	Timestamp  int64  `json:"timestamp"`
	ReqId      string `json:"reqid"`
	Param      string `json:"param"`
	EncryptKey string `json:"encrypt_key"`
	DecryptKey string `json:"decrypt_key"`
}

type EncryptResp struct {
	ReqParam            string              `json:"req_param"`
	OriginalData        EncryptOriginalResp `json:"original_data"`
	OriginalEncryptData string              `json:"original_encrypt_data"`
	EncryptData         string              `json:"encrypt_data"`
}

type DecryptResp struct {
	ReqParam     string              `json:"req_param"`
	OriginalData string              `json:"original_data"`
	DecryptData  EncryptOriginalResp `json:"decrypt_data"`
	Req          DecryptAccountReq   `json:"decrypt_req"`
}

type DecryptAccountReq struct {
	ID      uint64 `form:"id" json:"id" binding:"omitempty"`
	Account string `form:"account" json:"account" binding:"required,omitempty,max=32"`
	Name    string `form:"name" json:"name" binding:"required,omitempty,max=32"`
}
