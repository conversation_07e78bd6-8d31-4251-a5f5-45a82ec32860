package user

import (
	"vlab/app/dto/common"
)

type AdminUserAddrListReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminUserAddrListResp struct {
	List  []*common.CommonUserAddrInfo `json:"list"`
	Total int64                        `json:"total"`
}

type AdminSetUserAddrReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	SetUserAddrReq
}
type SetUserAddrReq struct {
	ID         uint64 `form:"id" json:"id" binding:"omitempty"`
	Consignee  string `form:"consignee" json:"consignee" binding:"required,omitempty,max=32"`
	Mobile     string `form:"mobile" json:"mobile" binding:"required,omitempty"`
	ProvinceID uint64 `form:"province_id" json:"province_id" binding:"required,omitempty"`
	CityID     uint64 `form:"city_id" json:"city_id" binding:"required,omitempty"`
	DistrictID uint64 `form:"district_id" json:"district_id" binding:"required,omitempty"`
	Address    string `form:"address" json:"address" binding:"required,omitempty,max=64"`
	IsDefault  uint32 `form:"is_default" json:"is_default" binding:"oneof=0 1"`
}

type AdminUserTypeListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminUserTypeListItem struct {
	ID     uint64 `json:"id"`
	Name   string `json:"name"`
	Status uint32 `json:"status,omitempty"`
}

type AdminUserTypeListResp struct {
	List  []*AdminUserTypeListItem `json:"list"`
	Total int64                    `json:"total"`
}

type AdminSetUserTypeReq struct {
	ID   uint64 `form:"id" json:"id" binding:"omitempty"`
	Name string `form:"name" json:"name" binding:"required,omitempty,max=64"`
	// Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminOperateUserTypeReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminUserTypeAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}

type AdminBuyLevelListReq struct {
	Name   string `form:"name" json:"name"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminBuyLevelListItem struct {
	ID               uint64 `json:"id"`
	Name             string `json:"name"`
	IsBookable       uint32 `json:"is_bookable"`
	DepositRatioType uint32 `json:"deposit_ratio_type"`
	DepositRatio     uint32 `json:"deposit_ratio"`
	FloatRatio       uint32 `json:"float_ratio"`
	AllowBuyLimit    uint32 `json:"allow_buy_limit"`
	AllowBuyOnline   uint32 `json:"allow_buy_online"`
	Status           uint32 `json:"status"`
}

type AdminBuyLevelListResp struct {
	List  []*AdminBuyLevelListItem `json:"list"`
	Total int64                    `json:"total"`
}

type AdminSetBuyLevelReq struct {
	ID               uint64 `form:"id" json:"id" binding:"omitempty"`
	Name             string `form:"name" json:"name" binding:"required,omitempty,max=64"`
	IsBookable       uint32 `form:"is_bookable" json:"is_bookable" binding:"oneof=1 2"`
	DepositRatioType uint32 `form:"deposit_ratio_type" json:"deposit_ratio_type" binding:"oneof=0 1"`
	DepositRatio     uint32 `form:"deposit_ratio" json:"deposit_ratio" binding:"min=0,max=100"`
	FloatRatio       uint32 `form:"float_ratio" json:"float_ratio"`
	AllowBuyLimit    uint32 `form:"allow_buy_limit" json:"allow_buy_limit" binding:"oneof=1 2"`
	AllowBuyOnline   uint32 `form:"allow_buy_online" json:"allow_buy_online" binding:"oneof=1 2"`
}

type AdminOperateBuyLevelReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type AdminBuyLevelAllReq struct {
	Enable uint32 `form:"enable" json:"enable"`
}

type AdminBuyLevelAllItem struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

type AdminUserLoginLogListReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminUserLoginLogListItem struct {
	ID            uint64 `json:"id"`
	UserID        uint64 `json:"user_id"`
	ClientIp      string `json:"client_ip"`
	Source        uint32 `json:"source"`
	Device        string `json:"device"`
	AgreeProtocol uint32 `json:"agree_protocol"`
	CreatedAt     string `json:"created_at"`
}
type AdminUserLoginLogListResp struct {
	List  []*AdminUserLoginLogListItem `json:"list"`
	Total int64                        `json:"total"`
}
