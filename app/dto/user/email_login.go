package user

// EmailSendCodeReq 发送邮箱验证码请求
type EmailSendCodeReq struct {
	Email   string `form:"email" json:"email" binding:"required,email,max=100"`
	UseType uint32 `form:"use_type" json:"use_type" binding:"required,oneof=1 2 3"` // 1:注册, 2::重置密码, 3:修改邮箱
}

// EmailVerifyCodeReq 验证邮箱验证码请求
type EmailVerifyCodeReq struct {
	Email string `form:"email" json:"email" binding:"required,email,max=100"`
	Code  string `form:"code" json:"code" binding:"required,len=6"`
}

// EmailRegisterReq 邮箱注册请求
type EmailRegisterReq struct {
	Email    string `form:"email" json:"email" binding:"required,email,max=100"`
	Code     string `form:"code" json:"code" binding:"required,len=6"`
	Password string `form:"password" json:"password" binding:"required,min=6,max=32"`
	//ConfirmPassword string `form:"confirm_password" json:"confirm_password" binding:"required,eqfield=Password"`
	Nickname string `form:"nickname" json:"nickname" binding:"max=32"`
}

// EmailLoginReq 邮箱登录请求
type EmailLoginReq struct {
	Email    string `form:"email" json:"email" binding:"required,email,max=100"`
	Password string `form:"password" json:"password" binding:"required,min=6,max=32"`
}

// EmailResetPasswordReq 邮箱重置密码请求
type EmailResetPasswordReq struct {
	Email    string `form:"email" json:"email" binding:"required,email,max=100"`
	Code     string `form:"code" json:"code" binding:"required,len=6"`
	Password string `form:"password" json:"password" binding:"required,min=6,max=32"`
	//ConfirmPassword string `form:"confirm_password" json:"confirm_password" binding:"required,eqfield=Password"`
}

// EmailVerifyCodeResp 验证邮箱验证码响应
type EmailVerifyCodeResp struct {
	IsValid bool   `json:"is_valid"`
	Message string `json:"message"`
}

type EmailCheckReq struct {
	Email string `form:"email" json:"email" binding:"required,email,max=100"`
}

type EmailCheckResp struct {
	IsValid bool   `json:"is_valid"` // 是否已注册
	Message string `json:"message"`  // 响应消息
}
