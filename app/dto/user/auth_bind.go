package user

// 统一绑定账号请求
type BindAuthReq struct {
	AuthType  uint32 `form:"auth_type" json:"auth_type" binding:"required,oneof=1 2 4"` // 认证类型 1-苹果 2-谷歌 4-邮箱
	AuthToken string `form:"auth_token" json:"auth_token"`                              // 第三方认证token（苹果/谷歌）
	Email     string `form:"email" json:"email"`                                        // 邮箱（邮箱绑定时必填）
	Code      string `form:"code" json:"code"`                                          // 验证码（邮箱绑定时必填）
	Password  string `form:"password" json:"password"`                                  // 密码（邮箱绑定时必填）
}

type BindAuthResp struct {
	*AdminUserListItem
	Auths []*AdminUserAuthItem `json:"auths"`
}

// 认证方式项
type AuthMethodItem struct {
	AuthType     uint32 `json:"auth_type"`      // 认证类型 1-苹果 2-谷歌 4-邮箱
	AuthTypeName string `json:"auth_type_name"` // 认证类型名称
	AuthUid      string `json:"auth_uid"`       // 认证唯一标识
	AuthEmail    string `json:"auth_email"`     // 认证邮箱（如果有）
	IsBound      bool   `json:"is_bound"`       // 是否已绑定
	BoundAt      string `json:"bound_at"`       // 绑定时间
}

// 认证方式列表响应
type AuthMethodListResp struct {
	AuthMethods []AuthMethodItem `json:"auth_methods"`
}

// 解绑认证方式请求
type UnbindAuthReq struct {
	AuthType uint32 `form:"auth_type" json:"auth_type" binding:"required,oneof=1 2 4"`
	Password string `form:"password" json:"password" binding:"required"` // 需要密码确认
}

// 绑定成功响应
type BindSuccessResp struct {
	Message string `json:"message"`
}
