package user

import "github.com/go-pay/gopay/paypal"

type AppStoreTransactionReq struct {
	TransactionID string `form:"transaction_id" json:"transaction_id" binding:"required,omitempty"`
	Environment   string `form:"environment" json:"environment" binding:"required,omitempty"`
}

type GooglePlayTransactionReq struct {
	PurchaseToken string `form:"purchase_token" json:"purchase_token" binding:"required,omitempty"`
	ProductId     string `form:"product_id" json:"product_id" binding:"required,omitempty"`
	PackageName   string `form:"package_name" json:"package_name" binding:"required,omitempty"`
	OrderId       string `form:"order_id" json:"order_id"`
	NotAck        uint32 `form:"not_ack" json:"not_ack"`
}

type ThirdTransactionCreateReq struct {
	ID         uint64 `form:"id" json:"id" binding:"required,omitempty"`
	PayType    string `form:"pay_type" json:"pay_type" binding:"oneof=paypal stripe"`
	SuccessURL string `form:"success_url" json:"success_url" binding:"required"`
	CancelURL  string `form:"cancel_url" json:"cancel_url" binding:"omitempty"`
}

type ThirdTransactionCreateResp struct {
	Paypal *PaypalTransactionCreateResp `json:"paypal"`
	Stripe *StripeTransactionCreateResp `json:"stripe"`
}

type PaypalTransactionCreateResp struct {
	OrderRet    *paypal.OrderDetail        `json:"order_ret"`
	Approve     string                     `json:"approve"`
	PayerAction string                     `json:"payer_action"`
	SubRet      *paypal.SubscriptionDetail `json:"sub_ret"`
}

type StripeTransactionCreateResp struct {
	Session *StripeSession `json:"session,omitempty"` // Stripe session information
}

type StripeSession struct {
	SessionID string `json:"session_id" binding:"required,omitempty"` // Stripe session ID
	URL       string `json:"url" binding:"required,omitempty"`        // Stripe checkout URL
}

type ThirdTransactionCheckReq struct {
	PayType    string `form:"pay_type" json:"pay_type" binding:"oneof=paypal stripe"`
	TranceType string `form:"trance_type" json:"trance_type" binding:"oneof=order sub"`
	TranceID   string `form:"trance_id" json:"trance_id" binding:"required,omitempty"`
}

type ThirdTransactionCheckResp struct {
	UserInfo *AdminUserListItem          `json:"user_info"`
	Stripe   *StripeTransactionCheckResp `json:"stripe"`
}

type StripeTransactionCheckResp struct {
	Session *StripeSession `json:"session,omitempty"` // Stripe session information
}
