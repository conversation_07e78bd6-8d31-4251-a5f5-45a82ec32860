package user

type UserLoginThirdReq struct {
	AuthType  uint32 `form:"auth_type" json:"auth_type" binding:"oneof=1 2 3"` // 1苹果ID 2google邮箱 3faceBook
	AuthToken string `form:"auth_token" json:"auth_token" binding:"required,omitempty"`
}

type UserLoginReq struct {
	Account string `form:"account" json:"account" binding:"required,omitempty"`
	Pwd     string `form:"pwd" json:"pwd" binding:"required,omitempty"`
}

type UserLoginResp struct {
	Token    string             `json:"token"`
	UserInfo *AdminUserInfoResp `json:"user_info"`
}

type UserEditReq struct {
	Name     string `form:"name" json:"name" binding:"required,omitempty,max=32"`
	Mobile   string `form:"mobile" json:"mobile" binding:"required,omitempty"`
	UserType uint32 `form:"user_type" json:"user_type" binding:"oneof=1 2"`
	// License     string `form:"license" json:"license"`                                                             // 营业执照
	// IdCardFront string `form:"id_card_front" json:"id_card_front"`                                                 // 身份证正面照
	// IdCardBack  string `form:"id_card_back" json:"id_card_back"`                                                   // 身份证背面照
	License     string `form:"license" json:"license" binding:"requiredIF=UserType 1" msg:"请上传营业执照"`               // 营业执照
	IdCardFront string `form:"id_card_front" json:"id_card_front" binding:"requiredIF=UserType 2" msg:"请上传身份证正面照"` // 身份证正面照
	IdCardBack  string `form:"id_card_back" json:"id_card_back" binding:"requiredIF=UserType 2" msg:"请上传身份证背面照"`   // 身份证背面照
}

type UserEditPwdReq struct {
	OldPwd     string `form:"old_pwd" json:"old_pwd" binding:"required"`
	NewPwd     string `form:"new_pwd" json:"new_pwd" binding:"required,omitempty,min=6,max=32"`
	ConfirmPwd string `form:"confirm_pwd" json:"confirm_pwd" binding:"required,omitempty,min=6,max=32"`
}
