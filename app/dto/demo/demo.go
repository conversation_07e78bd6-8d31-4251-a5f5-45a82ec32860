package demo

type AdminDemoListReq struct {
	Title  string `form:"title" json:"title"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type AdminDemoListItem struct {
	ID          uint64 `json:"id"`
	UID         uint64 `json:"user_id"`
	Title       string `json:"title"`
	Status      uint32 `json:"status"`
	CreatedTime string `json:"created_time,omitempty"` // 创建时间
}
type AdminDemoListResp struct {
	List  []*AdminDemoListItem `json:"list"`
	Total int64                `json:"total"`
}

type AdminDemoDetailReq struct {
	ID uint64 `form:"id" json:"id" binding:"required,omitempty"`
}

type AdminDemoDetailResp struct {
	ID          uint64 `json:"id"`
	UID         uint64 `json:"user_id"`
	Title       string `json:"title"`
	Status      uint32 `json:"status"`
	CreatedTime string `json:"created_time,omitempty"`
}

type AdminSetDemoReq struct {
	ID     uint64 `form:"id" json:"id" binding:"omitempty"`
	UID    uint64 `form:"user_id" json:"user_id`
	Title  string `form:"title" json:"title" binding:"required,omitempty,max=64"`
	Status uint32 `form:"status" json:"status" binding:"oneof=1 2"`
}

type AdminOperateDemoReq struct {
	ID     uint64 `form:"id" json:"id" binding:"required,omitempty"`
	Action string `form:"action" json:"action" binding:"oneof=open close,omitempty"`
	Value  uint32 `form:"value" json:"value" binding:"max=10000000"`
}

type DemoListReq struct {
	Title  string `form:"title" json:"title"`
	Status uint32 `form:"status" json:"status"`
	Page   int    `form:"page" json:"page"`
	Limit  int    `form:"limit" json:"limit"`
}

type DemoListItem struct {
	ID          uint64 `json:"id"`
	UID         uint64 `json:"user_id"`
	Title       string `json:"title"`
	Status      uint32 `json:"status"`
	CreatedTime string `json:"created_time,omitempty"` // 创建时间
}
type DemoListResp struct {
	List  []*DemoListItem `json:"list"`
	Total int64           `json:"total"`
}
