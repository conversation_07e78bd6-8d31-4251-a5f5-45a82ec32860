package client_config

import (
	"vlab/app/dto/common"
	"vlab/app/dto/show"
)

type AdminUpdateClientConfigReq struct {
	Key       string `json:"key" binding:"required"`  // 配置项的键
	Data      string `json:"data" binding:"required"` // 配置项的数据
	Status    uint32 `json:"status"`                  // 配置项的状态，1表示启用 2表示禁用
	ChannelID uint64 `json:"channel_id"`              // 渠道ID，0表示全局配置
}

type AdminUpdateClientConfigResp struct {
	AdminClientConfigItem
}

type AdminClientConfigReq struct {
	common.DataListReq
	Key       string `form:"key"`        // 配置项的键
	Status    uint32 `form:"status"`     // 配置项的状态，1表示启用 2表示禁用
	ChannelID uint64 `form:"channel_id"` // 渠道ID，0表示全局配置
}

type AdminClientConfigResp struct {
	List  []AdminClientConfigItem `json:"list"`  // 配置项列表
	Count int64                   `json:"count"` // 配置项数量
	IsEnd bool                    `json:"isEnd"` // 是否结束分页
}

type AdminClientConfigItem struct {
	ID     uint32 `json:"id"`     // 配置项ID
	Key    string `json:"key"`    // 配置项的键
	Data   string `json:"data"`   // 配置项的数据
	Status uint32 `json:"status"` // 配置项的状态，1表示启用 2表示禁用
	//ChannelID uint64           `json:"channel_id"` // 渠道ID，0表示全局配置
	Channel show.ChannelBase `json:"channel"` // 渠道信息
}

type AdminDeleteClientConfigReq struct {
	ID uint32 `form:"id" binding:"required"` // 配置项ID
}

type AdminDeleteClientConfigResp struct {
}
