package show

import (
	"vlab/app/api/tmdb"
	"vlab/app/dao"
	"vlab/app/dto/common"
	resourceDto "vlab/app/dto/resource"
)

type AdminShowListReq struct {
	common.DataListReq

	GenreID uint64 `form:"genre_id" json:"genre_id,omitempty"`

	YearID uint64 `form:"year_id" json:"year_id,omitempty"`

	RegionID uint64 `form:"region_id" json:"region_id,omitempty"`

	FranchiseID uint64 `form:"franchise_id" json:"franchise_id,omitempty"`

	Name string `form:"name" json:"name,omitempty"`

	ContentType uint32 `form:"content_type" json:"content_type,omitempty"`

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty"`

	Status uint32 `form:"status" json:"status,omitempty"`

	Channels []uint64 `form:"channels" json:"channels,omitempty"`

	ChannelID uint64 `form:"channel_id" json:"channel_id,omitempty"`
	VersionID uint64 `form:"version_id" json:"version_id,omitempty"`

	// 向量搜索参数
	UseVector    bool    `form:"use_vector" json:"use_vector,omitempty"`       // 是否使用向量搜索
	DenseWeight  float32 `form:"dense_weight" json:"dense_weight,omitempty"`   // 稠密向量权重 (0-1)
	SparseWeight float32 `form:"sparse_weight" json:"sparse_weight,omitempty"` // 稀疏向量权重 (0-1)
	VectorTopK   int     `form:"vector_top_k" json:"vector_top_k,omitempty"`   // 向量搜索候选数量
}

type AdminShowListResp struct {
	List      []*AdminShow `json:"list"`
	Count     int64        `json:"count"`
	RequestID string       `json:"request_id,omitempty"`
}

type AdminShow struct {
	*ShowBase

	NameKey     string `json:"name_key"`
	OverviewKey string `json:"overview_key"`
	AirDateKey  string `json:"air_date_key"`

	GenresKey    []string `json:"genres_key"`
	FranchiseKey string   `json:"franchise_key"`

	ShowNameI18n     []*dao.I18n `json:"show_name_i18n,omitempty"`
	ShowOverViewI18n []*dao.I18n `json:"show_over_view_i18n,omitempty"`
	ShowAirDateI18n  []*dao.I18n `json:"show_air_date_i18n,omitempty"`

	ShowGenresI18n    []*dao.I18n `json:"show_genres_i18n,omitempty"`
	ShowFranchiseI18n []*dao.I18n `json:"show_franchise_i18n,omitempty"`

	Channels []*resourceDto.AdminChannelListItem `json:"channels,omitempty"`

	Versions []*resourceDto.AdminVersionListItem `json:"versions,omitempty"`
}

type AdminShowDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"`
}

type AdminShowDetailResp struct {
	AdminShow

	Homepage    string        `json:"homepage"`   // 官网
	CreatedBy   []*CreditBase `json:"created_by"` // 导演
	Credits     *Credits      `json:"credits"`
	Description string        `json:"description"` // 描述

	Episodes []*EpisodeBase `json:"episodes"`

	//Seasons []*SeasonBase `json:"seasons"` // 季
	Images *Images `json:"images"` // 图片

	ShowNameI18n []*dao.I18n `json:"show_name_i18n,omitempty"`

	ShowOverViewI18n []*dao.I18n `json:"show_over_view_i18n,omitempty"`

	ShowAirDateI18n []*dao.I18n `json:"show_air_date_i18n,omitempty"`

	Classes []*Class `json:"classes,omitempty"`
}

type AdminShowCreateReq struct {
	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`

	OverView string `form:"over_view" json:"over_view,omitempty"`

	OverViewKey string `form:"over_view_key" json:"over_view_key,omitempty"`

	ContentType uint32 `form:"content_type" json:"content_type,omitempty"`

	AirDate string `form:"air_date" json:"air_date,omitempty"`

	AirDateKey string `form:"air_date_key" json:"air_date_key,omitempty"`

	Score uint32 `form:"score" json:"score,omitempty"`

	Genres []uint64 `form:"genres" json:"genres,omitempty"`

	Classes []*Class `form:"classes" json:"classes,omitempty"`

	Franchise uint64 `form:"franchise" json:"franchise,omitempty"`

	Homepage string `form:"homepage" json:"homepage,omitempty"`

	InProduction uint32 `form:"in_production" json:"in_production,omitempty"`

	Langs []string `form:"langs" json:"langs,omitempty"`

	// 图片
	Images []*ImageBase `form:"images" json:"images,omitempty"`

	// 演职员
	Credits []*CreditBase `form:"credits" json:"credits,omitempty"`

	// Limit Filter
	ChannelFilters []uint64 `form:"channel_filters" json:"channel_filters,omitempty"`

	// Version Filter
	VersionFilters []uint64 `form:"version_filters" json:"version_filters,omitempty"`

	// Audit Filter
	//AuditFilter uint64 `form:"audit_filter" json:"audit_filter,omitempty"`

	PresentationTime uint32 `form:"presentation_time" json:"presentation_time,omitempty"`
}

type Class struct {
	ClassID uint64 `json:"class_id"`
	FieldID uint64 `json:"field_id"`
	Name    string `json:"name"`
}

type AdminShowCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminShowUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	*AdminShowCreateReq

	Iso_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"`
}

type AdminShowUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminShowStatusBatchReq struct {
	IDs    []uint64 `form:"ids" json:"ids,omitempty"`
	Status uint32   `form:"status" json:"status,omitempty" binding:"required,oneof=1 2"`
}

type AdminShowStatusBatchResp struct {
}

type AdminShowUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminShowUpdatePatchResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminShowDeleteReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminShowDeleteResp struct {
}

type AdminShowTmdbReq struct {
	TmdbID uint64 `form:"tmdb_id" json:"tmdb_id,omitempty"`

	Name string `form:"name" json:"name,omitempty"`

	common.DataListReq
}

type AdminShowTmdbResp struct {
	*tmdb.TmdbList
}

type AdminShowTmdbDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminShowTmdbDetailResp struct {
}

type AdminShowTmdbUrlReq struct {
	Name string `form:"name" json:"name,omitempty"`
}

type AdminShowTmdbUrlResp struct {
}
