package show

import (
	"vlab/app/common/dbs"
	bannerDao "vlab/app/dao/admin_banner"
	"vlab/app/dto/common"
)

type AdminMarketBannerListReq struct {
	common.DataListReq

	Position uint32 `form:"position" json:"position"`

	Status dbs.StatusType `form:"status" json:"status"`

	ChannelID uint64 `form:"channel_id" json:"channel_id"`
}

type AdminMarketBannerListResp struct {
	List  []*AdminMarketBannerListItem `json:"list"`
	Total int64                        `json:"total"`
}

type AdminMarketBannerListItem struct {
	*AdminMarketBannerDetail
}

type AdminMarketBannerDetailReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"`
}

type AdminMarketBannerDetailResp struct {
	*AdminMarketBannerDetail
}

// AdminMarketBannerDetail ...
type AdminMarketBannerDetail struct {
	ID       uint64             `json:"id"`                  // market_banner ID
	Title    string             `json:"title"`               // banner标题
	TitleKey string             `json:"title_key,omitempty"` // banner标题key
	Cover    string             `json:"cover"`               // banner封面
	JumpType bannerDao.JumpType `json:"jump_type"`           // banner跳转类型
	Jump     string             `json:"jump"`                // banner跳转链接
	Position bannerDao.Position `json:"position"`            // banner位置
	Sort     uint64             `json:"sort"`                // banner排序
	Status   dbs.StatusType     `json:"status"`              // banner状态
	Channel  *ChannelBase       `json:"channel,omitempty"`   // 渠道信息
}

type AdminMarketBannerCreateReq struct {
	Title     string             `json:"title" binding:"required,checkStringLength=100" msg:"Banner 标题最多100个字符"` // banner标题
	TitleKey  string             `json:"title_key" binding:"omitempty"`                                          // banner标题key
	Cover     string             `json:"cover" binding:"required"`                                               // banner封面
	JumpType  bannerDao.JumpType `json:"jump_type" binding:"required"`                                           // banner跳转类型
	Jump      string             `json:"jump" binding:"required"`                                                // banner跳转链接
	Position  bannerDao.Position `json:"position" binding:"required"`                                            // banner位置
	Sort      uint64             `json:"sort"`                                                                   // banner排序
	ChannelID uint64             `json:"channel_id" binding:"required"`                                          // 渠道ID
}

type AdminMarketBannerCreateResp struct {
	ID uint64 `json:"id"` // market_banner ID
}

type AdminMarketBannerUpdateReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"` // market_banner ID
	*AdminMarketBannerCreateReq
}

type AdminMarketBannerUpdateResp struct {
	ID uint64 `json:"id"` // market_banner ID
}

type AdminMarketBannerUpdatePatchReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"` // market_banner ID

	Status dbs.StatusType `json:"status" binding:"required,oneof=1 2 3"` // banner状态
}

type AdminMarketBannerUpdatePatchResp struct {
	ID uint64 `json:"id"` // market_banner ID
}

type AdminMarketBannerDeleteReq struct {
	ID uint64 `json:"id" form:"id" binding:"required"` // market_banner ID
}

type AdminMarketBannerDeleteResp struct {
}
