package show

import (
	"vlab/app/dto/common"
)

type AdminAssignListReq struct {
	common.DataListReq

	Status    uint32 `form:"status" json:"status,omitempty"`
	ChannelID uint64 `form:"channel_id" json:"channel_id,omitempty"`
}

type AdminAssignListResp struct {
	List  []*AdminAssign `json:"list"`
	Count int64          `json:"count"`
}

type AdminAssign struct {
	ID      uint64       `json:"id"`
	Show    *ShowBase    `json:"show"`
	Status  uint32       `json:"status"`
	Channel *ChannelBase `json:"channel,omitempty"`
}

type AdminAssignDetailReq struct {
	ID uint64 `form:"id" binding:"required"`
}

type AdminAssignDetailResp struct {
	*AdminAssign
}

type AdminAssignCreateReq struct {
	ShowID    uint64 `json:"show_id" binding:"required"`
	ChannelID uint64 `json:"channel_id" binding:"required"`
}

type AdminAssignCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminAssignUpdateReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`

	ShowID    uint64 `json:"show_id" binding:"required"`
	ChannelID uint64 `json:"channel_id" binding:"required"`
}

type AdminAssignUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminAssignUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminAssignUpdatePatchResp struct{}

type AdminAssignDeleteReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminAssignDeleteResp struct{}
