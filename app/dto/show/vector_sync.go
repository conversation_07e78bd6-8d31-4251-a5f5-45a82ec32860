package show

// VectorSyncManualReq 手动同步请求
type VectorSyncManualReq struct {
	Hours int `json:"hours" form:"hours"` // 同步多少小时内的数据
}

// VectorSyncResp 向量同步响应
type VectorSyncResp struct {
	Status       string `json:"status"`        // 同步状态: success/failed
	Message      string `json:"message"`       // 消息
	TotalCount   int    `json:"total_count"`   // 总数量
	SuccessCount int    `json:"success_count"` // 成功数量
	FailedCount  int    `json:"failed_count"`  // 失败数量
	StartTime    string `json:"start_time"`    // 开始时间
	EndTime      string `json:"end_time"`      // 结束时间
}
