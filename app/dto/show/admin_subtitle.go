package show

import (
	"vlab/app/dto/common"
)

type AdminSubtitleListReq struct {
	common.DataListReq

	EpisodeID uint64 `form:"episode_id" json:"episode_id,omitempty" binding:"required"`
	VideoID   uint64 `form:"video_id" json:"video_id,omitempty"`
}

type AdminSubtitleListResp struct {
	List []*AdminSubtitle `json:"list,omitempty"`

	Count int64 `json:"count,omitempty"`
}

type AdminSubtitle struct {
	ID uint64 `json:"id,omitempty"`

	EpisodeID uint64 `json:"episode_id,omitempty"`
	VideoID   uint64 `json:"video_id,omitempty"`

	ISO_639_1 string `json:"iso_639_1,omitempty"`

	FilePath string `json:"file_path,omitempty"`
}

type AdminSubtitleDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminSubtitleDetailResp struct {
	ID uint64 `json:"id,omitempty"`

	EpisodeID uint64 `json:"episode_id,omitempty"`

	VideoID uint64 `json:"video_id,omitempty"`

	ISO_639_1 string `json:"iso_639_1,omitempty"`

	FilePath string `json:"file_path,omitempty"`
}

type AdminSubtitleCreateReq struct {
	EpisodeID uint64 `form:"episode_id" json:"episode_id,omitempty" binding:"required"`

	VideoID uint64 `form:"video_id" json:"video_id,omitempty" binding:"required"`

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"required"`

	FilePath string `form:"file_path" json:"file_path,omitempty" binding:"required"`
}

type AdminSubtitleCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminSubtitleCreateBatchReq struct {
	EpisodeID uint64 `form:"episode_id" json:"episode_id,omitempty" binding:"required"`

	VideoID uint64 `form:"video_id" json:"video_id,omitempty" binding:"required"`

	List []*AdminSubtitleCreateBatchItem `json:"list,omitempty"`
}

type AdminSubtitleCreateBatchItem struct {
	ISO_639_1 string `json:"iso_639_1,omitempty"`

	FilePath string `json:"file_path,omitempty"`
}

type AdminSubtitleCreateBatchResp struct {
}

type AdminSubtitleUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	EpisodeID uint64 `form:"episode_id" json:"episode_id,omitempty"`

	VideoID uint64 `form:"video_id" json:"video_id,omitempty"`

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty"`

	FilePath string `form:"file_path" json:"file_path,omitempty"`
}

type AdminSubtitleUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminSubtitleDeleteReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminSubtitleDeleteResp struct {
}
