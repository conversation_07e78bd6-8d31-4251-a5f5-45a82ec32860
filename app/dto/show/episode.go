package show

import watchVideoPv "vlab/app/dao/user/watch_video_pv"

type EpisodeDetailReq struct {
	ShowID uint64 `form:"show_id" json:"show_id" binding:"omitempty"` // 剧ID

	EpisodeID uint64 `form:"episode_id" json:"episode_id" binding:"omitempty"` // 集ID

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识

	Resolution uint32 `form:"resolution" json:"resolution,omitempty" binding:"omitempty,oneof=2 3 4 5"` // 分辨率

	Action watchVideoPv.LogType
	ShowFilterReq
}

type EpisodeDetailResp struct {
	*EpisodeBase

	Videos []*VideoBase `json:"videos"` // 视频
}

type EpisodeShowDetailReq struct {
	EpisodeID uint64 `form:"episode_id" json:"episode_id" binding:"required"`

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1,omitempty" binding:"omitempty"` // 语言标识
}

type EpisodeShowDetailResp struct {
	Show    *ShowBase            `json:"show"`
	Episode []*EpisodeDetailResp `json:"episode"`
}
