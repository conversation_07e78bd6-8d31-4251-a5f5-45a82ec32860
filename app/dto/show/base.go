package show

import (
	"vlab/app/dao"
)

type ShowBase struct {
	ID           uint64       `json:"id"`
	Posters      []*ImageBase `json:"posters"`
	Name         string       `json:"name"`
	OriginalName string       `json:"original_name"`
	Overview     string       `json:"overview"`
	Genres       []*GenreBase `json:"genres"`
	Score        uint32       `json:"score"`
	AirDate      string       `json:"air_date"`
	//NumberOfSeasons  uint32         `json:"number_of_seasons"`
	NumberOfEpisodes uint32         `json:"number_of_episodes"`
	ContentType      uint32         `json:"content_type"`
	InProduction     uint32         `json:"in_production"`
	Franchise        *FranchiseBase `json:"franchise"`

	Classes []*ClassBase `json:"classes"`

	Status uint32 `json:"status,omitempty"`
}

type ImageBase struct {
	ID          uint64  `json:"id,omitempty"`
	AspectRatio float32 `json:"aspect_ratio"`
	Height      uint32  `json:"height"`
	Iso6391     string  `json:"iso_639_1"`
	FilePath    string  `json:"file_path"`
	Width       uint32  `json:"width"`

	AspectType uint32 `json:"aspect_type"`
}

type GenreBase struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`

	NameKey string `json:"name_key,omitempty"`

	NameI18n []*dao.I18n `json:"name_i18n,omitempty"`
}

type FranchiseBase struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`

	NameKey string `json:"name_key"`

	NameI18n []*dao.I18n `json:"name_i18n,omitempty"`
}

type ClassBase struct {
	Id       uint64      `json:"id"`
	ClassID  uint64      `json:"class_id"`
	Name     string      `json:"name"`
	NameKey  string      `json:"name_key,omitempty"`
	NameI18n []*dao.I18n `json:"name_i18n,omitempty"`
}

type SeasonBase struct {
	Id           uint64         `json:"id"`
	AirDate      string         `json:"air_date"`
	EpisodeCount uint32         `json:"episode_count"`
	Name         string         `json:"name"`
	Overview     string         `json:"overview"`
	PosterPath   string         `json:"poster_path"`
	SeasonNumber uint32         `json:"season_number"`
	Episodes     []*EpisodeBase `json:"episodes"`
}

type EpisodeBase struct {
	Id            uint64 `json:"id"`
	Name          string `json:"name"`
	EpisodeNumber uint32 `json:"episode_number"`
}

type VideoBase struct {
	Id           uint64          `json:"id"`
	Still        *ImageBase      `json:"still"`
	Name         string          `json:"name"`
	OriginalName string          `json:"original_name"`
	VideoPath    string          `json:"video_path"`
	Runtime      int64           `json:"runtime"`      // 接口时长
	RealRuntime  int64           `json:"real_runtime"` // 实际时长
	Subtitles    []*SubtitleBase `json:"subtitles"`
	Resolution   uint32          `json:"resolution"`
}

type SubtitleBase struct {
	Id       uint64 `json:"id"`
	Iso6391  string `json:"iso_639_1"`
	FilePath string `json:"file_path"`
}

type PersonBase struct {
	Id           uint64 `json:"id"`
	Name         string `json:"name"`
	OriginalName string `json:"original_name"`
	Gender       uint32 `json:"gender"`
	ProfilePath  string `json:"profile_path"`
}

type ChannelBase struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

type CreditBase struct {
	Id uint64 `json:"id"`
	*PersonBase
	Job        uint32 `json:"job"`
	Department string `json:"department"`
	Character  string `json:"character"`
	PersonID   uint64 `json:"person_id"`
	Order      uint32 `json:"order"`
}
