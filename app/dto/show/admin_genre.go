package show

import (
	"vlab/app/dao"
	"vlab/app/dto/common"
)

type AdminGenreListReq struct {
	common.DataListReq

	Name string `form:"name" json:"name,omitempty"`

	Status uint32 `form:"status" json:"status,omitempty"`
}

type AdminGenreListResp struct {
	List []*AdminGenre `json:"list,omitempty"`

	Count int64 `json:"count,omitempty"`
}

type AdminGenreDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminGenre struct {
	ID uint64 `json:"id,omitempty"`

	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`

	Status uint32 `json:"status,omitempty"`

	GenreNameI18n []*dao.I18n `json:"genre_name_i18n"`
}

type AdminGenreDetailResp struct {
	*AdminGenre
}

type AdminGenreCreateReq struct {
	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`
}

type AdminGenreCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminGenreUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`
}

type AdminGenreUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminGenreUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminGenreUpdatePatchResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminGenreDeleteReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminGenreDeleteResp struct{}
