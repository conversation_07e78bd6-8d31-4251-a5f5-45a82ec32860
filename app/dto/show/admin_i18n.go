package show

import (
	"vlab/app/dao"
)

type GenI18nKeyReq struct {
	List []*dao.I18n `json:"list" binding:"required"`
}

type GenI18nKeyResp struct {
	Key string `json:"key"`
}

type AdminI18nCreateReq struct {
	List []*dao.I18n `json:"list" binding:"required"`
}

type AdminI18nCreateResp struct {
	Key string `json:"key"`
}

type AdminI18nDetailReq struct {
	Key string `form:"key" binding:"required"`
}

type AdminI18nDetailResp struct {
	List []*dao.I18n `json:"list" binding:"required"`
}
