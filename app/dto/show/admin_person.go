package show

import (
	"vlab/app/dao"
	"vlab/app/dto/common"
)

type AdminPersonListReq struct {
	common.DataListReq

	Name string `form:"name" json:"name,omitempty"`

	Status uint32 `form:"status" json:"status,omitempty"`
}

type AdminPersonListResp struct {
	List  []*AdminPerson `json:"list,omitempty"`
	Count int64          `json:"count,omitempty"`
}

type AdminPerson struct {
	ID   uint64 `json:"id,omitempty"`
	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`
	Gender  uint32 `json:"gender,omitempty"`

	Status uint32 `json:"status,omitempty"`

	PersonNameI18n []*dao.I18n `json:"person_name_i18n,omitempty"`

	Profile ImageBase `json:"profile,omitempty"`
}

type AdminPersonDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminPersonDetailResp struct {
	*AdminPerson
}

type AdminPersonCreateReq struct {
	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`

	Gender uint32 `form:"gender" json:"gender,omitempty"`

	Profile ImageBase `form:"profile" json:"profile,omitempty"`
}

type AdminPersonCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminPersonUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`

	Gender uint32 `form:"gender" json:"gender,omitempty"`

	Profile ImageBase `form:"profile" json:"profile,omitempty"`
}

type AdminPersonUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminPersonUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminPersonUpdatePatchResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminPersonDeleteReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminPersonDeleteResp struct{}
