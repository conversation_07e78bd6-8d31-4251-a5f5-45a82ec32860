package show

import (
	"vlab/app/dao"
	"vlab/app/dto/common"
)

type AdminRecommendListReq struct {
	common.DataListReq

	Status uint32 `form:"status" json:"status,omitempty"`

	ChannelID uint64 `form:"channel_id" json:"channel_id,omitempty"`
}

type AdminRecommendListResp struct {
	List []*AdminRecommend `json:"list"`

	Count int64 `json:"count"`
}

type AdminRecommend struct {
	ID uint64 `json:"id"`

	Show *ShowBase `json:"show"`

	Image *ImageBase `json:"image"`

	Name string `json:"name"`

	NameKey string `json:"name_key"`

	Status uint32 `json:"status"`

	RecommendNameI18n []*dao.I18n `json:"recommend_name_i18n"`

	Channel *ChannelBase `json:"channel,omitempty"`
}

type AdminRecommendDetailReq struct {
	ID uint64 `form:"id" binding:"required"`
}

type AdminRecommendDetailResp struct {
	*AdminRecommend
}

type AdminRecommendCreateReq struct {
	ShowID uint64 `json:"show_id" binding:"required"`

	Image *ImageBase `json:"image" binding:"omitempty"`

	Name string `json:"name" binding:"required"`

	NameKey string `json:"name_key" binding:"omitempty"`

	Order uint32 `json:"order" binding:"omitempty"`

	ChannelID uint64 `json:"channel_id" binding:"required"`
}

type AdminRecommendCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminRecommendUpdateReq struct {
	ID uint64 `json:"id" binding:"required"`

	*AdminRecommendCreateReq
}

type AdminRecommendUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminRecommendUpdatePatchReq struct {
	ID uint64 `json:"id" binding:"required"`
}

type AdminRecommendUpdatePatchResp struct {
}

type AdminRecommendDeleteReq struct {
	ID uint64 `form:"id" binding:"required"`
}

type AdminRecommendDeleteResp struct {
}
