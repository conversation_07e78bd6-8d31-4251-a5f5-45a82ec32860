package show

// ShowI18nExport 用于导出show记录的多语言JSON数据
type ShowI18nExport struct {
	ID           uint64   `json:"id"`            // show表的主键ID
	Name         string   `json:"name"`          // show表的name字段值
	NameI18n     []string `json:"name_i18n"`     // 所有语言版本的name值数组
	Overview     string   `json:"overview"`      // show表的overview字段值
	OverviewI18n []string `json:"overview_i18n"` // 所有语言版本的overview值数组
	Status       uint32   `json:"status"`        // show表的status字段值 (0=禁用, 1=启用)
	Score        uint32   `json:"score"`         // show表的score字段值（剧集评分）
	ContentType  uint32   `json:"content_type"`  // show表的content_type字段值 (1=Movie, 2=TV, 3=Comic)

	// 标量字段
	Region     string   `json:"region"`      // 地区名称 (class_id=2)
	RegionI18n []string `json:"region_i18n"` // 地区的多语言版本
	RegionIDs  []uint64 `json:"region_ids"`  // 地区的ID列表
	Year       string   `json:"year"`        // 年份名称 (class_id=3)
	YearI18n   []string `json:"year_i18n"`   // 年份的多语言版本
	YearIDs    []uint64 `json:"year_ids"`    // 年份的ID列表
	Genre      string   `json:"genre"`       // 类型名称 (class_id=5)
	GenreI18n  []string `json:"genre_i18n"`  // 类型的多语言版本
	GenreIDs   []uint64 `json:"genre_ids"`   // 类型的ID列表
}

// ShowI18nExportReq 导出请求参数
type ShowI18nExportReq struct {
	Status       *uint32 `json:"status,omitempty"`        // 过滤状态，不传则查询所有
	IncludeEmpty bool    `json:"include_empty,omitempty"` // 是否包含空翻译值
	BatchSize    int     `json:"batch_size,omitempty"`    // 批次大小，默认1000
	OutputPath   string  `json:"output_path,omitempty"`   // 输出文件路径
}

// ShowI18nExportConfig 导出配置
type ShowI18nExportConfig struct {
	BatchSize    int                                        // 每批处理的记录数
	OutputPath   string                                     // 输出文件路径
	Status       *uint32                                    // 过滤状态，不传则查询所有
	IncludeEmpty bool                                       // 是否包含空翻译值
	ProgressFunc func(current, total int64, message string) // 进度回调函数
}

// ShowI18nExportResp 导出响应
type ShowI18nExportResp struct {
	Total int64             `json:"total"` // 总记录数
	List  []*ShowI18nExport `json:"list"`  // 导出数据列表
}
