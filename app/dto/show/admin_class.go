package show

import (
	"vlab/app/common/dbs"
	"vlab/app/dto/common"
)

// AdminClassListReq ...
type AdminClassListReq struct {
	common.DataListReq

	ISO_639_1 string `form:"iso_639_1" json:"iso_639_1"`
}

// AdminClassListResp ...
type AdminClassListResp struct {
	Total uint64                `json:"total"`
	List  []*AdminClassListItem `json:"list"`
}

// AdminClassListItem ...
type AdminClassListItem struct {
	ID      uint64         `json:"id"`
	Name    string         `json:"name"`
	NameKey string         `json:"name_key"`
	Status  dbs.StatusType `json:"status"`
}

// AdminClassDetailReq ...
type AdminClassDetailReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassDetailResp ...
type AdminClassDetailResp struct {
	*AdminClassListItem
	Fields []*AdminClassFieldItem
}

type AdminClassFieldItem struct {
	ID      uint64         `json:"id"`
	ClassID uint64         `json:"class_id"`
	Name    string         `json:"name"`
	NameKey string         `json:"name_key"`
	Status  dbs.StatusType `json:"status"`
	Cover   string         `json:"cover"`
	Desc    string         `json:"desc"`
}

// AdminClassCreateReq ...
type AdminClassCreateReq struct {
	Name    string `json:"name"`
	NameKey string `json:"name_key"`
}

// AdminClassCreateResp ...
type AdminClassCreateResp struct {
	ID uint64 `json:"id"`
}

// AdminClassUpdateReq ...
type AdminClassUpdateReq struct {
	ID uint64 `form:"id" json:"id"`
	*AdminClassCreateReq
}

// AdminClassUpdateResp ...
type AdminClassUpdateResp struct {
	ID uint64 `json:"id"`
}

// AdminClassUpdatePatchReq ...
type AdminClassUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassUpdatePatchResp ...
type AdminClassUpdatePatchResp struct {
	ID uint64 `json:"id"`
}

// AdminClassDeleteReq ...
type AdminClassDeleteReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassDeleteResp ...
type AdminClassDeleteResp struct {
}

// AdminClassFieldListReq ...
type AdminClassFieldListReq struct {
	common.DataListReq
	ClassID uint64 `form:"class_id" json:"class_id"`
}

// AdminClassFieldListResp ...
type AdminClassFieldListResp struct {
	Total uint64                 `json:"total"`
	List  []*AdminClassFieldItem `json:"list"`
}

// AdminClassFieldDetailReq ...
type AdminClassFieldDetailReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassFieldDetailResp ...
type AdminClassFieldDetailResp struct {
	*AdminClassFieldItem
}

// AdminClassFieldCreateReq ...
type AdminClassFieldCreateReq struct {
	Name    string `json:"name"`
	NameKey string `json:"name_key"`
	ClassID uint64 `json:"class_id"`
	Cover   string `json:"cover"`
	Desc    string `json:"desc"`
}

// AdminClassFieldCreateResp ...
type AdminClassFieldCreateResp struct {
	ID uint64 `json:"id"`
}

// AdminClassFieldUpdateReq ...
type AdminClassFieldUpdateReq struct {
	ID uint64 `form:"id" json:"id"`
	*AdminClassFieldCreateReq
}

// AdminClassFieldUpdateResp ...
type AdminClassFieldUpdateResp struct {
	ID uint64 `json:"id"`
}

// AdminClassFieldUpdatePatchReq ...
type AdminClassFieldUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassFieldUpdatePatchResp ...
type AdminClassFieldUpdatePatchResp struct {
	ID uint64 `json:"id"`
}

// AdminClassFieldDeleteReq ...
type AdminClassFieldDeleteReq struct {
	ID uint64 `form:"id" json:"id"`
}

// AdminClassFieldDeleteResp ...
type AdminClassFieldDeleteResp struct {
}
