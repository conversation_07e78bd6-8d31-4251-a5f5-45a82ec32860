package show

import (
	"vlab/app/dao"
	"vlab/app/dto/common"
)

type AdminFranchiseListReq struct {
	common.DataListReq

	Name string `form:"name" json:"name,omitempty"`

	Status uint32 `form:"status" json:"status,omitempty"`
}

type AdminFranchiseListResp struct {
	List []*AdminFranchise `json:"list,omitempty"`

	Count int64 `json:"count,omitempty"`
}

type AdminFranchise struct {
	ID uint64 `json:"id,omitempty"`

	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`

	Status uint32 `json:"status,omitempty"`

	FranchiseNameI18n []*dao.I18n `json:"franchise_name_i18n,omitempty"`
}

type AdminFranchiseDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminFranchiseDetailResp struct {
	*AdminFranchise
}

type AdminFranchiseCreateReq struct {
	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`
}

type AdminFranchiseCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminFranchiseUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty"`
}

type AdminFranchiseUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminFranchiseUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminFranchiseUpdatePatchResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminFranchiseDeleteReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminFranchiseDeleteResp struct {
}
