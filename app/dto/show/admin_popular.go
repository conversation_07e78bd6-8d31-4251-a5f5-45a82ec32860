package show

import (
	"vlab/app/dto/common"
)

type AdminPopularListReq struct {
	common.DataListReq

	Status uint32 `form:"status" json:"status,omitempty"`

	ChannelID uint64 `form:"channel_id" json:"channel_id,omitempty"`
}

type AdminPopularListResp struct {
	List []*AdminPopular `json:"list"`

	Count int64 `json:"count"`
}

type AdminPopular struct {
	ID      uint64       `json:"id"`
	Show    *ShowBase    `json:"show"`
	Heat    uint32       `json:"heat"`
	Channel *ChannelBase `json:"channel,omitempty"`
	Status  uint32       `json:"status"`
}

type AdminPopularDetailReq struct {
	ID uint64 `form:"id" binding:"required"`
}

type AdminPopularDetailResp struct {
	*AdminPopular
}

type AdminPopularCreateReq struct {
	ShowID    uint64 `json:"show_id" binding:"required"`
	Heat      uint32 `json:"heat" binding:"required"`
	ChannelID uint64 `json:"channel_id" binding:"required"`
}

type AdminPopularCreateResp struct {
	ID uint64 `json:"id"`
}

type AdminPopularUpdateReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`

	ShowID uint64 `json:"show_id" binding:"required"`

	Heat uint64 `json:"heat" binding:"required"`

	ChannelID uint64 `json:"channel_id" binding:"required"`
}

type AdminPopularUpdateResp struct {
	ID uint64 `json:"id"`
}

type AdminPopularUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id" binding:"required"`
}

type AdminPopularUpdatePatchResp struct {
}

type AdminPopularDeleteReq struct {
	ID uint64 `form:"id" binding:"required"`
}

type AdminPopularDeleteResp struct {
}
