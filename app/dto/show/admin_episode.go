package show

import (
	"vlab/app/dao"
	episodeDao "vlab/app/dao/content_episode"
	videoDao "vlab/app/dao/content_video"
	"vlab/app/dto/common"
)

type AdminEpisodeListReq struct {
	common.DataListReq

	ShowID uint64 `form:"show_id" json:"show_id,omitempty" binding:"required"`

	Name string `form:"name" json:"name,omitempty"`

	EpisodeNumber uint32 `form:"episode_number" json:"episode_number,omitempty"`

	Status uint32 `form:"status" json:"status,omitempty"`
}

type AdminEpisodeListResp struct {
	List []*AdminEpisode `json:"list,omitempty"`

	Count int64 `json:"count,omitempty"`
}

type AdminEpisode struct {
	ID uint64 `json:"id,omitempty"`

	Status uint32 `json:"status,omitempty"`

	ShowID uint64 `json:"show_id,omitempty"`

	Name string `json:"name,omitempty"`

	NameKey string `json:"name_key,omitempty"`

	EpisodeNameI18n []*dao.I18n `json:"episode_name_i18n,omitempty"`

	EpisodeNumber uint32 `json:"episode_number,omitempty"`

	VideoID uint64 `json:"video_id,omitempty"`
}

type AdminEpisodeDetailReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminEpisodeDetailResp struct {
	*AdminEpisode

	Video *AdminEpisodeVideo `form:"video" json:"video,omitempty"`
}

type AdminEpisodeCreateReq struct {
	ShowID uint64 `form:"show_id" json:"show_id,omitempty" binding:"required"`

	Name string `form:"name" json:"name,omitempty" binding:"required"`

	NameKey string `form:"name_key" json:"name_key,omitempty" binding:"omitempty"`

	EpisodeNumber uint32 `form:"episode_number" json:"episode_number,omitempty" binding:"required"`

	Video *AdminEpisodeVideo `form:"video" json:"video,omitempty" binding:"required"`
}

type AdminEpisodeCreateResp struct {
	ID uint64 `json:"id,omitempty"`
}

type AdminEpisodeCreateBatchReq struct {
	ShowID uint64 `form:"show_id" json:"show_id,omitempty" binding:"required"`

	List []*AdminEpisodeCreateBatchItem `json:"list,omitempty" binding:"omitempty,dive"`
}

type AdminVideoImportBatchReq struct {
	EpisodeID uint64             `form:"episode_id" json:"episode_id,omitempty" binding:"required"`
	VideoID   uint64             `form:"video_id" json:"video_id,omitempty" binding:"required"`
	Name      string             `form:"name" json:"name,omitempty" binding:"required"`
	Video     *AdminEpisodeVideo `form:"video" json:"video,omitempty" binding:"required"`
}

type AdminVideoImportBatchResp struct {
}

type AdminEpisodeCreateBatchItem struct {
	Name string `json:"name,omitempty" binding:"required"`

	NameKey string `json:"name_key,omitempty" binding:"omitempty"`

	EpisodeNumber uint32 `json:"episode_number,omitempty" binding:"required"`

	Video *AdminEpisodeVideo `form:"video" json:"video,omitempty" binding:"required"`

	MappingType uint32 `json:"mapping_type,omitempty" binding:"omitempty"`
	MappingID   uint64 `json:"mapping_id,omitempty" binding:"omitempty"`
}

type AdminEpisodeVideo struct {
	VideoID uint64 `json:"video_id,omitempty"`

	//VideoName string `json:"video_name,omitempty"` // 文件名

	PathType uint32 `json:"path_type" binding:"oneof=1 2 3"` // 文件类型 1: 直接路径 2: vod 3: 原始方式

	Runtime int64 `json:"runtime,omitempty"` // 运行时长,单位秒

	VideoPath string `json:"video_path,omitempty"` // 文件路径

	UrlPaths []*UrlPathItem `json:"url_paths,omitempty" binding:"omitempty,dive"` // 文件路径

	//VideoEtag string `json:"video_etag,omitempty" binding:"required"` // 文件etag

	VideoMps []*AdminVideoMps `json:"video_mps,omitempty" binding:"omitempty"` // 不同清晰度返回
}

type AdminVideoMps struct {
	VideoMpsID uint64 `json:"video_mps_id,omitempty"`

	Resolution uint32 `json:"resolution,omitempty"` // 清晰度

	Status uint32 `json:"status,omitempty"` // 状态

}

type UrlPathItem struct {
	Resolution uint32 `json:"resolution" binding:"oneof=2 3 4 5" msg:"请选择正确的清晰度"`
	Url        string `json:"url"`
}

type AdminEpisodeCreateBatchResp struct {
	EpisodeList episodeDao.EpisodeList
	VideoList   videoDao.VideoList
}

type AdminEpisodeUpdatePatchReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`
}

type AdminEpisodeUpdatePatchResp struct {
}

type AdminEpisodeUpdateReq struct {
	ID uint64 `form:"id" json:"id,omitempty" binding:"required"`

	ShowID uint64 `form:"show_id" json:"show_id,omitempty" binding:"omitempty"`

	Name string `form:"name" json:"name,omitempty" binding:"omitempty"`

	NameKey string `form:"name_key" json:"name_key,omitempty" binding:"omitempty"`

	EpisodeNumber uint32 `form:"episode_number" json:"episode_number,omitempty" binding:"omitempty"`

	Video *AdminEpisodeVideo `form:"video" json:"video,omitempty" binding:"omitempty"`
}

type AdminEpisodeUpdateResp struct {
	ID uint64 `json:"id,omitempty"`
}
