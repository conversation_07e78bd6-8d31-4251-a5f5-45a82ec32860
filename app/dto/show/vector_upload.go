package show

// AdminShowVectorUploadReq 单个剧集向量上传请求
type AdminShowVectorUploadReq struct {
	ShowID uint64 `json:"show_id" binding:"required,gt=0"` // 剧集ID
}

// AdminShowVectorUploadResp 单个剧集向量上传响应
type AdminShowVectorUploadResp struct {
	ShowID        uint64 `json:"show_id"`        // 剧集ID
	Status        string `json:"status"`         // 上传状态: success/failed
	Message       string `json:"message"`        // 状态消息
	DocumentCount int    `json:"document_count"` // 上传的文档数量
}
