package byte

type UploadMediaByUrlReq struct {
	UrlList []string `form:"url_list" json:"url_list" binding:"required,omitempty"`
}

type QueryUploadTaskInfoReq struct {
	JobIds []string `form:"job_ids" json:"job_ids" binding:"required,omitempty"`
}

type GetMediaInfoReq struct {
	Vid string `form:"vid" json:"vid" binding:"required,omitempty"`
}

// ==========================================
type VodUploadInfoReq struct {
	FileName string `form:"file_name" json:"file_name" binding:"required,omitempty"`
	Title    string `form:"title" json:"title" binding:"required,omitempty"`
}

type VodUploadInfoResp struct {
	RequestId     string `json:"request_id"`
	UploadAddress string `json:"upload_address"`
	VideoId       string `json:"video_id"`
	UploadAuth    string `json:"upload_auth"`
	RegionID      string `json:"region_id"`
	RamUID        string `json:"ram_uid"`
}

type GetURLUploadInfosReq struct {
	JobIds []string `form:"job_ids" json:"job_ids" binding:"required,omitempty"`
}

type UploadMediaByUrlResp struct {
}
