package paypal

type PaypalProductSetReq struct {
	ProductType string `json:"product_type" form:"product_type" binding:"required,oneof=month quarter year"`
}

type PaypalProductInfoReq struct {
	ProductID string `json:"product_id" form:"product_id" binding:"required"`
}

type PaypalPlanInfoReq struct {
	PlanID string `json:"plan_id" form:"plan_id" binding:"required"`
}

type PaypalPlanCreateReq struct {
	ProductType string `json:"product_type" form:"product_type" binding:"required,oneof=month quarter year"`
}

type PaypalSubscripeInfoReq struct {
	SubID string `json:"sub_id" form:"sub_id" binding:"required"`
}

type PaypalOrderInfoReq struct {
	OrderID string `json:"order_id" form:"order_id" binding:"required"`
}
