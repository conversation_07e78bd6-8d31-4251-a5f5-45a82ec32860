package googleplay

type PurchaseSubscriptionInfoReq struct {
	PackageName   string `json:"package_name" form:"package_name" binding:"required"`
	PurchaseToken string `json:"purchase_token" form:"purchase_token" binding:"required"`
}

type PurchaseSubscriptionAcknowledgeReq struct {
	PackageName   string `json:"package_name" form:"package_name" binding:"required"`
	SubId         string `json:"sub_id" form:"sub_id" binding:"required"`
	PurchaseToken string `json:"purchase_token" form:"purchase_token" binding:"required"`
}
