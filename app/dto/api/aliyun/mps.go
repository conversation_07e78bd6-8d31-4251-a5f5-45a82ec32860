package aliyun

type SubmitTranscodeJobReq struct {
	VideoID  uint64 `form:"video_id" json:"video_id" binding:"required,omitempty"`
	VideoKey string `form:"video_key" json:"video_key" binding:"required,omitempty"`
}

type SubmitTranscodeJobChanReq struct {
	VideoID uint64 `form:"video_id" json:"video_id" binding:"required,omitempty"`
}

type GetTranscodeJobSignedUrl struct {
	SubmitTranscodeJobChanReq
	Resolution uint32 `form:"resolution" json:"resolution" binding:"oneof=1 2 3 4 5 6 7"`
}

type SubmitTranscodeJobResp struct {
	ObjKey     string `json:"obj_key"`
	SignedUrl  string `json:"signed_url"`
	Expiration string `json:"expiration"`
}
