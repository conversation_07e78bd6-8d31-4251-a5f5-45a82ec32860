package aliyun

type AliyunOssTokenReq struct {
	BucketType string `form:"bucket_type" json:"bucket_type" binding:"oneof=image video video_mps"`
}

type AliyunOssTokenResp struct {
	Bucket          string `json:"bucket"`
	Token           string `json:"token"`
	RegionID        string `json:"regionID"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
}

type AliyunOssGenVideoSignedUrlReq struct {
	BucketType string `form:"bucket_type" json:"bucket_type" binding:"oneof=image video video_mps"`
	ObjKey     string `form:"obj_key" json:"obj_key" binding:"required,omitempty"`
}

type GenVideoSignedUrlResp struct {
	SignedUrl  string `json:"signed_url"`
	Expiration string `json:"expiration"`
}
