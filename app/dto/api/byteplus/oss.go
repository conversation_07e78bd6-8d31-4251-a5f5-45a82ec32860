package byteplus

type BytePlusOssTokenReq struct {
	BucketType string `form:"bucket_type" json:"bucket_type" binding:"oneof=image subtitle video video_mps"`
}

type BytePlusOssTokenResp struct {
	Endpoint        string `json:"endpoint"`
	Bucket          string `json:"bucket"`
	Token           string `json:"token"`
	RegionID        string `json:"regionID"`
	AccessKeyId     string `json:"accessKeyId"`
	AccessKeySecret string `json:"accessKeySecret"`
}
