package statistic

import watchVideoPv "vlab/app/dao/user/watch_video_pv"

type GetShowDailyActiveReq struct {
	StartDate string `json:"start_date" form:"start_date" binding:"required,datetime=2006-01-02" msg:"开始日期格式不正确,应为YYYY-MM-DD格式"`
	EndDate   string `json:"end_date" form:"end_date" binding:"required,datetime=2006-01-02,gtefield=StartDate" msg:"结束日期格式不正确,应为YYYY-MM-DD格式且不能早于开始日期"`
	Date      string `json:"date" form:"date"`
	ChannelID uint64 `json:"channel_id" form:"channel_id"`
	ShowID    uint64 `json:"show_id" form:"show_id"`
	Lang      string `json:"lang" form:"lang"`
	LogType   watchVideoPv.LogType
}

type GetShowDailyActiveResp struct {
	Date string `json:"date"`
	Num  int64  `json:"num"`
}

type GetShowPlaynumTopReq struct {
	GetShowDailyActiveReq
	Limit int `json:"limit" form:"limit" binding:"required,min=5,max=100" msg:"limit范围为5-100"`
}

type GetShowPlaynumTopResp struct {
	ShowID   uint64 `json:"show_id"`
	ShowName string `json:"show_name"`
	Num      int64  `json:"num"`
}
