package common

type UserIDReq struct {
	UserID uint64 `form:"user_id" json:"user_id" binding:"required,omitempty"`
}

type UserAttachment struct {
	License     string `json:"license"`       // 营业执照
	IdCardFront string `json:"id_card_front"` // 身份证正面照
	IdCardBack  string `json:"id_card_back"`  // 身份证背面照
}

type UserFinance struct {
	Account        string `json:"account"`         // 开户名称
	BankName       string `json:"bank_name"`       // 开户银行
	BankAccount    string `json:"bank_account"`    // 银行账号
	TaxpayerID     string `json:"taxpayer_id"`     // 纳税人识别号
	InvoiceTitle   string `json:"invoice_title"`   // 发票抬头
	InvoiceContent string `json:"invoice_content"` // 发票内容
	AlipayAccount  string `json:"alipay_account"`  // 支付宝账号
}

type CommonUserInfo struct {
	ID            uint64 `json:"id"`
	Nickname      string `json:"nickname"`
	Avatar        string `json:"avatar"`
	Mobile        string `json:"mobile"`
	Email         string `json:"email"`
	Status        uint32 `json:"status"`
	LastLoginTime string `json:"last_login_time"`
	CreatedAt     string `json:"created_at"`
}

type SetUserFinanceReq struct {
	Account        string `form:"account" json:"account" binding:"omitempty,max=20"`
	BankName       string `form:"bank_name" json:"bank_name" binding:"omitempty,max=20"`
	BankAccount    string `form:"bank_account" json:"bank_account" binding:"omitempty,max=32"`
	TaxpayerID     string `form:"taxpayer_id" json:"taxpayer_id" binding:"omitempty,max=32"`
	InvoiceTitle   string `form:"invoice_title" json:"invoice_title" binding:"omitempty,max=32"`
	InvoiceContent string `form:"invoice_content" json:"invoice_content" binding:"omitempty,max=32"`
	AlipayAccount  string `form:"alipay_account" json:"alipay_account" binding:"omitempty,max=32"`
}

type CommonUserAddrInfo struct {
	ID           uint64 `json:"id"`
	UserID       uint64 `json:"user_id"`
	Consignee    string `json:"consignee"`
	Mobile       string `json:"mobile"`
	ProvinceID   uint64 `json:"province_id"`
	CityID       uint64 `json:"city_id"`
	DistrictID   uint64 `json:"district_id"`
	ProvinceName string `json:"province_name"`
	CityName     string `json:"city_name"`
	DistrictName string `json:"district_name"`
	Address      string `json:"address"`
	IsDefault    uint32 `json:"is_default"`
	CreatedAt    string `json:"created_at"`
}
