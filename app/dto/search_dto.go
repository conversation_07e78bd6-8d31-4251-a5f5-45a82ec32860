package dto

import "time"

// SearchRequest 搜索请求结构
type SearchRequest struct {
	// 基础搜索参数
	Query      string `json:"query" form:"query"`             // 搜索关键词
	SearchMode string `json:"search_mode" form:"search_mode"` // 搜索模式：vector, hybrid, filter_only
	Language   string `json:"language" form:"language"`       // 语言偏好：zh, en等

	// 筛选条件
	Filters SearchFilters `json:"filters" form:"filters"`

	// 分页参数
	Page     int `json:"page" form:"page"`           // 页码，从1开始
	PageSize int `json:"page_size" form:"page_size"` // 每页大小

	// 排序参数
	SortBy    string `json:"sort_by" form:"sort_by"`       // 排序字段：relevance, rating, year, created_at
	SortOrder string `json:"sort_order" form:"sort_order"` // 排序顺序：asc, desc

	// 向量搜索参数
	VectorParams VectorSearchParams `json:"vector_params" form:"vector_params"`
}

// SearchFilters 搜索筛选条件
type SearchFilters struct {
	// 内容分类
	Categories []string `json:"categories" form:"categories"` // 分类筛选
	Genres     []string `json:"genres" form:"genres"`         // 类型筛选

	// 时间筛选
	YearFrom int `json:"year_from" form:"year_from"` // 起始年份
	YearTo   int `json:"year_to" form:"year_to"`     // 结束年份

	// 评分筛选（支持新旧两种方式）
	RatingFrom float64 `json:"rating_from" form:"rating_from"` // 最低评分（0-10）
	RatingTo   float64 `json:"rating_to" form:"rating_to"`     // 最高评分（0-10）
	ScoreFrom  int     `json:"score_from" form:"score_from"`   // 最低分数（0-100）
	ScoreTo    int     `json:"score_to" form:"score_to"`       // 最高分数（0-100）

	// 时长筛选
	DurationFrom int `json:"duration_from" form:"duration_from"` // 最短时长（分钟）
	DurationTo   int `json:"duration_to" form:"duration_to"`     // 最长时长（分钟）

	// 地区和语言
	Regions   []string `json:"regions" form:"regions"`     // 地区筛选
	Languages []string `json:"languages" form:"languages"` // 语言筛选

	// 标签和状态
	Tags   []string `json:"tags" form:"tags"`     // 标签筛选
	Status []uint32 `json:"status" form:"status"` // 状态筛选：更新中、已完结

	// 演员和导演
	Director string   `json:"director" form:"director"` // 导演筛选
	Actors   []string `json:"actors" form:"actors"`     // 演员筛选

	// 新增 int 类型字段
	ContentTypes []int `json:"content_types" form:"content_types"` // 内容类型筛选：1=Movie, 2=TV, 3=Comic

	// ID 数组字段（用于精确匹配）
	RegionIDs []uint64 `json:"region_ids" form:"region_ids"` // 地区ID列表
	YearIDs   []uint64 `json:"year_ids" form:"year_ids"`     // 年份ID列表
	GenreIDs  []uint64 `json:"genre_ids" form:"genre_ids"`   // 类型ID列表
}

// VectorSearchParams 向量搜索参数
type VectorSearchParams struct {
	DenseWeight  float64 `json:"dense_weight" form:"dense_weight"`   // 稠密向量权重，默认0.5
	SparseWeight float64 `json:"sparse_weight" form:"sparse_weight"` // 稀疏向量权重，默认0.5
	TopK         int     `json:"top_k" form:"top_k"`                 // 向量搜索返回的候选数量，默认100
}

// SearchResponse 搜索响应结构
type SearchResponse struct {
	Items      []SearchResultItem `json:"items"`       // 搜索结果列表
	Total      int64              `json:"total"`       // 总数量
	Page       int                `json:"page"`        // 当前页码
	PageSize   int                `json:"page_size"`   // 每页大小
	TotalPages int                `json:"total_pages"` // 总页数
	SearchInfo SearchInfo         `json:"search_info"` // 搜索信息
}

// SearchResultItem 搜索结果项
type SearchResultItem struct {
	// 基础信息
	ID       uint64 `json:"id"`
	ShowID   uint64 `json:"show_id"`
	Name     string `json:"name"`
	Overview string `json:"overview"`

	// 多语言支持
	NameI18n     []string `json:"name_i18n,omitempty"`
	OverviewI18n []string `json:"overview_i18n,omitempty"`

	// 分类信息
	Category     string   `json:"category"`
	CategoryI18n []string `json:"category_i18n,omitempty"`
	Genre        []string `json:"genre"`  // 合并原始genre字段和genre_i18n
	Year         []string `json:"year"`   // 合并原始year字段和year_i18n
	Region       []string `json:"region"` // 合并原始region字段和region_i18n

	// 详细信息
	Rating   float64  `json:"rating"`
	Duration int      `json:"duration"`
	Director string   `json:"director"`
	Actors   []string `json:"actors"`
	Tags     []string `json:"tags"`
	Language string   `json:"language"`
	Season   int      `json:"season"`
	Episodes int      `json:"episodes"`
	Status   string   `json:"status"`

	// 搜索相关信息
	Score      float64  `json:"score"`                // 相关度分数
	MatchType  string   `json:"match_type"`           // 匹配类型：vector, filter, hybrid
	Highlights []string `json:"highlights,omitempty"` // 高亮片段
}

// SearchInfo 搜索信息
type SearchInfo struct {
	SearchMode      string        `json:"search_mode"`       // 实际使用的搜索模式
	ProcessingTime  time.Duration `json:"processing_time"`   // 处理时间
	VectorUsed      bool          `json:"vector_used"`       // 是否使用了向量搜索
	FilterUsed      bool          `json:"filter_used"`       // 是否使用了筛选条件
	Query           string        `json:"query"`             // 处理后的查询文本
	VectorAPIUsed   bool          `json:"vector_api_used"`   // 是否实际调用了向量搜索API
	VectorRequestID string        `json:"vector_request_id"` // 向量搜索API的请求ID
}

// SearchSuggestionRequest 搜索建议请求
type SearchSuggestionRequest struct {
	Query    string `json:"query" form:"query"`       // 部分输入的查询文本
	Language string `json:"language" form:"language"` // 语言偏好
	Limit    int    `json:"limit" form:"limit"`       // 建议数量限制，默认10
}

// SearchSuggestionResponse 搜索建议响应
type SearchSuggestionResponse struct {
	Suggestions []SearchSuggestion `json:"suggestions"`
}

// SearchSuggestion 搜索建议项
type SearchSuggestion struct {
	Text      string  `json:"text"`                 // 建议文本
	Type      string  `json:"type"`                 // 建议类型：show_name, category, genre, director, actor
	Score     float64 `json:"score"`                // 相关度分数
	ShowCount int     `json:"show_count,omitempty"` // 相关节目数量（对于分类建议）
}

// RecommendationRequest 推荐请求
type RecommendationRequest struct {
	ShowID     uint64   `json:"show_id,omitempty" form:"show_id"`         // 基于特定节目推荐
	Categories []string `json:"categories,omitempty" form:"categories"`   // 基于分类推荐
	UserID     uint64   `json:"user_id,omitempty" form:"user_id"`         // 用户ID（用于个性化推荐）
	Language   string   `json:"language" form:"language"`                 // 语言偏好
	Limit      int      `json:"limit" form:"limit"`                       // 推荐数量，默认20
	ExcludeIDs []uint64 `json:"exclude_ids,omitempty" form:"exclude_ids"` // 排除的节目ID
}

// RecommendationResponse 推荐响应
type RecommendationResponse struct {
	Items []SearchResultItem `json:"items"`
	Total int                `json:"total"`
	Type  string             `json:"type"` // 推荐类型：similar, category_based, personalized
}
