package googleplay

import (
	"vlab/app/api/googleplay/execute"
	googleplayDto "vlab/app/dto/api/googleplay"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func OrderInfo(ctx *gin.Context) {
	req := googleplayDto.OrderInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().OrderInfo(ctx, req.PackageName, req.OrderID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
