package googleplay

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"io"
	"net/http"
	"vlab/app/api/googleplay/execute"
	"vlab/app/dao/common"
	"vlab/app/service/user/vip"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

// GooglePlayNotify 谷歌支付回调通知接口
func GooglePlayNotify(ctx *gin.Context) {
	log.Ctx(ctx).Info("GooglePlayNotify Start")
	ctx.Set("channelID", uint64(common.ChannelSkyBoxAndroid))

	header := ctx.Request.Header
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ctx read body error"})
		return
	}
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	log.Ctx(ctx).With<PERSON>ield("header", header).WithField("body", string(body)).Info("GooglePlayNotifyData")

	req := &execute.GoogleNotification{}
	if err := ctx.ShouldBind(req); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotify invalid message")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}
	if req.Message == nil {
		log.Ctx(ctx).Error("GooglePlayNotify message nil")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}

	decodedData, err := base64.StdEncoding.DecodeString(req.Message.Data)
	if err != nil {
		log.Ctx(ctx).Error("GooglePlayNotify message data decode error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	data := &execute.DeveloperNotification{}
	if err := json.Unmarshal(decodedData, data); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotify message data unmarshal error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	if err := vip.GetService().GooglePlayNotify(ctx, req.Message.MessageID, data); err != nil {
		log.Ctx(ctx).WithError(err).Error("GooglePlayNotify Logic error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("GooglePlayNotify End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}

// GooglePlayNotifyVi 谷歌支付回调通知接口
func GooglePlayNotifyVi(ctx *gin.Context) {
	log.Ctx(ctx).Info("GooglePlayNotifyVi Start")
	ctx.Set("channelID", uint64(common.ChannelViBoxAndroid))

	header := ctx.Request.Header
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ctx read body error"})
		return
	}
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	log.Ctx(ctx).WithField("header", header).WithField("body", string(body)).Info("GooglePlayNotifyViData")

	req := &execute.GoogleNotification{}
	if err := ctx.ShouldBind(req); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyVi invalid message")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}
	if req.Message == nil {
		log.Ctx(ctx).Error("GooglePlayNotifyVi message nil")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}

	decodedData, err := base64.StdEncoding.DecodeString(req.Message.Data)
	if err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyVi message data decode error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	data := &execute.DeveloperNotification{}
	if err := json.Unmarshal(decodedData, data); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyVi message data unmarshal error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	if err := vip.GetService().GooglePlayNotify(ctx, req.Message.MessageID, data); err != nil {
		log.Ctx(ctx).WithError(err).Error("GooglePlayNotifyVi Logic error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("GooglePlayNotifyVi End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}

// GooglePlayNotifyHitv 谷歌支付回调通知接口
func GooglePlayNotifyHitv(ctx *gin.Context) {
	log.Ctx(ctx).Info("GooglePlayNotifyHitv Start")
	ctx.Set("channelID", uint64(common.ChannelHitvBoxAndroid))
	//     ctx: *gin.Context - Gin 框架的上下文对象，用于处理 HTTP 请求和响应
	//
	// 返回值:
	//     无

	header := ctx.Request.Header
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ctx read body error"})
		return
	}
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	log.Ctx(ctx).WithField("header", header).WithField("body", string(body)).Info("GooglePlayNotifyHitvData")

	req := &execute.GoogleNotification{}
	if err := ctx.ShouldBind(req); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyHitv invalid message")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}
	if req.Message == nil {
		log.Ctx(ctx).Error("GooglePlayNotifyHitv message nil")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message"})
		return
	}

	decodedData, err := base64.StdEncoding.DecodeString(req.Message.Data)
	if err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyHitv message data decode error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	data := &execute.DeveloperNotification{}
	if err := json.Unmarshal(decodedData, data); err != nil {
		log.Ctx(ctx).Error("GooglePlayNotifyHitv message data unmarshal error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid message data"})
		return
	}

	if err := vip.GetService().GooglePlayNotify(ctx, req.Message.MessageID, data); err != nil {
		log.Ctx(ctx).WithError(err).Error("GooglePlayNotifyHitv Logic error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("GooglePlayNotifyHitv End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
