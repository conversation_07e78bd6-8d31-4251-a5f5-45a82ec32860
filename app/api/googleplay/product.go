package googleplay

import (
	"vlab/app/api/googleplay/execute"
	googleplayDto "vlab/app/dto/api/googleplay"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func PurchaseProductInfo(ctx *gin.Context) {
	req := googleplayDto.PurchaseProductInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PurchaseProductInfo(ctx, req.PackageName, req.ProductId, req.PurchaseToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func PurchasedProductAcknowledge(ctx *gin.Context) {
	req := googleplayDto.PurchaseProductInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	err := execute.GetApi().PurchasedProductAcknowledge(ctx, req.PackageName, req.ProductId, req.PurchaseToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}

func PurchasedProductConsume(ctx *gin.Context) {
	req := googleplayDto.PurchaseProductInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	err := execute.GetApi().PurchasedProductConsume(ctx, req.PackageName, req.ProductId, req.PurchaseToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
