package googleplay

import (
	"vlab/app/api/googleplay/execute"
	googleplayDto "vlab/app/dto/api/googleplay"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func PurchaseSubscriptionInfo(ctx *gin.Context) {
	req := googleplayDto.PurchaseSubscriptionInfoReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PurchaseSubscriptionInfo(ctx, req.PackageName, req.PurchaseToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func PurchaseSubscriptionAcknowledge(ctx *gin.Context) {
	req := googleplayDto.PurchaseSubscriptionAcknowledgeReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	err := execute.GetApi().PurchaseSubscriptionAcknowledge(ctx, req.PackageName, req.SubId, req.PurchaseToken)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppResp(ctx, ecode.OK.Code(), ecode.OK.Message())
}
