package execute

import (
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/androidpublisher/v3"
)

// PurchaseSubscriptionInfo
func (e *Entry) PurchaseSubscriptionInfo(ctx *gin.Context, packageName, purchaseToken string) (*androidpublisher.SubscriptionPurchaseV2, error) {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return nil, err
	}

	call := svc.Purchases.Subscriptionsv2.Get(packageName, purchaseToken)
	subPurchase, err := call.Do()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Purchases.Subscriptionsv2.Get error")
		return nil, err
	}
	log.Ctx(ctx).WithField("purchaseToken", purchaseToken).WithField("subPurchase", subPurchase).
		Debug("svc.Purchases.Subscriptionsv2.Get Result")

	// if subPurchase.SubscriptionState != 0 {
	// 	return nil, ecode.UserGooglePlayPurchaseStateErr
	// }
	return subPurchase, nil
}

// PurchaseSubscriptionAcknowledge
func (e *Entry) PurchaseSubscriptionAcknowledge(ctx *gin.Context, packageName, subId, purchaseToken string) error {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return err
	}

	req := &androidpublisher.SubscriptionPurchasesAcknowledgeRequest{}
	call := svc.Purchases.Subscriptions.Acknowledge(packageName, subId, purchaseToken, req)
	if err = call.Do(); err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Purchases.Subscriptions.Acknowledge error")
		return err
	}

	return nil
}
