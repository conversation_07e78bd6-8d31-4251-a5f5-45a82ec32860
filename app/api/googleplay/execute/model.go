package execute

// AcknowledgementState 订阅的确认状态
// 参考文档:https://developers.google.com/android-publisher/api-ref/rest/v3/purchases.subscriptionsv2?hl=zh-cn#AcknowledgementState
type AcknowledgementState string

const (
	AcknowledgementStateUnspecified  AcknowledgementState = "ACKNOWLEDGEMENT_STATE_UNSPECIFIED"  // 未指定的确认状态
	AcknowledgementStatePending      AcknowledgementState = "ACKNOWLEDGEMENT_STATE_PENDING"      // 订阅尚未确认
	AcknowledgementStateAcknowledged AcknowledgementState = "ACKNOWLEDGEMENT_STATE_ACKNOWLEDGED" // 订阅已确认
)

// GoogleSubscriptionState 谷歌订阅状态
// 参考文档:https://developers.google.cn/android-publisher/api-ref/rest/v3/purchases.subscriptionsv2?hl=zh-cn#SubscriptionState
type GoogleSubscriptionState string

const (
	GoogleSubscriptionStateUnspecified   GoogleSubscriptionState = "SUBSCRIPTION_STATE_UNSPECIFIED"     // 未指定订阅状态。
	GoogleSubscriptionStatePending       GoogleSubscriptionState = "SUBSCRIPTION_STATE_PENDING"         // 订阅已创建，但在注册期间正在等待付款。在此状态下，所有商品都正在等待付款。
	GoogleSubscriptionStateActive        GoogleSubscriptionState = "SUBSCRIPTION_STATE_ACTIVE"          // 订阅处于有效状态。- (1) 如果订阅是自动续订方案，则至少有一个项目已自动续订且未过期。- (2) 如果订阅是预付费方案，至少有一项不会过期。
	GoogleSubscriptionStatePaused        GoogleSubscriptionState = "SUBSCRIPTION_STATE_PAUSED"          // 订阅已暂停。仅当订阅是自动续订方案时，这个状态才可用。在此状态下，所有内容都会处于暂停状态。
	GoogleSubscriptionStateInGracePeriod GoogleSubscriptionState = "SUBSCRIPTION_STATE_IN_GRACE_PERIOD" // 订阅处于宽限期。仅当订阅是自动续订方案时，这个状态才可用。在此状态下，所有内容都处于宽限期。
	GoogleSubscriptionStateOnHold        GoogleSubscriptionState = "SUBSCRIPTION_STATE_ON_HOLD"         // 订阅处于暂停状态（已暂停）。仅当订阅是自动续订方案时，这个状态才可用。在此状态下，所有内容都会处于保全状态。
	GoogleSubscriptionStateCanceled      GoogleSubscriptionState = "SUBSCRIPTION_STATE_CANCELED"        // 订阅已取消，但尚未到期。仅当订阅是自动续订方案时，这个状态才可用。所有内容的 autoRenewEnabled 都设为 false。
	GoogleSubscriptionStateExpired       GoogleSubscriptionState = "SUBSCRIPTION_STATE_EXPIRED"         // 订阅已过期。所有项的过期时间均为过去时间。
)

// GoogleNotification 谷歌回调信息结构体
// 参考文档:https://developer.android.com/google/play/billing/rtdn-reference?hl=zh-cn#encoding
type GoogleNotification struct {
	Message      *GoogleNotificationMessage `json:"message"`
	Subscription string                     `json:"subscription"`
}

type GoogleNotificationMessage struct {
	Data        string                 `json:"data"`
	MessageID   string                 `json:"messageId"`
	Attributes  map[string]interface{} `json:"attributes"`
	PublishTime string                 `json:"publishTime"`
	OrderingKey string                 `json:"orderingKey"`
}

// DeveloperNotification 谷歌回调信息荷载
// 参考文档:https://developer.android.com/google/play/billing/rtdn-reference#json_specification
type DeveloperNotification struct {
	Version                    string                      `json:"version"`
	PackageName                string                      `json:"packageName"`
	EventTimeMillis            string                      `json:"eventTimeMillis"`
	SubscriptionNotification   *SubscriptionNotification   `json:"subscriptionNotification,omitempty"`
	OneTimeProductNotification *OneTimeProductNotification `json:"oneTimeProductNotification,omitempty"`
	VoidedPurchaseNotification *VoidedPurchaseNotification `json:"voidedPurchaseNotification,omitempty"`
	TestNotification           *TestNotification           `json:"testNotification,omitempty"`
}

// SubNtfType 谷歌回调订阅通知类型
// 参考文档:https://developer.android.com/google/play/billing/rtdn-reference#sub
type SubNtfType int

const (
	SubNtfTypeRecovered            SubNtfType = iota + 1 // 从账号保留状态恢复订阅
	SubNtfTypeRenewed                                    // 续订
	SubNtfTypeCanceled                                   // 订阅取消 指的是用户手动进行的订阅取消操作
	SubNtfTypePurchased                                  // 新订阅
	SubNtfTypeAccountHold                                // 订阅已进入帐号保留状态，一般是用户的付款信息存在问题且已经任何关联的宽限期都结束时发生的。
	SubNtfTypeGracePeriod                                // 订阅已进入宽限期。宽限期指的是订阅周期结束之后的一段时间内提供的额外时间。（可选是否启用）
	SubNtfTypeRestarted                                  // 处理到期之前恢复订阅
	SubNtfTypePriceChangeConfirmed                       // 用户已成功确认订阅价格变动，表示业务方对订阅价格进行了更改，并且用户已经确认接受新价格。
	SubNtfTypeDeferred                                   // 续订时间延期 指的是订阅到期前，由于付款方式问题等原因导致续订付款失败
	SubNtfTypePaused                                     // 订阅已暂停，表示用户已经暂停了订阅。（可选是否启用）
	SubNtfTypePauseScheduleChanged                       // 订阅暂停计划已更改，表示用户已经更改了暂停订阅的计划。（依赖暂停功能启用）
	SubNtfTypeRevoked                                    // 订阅撤销 系统出于各种原因撤消用户的订阅，包括服务的主动调接口或购买交易被退款等
	SubNtfTypeExpired                                    // 订阅过期。
)

// SubscriptionNotification 谷歌回调订阅通知信息结构体
type SubscriptionNotification struct {
	Version          string     `json:"version"`
	NotificationType SubNtfType `json:"notificationType,omitempty"`
	PurchaseToken    string     `json:"purchaseToken,omitempty"`
	// SubscriptionID   string     `json:"subscriptionId,omitempty"` // 文档上没有
}

// GoogleOneNtfType 谷歌回调一次性购买通知类型
// 参考文档:https://developer.android.com/google/play/billing/rtdn-reference#one-time
type OneNtfType int

const (
	OneNtfTypePurchased OneNtfType = iota + 1
	OneNtfTypeCanceled
)

// OneTimeProductNotification 谷歌回调一次性购买通知信息结构体
type OneTimeProductNotification struct {
	Version          string     `json:"version"`
	NotificationType OneNtfType `json:"notificationType,omitempty"`
	PurchaseToken    string     `json:"purchaseToken,omitempty"`
	SKU              string     `json:"sku,omitempty"`
}

// https://developer.android.com/google/play/billing/rtdn-reference?hl=zh-cn#voided-purchase
type VoidedPurchaseNotification struct {
	PurchaseToken string `json:"purchaseToken,omitempty"`
	OrderID       string `json:"orderId,omitempty"`
	ProductType   int    `json:"productType,omitempty"` // 1 PRODUCT_TYPE_SUBSCRIPTION 订阅购买交易已作废, 2 PRODUCT_TYPE_ONE_TIME 一次性购买交易已作废.
	// 1 REFUND_TYPE_FULL_REFUND - 购买交易已完全作废。
	// 2 REFUND_TYPE_QUANTITY_BASED_PARTIAL_REFUND - 购买交易因基于数量的部分退款而部分作废，仅适用于多件购买交易。
	// 一笔购买交易可以多次部分作废。请注意，当多数量购买交易的剩余总数量得到退款时，refundType 将为 REFUND_TYPE_FULL_REFUND
	RefundType int `json:"refundType,omitempty"`
}

// TestNotification 通过Google Play开发者控制台发送的通知信息结构体
type TestNotification struct {
	Version string `json:"version"`
}
