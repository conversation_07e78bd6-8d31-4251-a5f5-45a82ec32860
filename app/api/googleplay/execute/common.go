package execute

import (
	"context"
	channelKeyDao "vlab/app/dao/resource_channel_key"
	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/androidpublisher/v3"
	"google.golang.org/api/option"
)

func (e *Entry) GetGoogleplayClient(ctx *gin.Context) (*androidpublisher.Service, *channelKeyDao.Model, error) {
	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckGooglePlayConfig() {
		return nil, nil, ecode.ChannelConfigInvalidErr
	}
	privateKey, err := config.LoadPrivateKeyByFilename(channelKeyInfo.GooglePlayPkFilename)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("LoadPrivateKeyByFilename error")
		return nil, nil, err
	}

	// 加载服务账号密钥
	creds, err := google.CredentialsFromJSON(ctx, privateKey, androidpublisher.AndroidpublisherScope)
	if err != nil {
		return nil, nil, err
	}

	// 创建HTTP客户端
	client := oauth2.NewClient(context.Background(), creds.TokenSource)
	srv, err := androidpublisher.NewService(context.Background(), option.WithHTTPClient(client))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("androidpublisher.NewService error")
		return nil, nil, err
	}

	return srv, channelKeyInfo, err
}
