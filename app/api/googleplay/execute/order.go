package execute

import (
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/androidpublisher/v3"
)

// OrderInfo .
func (e *Entry) OrderInfo(ctx *gin.Context, packageName, orderId string) (*androidpublisher.Order, error) {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return nil, err
	}

	call := svc.Orders.Get(packageName, orderId)
	oInfo, err := call.Do()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Orders.Get error")
		return nil, err
	}
	log.Ctx(ctx).WithField("orderId", orderId).WithField("oInfo", oInfo).Debug("svc.Orders.Get Result")

	return oInfo, nil
}
