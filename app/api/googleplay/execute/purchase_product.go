package execute

import (
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"google.golang.org/api/androidpublisher/v3"
)

// PurchaseProductInfo
func (e *Entry) PurchaseProductInfo(ctx *gin.Context, packageName, productId, purchaseToken string) (*androidpublisher.ProductPurchase, error) {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return nil, err
	}

	call := svc.Purchases.Products.Get(packageName, productId, purchaseToken)
	productPurchase, err := call.Do()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Purchases.Products.Get error")
		return nil, err
	}
	log.Ctx(ctx).WithField("productId", productId).
		WithField("purchaseToken", purchaseToken).
		With<PERSON>ield("productPurchase", productPurchase).
		Debug("svc.Purchases.Products.Get Result")

	// if productPurchase.PurchaseState != 0 {
	// 	return nil, ecode.UserGooglePlayPurchaseStateErr
	// }
	return productPurchase, nil
}

// PurchasedProductAcknowledge .
func (e *Entry) PurchasedProductAcknowledge(ctx *gin.Context, packageName, productId, purchaseToken string) error {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return err
	}

	req := &androidpublisher.ProductPurchasesAcknowledgeRequest{}
	call := svc.Purchases.Products.Acknowledge(packageName, productId, purchaseToken, req)
	if err = call.Do(); err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Purchases.Products.Acknowledge error")
		return err
	}

	return nil
}

// PurchasedProductConsume .
func (e *Entry) PurchasedProductConsume(ctx *gin.Context, packageName, productId, purchaseToken string) error {
	svc, _, err := e.GetGoogleplayClient(ctx)
	if err != nil {
		return err
	}

	// 消耗购买项，即标记为已使用
	call := svc.Purchases.Products.Consume(packageName, productId, purchaseToken)
	if err = call.Do(); err != nil {
		log.Ctx(ctx).WithError(err).Error("svc.Purchases.Products.Consume error")
		return err
	}

	return nil
}
