package execute

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"sync"
	"time"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

// Google公钥缓存结构
type GoogleCertsCache struct {
	keys       map[string]*rsa.PublicKey
	mutex      sync.RWMutex
	expiry     time.Time
	refreshing bool
}

// Google 公钥 URL
const (
	googleCertsURL = "https://www.googleapis.com/oauth2/v3/certs"
)

var googleCertsCache = &GoogleCertsCache{
	keys: make(map[string]*rsa.PublicKey),
}

// 获取Google公钥集（带缓存和错误处理）
func (e *Entry) GetGooglePublicKeys(ctx *gin.Context) (map[string]*rsa.PublicKey, error) {
	googleCertsCache.mutex.RLock()
	// 检查缓存是否有效
	if time.Now().Before(googleCertsCache.expiry) && len(googleCertsCache.keys) > 0 {
		defer googleCertsCache.mutex.RUnlock()
		return googleCertsCache.keys, nil
	}
	googleCertsCache.mutex.RUnlock()

	// 从Google获取最新公钥
	resp, err := http.Get(googleCertsURL)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("JWK endpoint returned %d status", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	defer resp.Body.Close()
	if err != nil {
		err = fmt.Errorf("failed to read JWK response: %w", err)
		log.Ctx(ctx).WithError(err).Error("failed to read JWK response")
		return nil, err
	}

	var certs struct {
		Keys []struct {
			Kid string `json:"kid"`
			N   string `json:"n"`
			E   string `json:"e"`
		} `json:"keys"`
	}
	if err := json.Unmarshal(body, &certs); err != nil {
		err = fmt.Errorf("failed to parse JWK: %w", err)
		log.Ctx(ctx).WithError(err).Error("failed to parse JWK")
		return nil, err
	}

	// 解析公钥
	newKeys := make(map[string]*rsa.PublicKey)
	for _, key := range certs.Keys {
		pubKey, err := e.parseRSAPublicKey(key.N, key.E)
		if err == nil {
			newKeys[key.Kid] = pubKey
		}
	}

	if len(newKeys) == 0 {
		err = errors.New("no valid public keys found")
		log.Ctx(ctx).WithError(err).Error("no valid public keys found")
		return nil, err
	}

	// 更新缓存（有效期为1小时，但使用max-age头更佳）
	googleCertsCache.mutex.Lock()
	googleCertsCache.keys = newKeys
	googleCertsCache.expiry = time.Now().Add(time.Hour)
	defer googleCertsCache.mutex.Unlock()

	return newKeys, nil
}

// 解析 Base64 编码的 RSA 公钥
func (e *Entry) parseRSAPublicKey(keyN, keyE string) (*rsa.PublicKey, error) {
	nb, err := base64.RawURLEncoding.DecodeString(keyN)
	if err != nil {
		return nil, err
	}
	eb, err := base64.RawURLEncoding.DecodeString(keyE)
	if err != nil {
		return nil, err
	}

	nInt := new(big.Int).SetBytes(nb)
	eInt := new(big.Int).SetBytes(eb)
	pubKey := &rsa.PublicKey{N: nInt, E: int(eInt.Int64())}

	return pubKey, nil
}
