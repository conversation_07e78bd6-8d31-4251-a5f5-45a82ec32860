package stripe

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"vlab/app/service/user/vip"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
)

// StripeNotify handles Stripe webhook notifications.
func StripeNotify(ctx *gin.Context) {
	log.Ctx(ctx).Debug("AppStoreNotifyStart")

	if err := vip.GetService().StripeNotify(ctx); err != nil {
		log.Ctx(ctx).WithError(err).Error("StripeNotify error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}
	log.Ctx(ctx).Debug("StripeNotify processed successfully")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
