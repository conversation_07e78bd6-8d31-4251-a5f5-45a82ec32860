package imdb

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"
)

// MatchService 匹配服务
type MatchService struct {
	client *Client
	debug  bool
	logger *log.Logger
}

// NewMatchService 创建匹配服务
func NewMatchService(client *Client) *MatchService {
	if client == nil {
		client = GetDefaultClient()
	}
	return &MatchService{
		client: client,
		debug:  false,
		logger: log.New(os.Stdout, "[IMDB] ", log.LstdFlags|log.Lmicroseconds),
	}
}

// SetDebug 设置调试模式
func (s *MatchService) SetDebug(debug bool) {
	s.debug = debug
	if s.client != nil {
		s.client.SetDebug(debug)
	}
}

// SetLogger 设置自定义logger
func (s *MatchService) SetLogger(logger *log.Logger) {
	if logger != nil {
		s.logger = logger
	}
}

// MatchShow 匹配剧集信息
func (s *MatchService) MatchShow(ctx context.Context, title string, year int, overview string,
	language string, genres []string, contentType string) (*MediaIDs, error) {

	// 参数验证
	if title == "" {
		return nil, fmt.Errorf("title is required")
	}
	// 允许year为0，表示年份未知，让API自动判断
	if year != 0 && (year < 1900 || year > 2100) {
		return nil, fmt.Errorf("invalid year: %d", year)
	}
	if overview == "" {
		overview = title
	}
	if language == "" {
		language = "en" // 默认英语
	}
	if len(genres) == 0 {
		genres = []string{} // 默认未知类型
	}
	if contentType == "" {
		contentType = "auto" // 默认自动识别
	}

	// 构建请求
	req := &MatchRequest{
		Type:     contentType,
		Title:    title,
		Year:     year,
		Overview: overview,
		Language: language,
		Genres:   genres,
	}

	// Debug: 记录请求参数
	if s.debug && s.logger != nil {
		s.logger.Printf("[DEBUG] IMDB API 请求参数:")
		s.logger.Printf("  Type: %s", req.Type)
		s.logger.Printf("  Title: %s", req.Title)
		s.logger.Printf("  Year: %d", req.Year)
		s.logger.Printf("  Overview: %.100s...", req.Overview)
		s.logger.Printf("  Language: %s", req.Language)
		s.logger.Printf("  Genres: %v", req.Genres)
	}

	// 调用API
	resp, err := s.client.Match(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to match show: %w", err)
	}

	// Debug: 记录响应结果
	if s.debug && s.logger != nil && resp != nil {
		s.logger.Printf("[DEBUG] IMDB API 响应:")
		s.logger.Printf("  Code: %d", resp.Code)
		s.logger.Printf("  Msg: %s", resp.Msg)
		if resp.Data != nil {
			s.logger.Printf("  IsMatch: %v", resp.Data.IsMatch)
			s.logger.Printf("  MatchScore: %.2f", resp.Data.MatchScore)
			s.logger.Printf("  Reason: %s", resp.Data.Reason)
			if resp.Data.Match != nil {
				s.logger.Printf("  MatchType: %s", resp.Data.Match.Type)
				s.logger.Printf("  MatchScore: %.2f", resp.Data.Match.Score)
				if resp.Data.Match.Movie != nil {
					s.logger.Printf("  Movie Title: %s", resp.Data.Match.Movie.Title)
					s.logger.Printf("  Movie Year: %d", resp.Data.Match.Movie.Year)
					if resp.Data.Match.Movie.IDs != nil {
						s.logger.Printf("  Movie IDs: %+v", resp.Data.Match.Movie.IDs)
					}
					if resp.Data.Match.Movie.Overview != "" {
						s.logger.Printf("  Movie Overview: %.100s...", resp.Data.Match.Movie.Overview)
					}
					if len(resp.Data.Match.Movie.Genres) > 0 {
						s.logger.Printf("  Movie Genres: %v", resp.Data.Match.Movie.Genres)
					}
				}
				if resp.Data.Match.Show != nil {
					s.logger.Printf("  Show Title: %s", resp.Data.Match.Show.Title)
					s.logger.Printf("  Show Year: %d", resp.Data.Match.Show.Year)
					if resp.Data.Match.Show.IDs != nil {
						s.logger.Printf("  Show IDs: %+v", resp.Data.Match.Show.IDs)
					}
				}
			}
		}
		s.logger.Printf("  MinScore: %.2f", s.client.config.MinScore)
	}

	// 检查是否有效匹配
	if !IsValidMatch(resp, s.client.config.MinScore) {
		if s.debug && s.logger != nil {
			s.logger.Printf("[DEBUG] 匹配失败: IsValidMatch返回false")
			if resp != nil && resp.Data != nil {
				s.logger.Printf("  原因: IsMatch=%v, MatchScore=%.2f < MinScore=%.2f",
					resp.Data.IsMatch, resp.Data.MatchScore, s.client.config.MinScore)
			}
		}
		// No valid match found
		return nil, nil
	}

	// 提取媒体ID
	mediaIDs := ParseMediaIDs(resp)
	if mediaIDs == nil {
		// Failed to parse media IDs
		if s.debug && s.logger != nil {
			s.logger.Printf("[DEBUG] ParseMediaIDs返回nil: 无法从响应中提取媒体ID")
		}
		return nil, nil
	}

	// Successfully matched

	return mediaIDs, nil
}

// BatchMatchShows 批量匹配剧集
func (s *MatchService) BatchMatchShows(ctx context.Context, shows []ShowMatchInput) map[string]*MediaIDs {
	results := make(map[string]*MediaIDs)

	for _, show := range shows {
		// 生成唯一键
		key := fmt.Sprintf("%s_%d", show.Title, show.Year)

		// 匹配单个剧集
		mediaIDs, err := s.MatchShow(ctx, show.Title, show.Year, show.Overview,
			show.Language, show.Genres, show.ContentType)

		if err != nil {
			// Failed to match show
			continue
		}

		if mediaIDs != nil {
			results[key] = mediaIDs
		}
	}

	return results
}

// ShowMatchInput 剧集匹配输入
type ShowMatchInput struct {
	Title       string
	Year        int
	Overview    string
	Language    string
	Genres      []string
	ContentType string
}

// NormalizeGenres 标准化类型标签
func NormalizeGenres(genres []string) []string {
	normalized := make([]string, 0, len(genres))

	for _, genre := range genres {
		// 转换为小写并去除空格
		genre = strings.ToLower(strings.TrimSpace(genre))

		// 映射常见的中文类型到英文
		switch genre {
		case "动作", "action":
			normalized = append(normalized, "action")
		case "喜剧", "comedy":
			normalized = append(normalized, "comedy")
		case "剧情", "drama":
			normalized = append(normalized, "drama")
		case "科幻", "sci-fi", "science-fiction":
			normalized = append(normalized, "science-fiction")
		case "恐怖", "horror":
			normalized = append(normalized, "horror")
		case "惊悚", "thriller":
			normalized = append(normalized, "thriller")
		case "爱情", "romance":
			normalized = append(normalized, "romance")
		case "动画", "animation":
			normalized = append(normalized, "animation")
		case "纪录片", "documentary":
			normalized = append(normalized, "documentary")
		case "战争", "war":
			normalized = append(normalized, "war")
		case "犯罪", "crime":
			normalized = append(normalized, "crime")
		case "悬疑", "mystery":
			normalized = append(normalized, "mystery")
		case "奇幻", "fantasy":
			normalized = append(normalized, "fantasy")
		case "冒险", "adventure":
			normalized = append(normalized, "adventure")
		case "家庭", "family":
			normalized = append(normalized, "family")
		case "音乐", "music":
			normalized = append(normalized, "music")
		case "历史", "history":
			normalized = append(normalized, "history")
		case "西部", "western":
			normalized = append(normalized, "western")
		case "传记", "biography":
			normalized = append(normalized, "biography")
		case "运动", "sport":
			normalized = append(normalized, "sport")
		default:
			if genre != "" {
				normalized = append(normalized, genre)
			}
		}
	}

	// 去重
	seen := make(map[string]bool)
	unique := make([]string, 0, len(normalized))
	for _, g := range normalized {
		if !seen[g] {
			seen[g] = true
			unique = append(unique, g)
		}
	}

	return unique
}

// DetermineContentType 根据剧集信息判断内容类型
func DetermineContentType(title string, overview string) string {
	// 检查是否包含剧集相关关键词
	showKeywords := []string{
		"season", "episode", "series", "episodes",
		"季", "集", "剧", "连续剧",
	}

	titleLower := strings.ToLower(title)
	overviewLower := strings.ToLower(overview)

	for _, keyword := range showKeywords {
		if strings.Contains(titleLower, keyword) || strings.Contains(overviewLower, keyword) {
			return "show"
		}
	}

	// 检查是否包含电影相关关键词
	movieKeywords := []string{
		"movie", "film", "cinema",
		"电影", "影片",
	}

	for _, keyword := range movieKeywords {
		if strings.Contains(titleLower, keyword) || strings.Contains(overviewLower, keyword) {
			return "movie"
		}
	}

	// 默认返回auto，让API自动判断
	return "auto"
}
