package paypal

import (
	"vlab/app/api/paypal/execute"
	"vlab/app/dto/api/paypal"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// PaypalOrderInfo 获取订单详情
func PaypalOrderInfo(ctx *gin.Context) {
	req := paypal.PaypalOrderInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalOrderInfo(ctx, req.OrderID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PaypalOrderCapture 捕获订单
func PaypalOrderCapture(ctx *gin.Context) {
	req := paypal.PaypalOrderInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalOrderCapture(ctx, req.OrderID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
