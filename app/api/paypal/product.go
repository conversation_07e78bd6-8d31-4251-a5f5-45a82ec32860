package paypal

import (
	"vlab/app/api/paypal/execute"
	"vlab/app/dto/api/paypal"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// PaypalProductList 列表
func PaypalProductList(ctx *gin.Context) {
	ret, err := execute.GetApi().PaypalProductList(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PaypalProductCreate 创建
func PaypalProductCreate(ctx *gin.Context) {
	req := paypal.PaypalProductSetReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), helper.Translate(err))
		return
	}

	ret, err := execute.GetApi().PaypalProductCreate(ctx, execute.ProductType(req.ProductType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PaypalProductInfo 详情
func PaypalProductInfo(ctx *gin.Context) {
	req := paypal.PaypalProductInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalProductInfo(ctx, req.ProductID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
