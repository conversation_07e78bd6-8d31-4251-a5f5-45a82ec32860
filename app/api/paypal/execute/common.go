package execute

import (
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	"vlab/app/dao/vip/product"
	"vlab/config"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// GetPaypalClient 获取Paypal客户端
func (e *Entry) GetPaypalClient(ctx *gin.Context) (*paypal.Client, error) {
	env := adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyCurrentEnv)
	client, err := paypal.NewClient(config.PayPalCfg.ClientID, config.PayPalCfg.Secret, dbs.IsProd(env))
	if err != nil {
		return nil, err
	}
	return client, nil
}

// GetProductBody .
func (e *Entry) GetProductBody(ctx *gin.Context, productType ProductType) (gopay.BodyMap, error) {
	bm := make(gopay.BodyMap)
	switch productType {
	case ProductTypeMonth:
		bm.Set("id", product.ProductMonthly)
		bm.Set("name", "Month Subscription")
		bm.Set("description", "Month subscription")
		bm.Set("category", "ENTERTAINMENT_AND_MEDIA")
		bm.Set("type", "SERVICE")
	case ProductTypeQuarter:
		bm.Set("id", product.ProductQuarterly)
		bm.Set("name", "Quarter Subscription")
		bm.Set("description", "Quarter subscription")
		bm.Set("category", "ENTERTAINMENT_AND_MEDIA")
		bm.Set("type", "SERVICE")
	case ProductTypeYear:
		bm.Set("id", product.ProductYearly)
		bm.Set("name", "Year Subscription")
		bm.Set("description", "Year subscription")
		bm.Set("category", "ENTERTAINMENT_AND_MEDIA")
		bm.Set("type", "SERVICE")
	}
	return bm, nil
}

// GetPlanBody .
func (e *Entry) GetPlanBody(ctx *gin.Context, productType ProductType) (gopay.BodyMap, error) {
	bm := make(gopay.BodyMap)
	bm.Set("payment_preferences", &paypal.PaymentPreferences{
		AutoBillOutstanding:     true,       // 自动收取逾期费用
		SetupFeeFailureAction:   "CONTINUE", // 设置费用失败后继续
		PaymentFailureThreshold: 5,          // 支付失败5次后，停止订阅
	})
	bm.Set("status", "ACTIVE")

	switch productType {
	case ProductTypeMonth:
		bm.Set("product_id", product.ProductMonthly)
		bm.Set("name", "Month Subscription Plan")
		bm.Set("description", "Month subscription plan")
		bm.Set("billing_cycles", []paypal.BillingCycles{
			{
				TenureType:  "REGULAR", // 持续时间类型
				Sequence:    1,         // 序列
				TotalCycles: 0,         // 总周期
				Frequency: &paypal.Frequency{
					IntervalUnit:  "MONTH", // 间隔单位
					IntervalCount: 1,       // 间隔数量 1表示每月 3表示每季度 12表示每年
				},
				PricingScheme: &paypal.PricingScheme{
					FixedPrice: &paypal.FixedPrice{
						Value:        "1",
						CurrencyCode: "USD",
					},
				},
			},
		})
	case ProductTypeQuarter:
		bm.Set("product_id", product.ProductQuarterly)
		bm.Set("name", "Quarter Subscription Plan")
		bm.Set("description", "Quarter subscription plan")
		bm.Set("billing_cycles", []paypal.BillingCycles{
			{
				TenureType:  "REGULAR", // 持续时间类型
				Sequence:    1,         // 序列
				TotalCycles: 0,         // 总周期
				Frequency: &paypal.Frequency{
					IntervalUnit:  "MONTH", // 间隔单位
					IntervalCount: 3,       // 间隔数量 1表示每月 3表示每季度 12表示每年
				},
				PricingScheme: &paypal.PricingScheme{
					FixedPrice: &paypal.FixedPrice{
						Value:        "2",
						CurrencyCode: "USD",
					},
				},
			},
		})
	case ProductTypeYear:
		bm.Set("product_id", product.ProductYearly)
		bm.Set("name", "Year Subscription Plan")
		bm.Set("description", "Year subscription plan")
		bm.Set("billing_cycles", []paypal.BillingCycles{
			{
				TenureType:  "REGULAR", // 持续时间类型
				Sequence:    1,         // 序列
				TotalCycles: 0,         // 总周期
				Frequency: &paypal.Frequency{
					IntervalUnit:  "YEAR", // 间隔单位
					IntervalCount: 1,      // 间隔数量
				},
				PricingScheme: &paypal.PricingScheme{
					FixedPrice: &paypal.FixedPrice{
						Value:        "3",
						CurrencyCode: "USD",
					},
				},
			},
		})
	}
	return bm, nil
}
