package execute

import (
	"vlab/app/dao/user"
	"vlab/app/dao/vip/product"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// 	subscriptionCreate       = "/v1/billing/subscriptions"                   // 创建订阅 POST
// 	subscriptionDetail       = "/v1/billing/subscriptions/%s"                // subscription_id 订阅详情 GET
// 	subscriptionUpdate       = "/v1/billing/subscriptions/%s"                // subscription_id 更新订阅 PATCH
// 	subscriptionRevise       = "/v1/billing/subscriptions/%s/revise"         // subscription_id 修改计划或订阅数量 POST
// 	subscriptionSuspend      = "/v1/billing/subscriptions/%s/suspend"        // subscription_id 暂停订阅 POST
// 	subscriptionCancel       = "/v1/billing/subscriptions/%s/cancel"         // subscription_id 取消订阅 POST
// 	subscriptionActivate     = "/v1/billing/subscriptions/%s/activate"       // subscription_id 激活订阅 POST
// 	subscriptionCapture      = "/v1/billing/subscriptions/%s/capture"        // subscription_id 订阅时获取授权付款 POST
// 	subscriptionTransactions = "/v1/billing/subscriptions/%s/transactions"   // subscription_id 列出订阅的交易 GET

// PaypalSubscripeCreate .
func (e *Entry) PaypalSubscripeCreate(ctx *gin.Context, uInfo *user.Model, pInfo *product.Model, logNo, returnUrl, cancelUrl string) (*paypal.SubscriptionDetail, gopay.BodyMap, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, nil, err
	}

	bm := make(gopay.BodyMap)
	bm.Set("plan_id", pInfo.PaypalPlanID).
		Set("custom_id", uInfo.UUID).
		SetBodyMap("application_context", func(b gopay.BodyMap) {
			b.Set("return_url", returnUrl).
				Set("cancel_url", cancelUrl).
				// Set("user_action", "CONTINUE").
				Set("shipping_preference", "NO_SHIPPING") // 明确不需要配送信息
			// UserAction: "CONTINUE", // 明确告知PayPal用户需继续支付流程
			// 以下参数可进一步简化流程（按需使用）
			// PaymentMethod:      "paypal",
			// Set("user_action", "PAY_NOW").
			// Set("locale", "en-US").
			// Set("brand_name", "gopay").

		})
	resp, err := client.SubscriptionCreate(ctx.Request.Context(), bm)
	if err != nil {
		return nil, nil, err
	}

	return resp.Response, bm, nil
}

// PaypalSubscripeInfo .
/* Status
APPROVAL_PENDING	The subscription is created but not yet approved by the buyer.
APPROVED	The buyer has approved the subscription.
ACTIVE	The subscription is active.
SUSPENDED	The subscription is suspended.
CANCELLED	The subscription is cancelled.
EXPIRED	The subscription is expired.
*/
func (e *Entry) PaypalSubscripeInfo(ctx *gin.Context, subId string) (*paypal.SubscriptionDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}
	bm := make(gopay.BodyMap)
	resp, err := client.SubscriptionDetails(ctx.Request.Context(), subId, bm)
	if err != nil {
		return nil, err
	}

	return resp.Response, nil
}

func (e *Entry) PaypalSubscriptionCapture(ctx *gin.Context, subId string) (*paypal.EmptyRsp, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := make(gopay.BodyMap)
	resp, err := client.SubscriptionCapture(ctx.Request.Context(), subId, bm)
	if err != nil {
		return nil, err
	}

	return resp, nil
}

// PaypalSubscriptionTransactionList
func (e *Entry) PaypalSubscriptionTransactionList(ctx *gin.Context) (*paypal.SubscriptionTransaction, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}
	bm := make(gopay.BodyMap)
	resp, err := client.SubscriptionTransactionList(ctx.Request.Context(), "", bm)
	if err != nil {
		return nil, err
	}

	return resp.Response, nil
}
