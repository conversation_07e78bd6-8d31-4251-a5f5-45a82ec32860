package execute

import (
	"vlab/config"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// PaypalWebhookSignature .
func (e *Entry) PaypalWebhookSignature(ctx *gin.Context, webhookEvent paypal.WebhookEvent) (*paypal.VerifyWebhookResponse, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := gopay.BodyMap{}
	bm.Set("auth_algo", ctx.GetHeader("PayPal-Auth-Algo")).
		Set("cert_url", ctx.GetHeader("PayPal-Cert-Url")).
		Set("transmission_id", ctx.GetHeader("PayPal-Transmission-Id")).
		Set("transmission_sig", ctx.GetHeader("PayPal-Transmission-Sig")).
		Set("transmission_time", ctx.GetHeader("PayPal-Transmission-Time")).
		Set("webhook_id", config.PayPalCfg.WebHookID).
		SetBodyMap("webhook_event", func(b gopay.BodyMap) {
			b.Set("event_version", webhookEvent.EventVersion).
				Set("resource_version", webhookEvent.ResourceVersion)
		})

	resp, err := client.VerifyWebhookSignature(ctx, bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("paypal verify webhook signature error")
		return nil, err
	}
	log.Ctx(ctx).WithField("VerifyWebhookSignature", resp).WithField("bm", bm).Info("VerifyWebhookSignature Resp")

	// "SUCCESS"  "FAILURE"

	return resp, nil
}

// PaypalWebhookEventDetail .
func (e *Entry) PaypalWebhookEventDetail(ctx *gin.Context, webhookEventId string) (*paypal.WebhookEventDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := client.ShowWebhookEventDetail(ctx, webhookEventId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("parse paypal webhook event error")
		return nil, err
	}

	log.Ctx(ctx).WithField("ShowWebhookEventDetail", resp).WithField("webhookEventId", webhookEventId).Info("ShowWebhookEventDetail Resp")
	return resp.Response, nil
}
