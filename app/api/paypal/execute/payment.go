package execute

import (
	"fmt"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/paypal"
)

// paymentAuthorizeDetail  = "/v2/payments/authorizations/%s"             // authorization_id 支付授权详情 GET
// paymentAuthorizeCapture = "/v2/payments/authorizations/%s/capture"     // authorization_id 支付授权捕获 POST
// paymentReauthorize      = "/v2/payments/authorizations/%s/reauthorize" // authorization_id 重新授权支付授权 POST
// paymentAuthorizeVoid    = "/v2/payments/authorizations/%s/void"        // authorization_id 作废支付授权 POST

// paymentCaptureDetail    = "/v2/payments/captures/%s"                   // capture_id 支付捕获详情 GET
// paymentCaptureRefund    = "/v2/payments/captures/%s/refund"            // capture_id 支付捕获退款 POST
// paymentRefundDetail     = "/v2/payments/refunds/%s"                    // refund_id 支付退款详情 GET

// PaypalPaymentCaptureDetail 详情
func (e *Entry) PaypalPaymentCaptureDetail(ctx *gin.Context, authorizationId string) (*paypal.PaymentAuthorizeDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := client.PaymentAuthorizeDetail(ctx.Request.Context(), authorizationId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PaypalPaymentCaptureDetail")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("PaypalPaymentCaptureDetail")
		return nil, fmt.Errorf("PaypalPaymentCaptureDetail: %s", resp.Error)
	}

	log.Ctx(ctx).WithField("PaymentAuthorizeDetail", resp.Response).WithField("authorizationId", authorizationId).Info("PaypalPaymentCaptureDetail Resp")
	return resp.Response, nil
}
