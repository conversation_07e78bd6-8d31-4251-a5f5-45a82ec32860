package execute

import (
	"fmt"
	"vlab/app/dao/user"
	"vlab/app/dao/vip/product"
	"vlab/pkg/log"
	"vlab/pkg/util/decimalUtil"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// orderCreate    = "/v2/checkout/orders"                           // 创建订单 POST
// orderUpdate    = "/v2/checkout/orders/%s"                        // order_id 更新订单 PATCH
// orderDetail    = "/v2/checkout/orders/%s"                        // order_id 订单详情 GET
// orderAuthorize = "/v2/checkout/orders/%s/authorize"              // order_id 订单支付授权 POST
// orderCapture   = "/v2/checkout/orders/%s/capture"                // order_id 订单支付捕获 POST
// orderConfirm   = "/v2/checkout/orders/%s/confirm-payment-source" // order_id 订单支付确认 POST

// PaypalOrderCreate 创建订单
func (e *Entry) PaypalOrderCreate(ctx *gin.Context, uInfo *user.Model, pInfo *product.Model, logNo, returnUrl, cancelUrl string) (*paypal.OrderDetail, gopay.BodyMap, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, nil, err
	}

	pus := []*paypal.PurchaseUnit{
		{
			ReferenceId: logNo, // 订单号
			Amount: &paypal.Amount{
				CurrencyCode: pInfo.Currency,
				Value:        fmt.Sprintf("%v", decimalUtil.AmountToFloat(pInfo.Price)),
			},
			CustomId: uInfo.UUID,
		},
	}

	bm := make(gopay.BodyMap)
	bm.Set("intent", "CAPTURE").
		Set("purchase_units", pus).
		SetBodyMap("payment_source", func(b gopay.BodyMap) {
			b.SetBodyMap("paypal", func(bb gopay.BodyMap) {
				bb.SetBodyMap("experience_context", func(bbb gopay.BodyMap) {
					bbb.Set("return_url", returnUrl).
						Set("cancel_url", cancelUrl).
						Set("user_action", "CONTINUE").
						Set("shipping_preference", "NO_SHIPPING") // 明确不需要配送信息
					// UserAction: "CONTINUE", // 明确告知PayPal用户需继续支付流程
					// 以下参数可进一步简化流程（按需使用）
					// PaymentMethod:      "paypal",
					// Set("user_action", "PAY_NOW").
					// Set("locale", "en-US").
					// Set("brand_name", "gopay").
				})
			})
		})
	order, err := client.CreateOrder(ctx, bm)
	if err != nil {
		return nil, nil, err
	}
	log.Ctx(ctx).WithField("PaypalOrderCreate", order.Response).WithField("bm", bm).Info("PaypalOrderCreateResp")

	return order.Response, bm, nil
}

// PaypalOrderCapture 捕获订单
/*
PAYER_ACTION_REQUIRED 需要付款人的进一步操作，例如同意条款和条件或完成身份验证。
APPROVED 客户批准了付款
VOIDED 无效的订单
CREATED	订单是使用指定的上下文创建的
SAVED	订单已保存并持久化。订单状态将继续进行，直到对订单中的所有购买单位执行最终捕获（final_capture = true）
COMPLETED 订单已完成，并且已创建付款资源。重要：在履行订单中的所有购买单位之前检查purchase_units[].payments.captures[].status。
完成的订单可以表示支付被授权、授权的支付已被捕获或支付被拒绝
*/
func (e *Entry) PaypalOrderCapture(ctx *gin.Context, orderID string) (*paypal.OrderDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := gopay.BodyMap{}
	resp, err := client.OrderCapture(ctx, orderID, bm)
	if err != nil {
		return nil, err
	}

	return resp.Response, nil
}

// PaypalOrderInfo 获取订单详情
func (e *Entry) PaypalOrderInfo(ctx *gin.Context, orderID string) (*paypal.OrderDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := gopay.BodyMap{}
	resp, err := client.OrderDetail(ctx, orderID, bm)
	if err != nil {
		return nil, err
	}

	return resp.Response, nil
}
