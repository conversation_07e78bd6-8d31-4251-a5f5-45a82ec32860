package execute

import (
	"fmt"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// PaypalProductList 列表
func (e *Entry) PaypalProductList(ctx *gin.Context) (*paypal.ProductsList, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := make(gopay.BodyMap)
	resp, err := client.ProductList(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ProductListError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("ProductListError")
		return nil, fmt.Errorf("ProductListError: %s", resp.Error)
	}

	return resp.Response, nil
}

// PaypalProductCreate 创建
func (e *Entry) PaypalProductCreate(ctx *gin.Context, productType ProductType) (*paypal.Product, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm, err := e.GetProductBody(ctx, productType)
	if err != nil {
		return nil, err
	}

	resp, err := client.ProductCreate(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ProductCreateError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("ProductCreateError")
		return nil, fmt.Errorf("ProductCreateError: %s", resp.Error)
	}

	return resp.Response, nil
}

// PaypalProductInfo 详情
func (e *Entry) PaypalProductInfo(ctx *gin.Context, productId string) (*paypal.ProductDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := make(gopay.BodyMap)
	resp, err := client.ProductDetails(ctx.Request.Context(), productId, bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ProductInfoError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("ProductInfoError")
		return nil, fmt.Errorf("ProductInfoError: %s", resp.Error)
	}

	return resp.Response, nil
}
