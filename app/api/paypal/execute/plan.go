package execute

import (
	"fmt"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/paypal"
)

// PaypalPlanList 列表
func (e *Entry) PaypalPlanList(ctx *gin.Context) (*paypal.BillingPlan, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm := make(gopay.BodyMap)
	resp, err := client.PlanList(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ProductListError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("ProductListError")
		return nil, fmt.Errorf("ProductListError: %s", resp.Error)
	}

	return resp.Response, nil
}

func (e *Entry) PaypalPlanCreate(ctx *gin.Context, productType ProductType) (*paypal.BillingDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	bm, err := e.GetPlanBody(ctx, productType)
	if err != nil {
		return nil, err
	}

	resp, err := client.CreateBillingPlan(ctx.Request.Context(), bm)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PlanCreateError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("PlanCreateError")
		return nil, fmt.Errorf("PlanCreateError: %s", resp.Error)
	}

	return resp.Response, nil
}

// PaypalPlanInfo 详情
func (e *Entry) PaypalPlanInfo(ctx *gin.Context, planId string) (*paypal.PlanDetail, error) {
	client, err := e.GetPaypalClient(ctx)
	if err != nil {
		return nil, err
	}

	resp, err := client.PlanDetails(ctx.Request.Context(), planId)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("PlanInfoError")
		return nil, err
	}

	if resp.Code != 0 {
		log.Ctx(ctx).WithError(err).Error("PlanInfoError")
		return nil, fmt.Errorf("PlanInfoError: %s", resp.Error)
	}

	return resp.Response, nil
}
