package paypal

import (
	"vlab/app/api/paypal/execute"
	"vlab/app/dto/api/paypal"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// PaypalPlanList 列表
func PaypalPlanList(ctx *gin.Context) {
	ret, err := execute.GetApi().PaypalPlanList(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func PaypalPlanCreate(ctx *gin.Context) {
	req := paypal.PaypalPlanCreateReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalPlanCreate(ctx, execute.ProductType(req.ProductType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PaypalPlanInfo 详情
func PaypalPlanInfo(ctx *gin.Context) {
	req := paypal.PaypalPlanInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalPlanInfo(ctx, req.PlanID)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
