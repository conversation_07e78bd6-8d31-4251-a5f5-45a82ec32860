package paypal

import (
	"vlab/app/api/paypal/execute"
	"vlab/app/dto/api/paypal"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// PaypalSubscripeInfo 详情
func PaypalSubscripeInfo(ctx *gin.Context) {
	req := paypal.PaypalSubscripeInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := execute.GetApi().PaypalSubscripeInfo(ctx, req.SubID)

	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// PaypalSubscriptionTransactionList 列表
func PaypalSubscriptionTransactionList(ctx *gin.Context) {
	ret, err := execute.GetApi().PaypalSubscriptionTransactionList(ctx)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
