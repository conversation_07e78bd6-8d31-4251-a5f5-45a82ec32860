package paypal

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"vlab/app/api/paypal/execute"
	"vlab/app/service/user/vip"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/paypal"
)

// PaypalNotify .
func PaypalNotify(ctx *gin.Context) {
	log.Ctx(ctx).Info("PaypalNotify Start")
	header := ctx.Request.Header
	body, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ctx read body error"})
		return
	}
	ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	log.Ctx(ctx).WithField("header", header).WithField("body", string(body)).Info("PaypalNotifyData")

	var event paypal.WebhookEvent
	if err := json.Unmarshal(body, &event); err != nil {
		log.Ctx(ctx).Error("PaypalNotify invalid request")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}

	ret, err := execute.GetApi().PaypalWebhookSignature(ctx, event)
	if err != nil {
		log.Ctx(ctx).Error("PaypalNotify webhook signature error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "webhook signature error"})
		return
	}
	if ret.VerificationStatus != "SUCCESS" {
		log.Ctx(ctx).Error("PaypalNotify verification status error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "verification status error"})
		return
	}

	if err := vip.GetService().PaypalNotify(ctx, event); err != nil {
		log.Ctx(ctx).WithError(err).Error("PaypalNotify Logic error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("PaypalNotify End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
