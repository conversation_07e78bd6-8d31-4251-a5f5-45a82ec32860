package tmdb

import (
	"encoding/json"
	"io"
	"net/http"
	"strconv"

	"vlab/config"
)

type TmdbResponse struct {
	StatusCode int
	Body       []byte
}

func doRequest(url string, method string, params map[string]string) (*TmdbResponse, error) {
	client := &http.Client{}

	// Create request
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}

	// Add query parameters
	if params != nil {
		q := req.URL.Query()
		for key, value := range params {
			q.Add(key, value)
		}
		req.URL.RawQuery = q.Encode()
	}

	// Add TMDB API key
	req.Header.Add("Content-Type", "application/json")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return &TmdbResponse{
		StatusCode: resp.StatusCode,
		Body:       body,
	}, nil
}

func SearchTmdbList(name string, page uint64) (*TmdbList, error) {
	resp := &TmdbList{}

	url := config.TmdbCfg.Url + name
	ret, err := doRequest(url, "GET", map[string]string{
		"page": strconv.FormatUint(page, 10),
	})
	if err != nil {
		return nil, err
	}
	if ret.StatusCode != http.StatusOK {
		return nil, nil
	}
	if err := json.Unmarshal(ret.Body, resp); err != nil {
		return nil, err
	}

	return resp, nil
}

type TmdbList struct {
	Page    int `json:"page"`
	Results []struct {
		Adult            bool     `json:"adult"`
		BackdropPath     *string  `json:"backdrop_path"`
		GenreIds         []int    `json:"genre_ids"`
		Id               int      `json:"id"`
		OriginCountry    []string `json:"origin_country"`
		OriginalLanguage string   `json:"original_language"`
		OriginalName     string   `json:"original_name"`
		Overview         string   `json:"overview"`
		Popularity       float64  `json:"popularity"`
		PosterPath       *string  `json:"poster_path"`
		FirstAirDate     string   `json:"first_air_date"`
		Name             string   `json:"name"`
		VoteAverage      float64  `json:"vote_average"`
		VoteCount        int      `json:"vote_count"`
	} `json:"results"`
	TotalPages   int `json:"total_pages"`
	TotalResults int `json:"total_results"`
}
