package bytes

import (
	"vlab/app/api/aliyun/common"
	"vlab/app/api/aliyun/oss"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// ByteTosToken .
func ByteTosToken(ctx *gin.Context) {
	req := aliyunDto.AliyunOssTokenReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	ret, err := oss.GetApi().GetOssToken(ctx, ctxAccount.AccountID, common.BucketType(req.BucketType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
