package bytes

import (
	"vlab/app/api/aliyun/common"
	"vlab/app/api/bytes/vod"
	"vlab/app/common/dbs"
	byteDto "vlab/app/dto/api/byte"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// UploadMediaByUrl .
func UploadMediaByUrl(ctx *gin.Context) {
	req := byteDto.UploadMediaByUrlReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().UploadMediaByUrl(ctx, req.UrlList)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UploadMediaByUrl .
func QueryUploadTaskInfo(ctx *gin.Context) {
	req := byteDto.QueryUploadTaskInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().QueryUploadTaskInfo(ctx, req.JobIds)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func GetPlayInfo(ctx *gin.Context) {
	req := byteDto.GetMediaInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().GetPlayInfo(ctx, req.Vid, common.BytesVodDefinitionOD, dbs.True)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

func GetMediaInfo(ctx *gin.Context) {
	req := byteDto.GetMediaInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().GetMediaInfo(ctx, req.Vid)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
