package oss

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/volcengine/volcengine-go-sdk/service/sts"
	"github.com/volcengine/volcengine-go-sdk/volcengine"
	"github.com/volcengine/volcengine-go-sdk/volcengine/credentials"
	stsRequest "github.com/volcengine/volcengine-go-sdk/volcengine/request"
	"github.com/volcengine/volcengine-go-sdk/volcengine/session"
	"vlab/app/api/aliyun/common"
	byteplusDto "vlab/app/dto/api/byteplus"
	"vlab/config"
)

// GetBytePlusOssToken .
func (e *Entry) GetBytePlusOssToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*byteplusDto.BytePlusOssTokenResp, error) {
	var (
		bucket  = common.GetBytePlusBucket(bt)
		request = &stsRequest.Request{}
		input   = &sts.AssumeRoleInput{}
		output  = &sts.AssumeRoleOutput{}
	)

	conf := volcengine.NewConfig().
		WithRegion(config.BytePlusCfg.RegionID).
		WithCredentials(
			credentials.NewStaticCredentials(
				config.BytePlusCfg.AccessKeyID,
				config.BytePlusCfg.AccessKeySecret,
				""))
	sess, err := session.NewSession(conf)
	if err != nil {
		return nil, err
	}
	stsClient := sts.New(sess)

	input.SetDurationSeconds(3600)
	input.SetRoleSessionName(fmt.Sprintf("%v-Account-%v", bucket, accountID))
	input.SetRoleTrn(config.BytePlusCfg.RoleTrn)

	// 创建AssumeRole请求
	request, output = stsClient.AssumeRoleRequest(input)
	if err := request.Send(); err != nil {
		return nil, err
	}

	return &byteplusDto.BytePlusOssTokenResp{
		Endpoint:        config.BytePlusCfg.Endpoint,
		Bucket:          bucket,
		RegionID:        config.BytePlusCfg.RegionID,
		Token:           *output.Credentials.SessionToken,
		AccessKeyId:     *output.Credentials.AccessKeyId,
		AccessKeySecret: *output.Credentials.SecretAccessKey,
	}, nil
}
