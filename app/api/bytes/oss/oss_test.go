package oss

import (
	"os"
	"testing"

	"github.com/gin-gonic/gin"
	"vlab/app/api/aliyun/common"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/config"
	"vlab/pkg/helper"
)

func TestMain(m *testing.M) {
	os.Setenv("config", "../../../../config/prd.ini")
	// 读取配置文件
	config.Setup()

	// Run the tests
	code := m.Run()

	// Exit with the result code from m.Run()
	os.Exit(code)
}

func TestEntry_GetBytePlusOssToken(t *testing.T) {

	type args struct {
		ctx       *gin.Context
		accountID uint64
		bt        common.BucketType
	}
	tests := []struct {
		name    string
		args    args
		want    *aliyunDto.AliyunOssTokenResp
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "GetBytePlusOssToken",
			args: args{
				ctx:       helper.GenGinCtx(),
				accountID: 123456,
				bt:        common.BucketTypeSubtitle,
			},
			want: &aliyunDto.AliyunOssTokenResp{
				Bucket:          config.BytePlusCfg.BucketSubtitle,
				RegionID:        config.BytePlusCfg.OssRegionID,
				Token:           "test-token",
				AccessKeyId:     "test-access-key-id",
				AccessKeySecret: "test-access-key-secret",
			},
			wantErr: false,
		},
		{
			name: "GetBytePlusOssToken",
			args: args{
				ctx:       helper.GenGinCtx(),
				accountID: 123456,
				bt:        common.BucketTypeImage,
			},
			want: &aliyunDto.AliyunOssTokenResp{
				Bucket:          config.BytePlusCfg.BucketSubtitle,
				RegionID:        config.BytePlusCfg.OssRegionID,
				Token:           "test-token",
				AccessKeyId:     "test-access-key-id",
				AccessKeySecret: "test-access-key-secret",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &Entry{}
			got, err := e.GetBytePlusOssToken(tt.args.ctx, tt.args.accountID, tt.args.bt)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBytePlusOssToken() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.Token == "" {
				t.Errorf("GetBytePlusOssToken() got = %v, want non-empty token", got)
				return
			}
			if got.AccessKeyId == "" || got.AccessKeySecret == "" {
				t.Errorf("GetBytePlusOssToken() got = %v, want non-empty AccessKeyId and AccessKeySecret", got)
				return
			}
		})
	}
}
