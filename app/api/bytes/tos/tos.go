package tos

import (
	"fmt"
	"vlab/app/api/aliyun/common"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/config"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"github.com/gin-gonic/gin"
)

// GetTosToken .
func (e *Entry) GetTosToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssTokenResp, error) {
	var (
		bucket      = common.GetBucket(bt)
		client, err = sts.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return nil, err
	}
	// 创建AssumeRole请求
	request := sts.CreateAssumeRoleRequest()
	request.Scheme = "https"
	request.RoleArn = config.AliyunCfg.RoleArn
	request.RoleSessionName = fmt.Sprintf("%v-Account-%v", bucket, accountID)
	request.DurationSeconds = "3600" // 临时凭证有效时间，单位为秒（最多3600秒）

	// 发送请求并获取响应
	resp, err := client.AssumeRole(request)
	if err != nil {
		return nil, err
	}
	return &aliyunDto.AliyunOssTokenResp{
		Bucket:          bucket,
		RegionID:        config.AliyunCfg.OssRegionID,
		Token:           resp.Credentials.SecurityToken,
		AccessKeyId:     resp.Credentials.AccessKeyId,
		AccessKeySecret: resp.Credentials.AccessKeySecret,
	}, nil
}
