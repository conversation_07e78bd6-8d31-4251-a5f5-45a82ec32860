package tos

import (
	"sync"
	adminAccount "vlab/app/dao/admin_account"
	adminArea "vlab/app/dao/admin_area"
	adminMenu "vlab/app/dao/admin_menu"
	menuBackup "vlab/app/dao/admin_menu_backup"
	adminRole "vlab/app/dao/admin_role"
)

type Api interface {
}

type Entry struct {
	MenuRepo       adminMenu.Repo
	AreaRepo       adminArea.Repo
	AccountRepo    adminAccount.Repo
	RoleRepo       adminRole.Repo
	MenuBackupRepo menuBackup.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RoleRepo:       adminRole.GetRepo(),
		AccountRepo:    adminAccount.GetRepo(),
		MenuRepo:       adminMenu.GetRepo(),
		MenuBackupRepo: menuBackup.GetRepo(),
		AreaRepo:       adminArea.GetRepo(),
	}
}
