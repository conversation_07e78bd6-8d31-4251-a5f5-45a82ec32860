package bytes

import (
	"github.com/gin-gonic/gin"
	"vlab/app/api/aliyun/common"
	"vlab/app/api/bytes/oss"
	byteplusDto "vlab/app/dto/api/byteplus"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func BytePlusOssToken(ctx *gin.Context) {
	req := byteplusDto.BytePlusOssTokenReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	ret, err := oss.GetApi().GetBytePlusOssToken(ctx, ctxAccount.AccountID, common.BucketType(req.BucketType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
