package vod

import (
	"fmt"
	"strings"

	"vlab/app/common/dbs"
	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod/models/business"
	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod/models/request"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// UploadMediaByUrl .
func (e *Entry) UploadMediaByUrl(ctx *gin.Context, urlList []string) (*business.VodUrlResponseData, error) {
	var (
		instance = e.InitVodClient(ctx)
		urlSets  = make([]*business.VodUrlUploadURLSet, 0)
	)
	if len(urlList) == dbs.False || len(urlList) > 20 {
		return nil, ecode.VodUploadMediaByUrlsErr
	}

	for _, val := range urlList {
		if strings.TrimSpace(val) == "" {
			continue
		}

		urlSet := &business.VodUrlUploadURLSet{
			SourceUrl: strings.TrimSpace(val),                      // 源视频 URL
			FileName:  fmt.Sprintf("%s.m3u8", uuid.New().String()), // 设置文件名
			// FileExtension: ".m3u8",                // 设置文件后缀，以 . 开头，不超过8位，非必须字段
		}
		urlSets = append(urlSets, urlSet)
	}

	// 创建request
	request := &request.VodUrlUploadRequest{
		SpaceName: config.BytePlusCfg.SpaceName,
		URLSets:   urlSets,
	}

	resp, _, err := instance.UploadMediaByUrl(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UploadMediaByUrl, err")
		return nil, err
	}
	log.Ctx(ctx).WithField("resp", resp).Info("UploadMediaByUrlResp")

	// if resp.ResponseMetadata.Error != nil {
	// 	log.Ctx(ctx).WithField("resp.ResponseMetadata.Error", resp.ResponseMetadata.Error).Error("resp.ResponseMetadata.Error, err")
	// 	return nil, err
	// }

	return resp.GetResult(), nil
}

func (e *Entry) QueryUploadTaskInfo(ctx *gin.Context, jobIds []string) (*business.VodQueryData, error) {
	if len(jobIds) == 0 {
		return nil, ecode.ParamInvalidErr
	}
	var (
		instance = e.InitVodClient(ctx)
		request  = &request.VodQueryUploadTaskInfoRequest{JobIds: strings.Join(jobIds, ",")}
	)

	resp, _, err := instance.QueryUploadTaskInfo(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("QueryUploadTaskInfo, err")
		return nil, err
	}
	// if resp.ResponseMetadata.Error != nil {
	// 	log.Ctx(ctx).WithField("resp.ResponseMetadata.Error", resp.ResponseMetadata.Error).Error("resp.ResponseMetadata.Error, err")
	// 	return nil, err
	// }

	return resp.GetResult(), nil
}
