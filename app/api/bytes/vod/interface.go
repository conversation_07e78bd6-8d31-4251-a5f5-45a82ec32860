package vod

import (
	"sync"
	videoMpsDao "vlab/app/dao/content_video_mps"
)

// https://help.aliyun.com/zh/vod/developer-reference/api-vod-2017-03-21-geturluploadinfos
var uploadJobStateMapStatus = map[string]videoMpsDao.MpsStatus{
	"Uploading":     videoMpsDao.MpsStatusIng,
	"SUCCESS":       videoMpsDao.MpsStatusSuccess,
	"UploadFail":    videoMpsDao.MpsStatusFailed,
	"TranscodeFail": videoMpsDao.MpsStatusFailed,
}

type Api interface {
}

type Entry struct {
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{}
}
