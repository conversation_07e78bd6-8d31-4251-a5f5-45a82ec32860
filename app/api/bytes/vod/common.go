package vod

import (
	"vlab/config"

	"github.com/byteplus-sdk/byteplus-sdk-golang/base"
	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod"
	"github.com/gin-gonic/gin"
)

func (e *Entry) InitVodClient(ctx *gin.Context) *vod.Vod {
	instance := vod.NewInstance()
	instance.SetCredential(base.Credentials{
		AccessKeyID:     config.BytePlusCfg.Ak,
		SecretAccessKey: config.BytePlusCfg.Sk,
	})
	return instance
}
