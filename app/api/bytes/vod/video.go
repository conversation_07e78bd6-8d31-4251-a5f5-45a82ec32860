package vod

import (
	"encoding/json"
	"strings"
	"vlab/app/api/aliyun/common"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod/models/business"
	"github.com/byteplus-sdk/byteplus-sdk-golang/service/vod/models/request"
	"github.com/gin-gonic/gin"
)

// GetPlayInfo .
func (e *Entry) GetPlayInfo(ctx *gin.Context, vid string, definition common.VodDefinition, action watchVideoPv.LogType) (*business.VodPlayInfoModel, error) {
	instance := e.InitVodClient(ctx)
	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	request := &request.VodGetPlayInfoRequest{
		Vid:        vid,                // 视频ID
		Definition: string(definition), // 视频流清晰度，支持：240p,360p,480p,540p,720p,1080p,2k,4k,od,oe.默认返回全部
		Ssl:        "1",                // 返回https播放地址，默认否, 1-是；0-否
		// Format:     "",   // 封装格式，支持 mp4、dash、hls、mp3、m4a、ogg, 默认mp4
		// Codec:      "",   // 编码格式，支持 mp3、aac、opus、H264、H265、H266, 视频默认H264，音频默认aac
		// FileType:   "",   // 流文件类型,支持: 加密视频流evideo，加密音频流传eaudio,非加密视频流video,普通音频音频流audio.默认video
		// LogoType:   "",   // 水印贴片标签
		// Base64:     "",   // 播放地址是否base64编码，默认否，支持设置： 0-否，1-是
		// NeedThumbs:         "",   // 是否需要雪碧图（缩略图），默认否，1-是；0-否
		// NeedBarrageMask:    "",   // 是否需要蒙版弹幕，默认否，1-是；0-否
		// CdnType:            "",   // 指定CDN类型, 默认不传为普通CDN, 若需使用请联系技术支持
		// UnionInfo:          "",   // 唯一性标识信息, 若需使用请联系技术支持
		// HDRDefinition:      "",   // HDR清晰度，默认不查询，支持：all, 240p, 360p, 420p, 480p, 540p, 720p, 1080p, 2k, 4k
		// PlayScene:          "",   // 播放场景，指定获取对应场景的音视频流。当前支持：preview-试看
		// DrmExpireTimestamp: "",   // DRM过期时间戳, 若需使用请联系技术支持
		// Quality:            "",   // 音频音质。当FileType为audio和eaudio时起作用,表示音频音质参数.支持: medium, higher, highest.默认返回所有音频流
		// PlayConfig:         "",   // 播放配置,可指定播放域名
		// ForceExpire:        "",   // 强行指定本次请求的时间戳防盗链 单位秒
		// DashMode:           "",   // format=dash时,指定下发video model还是mpd文件
	}
	playConfigMap := map[string]string{
		"PlayDomain": channelKeyInfo.VodPlayDomain,
	}
	if action == watchVideoPv.LogTypeDownload {
		playConfigMap["PlayDomain"] = channelKeyInfo.VodDownloadDomain
	}
	playConfigStr, _ := json.Marshal(playConfigMap)
	request.PlayConfig = string(playConfigStr)

	resp, _, err := instance.GetPlayInfo(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetMediaInfos, err")
		return nil, err
	}
	if resp.ResponseMetadata.Error != nil {
		log.Ctx(ctx).WithError(err).Error("resp.ResponseMetadata.Error, err")
		return nil, err
	}

	return resp.Result, nil
}

func (e *Entry) GetMediaInfo(ctx *gin.Context, vid string) (*business.VodMediaInfo, error) {
	retMap, err := e.GetMediaInfos(ctx, []string{vid})
	if err != nil {
		return nil, err
	}
	if ret, ok := retMap[vid]; ok {
		return ret, nil
	}
	return nil, ecode.ParamInvalidErr
}

func (e *Entry) GetMediaInfos(ctx *gin.Context, Vids []string) (map[string]*business.VodMediaInfo, error) {
	var (
		instance = e.InitVodClient(ctx)
		request  = &request.VodGetMediaInfosRequest{Vids: strings.Join(Vids, ",")}
		retMap   = map[string]*business.VodMediaInfo{}
	)
	resp, _, err := instance.GetMediaInfos(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("GetMediaInfos, err")
		return nil, err
	}

	if resp.ResponseMetadata.Error != nil {
		log.Ctx(ctx).WithError(err).Error("resp.ResponseMetadata.Error, err")
		return nil, err
	}

	for _, media := range resp.Result.MediaInfoList {
		if media.BasicInfo.Vid == "" {
			continue
		}
		retMap[media.BasicInfo.Vid] = media
	}

	return retMap, nil
}
