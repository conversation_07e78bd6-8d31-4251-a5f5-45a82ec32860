package auto

type TvShowType struct {
	ProgrammeType string `json:"programmeType"`
	ProgrammeName string `json:"programmeName"`
}

type IosStappubtitlingInfo struct {
	WordUrl        string `json:"wordUrl"`
	WordAbbr       string `json:"wordAbbr"`
	WordName       string `json:"wordName"`
	TranslateStyle int    `json:"translateStyle"`
}

type IosStappDefinitionInfo struct {
	ClarityDetail    string `json:"clarityDetail"`
	ClarityCode      string `json:"clarityCode"`
	AllClarityDetail string `json:"allClarityDetail"`

	*PlayResp
}

type IosStappContentEpisodeVo struct {
	IosStappubtitlingInfos  []*IosStappubtitlingInfo  `json:"iosStappubtitlingInfos"`
	RightFilmId             uint64                    `json:"rightFilmId"`
	IosStappDefinitionInfos []*IosStappDefinitionInfo `json:"iosStappDefinitionInfos"`
	VideoTranslateId        string                    `json:"videoTranslateId"`
	FullSecondAmount        int                       `json:"fullSecondAmount"`
	RightFilmName           string                    `json:"rightFilmName"`
	WhetherShowApp          bool                      `json:"whetherShowApp"`
	FullSeason              int                       `json:"fullSeason"`
	ResourceType            int                       `json:"resourceType"`
}

type IosStappUpInfo struct {
	ChannelOwnerName string `json:"channelOwnerName"`
	ChannelOwnerJpg  string `json:"channelOwnerJpg"`
	ChannelOwnerID   int    `json:"channelOwnerID"`
}

type IosStappDrameTypeVo struct {
	ProgrammeType string `json:"programmeType"`
	ProgrammeName string `json:"programmeName"`
}

type IosStappLikeInfo struct {
	LandscapeImageURL     string              `json:"landscapeImageURL"`
	LabelNames            []string            `json:"labelNames"`
	IosStappDrameTypeVo   IosStappDrameTypeVo `json:"iosStappDrameTypeVo"`
	AhterName             string              `json:"ahterName"`
	TvArgs                string              `json:"tvArgs"`
	PremiereDate          int                 `json:"premiereDate"`
	ResourceStatus        int                 `json:"resourceStatus"`
	Name                  string              `json:"name"`
	AhterImg              string              `json:"ahterImg"`
	TvStyle               int                 `json:"tvStyle"`
	Rank                  float64             `json:"rank"`
	ShowRegionNames       []string            `json:"showRegionNames"`
	Id                    string              `json:"id"`
	ShowRegionNameLists   []*ShowRegionName   `json:"showRegionNameLists"`
	UprightCoverUrl       string              `json:"uprightCoverUrl"`
	ItemizeLabelNameLists []*ItemizeLabelName `json:"itemizeLabelNameLists"`
}

type ShowRegionName struct {
	Name string `json:"name"`
	Id   int    `json:"id"`
}

type ItemizeLabelName struct {
	Name string `json:"name"`
	Id   int    `json:"id"`
}

type IosStappOpenApiIDNameVO struct {
	Name string `json:"name"`
	Id   uint64 `json:"id"`
}

type PlayZoneNameVO struct {
	Name string `json:"name"`
	Id   uint64 `json:"id"`
}

type IosStappUpdateInfo struct {
	UpdateDate  string `json:"updateDate"`
	UpdateRight int    `json:"updateRight"`
}

type IosStappContentTagResource struct {
}

type IosStappOpenApiStarVO struct {
	IdentityName         string `json:"identityName"`
	CelebrityID          int    `json:"celebrityID"`
	NativePlaceName      string `json:"nativePlaceName"`
	Position             string `json:"position"`
	VisualRepresentation string `json:"visualRepresentation"`
}

type DetailResp struct {
	Iso6391 string `json:"iso6391" binding:"required"`

	CinematicType                 int                           `json:"cinematicType"`
	HorizontalImageLink           string                        `json:"horizontalImageLink"`
	TvShowType                    TvShowType                    `json:"tvShowType"`
	IosStappContentTagResources   []*IosStappContentTagResource `json:"iosStappContentTagResources"`
	IosStappContentEpisodeVos     []*IosStappContentEpisodeVo   `json:"iosStappContentEpisodeVos"`
	AmountOfEpisodesOfCinemas     int                           `json:"amountOfEpisodesOfCinemas"`
	StandingCoverLink             string                        `json:"standingCoverLink"`
	MotionPictureId               string                        `json:"motionPictureId" binding:"required"`
	CinematicNickName             string                        `json:"cinematicNickName"`
	BriefIntroduction             string                        `json:"briefIntroduction"`
	PresentationTime              int                           `json:"presentationTime"`
	IosStappOpenApiStarVOS        []*IosStappOpenApiStarVO      `json:"iosStappOpenApiStarVOS"`
	IosStappUpInfo                IosStappUpInfo                `json:"iosStappUpInfo"`
	RightView                     string                        `json:"rightView"`
	LabelLists                    []string                      `json:"labelLists"`
	WhetherToDisplayTheSeriesName bool                          `json:"whetherToDisplayTheSeriesName"`
	IosStappLikeInfos             []*IosStappLikeInfo           `json:"iosStappLikeInfos"`
	Ordered                       bool                          `json:"ordered"`
	InSavedItems                  bool                          `json:"inSavedItems"`
	CreditScore                   float64                       `json:"creditScore"`
	IosStappOpenApiIDNameVOS      []*IosStappOpenApiIDNameVO    `json:"iosStappOpenApiIDNameVOS"`
	PlayZoneNameVOS               []*PlayZoneNameVO             `json:"playZoneNameVOS"`
	IosStappRefInfos              []*IosStappRefInfo            `json:"iosStappRefInfos"`
	PlayZoneNameNames             []string                      `json:"playZoneNameNames"`
	IosStappUpdateInfo            IosStappUpdateInfo            `json:"iosStappUpdateInfo"`
	DispelType                    int                           `json:"dispelType"`
	OfficialKeywords              string                        `json:"officialKeywords"`
	CinematicName                 string                        `json:"cinematicName"`
	SequenceHowManySeasons        int                           `json:"sequenceHowManySeasons"`
}

type IosStappRefInfo struct {
	IosStappDrameTypeVo     IosStappDrameTypeVo `json:"iosStappDrameTypeVo"`
	PlatformMediaParams     string              `json:"platformMediaParams"`
	HorizontalImageLink     string              `json:"horizontalImageLink"`
	PlatformMediaName       string              `json:"platformMediaName"`
	VerticalContentImageUrl string              `json:"verticalContentImageUrl"`
	Style                   int                 `json:"style"`
	HowManySeason           int                 `json:"howManySeason"`
	PlatformMediaId         string              `json:"platformMediaId"`
}

type PlayReq struct {
	PlayResp

	FullVersionID uint64 `json:"fullVersionId" binding:"required"` // lok episode id
	//InformationID uint64 `json:"informationId"` // lok show id
	//PlaybackQuality string `json:"playbackQuality"` // resolution
}

type PlayResp struct {
	VisualEffects string `json:"visualEffects"`
	CinemaUrl     string `json:"cinemaUrl"`
	FilmBusType   int    `json:"filmBusType"`
	KeyScenesId   string `json:"keyScenesId"`
	VideoSize     int    `json:"videoSize"`
	AllTime       int64  `json:"allTime"`
}

type ChannelListResp struct {
	IosStappNavigationBarItemVOS []struct {
		NavMenuStyle string `json:"navMenuStyle"`
		NavMenuSkip  string `json:"navMenuSkip"`
		NavMenuName  string `json:"navMenuName"`
		NavMenuFile  string `json:"navMenuFile"`
		NavMenuUrl   string `json:"navMenuUrl"`
		NavMenuOrder uint64 `json:"navMenuOrder"`
		NavMenuId    uint64 `json:"navMenuId"`
	} `json:"iosStappNavigationBarItemVOS"`
}

type ChannelDetailResp struct {
	SearchTerms                     string `json:"searchTerms"`
	CurrentPageNumber               int    `json:"currentPageNumber"`
	DashboardSearchBlockInformation []struct {
		WidthToHeightRatio  float64 `json:"widthToHeightRatio,omitempty"`
		LinkParameters      string  `json:"linkParameters"`
		EntranceSectionName string  `json:"entranceSectionName"`
		EntranceSectionId   int     `json:"entranceSectionId"`
		EntranceSectionData []struct {
			DisplayMarker     bool    `json:"displayMarker,omitempty"`
			NavigationAddress string  `json:"navigationAddress,omitempty"`
			VName             string  `json:"vName,omitempty"`
			IgUrl             string  `json:"igUrl,omitempty"`
			NameTitle         string  `json:"nameTitle"`
			LoginRequired     bool    `json:"loginRequired,omitempty"`
			CtType            string  `json:"ctType,omitempty"`
			Id                int     `json:"id,omitempty"`
			NavigationType    string  `json:"navigationType,omitempty"`
			NameTitleCt       bool    `json:"nameTitleCt,omitempty"`
			WesPm             string  `json:"wesPm,omitempty"`
			Rating            float64 `json:"rating,omitempty"`
			ContentNum        int     `json:"contentNum,omitempty"`
			Classification    int     `json:"classification,omitempty"`
			ContentStatus     int     `json:"contentStatus,omitempty"`
			DTypeVo           struct {
				DramaName string `json:"dramaName"`
				DramaType string `json:"dramaType"`
			} `json:"dTypeVo,omitempty"`
			PublicationDate int    `json:"publicationDate,omitempty"`
			ReleaseTime     string `json:"releaseTime,omitempty"`
			Booked          bool   `json:"booked,omitempty"`
			ResourceNum     string `json:"resourceNum,omitempty"`
			RkContent       []struct {
				ResourceId        string `json:"resourceId"`
				NavigationAddress string `json:"navigationAddress"`
				NameTitle         string `json:"nameTitle"`
				VlFormatImage     string `json:"vlFormatImage"`
				ContentNum        int    `json:"contentNum,omitempty"`
				Classification    int    `json:"classification"`
				RkValue           int    `json:"rkValue,omitempty"`
				ContentStatus     int    `json:"contentStatus"`
			} `json:"rkContent,omitempty"`
			RkIdentifier         int    `json:"rkIdentifier,omitempty"`
			TypeOfRecommendation string `json:"typeOfRecommendation,omitempty"`
		} `json:"entranceSectionData"`
		BlockGroupSize         int    `json:"blockGroupSize,omitempty"`
		DisplayedItems         string `json:"displayedItems"`
		IfCensorAdultMaterials int    `json:"ifCensorAdultMaterials"`
		ForwardLink            string `json:"forwardLink"`
		EntranceSectionType    string `json:"entranceSectionType"`
		CoverAppearanceStyle   int    `json:"coverAppearanceStyle,omitempty"`
		PlayListId             int    `json:"playListId,omitempty"`
		AutoRecommendSeeMark   int    `json:"autoRecommendSeeMark,omitempty"`
	} `json:"dashboardSearchBlockInformation"`
}
