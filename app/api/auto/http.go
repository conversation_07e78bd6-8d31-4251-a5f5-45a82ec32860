package auto

import (
	"encoding/json"
	"io"
	"math/rand"
	"net/http"
	"strconv"
	"time"
)

var (
	DOMAIN_LIST = []string{
		"https://ll-code-dvlulryifw.ap-southeast-3.fcapp.run",
		"https://ll-code-dvlvlryifw.ap-southeast-3.fcapp.run",
		"https://ll-code-nfzwcbfgwa.ap-southeast-3.fcapp.run",
		"https://ll-code-nfzxcbfgwa.ap-southeast-3.fcapp.run",
		"https://ll-code-nfzucbfgwa.ap-southeast-3.fcapp.run",
		"https://ll-api-wvxpiodjgt.ap-southeast-5.fcapp.run",
		"https://ll-test-uelolrynfg.ap-southeast-5.fcapp.run",
		"https://ll-test-uelnlrynfg.ap-southeast-5.fcapp.run",
		"https://ll-test-uelqlrynfg.ap-southeast-5.fcapp.run",
	}
)

const (
	CHANNEL_LIST   = "/dashboard/navList"
	CHANNEL_DETAIL = "/dashboard/getContent"
	SHOW_DETAIL    = "/cms/cinema/film/get"
	SHOW_PLAY      = "/cms/media/stream"
)

func getRandomDomain() string {
	// Create a new random source with the current time as seed
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	randomIndex := r.Intn(len(DOMAIN_LIST))

	return DOMAIN_LIST[randomIndex]
}

func getRequest(url string, method string, params map[string]string) ([]byte, error) {
	client := &http.Client{}

	// Create request
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}

	// Add query parameters
	if params != nil {
		q := req.URL.Query()
		for key, value := range params {
			q.Add(key, value)
		}
		req.URL.RawQuery = q.Encode()
	}

	req.Header.Add("Content-Type", "application/json")

	// Send request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	return body, nil
}

func GetChannelList() (*ChannelListResp, error) {
	var (
		body []byte
		err  error
		resp struct {
			Success bool             `json:"success"`
			Data    *ChannelListResp `json:"data"`
		}
	)

	body, err = getRequest(getRandomDomain()+CHANNEL_LIST, "GET", nil)
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	if !resp.Success {
		return nil, err
	}

	return resp.Data, nil
}

func GetChannel(homePageBarId, homePageNo uint64) (*ChannelDetailResp, error) {
	var (
		body []byte
		err  error
		resp struct {
			Success bool               `json:"success"`
			Data    *ChannelDetailResp `json:"data"`
		}
	)

	body, err = getRequest(getRandomDomain()+CHANNEL_DETAIL, "GET", map[string]string{
		"homePageBarId": strconv.FormatUint(homePageBarId, 10),
		"homePageNo":    strconv.FormatUint(homePageNo, 10),
	})
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	if !resp.Success {
		return nil, err
	}

	return resp.Data, nil
}

func GetDetail(workID, workType uint64) (*DetailResp, error) {

	var (
		body []byte
		err  error
		resp struct {
			Success bool        `json:"success"`
			Data    *DetailResp `json:"data"`
		}
		url = getRandomDomain() + SHOW_DETAIL
	)

	body, err = getRequest(url, "GET", map[string]string{
		"cinematicWorkId":   strconv.FormatUint(workID, 10),
		"cinematicWorkType": strconv.FormatUint(workType, 10),
	})
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	if !resp.Success {
		return nil, err
	}

	return resp.Data, nil

}

func GetPlay(fullVersionId, informationId, materialType uint64, playbackQuality string) (*PlayResp, error) {

	var (
		body []byte
		err  error
		resp struct {
			Success bool      `json:"success"`
			Data    *PlayResp `json:"data"`
		}
	)

	body, err = getRequest(getRandomDomain()+SHOW_PLAY, "GET", map[string]string{
		"fullVersionId":   strconv.FormatUint(fullVersionId, 10),
		"informationId":   strconv.FormatUint(informationId, 10),
		"materialType":    strconv.FormatUint(materialType, 10),
		"playbackQuality": playbackQuality,
	})
	if err != nil {
		return nil, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, err
	}
	if !resp.Success {
		return nil, err
	}
	return resp.Data, nil
}
