package appstore

import (
	"net/http"
	"vlab/app/dao/common"
	"vlab/app/service/user/vip"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

func AppStoreNotify(ctx *gin.Context) {
	log.Ctx(ctx).Info("AppStoreNotify Start")
	ctx.Set("channelID", uint64(common.ChannelSkyBoxIos))
	type NotifyReq struct {
		SignedPayload string `json:"signedPayload" binding:"required,omitempty"`
	}

	req := NotifyReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		log.Ctx(ctx).Error("AppStoreNotify invalid request")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}

	if err := vip.GetService().AppStoreNotify(ctx, req.SignedPayload); err != nil {
		log.Ctx(ctx).WithError(err).Error("AppStoreNotify error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("AppStoreNotify End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}

func AppStoreNotifyVi(ctx *gin.Context) {
	log.Ctx(ctx).Info("AppStoreNotifyVi Start")
	ctx.Set("channelID", uint64(common.ChannelViBoxIos))
	type NotifyReq struct {
		SignedPayload string `json:"signedPayload" binding:"required,omitempty"`
	}

	req := NotifyReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		log.Ctx(ctx).Error("AppStoreNotifyVi invalid request")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "invalid request"})
		return
	}

	if err := vip.GetService().AppStoreNotify(ctx, req.SignedPayload); err != nil {
		log.Ctx(ctx).WithError(err).Error("AppStoreNotifyVi error")
		ctx.JSON(http.StatusBadRequest, gin.H{"error": ecode.Cause(err).Message()})
		return
	}

	log.Ctx(ctx).Info("AppStoreNotifyVi End")
	ctx.JSON(http.StatusOK, gin.H{"status": "success"})
}
