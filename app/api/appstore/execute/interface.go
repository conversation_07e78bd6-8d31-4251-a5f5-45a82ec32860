package execute

import (
	"sync"
)

type ProductType string

const (
	ProductTypeMonth   ProductType = "month"
	ProductTypeQuarter ProductType = "quarter"
	ProductTypeYear    ProductType = "year"
)

type Api interface {
}

type Entry struct {
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{}
}
