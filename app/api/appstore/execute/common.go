package execute

import (
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/apple"
)

type AppleEnv string

const (
	EnvSandbox AppleEnv = "Sandbox"
	EnvProd    AppleEnv = "Production"
)

func (e *Entry) IsSandbox(env string) bool {
	return env == string(EnvSandbox)
}

func (e *Entry) IsProduction(env string) bool {
	return env == string(EnvProd)
}

func (e *Entry) GetCurrentEnv(ctx *gin.Context) string {
	env := adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyCurrentEnv)
	if dbs.IsProd(env) {
		return string(EnvProd)
	} else {
		return string(EnvSandbox)
	}
}

// GetAppleClient .
func (e *Entry) GetAppleClient(ctx *gin.Context, env string) (*apple.Client, error) {
	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckAppStoreConfig() {
		return nil, ecode.ChannelConfigInvalidErr
	}
	privateKey, err := config.LoadPrivateKeyByFilename(channelKeyInfo.AppStorePkFilename)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("LoadPrivateKeyByFilename error")
		return nil, err
	}
	client, err := apple.NewClient(channelKeyInfo.AppStoreIssuer,
		channelKeyInfo.AppStoreBundleID,
		channelKeyInfo.AppStoreKeyID,
		string(privateKey),
		e.IsProduction(env),
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("apple.NewClient error")
		return nil, err
	}
	return client, nil
}
