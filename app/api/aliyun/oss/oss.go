package oss

import (
	"fmt"
	"time"
	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/config"
	"vlab/pkg/log"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/sts"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss/credentials"
	"github.com/gin-gonic/gin"
)

// GetOssToken .
func (e *Entry) GetOssToken(ctx *gin.Context, accountID uint64, bt common.BucketType) (*aliyunDto.AliyunOssTokenResp, error) {
	var (
		bucket      = common.GetBucket(bt)
		client, err = sts.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return nil, err
	}
	// 创建AssumeRole请求
	request := sts.CreateAssumeRoleRequest()
	request.Scheme = "https"
	request.RoleArn = config.AliyunCfg.RoleArn
	request.RoleSessionName = fmt.Sprintf("%v-Account-%v", bucket, accountID)
	request.DurationSeconds = "3600" // 临时凭证有效时间，单位为秒（最多3600秒）

	// 发送请求并获取响应
	resp, err := client.AssumeRole(request)
	if err != nil {
		return nil, err
	}
	return &aliyunDto.AliyunOssTokenResp{
		Bucket:          bucket,
		RegionID:        config.AliyunCfg.OssRegionID,
		Token:           resp.Credentials.SecurityToken,
		AccessKeyId:     resp.Credentials.AccessKeyId,
		AccessKeySecret: resp.Credentials.AccessKeySecret,
	}, nil
}

// GenVideoSignedUrl .
func (e *Entry) GenVideoSignedUrl(ctx *gin.Context, bt common.BucketType, objKey string) (*aliyunDto.GenVideoSignedUrlResp, error) {
	var (
		bucket = common.GetBucket(bt)
		urlTtl = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyVideoSignUrlTtl)
		cfg    = oss.LoadDefaultConfig().
			WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret, "")).
			WithRegion(config.AliyunCfg.RegionID)
		client = oss.NewClient(cfg)
	)

	result, err := client.Presign(ctx.Request.Context(),
		&oss.GetObjectRequest{
			Bucket:  oss.Ptr(bucket),
			Key:     oss.Ptr(objKey),
			Process: oss.Ptr("hls/sign"),
		},
		oss.PresignExpires(time.Duration(urlTtl)*time.Minute),
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("GenVideoSignedUrl err")
		return nil, err
	}

	return &aliyunDto.GenVideoSignedUrlResp{
		SignedUrl:  result.URL,
		Expiration: result.Expiration.Format(dbs.TimeDateFormatFull),
	}, nil
}

// GenVideoPresignUrl .
func (e *Entry) GenVideoPresignUrl(ctx *gin.Context, bt common.BucketType, url string) (*aliyunDto.GenVideoSignedUrlResp, error) {
	var (
		bucket = common.GetBucket(bt)
		urlTtl = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyVideoSignUrlTtl)
		cfg    = oss.LoadDefaultConfig().
			WithCredentialsProvider(credentials.NewStaticCredentialsProvider(config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret, "")).
			WithRegion(config.AliyunCfg.RegionID)
		client = oss.NewClient(cfg)
	)

	result, err := client.Presign(ctx.Request.Context(),
		&oss.GetObjectRequest{
			Bucket: oss.Ptr(bucket),
			Key:    oss.Ptr(common.GenVideoKeyByUrl(url)),
			// Process: oss.Ptr("hls/sign"),
		},
		oss.PresignExpires(time.Duration(urlTtl)*time.Minute),
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("GenVideoSignedUrl err")
		return nil, err
	}

	return &aliyunDto.GenVideoSignedUrlResp{
		SignedUrl:  result.URL,
		Expiration: result.Expiration.Format(dbs.TimeDateFormatFull),
	}, nil
}
