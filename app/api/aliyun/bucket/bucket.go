package bucket

import (
	"github.com/aliyun/alibabacloud-oss-go-sdk-v2/oss"
)

// CreateBucket .
/*
err := createBucket("video-bucket-example", oss.StorageIA, "oss-cn-hangzhou", false) // 视频 Bucket
err = createBucket("image-bucket-example", oss.StorageStandard, "oss-cn-hangzhou", true) // 图片 Bucket
*/
func (e *Entry) CreateBucket(bucketName string, storageType oss.StorageClassType, region string, public bool) error {
	// 创建 OSS 客户端
	// client, err := oss.New(region, "<YourAccessKeyID>", "<YourAccessKeySecret>")
	// if err != nil {
	// 	return fmt.Errorf("failed to create OSS client: %v", err)
	// }
	// client := oss.NewClient(&oss.Config{
	// 	Region: &config.AliyunCfg.RegionID,
	// })

	// // 创建 Bucket
	// options := []oss.Option{
	// 	oss.StorageClass(storageType),
	// }

	// if public {
	// 	options = append(options, oss.ACL(oss.ACLPublicRead))
	// } else {
	// 	options = append(options, oss.ACL(oss.ACLPrivate))
	// }

	// err = client.CreateBucket(bucketName, options...)
	// if err != nil {
	// 	return fmt.Errorf("failed to create bucket: %v", err)
	// }

	// fmt.Println("Bucket created successfully:", bucketName)
	return nil
}
