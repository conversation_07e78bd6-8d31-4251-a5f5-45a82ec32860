package aliyun

import (
	"vlab/app/api/aliyun/vod"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// VodUploadInfo .
func VodUploadInfo(ctx *gin.Context) {
	req := aliyunDto.VodUploadInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().GetUploadInfo(ctx, req)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// UploadMediaByUrl .
func UploadMediaByUrl(ctx *gin.Context) {
	req := aliyunDto.UploadMediaByUrlReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().UploadMediaByUrl(ctx, req.UrlList)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GetURLUploadInfos .
func GetURLUploadInfos(ctx *gin.Context) {
	req := aliyunDto.GetURLUploadInfosReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().GetURLUploadInfos(ctx, req.JobIds)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// GetURLUploadInfos .
func GetMezzanineInfo(ctx *gin.Context) {
	req := aliyunDto.GetMezzanineInfoReq{}
	if err := ctx.ShouldBind(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ret, err := vod.GetApi().GetMezzanineInfo(ctx, req.Vid)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
