package common

type BucketType string        // bucket 类型
type Resolution uint32        // 分辨率
type TranscodingFormat uint32 // 转码格式
type TranscodingTemp string   // 转码模版
type PipelineId string        // 管道id
type VodDefinition string

const (
	BucketTypeImage    BucketType = "image"     // 图片
	BucketTypeSubtitle BucketType = "subtitle"  // 字幕
	BucketTypeVideo    BucketType = "video"     // 原视频
	BucketTypeVideoMps BucketType = "video_mps" // 转码视频

	Resolution_320  Resolution = 1 // 极速320
	Resolution_640  Resolution = 2 // 流畅640
	Resolution_848  Resolution = 3 // 标清848
	Resolution_1280 Resolution = 4 // 高清1280 - 720p - hd
	Resolution_1920 Resolution = 5 // 超清1920 - 1080p - fhd
	Resolution_2k   Resolution = 6 // 2k
	Resolution_4k   Resolution = 7 // 4k

	TranscodingFormatM3u8 TranscodingFormat = 1 // m3u8
	TranscodingFormatMp4  TranscodingFormat = 2 // mp4

	M3u8_320  TranscodingTemp = "S00000001-100050" // 极速320-es
	M3u8_640  TranscodingTemp = "S00000001-100010" // 流畅640-ld
	M3u8_848  TranscodingTemp = "S00000001-100020" // 标清848 - sd
	M3u8_1280 TranscodingTemp = "S00000001-100030" // 高清1280 - 720p - hd
	M3u8_1920 TranscodingTemp = "S00000001-100040" // 超清1920 - 1080p - fhd
	M3u8_2k   TranscodingTemp = "S00000001-100060" // 2k - 1440p - wqhd(宽准高清晰度)
	M3u8_4k   TranscodingTemp = "S00000001-100070" // 4k - 2160p - uhd

	Mp4_320  TranscodingTemp = "S00000001-200050" // 极速320
	Mp4_640  TranscodingTemp = "S00000001-200010" // 流畅640
	Mp4_848  TranscodingTemp = "S00000001-200020" // 标清848
	Mp4_1280 TranscodingTemp = "S00000001-200030" // 高清1280
	Mp4_1920 TranscodingTemp = "S00000001-200040" // 超清1920
	Mp4_2k   TranscodingTemp = "S00000001-200060" // 2k
	Mp4_4k   TranscodingTemp = "S00000001-200070" // 4k

	Vod_M3u8_640  TranscodingTemp = "S00000001-100010" // 流畅640-ld
	Vod_M3u8_848  TranscodingTemp = "S00000001-100020" // 标清848 - sd
	Vod_M3u8_1280 TranscodingTemp = "S00000001-100030" // 高清1280 - 720p - hd

	VodDefinitionOD VodDefinition = "OD" // 原画
	VodDefinitionFD VodDefinition = "FD" // 流畅
	VodDefinitionLD VodDefinition = "LD" // 标清
	VodDefinitionSD VodDefinition = "SD" // 高清
	VodDefinitionHD VodDefinition = "HD" // 超清
	VodDefinition2K VodDefinition = "2K" // 2K
	VodDefinition4K VodDefinition = "4K" // 4K

	BytesVodDefinitionOD   VodDefinition = "od"    // 原画
	BytesVodDefinition360  VodDefinition = "360p"  // 极速
	BytesVodDefinition480  VodDefinition = "480p"  // 流畅
	BytesVodDefinition540  VodDefinition = "540p"  // 标清
	BytesVodDefinition720  VodDefinition = "720p"  // 高清
	BytesVodDefinition1080 VodDefinition = "1080p" // 超清
	BytesVodDefinition2K   VodDefinition = "2k"    // 2K
	BytesVodDefinition4K   VodDefinition = "4k"    // 4K
)

var (
	ResolutionList = []Resolution{
		Resolution_640, Resolution_848, Resolution_1280,
	}
	TranscodingTempMap = map[TranscodingFormat]map[Resolution]TranscodingTemp{
		TranscodingFormatM3u8: {
			Resolution_320:  M3u8_320,
			Resolution_640:  M3u8_640,
			Resolution_848:  M3u8_848,
			Resolution_1280: M3u8_1280,
			Resolution_1920: M3u8_1920,
			Resolution_2k:   M3u8_2k,
			Resolution_4k:   M3u8_4k,
		},
		TranscodingFormatMp4: {
			Resolution_320:  Mp4_320,
			Resolution_640:  Mp4_640,
			Resolution_848:  Mp4_848,
			Resolution_1280: Mp4_1280,
			Resolution_1920: Mp4_1920,
			Resolution_2k:   Mp4_2k,
			Resolution_4k:   Mp4_4k,
		},
	}
	TranscodingFormatNameMap = map[TranscodingFormat]string{
		TranscodingFormatM3u8: "m3u8",
		TranscodingFormatMp4:  "mp4",
	}
	TranscodingTempNameMap = map[TranscodingTemp]string{
		M3u8_320:  "m3u8-es",
		M3u8_640:  "m3u8-ld",
		M3u8_848:  "m3u8-sd",
		M3u8_1280: "m3u8-hd",
		M3u8_1920: "m3u8-fhd",
		M3u8_2k:   "m3u8-wqhd",
		M3u8_4k:   "m3u8-uhd",
		Mp4_320:   "mp4-es",
		Mp4_640:   "mp4-ld",
		Mp4_848:   "mp4-sd",
		Mp4_1280:  "mp4-hd",
		Mp4_1920:  "mp4-fhd",
		Mp4_2k:    "mp4-wqhd",
		Mp4_4k:    "mp4-uhd",
	}
)
