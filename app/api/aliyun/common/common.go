package common

import (
	"fmt"
	"path/filepath"
	"strings"

	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

func GetBucket(bt BucketType) string {
	switch bt {
	case BucketTypeVideo:
		return config.AliyunCfg.BucketVideo
	case BucketTypeVideoMps:
		return config.AliyunCfg.BucketVideoMps
	default:
		return config.AliyunCfg.BucketImage
	}
}

func GetBytePlusBucket(bt BucketType) string {
	switch bt {
	case BucketTypeVideo:
		return config.BytePlusCfg.BucketVideo
	case BucketTypeVideoMps:
		return config.BytePlusCfg.BucketVideoMps
	case BucketTypeImage:
		return config.BytePlusCfg.BucketImage
	case BucketTypeSubtitle:
		return config.BytePlusCfg.BucketSubtitle
	default:
		return config.BytePlusCfg.BucketImage
	}
}

func GetOssEndpoint(ossRegionId string, internal bool) string {
	if internal {
		return fmt.Sprintf("%s-internal.aliyuncs.com", ossRegionId) // 内网
	} else {
		return fmt.Sprintf("%s.aliyuncs.com", ossRegionId) // 外网
	}
}

// GetTranscodingTemp .
func GetTranscodingTemp(ctx *gin.Context, format TranscodingFormat, rel Resolution) (TranscodingTemp, error) {
	if resolutionMap, ok := TranscodingTempMap[format]; ok {
		if temp, ok := resolutionMap[rel]; ok {
			return temp, nil
		}
	}
	return "", ecode.VideoMpsTranscodingTempNotExist
}

// GetMpsVideoKey .
func GetMpsVideoKey(videoKey string, tempID TranscodingTemp) string {
	var (
		nowDate   = carbon.Now().ToDateString()
		fileName  = filepath.Base(videoKey)
		fileExt   = filepath.Ext(videoKey)
		videoName = strings.TrimSuffix(fileName, fileExt)
		tempName  = "default-common"
	)
	if videoName == "" {
		videoName = helper.RandCode(16)
	}
	if val, ok := TranscodingTempNameMap[tempID]; ok {
		tempName = val
	}

	return fmt.Sprintf("%s/%s/%s", nowDate, tempName, videoName)
}

// GenVideoKeyByUrl .
func GenVideoKeyByUrl(url string) string {
	var (
		nowDate = carbon.Now().ToDateString()
		// fileName  = filepath.Base(url)
		fileExt   = filepath.Ext(url)
		videoName = helper.RandCode(16)
		tempName  = "od"
	)

	return fmt.Sprintf("%s/%s/%s.%s", nowDate, tempName, videoName, fileExt)
}
