package aliyun

import (
	"vlab/app/api/aliyun/common"
	"vlab/app/api/aliyun/oss"
	"vlab/app/common/dbs"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

// AliyunOssToken .
func AliyunOssToken(ctx *gin.Context) {
	req := aliyunDto.AliyunOssTokenReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	ret, err := oss.GetApi().GetOssToken(ctx, ctxAccount.AccountID, common.BucketType(req.BucketType))
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}

// AliyunOssGenSignedUrl .
func AliyunOssGenSignedUrl(ctx *gin.Context) {
	req := aliyunDto.AliyunOssGenVideoSignedUrlReq{}
	if err := ctx.ShouldBindQuery(&req); err != nil {
		helper.AppResp(ctx, ecode.ParamErr.Code(), ecode.ParamErr.Message())
		return
	}

	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if ctxAccount.AccountID != dbs.RootUID {
		helper.AppResp(ctx, ecode.AccountNeedRootAuthErr.Code(), ecode.AccountNeedRootAuthErr.Message())
		return
	}

	ret, err := oss.GetApi().GenVideoSignedUrl(ctx, common.BucketType(req.BucketType), req.ObjKey)
	if err != nil {
		helper.AppResp(ctx, ecode.Cause(err).Code(), ecode.Cause(err).Message())
		return
	}
	helper.AppWithDataResp(ctx, ecode.OK.Code(), ecode.OK.Message(), ret)
}
