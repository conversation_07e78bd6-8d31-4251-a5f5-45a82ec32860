package mps

import (
	"fmt"
	"strings"
	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	videoMpsDao "vlab/app/dao/content_video_mps"
	"vlab/config"
	"vlab/pkg/log"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/mts"
	"github.com/gin-gonic/gin"
)

// SubmitTranscodeJob .
func (e *Entry) SubmitTranscodeJob(ctx *gin.Context, videoMps *videoMpsDao.Model, resolutionList []common.Resolution) (videoMpsDao.ModelList, error) {
	var (
		inBucket        = config.AliyunCfg.BucketVideo
		outBucket       = config.AliyunCfg.BucketVideoMps
		mpsVideoKey     string
		transcodeFromat = common.TranscodingFormat(adminConfig.RedisGetConfig[uint32](ctx, adminConfig.KeyVideoTranscodeFormat))
		client, err     = mts.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
		mpsList         = videoMpsDao.ModelList{}
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("mts.NewClientWithAccessKey err")
		return nil, err
	}

	for _, resolution := range resolutionList {
		tempID, err := common.GetTranscodingTemp(ctx, transcodeFromat, resolution)
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("common.GetTranscodingTemp err")
			return nil, err
		}
		mpsVideoFn, mpsVideoExt := common.GetMpsVideoKey(videoMps.VideoKey, tempID), common.TranscodingFormatNameMap[transcodeFromat]
		switch transcodeFromat {
		case common.TranscodingFormatM3u8:
			mpsVideoKey = mpsVideoFn
		default:
			mpsVideoKey = fmt.Sprintf("%s.%s", mpsVideoFn, mpsVideoExt)
		}

		request := mts.CreateSubmitJobsRequest()
		request.Scheme = "https"
		request.Outputs = fmt.Sprintf(`[{"OutputObject":"%v","TemplateId":"%v"}]`, mpsVideoKey, tempID)
		request.OutputLocation = config.AliyunCfg.OssRegionID
		request.PipelineId = config.AliyunCfg.MpsPipelineId
		request.Input = fmt.Sprintf(`{"Bucket":"%s","Location":"%s","Object":"%s"}`, inBucket, config.AliyunCfg.OssRegionID, videoMps.VideoKey)
		request.OutputBucket = outBucket

		resp, err := client.SubmitJobs(request)
		log.Ctx(ctx).WithField("request", request).WithField("resp", resp).Info("client.SubmitJobs ret")
		if err != nil {
			log.Ctx(ctx).WithError(err).Warn("client.SubmitJobs err")
			return nil, err
		}

		item := &videoMpsDao.Model{
			ID:              videoMps.ID,
			VideoID:         videoMps.VideoID,
			Resolution:      resolution,
			TranscodeFormat: transcodeFromat,
			Status:          videoMpsDao.MpsStatusIng,
			VideoKey:        videoMps.VideoKey,
			MpsVideoKey:     fmt.Sprintf("%s.%s", mpsVideoFn, mpsVideoExt),
			JobID:           resp.JobResultList.JobResult[0].Job.JobId,
			TempID:          string(tempID),
			Retry:           videoMps.Retry + 1,
			OperateType:     uint32(videoMpsDao.OtOssUpload),
		}
		mpsList = append(mpsList, item)
	}
	if len(mpsList) > dbs.False {
		if err = e.VideoMpsRepo.BatchCreateOrUpdate(ctx, mpsList); err != nil {
			return nil, err
		}
	}

	return mpsList, nil
}

// QueryTranscodeJobStatus 查询任务状态
func (e *Entry) QueryTranscodeJobStatus(ctx *gin.Context, jobIds []string) (map[string]videoMpsDao.MpsStatus, error) {
	var (
		client, err = mts.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
		jobRet      = map[string]videoMpsDao.MpsStatus{}
	)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("mts.NewClientWithAccessKey err")
		return jobRet, err
	}

	request := mts.CreateQueryJobListRequest()
	request.Scheme = "https"
	request.JobIds = strings.Join(jobIds, ",")

	resp, err := client.QueryJobList(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("client.QueryJobList err")
		return jobRet, fmt.Errorf("failed to query job status: %v", err)
	}
	log.Ctx(ctx).WithField("resp", resp).Info("client.QueryJobList rest")

	for _, jobInfo := range resp.JobList.Job {
		if ss, ok := jobStateMapStatus[jobInfo.State]; ok && ss != videoMpsDao.MpsStatusIng {
			jobRet[jobInfo.JobId] = ss
		}
	}

	return jobRet, nil
}
