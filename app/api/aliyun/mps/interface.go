package mps

import (
	"sync"
	adminAccount "vlab/app/dao/admin_account"
	adminArea "vlab/app/dao/admin_area"
	adminMenu "vlab/app/dao/admin_menu"
	menuBackup "vlab/app/dao/admin_menu_backup"
	adminRole "vlab/app/dao/admin_role"
	videoMpsDao "vlab/app/dao/content_video_mps"
)

type BatchNum uint32

const (
	QueryTransJobNum BatchNum = 10 // 阿里云接口一次最多支持10个
)

var jobStateMapStatus = map[string]videoMpsDao.MpsStatus{
	"Submitted":          videoMpsDao.MpsStatusIng,
	"Transcoding":        videoMpsDao.MpsStatusIng,
	"TranscodeSuccess":   videoMpsDao.MpsStatusSuccess,
	"TranscodeFail":      videoMpsDao.MpsStatusFailed,
	"TranscodeCancelled": videoMpsDao.MpsStatusFailed,
}

type Api interface {
}

type Entry struct {
	VideoMpsRepo videoMpsDao.Repo

	MenuRepo       adminMenu.Repo
	AreaRepo       adminArea.Repo
	AccountRepo    adminAccount.Repo
	RoleRepo       adminRole.Repo
	MenuBackupRepo menuBackup.Repo
}

// TODO 替换
var (
	// defaultEntry         Api
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

func GetApi() *Entry {
	// func GetApi() Api {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		VideoMpsRepo: videoMpsDao.GetRepo(),
		// RoleRepo:       adminRole.GetRepo(),
		// AccountRepo:    adminAccount.GetRepo(),

		// MenuRepo:       adminMenu.GetRepo(),
		// MenuBackupRepo: menuBackup.GetRepo(),
		// AreaRepo:       adminArea.GetRepo(),
	}
}
