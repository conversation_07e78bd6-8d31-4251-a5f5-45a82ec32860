package vod

import (
	"net/url"
	"strings"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/config"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/vod"
	"github.com/gin-gonic/gin"
)

// GetUploadInfo .
func (e *Entry) GetUploadInfo(ctx *gin.Context, req aliyunDto.VodUploadInfoReq) (*aliyunDto.VodUploadInfoResp, error) {
	var (
		client, err = vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return nil, err
	}

	// 创建request
	request := vod.CreateCreateUploadVideoRequest()
	request.Scheme = "https"
	request.FileName = req.FileName
	request.Title = req.Title
	request.TemplateGroupId = adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyVodTranscodeTemplateGroupId)

	resp, err := client.CreateUploadVideo(request)
	if err != nil {
		return nil, err
	}

	return &aliyunDto.VodUploadInfoResp{
		RequestId:     resp.RequestId,
		UploadAddress: resp.UploadAddress,
		VideoId:       resp.VideoId,
		UploadAuth:    resp.UploadAuth,
		RegionID:      config.AliyunCfg.RegionID,
		RamUID:        config.AliyunCfg.RamUID,
	}, nil
}

// UploadMediaByUrl .
func (e *Entry) UploadMediaByUrl(ctx *gin.Context, urlList []string) (*vod.UploadMediaByURLResponse, error) {
	var (
		uploadUrls = []string{}
	)
	for _, val := range urlList {
		if strings.TrimSpace(val) == "" {
			continue
		}
		uploadUrls = append(uploadUrls, url.QueryEscape(strings.TrimSpace(val)))
	}
	if len(uploadUrls) == dbs.False || len(uploadUrls) > 20 {
		return nil, ecode.VodUploadMediaByUrlsErr
	}

	client, err := vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	// 创建request
	request := vod.CreateUploadMediaByURLRequest()
	request.Scheme = "https"
	request.UploadURLs = strings.Join(urlList, ",")
	request.TemplateGroupId = adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyVodPullTranscodeTemplateGroupId)

	resp, err := client.UploadMediaByURL(request)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UploadMediaByUrl, err")
		return nil, err
	}

	return resp, nil
}

// GetURLUploadInfos .
func (e *Entry) GetURLUploadInfos(ctx *gin.Context, jobIds []string) (*vod.GetURLUploadInfosResponse, error) {
	client, err := vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	// 创建request
	request := vod.CreateGetURLUploadInfosRequest()
	request.Scheme = "https"
	request.JobIds = strings.Join(jobIds, ",")

	resp, err := client.GetURLUploadInfos(request)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
