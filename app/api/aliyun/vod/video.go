package vod

import (
	"strings"
	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"
	aliyunDto "vlab/app/dto/api/aliyun"
	"vlab/config"

	"github.com/aliyun/alibaba-cloud-sdk-go/services/vod"
	"github.com/gin-gonic/gin"
)

// GetVideoPlayInfo .
func (e *Entry) GetVideoPlayInfo(ctx *gin.Context, vodID string, definition common.VodDefinition) (vod.VideoBase, vod.PlayInfo, error) {
	var (
		ret         = vod.PlayInfo{}
		vodBase     = vod.VideoBase{}
		client, err = vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return vodBase, ret, err
	}

	// 创建request
	request := vod.CreateGetPlayInfoRequest()
	request.Scheme = "https"
	request.VideoId = vodID
	request.Definition = string(definition)

	resp, err := client.GetPlayInfo(request)
	if err != nil {
		return vodBase, ret, err
	}
	vodBase = resp.VideoBase

	if len(resp.PlayInfoList.PlayInfo) > dbs.False {
		ret = resp.PlayInfoList.PlayInfo[0]
	}

	return vodBase, ret, nil
}

// GetVideoInfo .
func (e *Entry) GetVideoInfo(ctx *gin.Context, vodID string, definition common.VodDefinition) (vod.VideoBase, vod.PlayInfo, error) {
	var (
		ret     = vod.PlayInfo{}
		vodBase = vod.VideoBase{}
		// client, err = vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	// if err != nil {
	// 	return vodBase, ret, err
	// }

	// // 创建request
	// request := vod.CreateGetPlayInfoRequest()
	// request.Scheme = "https"
	// request.VideoId = vodID
	// request.Definition = string(definition)

	// resp, err := client.GetPlayInfo(request)
	// if err != nil {
	// 	return vodBase, ret, err
	// }
	// vodBase = resp.VideoBase

	// if len(resp.PlayInfoList.PlayInfo) > dbs.False {
	// 	ret = resp.PlayInfoList.PlayInfo[0]
	// }

	return vodBase, ret, nil
}

// GetVideoInfos .
func (e *Entry) GetVideoInfos(ctx *gin.Context, vodIds []string) (map[string]vod.Video, error) {
	client, err := vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	if err != nil {
		return nil, err
	}

	// 创建request
	request := vod.CreateGetVideoInfosRequest()
	request.Scheme = "https"
	request.VideoIds = strings.Join(vodIds, ",")

	resp, err := client.GetVideoInfos(request)
	if err != nil {
		return nil, err
	}

	vodMap := map[string]vod.Video{}
	for _, vod := range resp.VideoList {
		if _, ok := vodMap[vod.VideoId]; !ok {
			vodMap[vod.VideoId] = vod
		}
	}

	return vodMap, nil
}

// GetMezzanineInfo  获取源文件信息
func (e *Entry) GetMezzanineInfo(ctx *gin.Context, vodID string) (vod.MezzanineInGetMezzanineInfo, error) {
	var (
		ret         = vod.MezzanineInGetMezzanineInfo{}
		client, err = vod.NewClientWithAccessKey(config.AliyunCfg.RegionID, config.AliyunCfg.AccessKeyID, config.AliyunCfg.AccessKeySecret)
	)
	if err != nil {
		return ret, err
	}

	// 创建request
	request := vod.CreateGetMezzanineInfoRequest()
	request.Scheme = "https"
	request.VideoId = vodID

	resp, err := client.GetMezzanineInfo(request)
	if err != nil {
		return ret, err
	}
	ret = resp.Mezzanine

	return ret, nil
}

// GetVideoList .
func (e *Entry) GetVideoList(ctx *gin.Context, accountID uint64, req aliyunDto.VodUploadInfoReq) (*aliyunDto.VodUploadInfoResp, error) {
	return nil, nil
}

// DeleteVideo .
func (e *Entry) DeleteVideo(ctx *gin.Context, accountID uint64, req aliyunDto.VodUploadInfoReq) (*aliyunDto.VodUploadInfoResp, error) {
	return nil, nil
}
