package client_config

import (
	"time"

	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	configDao "vlab/app/dao/config_client"
	channelDao "vlab/app/dao/resource_channel"
	configDto "vlab/app/dto/client_config"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
	redisPkg "vlab/pkg/redis"
)

// AdminClientConfig 管理后台 - 获取页面配置列表
func (e *Entry) AdminClientConfig(ctx *gin.Context, req *configDto.AdminClientConfigReq) (*configDto.AdminClientConfigResp, error) {
	resp := &configDto.AdminClientConfigResp{
		List:  make([]configDto.AdminClientConfigItem, 0),
		Count: 0,
		IsEnd: true,
	}

	// 构建查询条件
	filter := &configDao.Filter{
		ConfigType: 2,
	}

	// 添加筛选条件
	if req.Key != "" {
		filter.ConfigKey = configDao.ConfigKey(req.Key)
	}
	if req.Status != 0 {
		filter.Status = req.Status
	}
	// 支持按渠道筛选，如果不指定则查询所有
	if req.ChannelID != 0 {
		filter.ChannelID = req.ChannelID
	}

	// 分页查询
	total, list, err := e.ConfigRepo.DataPageList(ctx, filter, int32(req.Page), int32(req.Limit))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminClientConfig DataPageList error")
		return resp, ecode.SystemErr
	}

	resp.Count = total
	if total == 0 {
		return resp, nil
	}

	var channelMap map[uint64]*channelDao.Model
	if channelIDs := list.GetChannelList(); len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
			IDS: channelIDs,
		})
		if err == nil && len(channelList) > 0 {
			channelMap = channelList.GetIDMap()
		}
	}

	// 转换数据格式
	for _, item := range list {

		if item == nil {
			log.Ctx(ctx).Warn("Empty config item found in AdminClientConfig")
			continue
		}

		channel := showDto.ChannelBase{}
		if i, ok := channelMap[item.ChannelID]; ok {
			channel = showDto.ChannelBase{
				ID:   i.ID,
				Name: i.Name,
			}
		}

		resp.List = append(resp.List, configDto.AdminClientConfigItem{
			ID:      uint32(item.ID),
			Key:     item.ConfigKey,
			Data:    item.ConfigValue,
			Status:  item.Status,
			Channel: channel,
		})
	}

	// 判断是否结束分页
	resp.IsEnd = int64(req.Page*req.Limit) >= total

	return resp, nil
}

// AdminUpdateClientConfig 管理后台 - 更新页面配置
func (e *Entry) AdminUpdateClientConfig(ctx *gin.Context, req *configDto.AdminUpdateClientConfigReq) (*configDto.AdminUpdateClientConfigResp, error) {
	// 基本参数验证
	if req.Key == "" {
		log.Ctx(ctx).Warn("Empty config key")
		return nil, ecode.ParamErr
	}

	// 构建数据模型
	model := &configDao.Model{
		ConfigType:  2, // 页面配置类型
		ConfigKey:   req.Key,
		ConfigValue: req.Data,
		Status:      req.Status,
		ChannelID:   req.ChannelID, // 支持渠道级别配置
		UpdatedAt:   time.Now(),
	}

	if model.ChannelID == 0 {
		// 如果没有指定渠道ID，则默认为全局配置
		model.ChannelID = 9999 // 全局配置的特殊标识
	}

	// 如果Status为0，默认设置为启用
	if model.Status == 0 {
		model.Status = uint32(dbs.StatusEnable)
	}

	// 创建或更新配置
	if err := e.ConfigRepo.CreateOrUpdate(ctx, model); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminUpdateClientConfig CreateOrUpdate error")
		return nil, ecode.SystemErr
	}

	// 清除相关缓存
	if err := e.clearClientConfigCache(ctx, req.Key, req.ChannelID); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Clear page config cache error")
	}

	return &configDto.AdminUpdateClientConfigResp{
		AdminClientConfigItem: configDto.AdminClientConfigItem{
			ID:     uint32(model.ID),
			Key:    model.ConfigKey,
			Data:   model.ConfigValue,
			Status: model.Status,
			//ChannelID: model.ChannelID,
		},
	}, nil
}

// AdminDeleteClientConfig 管理后台 - 删除页面配置
func (e *Entry) AdminDeleteClientConfig(ctx *gin.Context, req *configDto.AdminDeleteClientConfigReq) (*configDto.AdminDeleteClientConfigResp, error) {
	// 参数验证
	if req.ID == 0 {
		log.Ctx(ctx).Warn("Invalid config ID")
		return nil, ecode.ParamErr
	}

	// 先查询配置是否存在
	config, err := e.ConfigRepo.FetchByID(ctx, uint64(req.ID))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminDeleteClientConfig FetchByID error")
		return nil, ecode.SystemErr
	}

	if config == nil || config.ID == 0 {
		log.Ctx(ctx).Warn("Config not found")
		return nil, ecode.NotFoundErr
	}

	// 执行软删除
	updateMap := map[string]interface{}{
		string(dbs.SoftDelField): dbs.True,
	}

	if err := e.ConfigRepo.UpdateMapByID(ctx, uint64(req.ID), updateMap); err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminDeleteClientConfig UpdateMapByID error")
		return nil, ecode.SystemErr
	}

	// 清除相关缓存
	if err := e.clearClientConfigCache(ctx, config.ConfigKey, config.ChannelID); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Clear config cache error after delete")
	}

	return &configDto.AdminDeleteClientConfigResp{}, nil
}

// clearClientConfigCache 清除页面配置相关缓存（支持渠道级别缓存）
func (e *Entry) clearClientConfigCache(ctx *gin.Context, key string, channelID uint64) error {
	// 清除页面配置列表缓存
	if err := e.ConfigRepo.RedisClearPageConfigList(ctx); err != nil {
		return err
	}

	// 清除单个配置缓存
	if channelID > 0 {
		// 清除渠道特定配置缓存
		channelCacheKey := redisPkg.GetClientConfigChannelKey(channelID, key)
		e.RedisCli.Del(ctx.Request.Context(), channelCacheKey)

		// 清除渠道配置列表缓存
		channelListCacheKey := redisPkg.GetClientConfigChannelListKey(channelID)
		e.RedisCli.Del(ctx.Request.Context(), channelListCacheKey)
	} else {
		// 清除全局配置缓存
		globalCacheKey := redisPkg.GetClientConfigKey(key)
		e.RedisCli.Del(ctx.Request.Context(), globalCacheKey)
	}

	// 清除通用配置列表缓存
	if err := e.ConfigRepo.RedisClearConfigList(ctx); err != nil {
		return err
	}

	return nil
}
