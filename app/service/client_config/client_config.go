package client_config

import (
	"strings"

	"github.com/gin-gonic/gin"
	configDao "vlab/app/dao/config_client"
	configDto "vlab/app/dto/client_config"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
)

// ClientConfig 微信小程序端 - 获取页面配置（支持渠道级别配置继承）
func (e *Entry) ClientConfig(ctx *gin.Context, req *configDto.ClientConfigReq) (*configDto.ClientConfigResp, error) {
	resp := &configDto.ClientConfigResp{
		Config: make(map[string]string),
	}

	// 从 context 中获取 channel_id（由中间件从 X-Channel header 解析）
	channelID, err := helper.GetCtxChannelID(ctx)
	if err != nil {
		// 如果获取失败，使用全局配置（channel_id = 0）
		log.Ctx(ctx).WithError(err).Warn("Failed to get channel ID from context, using global config")
		channelID = 0
	}

	if req.Key != "" {
		// 解析逗号分隔的key
		keys := strings.Split(req.Key, ",")
		for _, key := range keys {
			// 去除首尾空格
			key = strings.TrimSpace(key)
			if key == "" {
				continue // 跳过空字符串
			}

			// 使用新的配置继承查询方法
			config, err := e.ConfigRepo.FindConfigWithFallback(ctx, key, channelID)
			if err != nil {
				log.Ctx(ctx).WithError(err).WithField("key", key).WithField("channel_id", channelID).Error("ClientConfig FindConfigWithFallback error")
				return resp, ecode.SystemErr
			}
			if config != nil && config.ConfigValue != "" {
				resp.Config[key] = config.ConfigValue
			}
		}
	} else {
		// 获取所有页面配置（支持渠道配置继承）
		if channelID > 0 {
			// 有渠道ID时，需要合并渠道配置和全局配置
			channelConfigMap, err := e.getChannelConfigMap(ctx, channelID)
			if err != nil {
				log.Ctx(ctx).WithError(err).WithField("channel_id", channelID).Error("ClientConfig getChannelConfigMap error")
				return resp, ecode.SystemErr
			}
			resp.Config = channelConfigMap
		} else {
			// 只获取全局配置
			globalConfigMap, err := e.ConfigRepo.RedisPageConfigMap(ctx)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("ClientConfig RedisPageConfigMap error")
				return resp, ecode.SystemErr
			}
			resp.Config = globalConfigMap
		}
	}

	return resp, nil
}

// getChannelConfigMap 获取渠道配置映射（渠道配置优先，全局配置补充）
func (e *Entry) getChannelConfigMap(ctx *gin.Context, channelID uint64) (map[string]string, error) {
	configMap := make(map[string]string)

	// 先获取全局配置作为基础
	globalFilter := &configDao.Filter{
		ConfigType: 2,    // 页面配置类型
		ChannelID:  9999, // 全局配置
		Status:     1,    // 启用状态
	}

	globalConfigs, err := e.ConfigRepo.FindByFilter(ctx, globalFilter)
	if err != nil {
		return configMap, err
	}

	// 填充全局配置
	for _, config := range globalConfigs {
		if config.ConfigValue != "" {
			configMap[config.ConfigKey] = config.ConfigValue
		}
	}

	// 获取渠道特定配置，覆盖全局配置
	channelFilter := &configDao.Filter{
		ConfigType: 2,         // 页面配置类型
		ChannelID:  channelID, // 渠道配置
		Status:     1,         // 启用状态
	}

	channelConfigs, err := e.ConfigRepo.FindByFilter(ctx, channelFilter)
	if err != nil {
		return configMap, err
	}

	// 渠道配置覆盖全局配置
	for _, config := range channelConfigs {
		if config.ConfigValue != "" {
			configMap[config.ConfigKey] = config.ConfigValue
		}
	}

	return configMap, nil
}
