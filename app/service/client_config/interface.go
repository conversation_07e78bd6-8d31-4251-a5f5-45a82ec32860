package client_config

import (
	"sync"

	"github.com/gin-gonic/gin"
	configDao "vlab/app/dao/config_client"
	channelDao "vlab/app/dao/resource_channel"
	configDto "vlab/app/dto/client_config"
	"vlab/pkg/redis"
)

type Server interface {
	ClientConfig
}

type ClientConfig interface {
	AdminClientConfig(ctx *gin.Context, req *configDto.AdminClientConfigReq) (*configDto.AdminClientConfigResp, error)
	AdminUpdateClientConfig(ctx *gin.Context, req *configDto.AdminUpdateClientConfigReq) (*configDto.AdminUpdateClientConfigResp, error)
	AdminDeleteClientConfig(ctx *gin.Context, req *configDto.AdminDeleteClientConfigReq) (*configDto.AdminDeleteClientConfigResp, error)
	ClientConfig(ctx *gin.Context, req *configDto.ClientConfigReq) (*configDto.ClientConfigResp, error)
}

// TODO替换
type Entry struct {
	RedisCli *redis.RedisClient

	ConfigRepo  configDao.Repo
	ChannelRepo channelDao.Repo
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		RedisCli:    redis.GetRedisClient(),
		ConfigRepo:  configDao.GetRepo(),
		ChannelRepo: channelDao.GetRepo(),
	}
}
