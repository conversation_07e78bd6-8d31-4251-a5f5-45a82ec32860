package resource

import (
	"vlab/app/common/dbs"
	channelDao "vlab/app/dao/resource_channel"
	versionDao "vlab/app/dao/resource_version"
	resourceDto "vlab/app/dto/resource"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

func (e *Entry) GetVersions(ctx *gin.Context, ids []uint64) (versionDao.ModelList, error) {
	if len(ids) == 0 {
		return make(versionDao.ModelList, 0), nil
	}

	return e.VersionRepo.FindByFilter(ctx, &versionDao.Filter{
		IDS: ids,
	})
}

// AdminVersionList .
func (e *Entry) AdminVersionList(ctx *gin.Context, req resourceDto.AdminVersionListReq) (*resourceDto.AdminVersionListResp, error) {
	total, list, err := e.VersionRepo.DataPageList(ctx, &versionDao.Filter{
		Name:      req.Name,
		Status:    req.Status,
		ChannelID: req.ChannelID,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	var (
		retList    = make([]*resourceDto.AdminVersionListItem, 0, len(list))
		channelIDs = list.GetChannelIDs()
		channelMap = make(map[uint64]*channelDao.Model, len(channelIDs))
	)

	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{IDS: channelIDs})
		if err != nil {
			return nil, err
		}
		channelMap = channelList.GetIDMap()
	}

	for _, val := range list {
		item := &resourceDto.AdminVersionListItem{
			ID:      val.ID,
			Name:    val.Name,
			Version: val.Version,
			Status:  val.Status,
		}

		if channel, ok := channelMap[val.ChannelID]; ok {
			item.Channel.ID = channel.ID
			item.Channel.Name = channel.Name
			item.Channel.Status = channel.Status
		}

		retList = append(retList, item)
	}
	return &resourceDto.AdminVersionListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminVersionSet .
func (e *Entry) AdminVersionSet(ctx *gin.Context, req resourceDto.AdminVersionSetReq) (uint64, error) {
	m := &versionDao.Model{
		ID:        req.ID,
		Name:      req.Name,
		ChannelID: req.ChannelID,
		Version:   req.Version,
		Status:    req.Status,
	}
	var (
		eg    errgroup.Group
		model = &versionDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.VersionRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.VersionRepo.CountByFilter(ctx, &versionDao.Filter{
				Version:   req.Version,
				NotID:     req.ID,
				ChannelID: req.ChannelID,
			}); err != nil {
				return
			}
			if num > 0 {
				return ecode.VersionExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.VersionRepo.CountByFilter(ctx, &versionDao.Filter{
				Version:   req.Version,
				ChannelID: req.ChannelID,
			}); err != nil {
				return
			}
			if num > 0 {
				return ecode.VersionExistErr
			}
			return
		})
	}

	// 校验渠道ID
	eg.Go(func() (err error) {
		var channel *channelDao.Model
		channel, err = e.ChannelRepo.FetchByID(ctx, req.ChannelID)
		if err != nil {
			return err
		}

		if channel == nil || channel.ID <= 0 {
			return ecode.ChannelNotExistErr
		}

		return nil
	})

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.VersionRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminVersionAll .
func (e *Entry) AdminVersionAll(ctx *gin.Context, req resourceDto.AdminVersionAllReq) ([]*resourceDto.AdminVersionListItem, error) {
	var (
		list = versionDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.VersionRepo.RedisEnableVersionList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.VersionRepo.RedisVersionList(ctx); err != nil {
			return nil, err
		}
	}

	var (
		retList    = make([]*resourceDto.AdminVersionListItem, 0, len(list))
		channelIDs = list.GetChannelIDs()
		channelMap = make(map[uint64]*channelDao.Model, len(channelIDs))
	)

	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{IDS: channelIDs})
		if err != nil {
			return nil, err
		}
		channelMap = channelList.GetIDMap()
	}
	for _, val := range list {
		if req.ChannelID > 0 && val.ChannelID != req.ChannelID {
			continue
		}
		item := &resourceDto.AdminVersionListItem{
			ID:      val.ID,
			Name:    val.Name,
			Version: val.Version,
			Status:  val.Status,
		}
		if channel, ok := channelMap[val.ChannelID]; ok {
			item.Channel.ID = channel.ID
			item.Channel.Name = channel.Name
			item.Channel.Status = channel.Status
		}
		retList = append(retList, item)
	}
	return retList, nil
}
