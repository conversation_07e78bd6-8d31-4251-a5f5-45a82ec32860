package resource

import (
	"sync"

	"vlab/app/common/dbs"
	channelDao "vlab/app/dao/resource_channel"
	channelKeyDao "vlab/app/dao/resource_channel_key"
	versionDao "vlab/app/dao/resource_version"
	resourceDto "vlab/app/dto/resource"

	"github.com/gin-gonic/gin"
)

type Server interface {
	AdminChannel
	AdminChannelKey
	AdminVersion
	Version
}

type AdminChannel interface {
	AdminChannelList(*gin.Context, resourceDto.AdminChannelListReq) (*resourceDto.AdminChannelListResp, error)
	AdminChannelSet(*gin.Context, resourceDto.AdminChannelSetReq) (uint64, error)
	AdminChannelOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminChannelAll(*gin.Context, resourceDto.AdminChannelAllReq) ([]*resourceDto.AdminChannelListItem, error)

	GetChannels(ctx *gin.Context, ids []uint64) (channelDao.ModelList, error)
}

type AdminChannelKey interface {
	AdminChannelKeyList(*gin.Context, resourceDto.AdminChannelKeyListReq) (*resourceDto.AdminChannelKeyListResp, error)
	AdminChannelKeySet(*gin.Context, resourceDto.AdminChannelKeySetReq) (uint64, error)
	AdminChannelKeyOperate(*gin.Context, uint64, dbs.OperateAction, uint32) error
	AdminChannelKeyAll(*gin.Context, resourceDto.AdminChannelKeyAllReq) ([]*resourceDto.AdminChannelKeyListItem, error)
}

type AdminVersion interface {
	AdminVersionList(*gin.Context, resourceDto.AdminVersionListReq) (*resourceDto.AdminVersionListResp, error)
	AdminVersionSet(*gin.Context, resourceDto.AdminVersionSetReq) (uint64, error)
	AdminVersionAll(*gin.Context, resourceDto.AdminVersionAllReq) ([]*resourceDto.AdminVersionListItem, error)

	GetVersions(ctx *gin.Context, ids []uint64) (versionDao.ModelList, error)
}

type Version interface {
	VersionCheck(*gin.Context, *resourceDto.VersionCheckReq) (*resourceDto.VersionCheckResp, error)

	GetConfig(*gin.Context) (*resourceDto.ConfigResp, error)
	ErrorReport(*gin.Context, resourceDto.ErrorReportReq) error
}

type Entry struct {
	ChannelRepo    channelDao.Repo
	ChannelKeyRepo channelKeyDao.Repo
	VersionRepo    versionDao.Repo
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		ChannelRepo:    channelDao.GetRepo(),
		ChannelKeyRepo: channelKeyDao.GetRepo(),
		VersionRepo:    versionDao.GetRepo(),
	}
}
