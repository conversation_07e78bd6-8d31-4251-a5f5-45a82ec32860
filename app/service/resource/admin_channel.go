package resource

import (
	"vlab/app/common/dbs"
	channelDao "vlab/app/dao/resource_channel"
	versionDao "vlab/app/dao/resource_version"
	resourceDto "vlab/app/dto/resource"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

func (e *Entry) GetChannels(ctx *gin.Context, ids []uint64) (channelDao.ModelList, error) {
	if len(ids) == 0 {
		return make(channelDao.ModelList, 0), nil
	}

	return e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
		IDS:    ids,
		Status: uint32(dbs.StatusEnable),
	})
}

// AdminChannelList .
func (e *Entry) AdminChannelList(ctx *gin.Context, req resourceDto.AdminChannelListReq) (*resourceDto.AdminChannelListResp, error) {
	total, list, err := e.ChannelRepo.DataPageList(ctx, &channelDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	var (
		retList    = make([]*resourceDto.AdminChannelListItem, 0, len(list))
		versionIDs = list.GetVersionIDs()
		versionMap map[uint64]*versionDao.Model
	)
	if len(versionIDs) > 0 {
		versionMap, err = e.VersionRepo.RedisVersionIDMap(ctx)
		if err != nil {
			return nil, err
		}
	}
	for _, val := range list {
		item := &resourceDto.AdminChannelListItem{
			ID:         val.ID,
			Name:       val.Name,
			Status:     val.Status,
			UpdateType: uint32(val.UpdateType),
		}
		if val.NeedUpdate() {
			item.Url = val.Url
			item.Desc = val.Desc
			item.UpdateVersionID = val.UpdateVersionID
			if version, ok := versionMap[val.UpdateVersionID]; ok {
				item.Version = version.Version
			}
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminChannelListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminChannelSet .
func (e *Entry) AdminChannelSet(ctx *gin.Context, req resourceDto.AdminChannelSetReq) (uint64, error) {
	m := &channelDao.Model{
		ID:         req.ID,
		Name:       req.Name,
		Status:     uint32(dbs.StatusEnable),
		UpdateType: channelDao.UpdateType(req.UpdateType),
	}
	var (
		eg           errgroup.Group
		model        = &channelDao.Model{}
		versionModel = &versionDao.Model{}
		num          int64
		err          error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.ChannelRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		eg.Go(func() (err error) {
			if num, err = e.ChannelRepo.CountByFilter(ctx, &channelDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.ChannelRepo.CountByFilter(ctx, &channelDao.Filter{Name: req.Name}); err != nil {
				return
			}
			if num > 0 {
				return ecode.NameExistErr
			}
			return
		})
	}
	if m.NeedUpdate() && req.VersionID > 0 {
		eg.Go(func() (err error) {
			if versionModel, err = e.VersionRepo.FetchByID(ctx, req.VersionID); err != nil {
				return
			}
			if versionModel == nil || versionModel.ID == 0 {
				return ecode.ParamErr
			}
			m.UpdateVersionID = req.VersionID
			m.Url = req.Url
			m.Desc = req.Desc
			return nil
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.ChannelRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminChannelOperate .
func (e *Entry) AdminChannelOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.ChannelRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionSort:
		return e.ChannelRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminChannelAll .
func (e *Entry) AdminChannelAll(ctx *gin.Context, req resourceDto.AdminChannelAllReq) ([]*resourceDto.AdminChannelListItem, error) {
	var (
		list = channelDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.ChannelRepo.RedisEnableChannelList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.ChannelRepo.RedisChannelList(ctx); err != nil {
			return nil, err
		}
	}

	var (
		retList    = make([]*resourceDto.AdminChannelListItem, 0, len(list))
		versionIDs = list.GetVersionIDs()
		versionMap map[uint64]*versionDao.Model
	)
	if len(versionIDs) > 0 {
		versionMap, err = e.VersionRepo.RedisVersionIDMap(ctx)
		if err != nil {
			return nil, err
		}
	}
	for _, val := range list {
		item := &resourceDto.AdminChannelListItem{
			ID:         val.ID,
			Name:       val.Name,
			Status:     val.Status,
			UpdateType: uint32(val.UpdateType),
		}
		if val.NeedUpdate() {
			item.Url = val.Url
			item.Desc = val.Desc
			item.UpdateVersionID = val.UpdateVersionID
			if version, ok := versionMap[val.UpdateVersionID]; ok {
				item.Version = version.Version
			}
		}
		retList = append(retList, item)
	}
	return retList, nil
}
