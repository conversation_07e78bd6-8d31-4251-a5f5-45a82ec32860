package resource

import (
	"encoding/json"
	errorReportDao "vlab/app/dao/monitor/error_report"
	channelDao "vlab/app/dao/resource_channel"
	versionDao "vlab/app/dao/resource_version"
	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	resourceDto "vlab/app/dto/resource"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func (e *Entry) VersionCheck(ctx *gin.Context, req *resourceDto.VersionCheckReq) (*resourceDto.VersionCheckResp, error) {
	var (
		res = &resourceDto.VersionCheckResp{
			UpdateInfo: &channelDao.UpdateInfo{},
		}
		versionInfo   = &versionDao.Model{}
		channelInfo   = &channelDao.Model{}
		updateVersion = &versionDao.Model{}
		err           error
	)

	channelInfo, err = e.ChannelRepo.FetchByID(ctx, req.ChannelID)
	if err != nil {
		return res, err
	}

	if versionInfo, err = e.VersionRepo.FetchByID(ctx, req.VersionID); err != nil {
		return res, err
	}

	if channelInfo.NeedUpdateNone() {
		return res, nil
	}

	if channelInfo.UpdateVersionID <= 0 {
		return res, nil
	}

	updateVersion, err = versionDao.GetRepo().FetchByID(ctx, channelInfo.UpdateVersionID)
	if err != nil {
		return res, err
	}

	if helper.CheckVersionUpdate(updateVersion.Version, versionInfo.Version) {
		// 当前版本小于等于配置的版本号 需要更新
		res.UpdateInfo = channelInfo.GetUpdateInfo()
		return res, nil
	}

	return res, nil
}

func (e *Entry) GetConfig(ctx *gin.Context) (*resourceDto.ConfigResp, error) {
	var (
		resp         = &resourceDto.ConfigResp{}
		err          error
		channelID, _ = helper.GetCtxChannelID(ctx)
	)

	if channelID <= 0 {
		return resp, nil
	}

	channelKey, err := e.ChannelKeyRepo.RedisEnableChannelKeyMap(ctx)
	if err != nil {
		return nil, err
	}

	if ck, ok := channelKey[channelID]; ok {
		resp.ProductUrl = ck.ProductUrl
	} else {
		log.Ctx(ctx).WithFields(logrus.Fields{
			"channelID": channelID,
		}).Warn("channel key not found for channelID: %d", channelID)
	}

	return resp, nil
}

func (e *Entry) ErrorReport(ctx *gin.Context, req resourceDto.ErrorReportReq) error {
	var (
		ctxUser, _    = helper.GetCtxUser(ctx)
		clientType, _ = helper.GetClientType(ctx)
		versisonID, _ = helper.GetCtxVersionID(ctx)
		channelID, _  = helper.GetCtxChannelID(ctx)
		deviceID, _   = helper.GetDeviceNo(ctx)
		headers, _    = json.Marshal(ctx.Request.Header)
		model         = &errorReportDao.Model{
			EntityID:   ctxUser.UID,
			EntityType: uint32(userDao.EtUser),
			ShowID:     req.ShowID,
			EpisodeID:  req.EpisodeID,
			VideoID:    req.VideoID,
			Resolution: req.Resolution,
			ErrCode:    req.ErrCode,
			ErrMsg:     req.ErrMsg,
			Date:       timeUtil.NowToDateStringByZone(ctx),
			Lang:       ctx.GetHeader("X-Language"),
			ClientType: uint32(clientType),
			VersionID:  versisonID,
			ChannelID:  channelID,
			TraceID:    helper.GetGinRequestID(ctx),
			Header:     string(headers),
		}
	)
	if ctxUser.UID == 0 {
		dInfo, err := deviceDao.GetRepo().RedisDeviceInfo(ctx, deviceID)
		if err != nil {
			return err
		}
		model.EntityID = dInfo.ID
		model.EntityType = uint32(userDao.EtVisitor)
	}

	if err := errorReportDao.GetRepo().Create(ctx, model); err != nil {
		return err
	}

	return nil
}
