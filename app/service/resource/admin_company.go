package resource

// import (
// 	"vlab/app/common/dbs"
// 	companyDao "vlab/app/dao/resource_company"
// 	resourceDto "vlab/app/dto/resource"
// 	"vlab/pkg/ecode"

// 	"github.com/gin-gonic/gin"
// 	"golang.org/x/sync/errgroup"
// )

// // AdminCompanyList .
// func (e *Entry) AdminCompanyList(ctx *gin.Context, req resourceDto.AdminCompanyListReq) (*resourceDto.AdminCompanyListResp, error) {
// 	total, list, err := e.CompanyRepo.DataPageList(ctx, &companyDao.Filter{Name: req.Name, Status: req.Status}, req.Page, req.Limit)
// 	if err != nil {
// 		return nil, err
// 	}

// 	retList := make([]*resourceDto.AdminCompanyListItem, 0, len(list))
// 	for _, val := range list {
// 		item := &resourceDto.AdminCompanyListItem{
// 			ID:     val.ID,
// 			Name:   val.Name,
// 			Cover:  val.Cover,
// 			Status: val.Status,
// 		}
// 		retList = append(retList, item)
// 	}
// 	return &resourceDto.AdminCompanyListResp{
// 		List:  retList,
// 		Total: total,
// 	}, nil
// }

// // AdminCompanySet .
// func (e *Entry) AdminCompanySet(ctx *gin.Context, req resourceDto.AdminCompanySetReq) (uint64, error) {
// 	m := &companyDao.Model{
// 		ID:     req.ID,
// 		Name:   req.Name,
// 		Cover:  req.Cover,
// 		Status: uint32(dbs.StatusEnable),
// 	}
// 	var (
// 		eg    errgroup.Group
// 		model = &companyDao.Model{}
// 		num   int64
// 		err   error
// 	)
// 	if req.ID > 0 {
// 		eg.Go(func() (err error) {
// 			if model, err = e.CompanyRepo.FetchByID(ctx, req.ID); err != nil {
// 				return
// 			}
// 			if model.ID == 0 {
// 				return ecode.ParamErr
// 			}
// 			return
// 		})
// 		eg.Go(func() (err error) {
// 			if num, err = e.CompanyRepo.CountByFilter(ctx, &companyDao.Filter{Name: req.Name, NotID: req.ID}); err != nil {
// 				return
// 			}
// 			if num > 0 {
// 				return ecode.NameExistErr
// 			}
// 			return
// 		})
// 	} else {
// 		eg.Go(func() (err error) {
// 			if num, err = e.CompanyRepo.CountByFilter(ctx, &companyDao.Filter{Name: req.Name}); err != nil {
// 				return
// 			}
// 			if num > 0 {
// 				return ecode.NameExistErr
// 			}
// 			return
// 		})
// 	}

// 	if err := eg.Wait(); err != nil {
// 		return 0, err
// 	}

// 	if err = e.CompanyRepo.CreateOrUpdate(ctx, m); err != nil {
// 		return 0, err
// 	}

// 	return m.ID, nil
// }

// // AdminCompanyOperate .
// func (e *Entry) AdminCompanyOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
// 	switch action {
// 	case dbs.ActionOpen, dbs.ActionClose:
// 		return e.CompanyRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
// 	case dbs.ActionSort:
// 		return e.CompanyRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
// 	}
// 	return nil
// }

// // AdminCompanyAll .
// func (e *Entry) AdminCompanyAll(ctx *gin.Context, req resourceDto.AdminCompanyAllReq) ([]*resourceDto.AdminCompanyListItem, error) {
// 	var (
// 		list = companyDao.ModelList{}
// 		err  error
// 	)
// 	if req.Enable == dbs.True {
// 		if list, err = e.CompanyRepo.RedisEnableCompanyList(ctx); err != nil {
// 			return nil, err
// 		}
// 	} else {
// 		if list, err = e.CompanyRepo.RedisCompanyList(ctx); err != nil {
// 			return nil, err
// 		}
// 	}

// 	retList := make([]*resourceDto.AdminCompanyListItem, 0, len(list))
// 	for _, val := range list {
// 		item := &resourceDto.AdminCompanyListItem{
// 			ID:   val.ID,
// 			Name: val.Name,
// 		}
// 		retList = append(retList, item)
// 	}
// 	return retList, nil
// }
