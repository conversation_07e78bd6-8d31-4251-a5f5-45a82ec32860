package resource

import (
	"vlab/app/common/dbs"
	channelKeyDao "vlab/app/dao/resource_channel_key"
	resourceDto "vlab/app/dto/resource"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// AdminChannelKeyList .
func (e *Entry) AdminChannelKeyList(ctx *gin.Context, req resourceDto.AdminChannelKeyListReq) (*resourceDto.AdminChannelKeyListResp, error) {
	total, list, err := e.ChannelKeyRepo.DataPageList(ctx, &channelKeyDao.Filter{ChannelID: req.ChannelID, Status: req.Status}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	retList := make([]*resourceDto.AdminChannelKeyListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminChannelKeyListItem{
			ID:        val.ID,
			ChannelID: val.ChannelID,
			SignKey:   val.SignKey,
			ReqKey:    val.ReqKey,
			RespKey:   val.Resp<PERSON>ey,
			IV:        val.IV,
			Status:    val.Status,
		}
		retList = append(retList, item)
	}
	return &resourceDto.AdminChannelKeyListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminChannelKeySet .
func (e *Entry) AdminChannelKeySet(ctx *gin.Context, req resourceDto.AdminChannelKeySetReq) (uint64, error) {
	m := &channelKeyDao.Model{
		ID:        req.ID,
		ChannelID: req.ChannelID,
		SignKey:   req.SignKey,
		ReqKey:    req.ReqKey,
		RespKey:   req.RespKey,
		IV:        req.IV,
		Status:    uint32(dbs.StatusEnable),
	}
	var (
		eg    errgroup.Group
		model = &channelKeyDao.Model{}
		num   int64
		err   error
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.ChannelKeyRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			return
		})
		// eg.Go(func() (err error) {
		// 	if num, err = e.ChannelKeyRepo.CountByFilter(ctx, &channelKeyDao.Filter{ChannelID: req.ChannelID, NotID: req.ID}); err != nil {
		// 		return
		// 	}
		// 	if num > 0 {
		// 		return ecode.NameExistErr
		// 	}
		// 	return
		// })
	} else {
		eg.Go(func() (err error) {
			if num, err = e.ChannelKeyRepo.CountByFilter(ctx, &channelKeyDao.Filter{ChannelID: req.ChannelID}); err != nil {
				return
			}
			if num > 0 {
				return ecode.ChannelKeyChannelIDExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return 0, err
	}

	if err = e.ChannelKeyRepo.CreateOrUpdate(ctx, m); err != nil {
		return 0, err
	}

	return m.ID, nil
}

// AdminChannelKeyOperate .
func (e *Entry) AdminChannelKeyOperate(ctx *gin.Context, id uint64, action dbs.OperateAction, val uint32) error {
	switch action {
	case dbs.ActionOpen, dbs.ActionClose:
		return e.ChannelKeyRepo.UpdateMapByID(ctx, id, map[string]interface{}{"status": dbs.OperateActionMap[action]})
	case dbs.ActionSort:
		return e.ChannelKeyRepo.UpdateMapByID(ctx, id, map[string]interface{}{"sort": val})
	}
	return nil
}

// AdminChannelKeyAll .
func (e *Entry) AdminChannelKeyAll(ctx *gin.Context, req resourceDto.AdminChannelKeyAllReq) ([]*resourceDto.AdminChannelKeyListItem, error) {
	var (
		list = channelKeyDao.ModelList{}
		err  error
	)
	if req.Enable == dbs.True {
		if list, err = e.ChannelKeyRepo.RedisEnableChannelKeyList(ctx); err != nil {
			return nil, err
		}
	} else {
		if list, err = e.ChannelKeyRepo.RedisChannelKeyList(ctx); err != nil {
			return nil, err
		}
	}

	retList := make([]*resourceDto.AdminChannelKeyListItem, 0, len(list))
	for _, val := range list {
		item := &resourceDto.AdminChannelKeyListItem{
			ID:        val.ID,
			ChannelID: val.ChannelID,
			SignKey:   val.SignKey,
			ReqKey:    val.ReqKey,
			RespKey:   val.RespKey,
			IV:        val.IV,
		}
		retList = append(retList, item)
	}
	return retList, nil
}
