package show

import (
	"errors"

	"vlab/app/common/dbs"
	franchiseDao "vlab/app/dao/content_franchise"
	genreDao "vlab/app/dao/content_genre"
	imageDao "vlab/app/dao/content_image"
	posterDao "vlab/app/dao/content_poster"
	showDao "vlab/app/dao/content_show"
	"vlab/app/dto/common"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// AdminRelationGroupListReq 关联组列表请求
type AdminRelationGroupListReq struct {
	common.DataListReq
	Name         string `form:"name" json:"name,omitempty"`                   // 关联组名称
	RelationType uint32 `form:"relation_type" json:"relation_type,omitempty"` // 关联类型
	Status       uint32 `form:"status" json:"status,omitempty"`               // 状态
}

// AdminRelationGroupListResp 关联组列表响应
type AdminRelationGroupListResp struct {
	List  []*AdminRelationGroup `json:"list"`
	Count int64                 `json:"count"`
}

// AdminRelationGroup 关联组信息
type AdminRelationGroup struct {
	ID           uint64              `json:"id"`
	Name         string              `json:"name"`          // 关联组名称
	Description  string              `json:"description"`   // 关联组描述
	RelationType uint32              `json:"relation_type"` // 关联类型
	Status       uint32              `json:"status"`        // 状态
	ShowCount    int                 `json:"show_count"`    // 组内剧集数量
	Shows        []uint64            `json:"shows"`         // 组内剧集ID列表
	ShowDetails  []*showDto.ShowBase `json:"show_details"`  // 组内剧集详细信息
	CreatedAt    string              `json:"created_at"`
	UpdatedAt    string              `json:"updated_at"`
}

// AdminRelationGroupCreateReq 创建关联组请求
type AdminRelationGroupCreateReq struct {
	Name         string   `json:"name" binding:"required"`          // 关联组名称
	Description  string   `json:"description"`                      // 关联组描述
	RelationType uint32   `json:"relation_type" binding:"required"` // 关联类型
	ShowIDs      []uint64 `json:"show_ids"`                         // 初始剧集ID列表
}

// AdminRelationGroupUpdateReq 更新关联组请求
type AdminRelationGroupUpdateReq struct {
	ID          uint64 `json:"id" binding:"required"` // 关联组ID
	Name        string `json:"name"`                  // 关联组名称
	Description string `json:"description"`           // 关联组描述
}

type AdminRelationGroupUpdatePatchReq struct {
	ID uint64 `json:"id" binding:"required"` // 关联组ID

	Status uint32 `json:"status" binding:"required"` // 状态
}

// AdminRelationMemberBatchReq 批量设置组成员请求
type AdminRelationMemberBatchReq struct {
	Id      uint64   `json:"id" binding:"required"`       // 关联组ID
	ShowIDs []uint64 `json:"show_ids" binding:"required"` // 剧集ID列表
}

// AdminShowRelationListReq 查看剧的关联关系请求
type AdminShowRelationListReq struct {
	ShowID uint64 `form:"show_id" json:"show_id" binding:"required"` // 剧集ID
}

// AdminShowRelationListResp 查看剧的关联关系响应
type AdminShowRelationListResp struct {
	Recommend []*AdminRelationGroup `json:"recommend"` // 相关推荐
	Series    []*AdminRelationGroup `json:"series"`    // 同系列
	Similar   []*AdminRelationGroup `json:"similar"`   // 相似题材
	I18n      []*AdminRelationGroup `json:"i18n"`      // 多语言组合
}

// AdminRelationGroupList 获取关联组列表
func (e Entry) AdminRelationGroupList(ctx *gin.Context, req *AdminRelationGroupListReq) (resp *AdminRelationGroupListResp, err error) {
	resp = &AdminRelationGroupListResp{}
	resp.List = make([]*AdminRelationGroup, 0)

	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	filter := &showDao.RelationGroupFilter{
		Name:         req.Name,
		RelationType: showDao.RelationType(req.RelationType),
		Status:       req.Status,
	}

	// 使用分页查询
	total, groups, err := e.ShowRepo.RelationGroupPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	resp.Count = total

	if total == 0 || len(groups) == 0 {
		return resp, nil
	}

	// 获取每个组的成员信息
	groupIDs := groups.GetIDs()
	memberFilter := &showDao.RelationMemberFilter{
		GroupIDs: groupIDs,
	}
	members, err := e.ShowRepo.FindRelationMemberByFilter(ctx, memberFilter)
	if err != nil {
		return nil, err
	}

	// 构建组ID到成员的映射
	groupMemberMap := members.GetGroupShowMap()

	// 获取所有涉及的剧集ID
	allShowIDs := make([]uint64, 0)
	for _, showIDs := range groupMemberMap {
		allShowIDs = append(allShowIDs, showIDs...)
	}

	// 批量获取剧集详细信息
	showDetailsMap := make(map[uint64]*showDto.ShowBase)
	if len(allShowIDs) > 0 {
		showDetailsMap, err = e.getAdminShowsByIDs(ctx, allShowIDs)
		if err != nil {
			// 记录错误但不影响返回，仅不返回详细信息
			showDetailsMap = make(map[uint64]*showDto.ShowBase)
		}
	}

	// 转换为响应格式
	for _, group := range groups {
		item := &AdminRelationGroup{
			ID:           group.ID,
			Name:         group.Name,
			Description:  group.Description,
			RelationType: uint32(group.RelationType),
			Status:       group.Status,
			CreatedAt:    group.CreatedAt.Format(dbs.TimeDateFormatFull),
			UpdatedAt:    group.UpdatedAt.Format(dbs.TimeDateFormatFull),
			ShowDetails:  make([]*showDto.ShowBase, 0),
		}

		// 添加组内剧集信息
		if showIDs, ok := groupMemberMap[group.ID]; ok {
			item.ShowCount = len(showIDs)
			item.Shows = showIDs

			// 添加剧集详细信息
			for _, showID := range showIDs {
				if showDetail, ok := showDetailsMap[showID]; ok {
					item.ShowDetails = append(item.ShowDetails, showDetail)
				}
			}
		}

		resp.List = append(resp.List, item)
	}

	return resp, nil
}

// AdminRelationGroupCreate 创建关联组
func (e Entry) AdminRelationGroupCreate(ctx *gin.Context, req *AdminRelationGroupCreateReq) (err error) {
	// 创建关联组
	group := &showDao.ShowRelationGroup{
		Name:         req.Name,
		Description:  req.Description,
		RelationType: showDao.RelationType(req.RelationType),
		Status:       uint32(dbs.StatusEnable),
	}

	// 如果提供了初始剧集，构建成员列表
	members := make(showDao.RelationMemberList, 0, len(req.ShowIDs))
	for i, showID := range req.ShowIDs {
		member := &showDao.ShowRelationMember{
			ShowID: showID,
			Order:  uint32(i + 1),
		}
		members = append(members, member)
	}

	// 调用 DAO 层的事务方法
	return e.ShowRepo.CreateRelationGroupWithMembers(ctx, group, members)
}

// AdminRelationGroupUpdate 更新关联组
func (e Entry) AdminRelationGroupUpdate(ctx *gin.Context, req *AdminRelationGroupUpdateReq) (err error) {
	// 检查关联组是否存在
	group, err := e.ShowRepo.FetchRelationGroupByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ecode.NotFoundErr
		}
		return err
	}

	// 更新关联组信息
	group.Name = req.Name
	group.Description = req.Description

	return e.ShowRepo.UpdateRelationGroup(ctx, group)
}

// AdminRelationGroupUpdatePatch 更新关联组状态
func (e Entry) AdminRelationGroupUpdatePatch(ctx *gin.Context, req *AdminRelationGroupUpdatePatchReq) (err error) {
	// 检查关联组是否存在
	group, err := e.ShowRepo.FetchRelationGroupByID(ctx, req.ID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ecode.NotFoundErr
		}
		return err
	}

	// 更新状态
	group.Status = req.Status

	// 调用 DAO 层的更新方法
	return e.ShowRepo.UpdateRelationGroup(ctx, group)
}

// AdminRelationGroupDelete 删除关联组
func (e Entry) AdminRelationGroupDelete(ctx *gin.Context, id uint64) (err error) {
	// 调用 DAO 层的事务方法
	return e.ShowRepo.DeleteRelationGroupWithMembers(ctx, id)
}

// AdminRelationMemberBatch 批量设置组成员
func (e Entry) AdminRelationMemberBatch(ctx *gin.Context, req *AdminRelationMemberBatchReq) (err error) {
	// 检查关联组是否存在
	_, err = e.ShowRepo.FetchRelationGroupByID(ctx, req.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return ecode.NotFoundErr
		}
		return err
	}

	// 构建新成员列表
	members := make(showDao.RelationMemberList, 0, len(req.ShowIDs))
	for i, showID := range req.ShowIDs {
		member := &showDao.ShowRelationMember{
			GroupID: req.Id,
			ShowID:  showID,
			Order:   uint32(i + 1),
		}
		members = append(members, member)
	}

	// 调用 DAO 层的事务方法
	return e.ShowRepo.BatchSetRelationMembers(ctx, req.Id, members)
}

// AdminShowRelationList 查看剧的关联关系
func (e Entry) AdminShowRelationList(ctx *gin.Context, req *AdminShowRelationListReq) (resp *AdminShowRelationListResp, err error) {
	resp = &AdminShowRelationListResp{
		Recommend: make([]*AdminRelationGroup, 0),
		Series:    make([]*AdminRelationGroup, 0),
		Similar:   make([]*AdminRelationGroup, 0),
		I18n:      make([]*AdminRelationGroup, 0),
	}

	// 查找该剧所属的所有关联组
	groupIDs, err := e.ShowRepo.FindRelationGroupsByShowID(ctx, req.ShowID)
	if err != nil || len(groupIDs) == 0 {
		return resp, nil
	}

	// 获取关联组详情
	groupFilter := &showDao.RelationGroupFilter{
		IDs: groupIDs,
	}
	groups, err := e.ShowRepo.FindRelationGroupByFilter(ctx, groupFilter)
	if err != nil {
		return nil, err
	}

	// 获取每个组的所有成员
	memberFilter := &showDao.RelationMemberFilter{
		GroupIDs: groupIDs,
	}
	members, err := e.ShowRepo.FindRelationMemberByFilter(ctx, memberFilter)
	if err != nil {
		return nil, err
	}

	// 构建组ID到成员的映射
	groupMemberMap := members.GetGroupShowMap()

	// 获取所有涉及的剧集ID
	allShowIDs := make([]uint64, 0)
	for _, showIDs := range groupMemberMap {
		allShowIDs = append(allShowIDs, showIDs...)
	}

	// 批量获取剧集详细信息
	showDetailsMap := make(map[uint64]*showDto.ShowBase)
	if len(allShowIDs) > 0 {
		showDetailsMap, err = e.getAdminShowsByIDs(ctx, allShowIDs)
		if err != nil {
			// 记录错误但不影响返回，仅不返回详细信息
			showDetailsMap = make(map[uint64]*showDto.ShowBase)
		}
	}

	// 转换为响应格式，按 relation_type 分组
	for _, group := range groups {
		item := &AdminRelationGroup{
			ID:           group.ID,
			Name:         group.Name,
			Description:  group.Description,
			RelationType: uint32(group.RelationType),
			Status:       group.Status,
			CreatedAt:    group.CreatedAt.Format(dbs.TimeDateFormatFull),
			UpdatedAt:    group.UpdatedAt.Format(dbs.TimeDateFormatFull),
			ShowDetails:  make([]*showDto.ShowBase, 0),
		}

		// 添加组内剧集信息
		if showIDs, ok := groupMemberMap[group.ID]; ok {
			item.ShowCount = len(showIDs)
			item.Shows = showIDs

			// 添加剧集详细信息
			for _, showID := range showIDs {
				if showDetail, ok := showDetailsMap[showID]; ok {
					item.ShowDetails = append(item.ShowDetails, showDetail)
				}
			}
		}

		// 根据 relation_type 分配到对应的切片
		switch group.RelationType {
		case showDao.RelationTypeRecommend:
			resp.Recommend = append(resp.Recommend, item)
		case showDao.RelationTypeSeries:
			resp.Series = append(resp.Series, item)
		case showDao.RelationTypeSimilar:
			resp.Similar = append(resp.Similar, item)
		case showDao.RelationTypeI18n:
			resp.I18n = append(resp.I18n, item)
		}
	}

	return resp, nil
}

// getAdminShowsByIDs 根据ID批量获取剧集详细信息
func (e Entry) getAdminShowsByIDs(ctx *gin.Context, showIDs []uint64) (map[uint64]*showDto.ShowBase, error) {
	if len(showIDs) == 0 {
		return make(map[uint64]*showDto.ShowBase), nil
	}

	// 获取剧集基本信息
	filter := &showDao.Filter{
		IDs: showIDs,
	}
	shows, err := e.ShowRepo.FindByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	if len(shows) == 0 {
		return make(map[uint64]*showDto.ShowBase), nil
	}

	// 获取海报信息
	var posters posterDao.PosterList
	if len(showIDs) > 0 {
		posters, err = e.PosterRepo.FindByFilter(ctx, &posterDao.Filter{
			ShowIDs: showIDs,
		})
		if err != nil {
			return nil, err
		}
	}

	// 获取图片信息
	var images imageDao.ImageList
	imageIDs := posters.GetImageIDs()
	if len(imageIDs) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
		if err != nil {
			return nil, err
		}
	}

	// 获取类型信息
	var withGenres showDao.ShowWithGenreList
	var genres genreDao.GenreList
	if len(showIDs) > 0 {
		withGenres, err = e.ShowRepo.FindGenreByFilter(ctx, &showDao.GenreFilter{
			ShowIDs: showIDs,
		})
		if err != nil {
			return nil, err
		}

		genreIDs := withGenres.GetGenreIDs()
		if len(genreIDs) > 0 {
			genres, err = e.GenreRepo.FindByFilter(ctx, &genreDao.Filter{
				IDs: genreIDs,
			})
			if err != nil {
				return nil, err
			}
		}
	}

	// 获取系列信息
	franchiseIDs := make([]uint64, 0)
	for _, show := range shows {
		if show.FranchiseID > 0 {
			franchiseIDs = append(franchiseIDs, show.FranchiseID)
		}
	}
	var franchises franchiseDao.FranchiseList
	if len(franchiseIDs) > 0 {
		franchises, err = e.FranchiseRepo.FindByFilter(ctx, &franchiseDao.Filter{
			IDs: franchiseIDs,
		})
		if err != nil {
			return nil, err
		}
	}

	// 获取渠道配置信息
	channelKeyInfo, err := helper.GetCtxChannelKeyInfo(ctx, true)
	if err != nil || !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	// 构建映射（确保映射不为 nil）
	imageMap := make(map[uint64]*imageDao.Image)
	if images != nil {
		imageMap = images.GetMap()
	}

	posterShowMap := make(map[uint64]posterDao.PosterList)
	if posters != nil {
		posterShowMap = posters.GetShowMap()
	}

	showGenreMap := make(map[uint64]showDao.ShowWithGenreList)
	if withGenres != nil {
		showGenreMap = withGenres.GetShowIDMap()
	}

	genreMap := make(map[uint64]*genreDao.Genre)
	if genres != nil {
		genreMap = genres.GetMap()
	}

	franchiseMap := make(map[uint64]*franchiseDao.Franchise)
	if franchises != nil {
		franchiseMap = franchises.GetMap()
	}

	// 构建返回结果
	result := make(map[uint64]*showDto.ShowBase, len(shows))
	for _, show := range shows {
		showBase := &showDto.ShowBase{
			ID:               show.ID,
			Name:             show.Name,
			OriginalName:     show.Name,
			Overview:         show.Overview,
			Score:            show.Score,
			AirDate:          show.AirDate,
			NumberOfEpisodes: 0, // 需要从episode表获取
			ContentType:      uint32(show.ContentType),
			InProduction:     uint32(show.InProduction),
			Status:           show.Status,
			Posters:          make([]*showDto.ImageBase, 0),
			Genres:           make([]*showDto.GenreBase, 0),
		}

		// 添加海报信息
		if showPosters, ok := posterShowMap[show.ID]; ok {
			for _, poster := range showPosters {
				if img, ok := imageMap[poster.ImageID]; ok {
					showBase.Posters = append(showBase.Posters, &showDto.ImageBase{
						ID:          img.ID,
						AspectRatio: img.AspectRatio,
						Height:      img.Height,
						Iso6391:     img.ISO_639_1,
						FilePath:    img.GetFilePath(channelKeyInfo.OssImageHost),
						Width:       img.Width,
						AspectType:  img.AspectType,
					})
				}
			}
		}

		// 添加类型信息
		if showGenres, ok := showGenreMap[show.ID]; ok {
			for _, sg := range showGenres {
				if genre, ok := genreMap[sg.GenreID]; ok {
					showBase.Genres = append(showBase.Genres, &showDto.GenreBase{
						Id:   genre.ID,
						Name: genre.Name,
					})
				}
			}
		}

		// 添加系列信息
		if franchise, ok := franchiseMap[show.FranchiseID]; ok && show.FranchiseID > 0 {
			showBase.Franchise = &showDto.FranchiseBase{
				Id:   franchise.ID,
				Name: franchise.Name,
			}
		}

		result[show.ID] = showBase
	}

	return result, nil
}
