package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	genreDao "vlab/app/dao/content_genre"
	i18n "vlab/app/dao/content_i18n"
	showDto "vlab/app/dto/show"
)

func (e Entry) AdminGenreList(ctx *gin.Context, req *showDto.AdminGenreListReq) (resp *showDto.AdminGenreListResp, err error) {
	resp = &showDto.AdminGenreListResp{}
	resp.List = make([]*showDto.AdminGenre, 0)

	var (
		nameKeys = make([]string, 0)
		i18ns    = make(i18n.I18nList, 0)
		i18nMap  = make(map[string][]*i18n.I18n)
	)

	count, list, err := e.GenreRepo.DataPageList(ctx, &genreDao.Filter{
		Name:   req.Name,
		Status: req.Status,
	}, req.<PERSON>, req.Limit)
	if err != nil {
		return nil, err
	}
	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	nameKeys = append(nameKeys, list.GetNameKeys()...)

	i18ns, err = e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
		Keys: nameKeys,
	})
	if err != nil {
		return nil, err
	}

	i18nMap = i18ns.GetI18nKey()

	for _, v := range list {
		genre := &showDto.AdminGenre{
			ID:            v.ID,
			Name:          v.Name,
			NameKey:       v.NameKey,
			Status:        v.Status,
			GenreNameI18n: make([]*dao.I18n, 0),
		}

		if v, ok := i18nMap[v.NameKey]; ok {
			for _, n := range v {
				genre.GenreNameI18n = append(genre.GenreNameI18n, &dao.I18n{
					ISO_639_1: n.ISO_639_1,
					Value:     n.Value,
				})
			}
		}

		resp.List = append(resp.List, genre)
	}

	return
}

func (e Entry) AdminGenreDetail(ctx *gin.Context, req *showDto.AdminGenreDetailReq) (resp *showDto.AdminGenreDetailResp, err error) {
	resp = &showDto.AdminGenreDetailResp{}
	resp.AdminGenre = &showDto.AdminGenre{}

	var (
		nameKeys = make([]string, 0)
		genre    = &genreDao.Genre{}
	)

	genre, err = e.GenreRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	nameKeys = append(nameKeys, genre.NameKey)

	i18ns, err := e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
		Keys: nameKeys,
	})
	if err != nil {
		return nil, err
	}

	resp.AdminGenre.ID = genre.ID
	resp.AdminGenre.Name = genre.Name
	resp.AdminGenre.NameKey = genre.NameKey
	resp.AdminGenre.Status = genre.Status
	resp.AdminGenre.GenreNameI18n = make([]*dao.I18n, 0)

	for _, n := range i18ns {
		resp.AdminGenre.GenreNameI18n = append(resp.AdminGenre.GenreNameI18n, &dao.I18n{
			ISO_639_1: n.ISO_639_1,
			Value:     n.Value,
		})
	}

	return
}

func (e Entry) AdminGenreCreate(ctx *gin.Context, req *showDto.AdminGenreCreateReq) (resp *showDto.AdminGenreCreateResp, err error) {
	resp = &showDto.AdminGenreCreateResp{}

	var (
		genre = &genreDao.Genre{
			Name:    req.Name,
			NameKey: req.NameKey,
			Status:  uint32(dbs.StatusEnable),
		}

		tx        = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		i18nModel = &genreDao.Genre{}
	)

	_, err = e.GenreRepo.CreateWithTx(ctx, tx, genre)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	resp.ID = genre.ID
	return
}

func (e Entry) AdminGenreUpdate(ctx *gin.Context, req *showDto.AdminGenreUpdateReq) (resp *showDto.AdminGenreUpdateResp, err error) {
	resp = &showDto.AdminGenreUpdateResp{}
	var (
		i18nModel = &genreDao.Genre{}
	)

	genre, err := e.GenreRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if genre.ID == 0 {
		return resp, nil
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()

	err = e.GenreRepo.UpdateMapByIDWithTx(ctx, tx, req.ID, map[string]interface{}{
		"name":     req.Name,
		"name_key": req.NameKey,
	})
	if err != nil {
		tx.Rollback()
		return
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminGenreUpdatePatch(ctx *gin.Context, req *showDto.AdminGenreUpdatePatchReq) (resp *showDto.AdminGenreUpdatePatchResp, err error) {
	resp = &showDto.AdminGenreUpdatePatchResp{}

	genre, err := e.GenreRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if genre.ID == 0 {
		return resp, nil
	}

	updateMap := make(map[string]interface{})
	if genre.Status == uint32(dbs.StatusEnable) {
		updateMap["status"] = dbs.StatusDisable
	} else {
		updateMap["status"] = dbs.StatusEnable
	}

	err = e.GenreRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}

func (e Entry) AdminGenreDelete(ctx *gin.Context, req *showDto.AdminGenreDeleteReq) (resp *showDto.AdminGenreDeleteResp, err error) {
	resp = &showDto.AdminGenreDeleteResp{}

	genre, err := e.GenreRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if genre.ID == 0 {
		return
	}

	if genre.IsDeleted == dbs.True {
		return
	}

	err = e.GenreRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		string(dbs.SoftDelField): dbs.True,
	})
	if err != nil {
		return nil, err
	}

	return

}
