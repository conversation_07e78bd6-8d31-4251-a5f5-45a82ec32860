package show

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"vlab/app/common/dbs"
	bannerDao "vlab/app/dao/admin_banner"
	channelDao "vlab/app/dao/resource_channel"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
)

func (e Entry) AdminMarketBannerList(ctx *gin.Context, req *showDto.AdminMarketBannerListReq) (*showDto.AdminMarketBannerListResp, error) {
	var (
		res = &showDto.AdminMarketBannerListResp{}
		err error
	)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	res.List = make([]*showDto.AdminMarketBannerListItem, 0)
	total, list, err := e.BannerRepo.DataPageList(ctx, &bannerDao.Filter{
		Position:  bannerDao.Position(req.Position),
		Status:    req.Status,
		ChannelID: req.ChannelID,
		Sort: []clause.OrderByColumn{
			{
				Column: clause.Column{
					Name: "sort", // Sort 正序
				},
				Desc: false,
			},
			{
				Column: clause.Column{
					Name: "id", // ID 倒序
				},
				Desc: true,
			},
		},
	}, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBannerList failed")
		return nil, err
	}
	if total <= 0 || len(list) <= 0 {
		return res, nil
	}
	res.Total = total

	// 获取所有的channel_id
	channelIDs := list.GetChannelIDs()

	// 批量查询channel信息
	var channelMap map[uint64]*showDto.ChannelBase
	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
			IDS: channelIDs,
		})
		if err == nil && len(channelList) > 0 {
			channelMap = ConvertChannelListToMap(channelList)
		}
	}
	if channelMap == nil {
		channelMap = make(map[uint64]*showDto.ChannelBase)
	}

	for _, item := range list {
		tmp := &showDto.AdminMarketBannerListItem{
			AdminMarketBannerDetail: &showDto.AdminMarketBannerDetail{
				ID:       item.ID,
				Title:    item.Title,
				TitleKey: item.TitleKey,
				Cover:    item.GetFilePath(channelKeyInfo.OssImageHost),
				JumpType: item.JumpType,
				Jump:     item.Jump,
				Position: item.Position,
				Sort:     item.Sort,
				Status:   item.Status,
				Channel:  GetChannelFromMap(channelMap, item.ChannelID),
			},
		}

		res.List = append(res.List, tmp)
	}

	return res, nil
}

func (e Entry) AdminMarketBannerDetail(ctx *gin.Context, req *showDto.AdminMarketBannerDetailReq) (res *showDto.AdminMarketBannerDetailResp, err error) {
	res = &showDto.AdminMarketBannerDetailResp{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	bannerModel, err := e.checkExistByBannerID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkExistByBannerID failed")
		return nil, err
	}

	// 查询channel信息
	var channel *showDto.ChannelBase
	if bannerModel.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, bannerModel.ChannelID)
		if err == nil && channelInfo != nil {
			channel = &showDto.ChannelBase{
				ID:   channelInfo.ID,
				Name: channelInfo.Name,
			}
		}
	}

	res.AdminMarketBannerDetail = &showDto.AdminMarketBannerDetail{
		ID:       bannerModel.ID,
		Title:    bannerModel.Title,
		TitleKey: bannerModel.TitleKey,
		Cover:    bannerModel.GetFilePath(channelKeyInfo.OssImageHost),
		JumpType: bannerModel.JumpType,
		Jump:     bannerModel.Jump,
		Position: bannerModel.Position,
		Sort:     bannerModel.Sort,
		Status:   bannerModel.Status,
		Channel:  channel,
	}

	return res, nil
}

func (e Entry) AdminMarketBannerCreate(ctx *gin.Context, req *showDto.AdminMarketBannerCreateReq) (res *showDto.AdminMarketBannerCreateResp, err error) {
	res = &showDto.AdminMarketBannerCreateResp{}

	model := &bannerDao.Banner{
		Title:     req.Title,
		TitleKey:  req.TitleKey,
		Cover:     dbs.OssFilePath(req.Cover),
		JumpType:  req.JumpType,
		Jump:      req.Jump,
		Position:  req.Position,
		Sort:      req.Sort,
		Status:    dbs.StatusEnable,
		ChannelID: req.ChannelID,
	}

	id, err := e.BannerRepo.Create(ctx, model)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBannerCreate failed")
		return nil, err
	}

	res.ID = id

	return res, nil
}

func (e Entry) AdminMarketBannerUpdate(ctx *gin.Context, req *showDto.AdminMarketBannerUpdateReq) (res *showDto.AdminMarketBannerUpdateResp, err error) {
	res = &showDto.AdminMarketBannerUpdateResp{}

	_, err = e.checkExistByBannerID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkExistByBannerID failed")
		return nil, err
	}

	updates := make(map[string]interface{})

	if req.Title != "" {
		updates["title"] = req.Title
	}
	if req.TitleKey != "" {
		updates["title_key"] = req.TitleKey
	}
	if req.Cover != "" {
		updates["cover"] = req.Cover
	}
	if req.JumpType > 0 {
		updates["jump_type"] = req.JumpType
	}
	if req.Jump != "" {
		updates["jump"] = req.Jump
	}
	if req.Position > 0 {
		updates["position"] = req.Position
	}
	if req.Sort > 0 {
		updates["sort"] = req.Sort
	}
	if req.ChannelID > 0 {
		updates["channel_id"] = req.ChannelID
	}

	err = e.BannerRepo.UpdateMapByID(ctx, req.ID, updates)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByID failed")
		return nil, err
	}
	res.ID = req.ID

	return res, nil
}

func (e Entry) AdminMarketBannerUpdatePatch(ctx *gin.Context, req *showDto.AdminMarketBannerUpdatePatchReq) (res *showDto.AdminMarketBannerUpdatePatchResp, err error) {
	res = &showDto.AdminMarketBannerUpdatePatchResp{}

	bannerModel, err := e.checkExistByBannerID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkExistByBannerID failed")
		return nil, err
	}
	_ = bannerModel

	updates := make(map[string]interface{})
	updates["status"] = req.Status

	err = e.BannerRepo.UpdateMapByID(ctx, req.ID, updates)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("UpdateMapByID failed")
		return nil, err
	}

	res.ID = req.ID
	return
}

func (e Entry) AdminMarketBannerDelete(ctx *gin.Context, req *showDto.AdminMarketBannerDeleteReq) (*showDto.AdminMarketBannerDeleteResp, error) {
	var (
		res = &showDto.AdminMarketBannerDeleteResp{}
		err error
	)
	if _, err = e.checkExistByBannerID(ctx, req.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("checkExistByBannerID failed")
		return nil, err
	}

	err = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Transaction(func(tx *gorm.DB) error {
		err = e.BannerRepo.DeleteByIDWithTx(ctx, tx, req.ID)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("AdminBannerDelete failed")
		return nil, err
	}

	return res, nil
}

func (e Entry) checkExistByBannerID(ctx *gin.Context, id uint64) (model *bannerDao.Banner, err error) {
	model, err = e.BannerRepo.FetchByID(ctx, id)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("checkExistByID failed")
		return nil, err
	}
	if model == nil {
		return nil, ecode.NotFoundErr
	}
	return model, nil
}
