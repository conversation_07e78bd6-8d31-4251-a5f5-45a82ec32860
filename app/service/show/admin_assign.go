package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	assignDao "vlab/app/dao/content_assign"
	showDao "vlab/app/dao/content_show"
	channelDao "vlab/app/dao/resource_channel"
	showDto "vlab/app/dto/show"
	"vlab/pkg/log"
)

func (e Entry) AdminAssignList(ctx *gin.Context, req *showDto.AdminAssignListReq) (resp *showDto.AdminAssignListResp, err error) {
	resp = &showDto.AdminAssignListResp{}
	resp.List = make([]*showDto.AdminAssign, 0)

	count, list, err := e.AssignRepo.DataPageList(ctx, &assignDao.Filter{
		Status:    req.Status,
		ChannelID: req.ChannelID,
	}, req.Page, req.Limit)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("AdminAssignList failed")
		return nil, err
	}

	if count == 0 {
		return
	}

	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	var (
		showIDs        = list.GetShowIDs()
		showMap        = make(map[uint64]*showDao.Show)
		showEpisodeMap = make(map[uint64]int64)
		channelIDs     = make([]uint64, 0, len(list))
		channelMap     = make(map[uint64]*showDto.ChannelBase)
	)

	// 获取所有的channel_id
	channelIDs = list.GetChannelIDs()

	showList, err := e.ShowRepo.FindByFilter(ctx, &showDao.Filter{IDs: showIDs})
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("FindByFilter failed")
		return nil, err
	}

	showMap = showList.GetMap()

	// 批量查询channel信息
	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
			IDS: channelIDs,
		})
		if err == nil && len(channelList) > 0 {
			channelMap = ConvertChannelListToMap(channelList)
		}
	}

	// 计算每个show下面有多少episode
	showEpisodeMap, err = e.getAdminShowEpisodeInfo(ctx, showIDs)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("getAdminShowEpisodeInfo failed")
		return nil, err
	}
	for _, v := range list {
		item := &showDto.AdminAssign{
			ID:      v.ID,
			Status:  v.Status,
			Channel: GetChannelFromMap(channelMap, v.ChannelID),
		}

		if show, ok := showMap[v.ShowID]; ok {
			item.Show = &showDto.ShowBase{
				ID:           show.ID,
				Posters:      nil,
				Name:         show.Name,
				OriginalName: show.Name,
				Overview:     show.Overview,
				Genres:       nil,
				Score:        show.Score,
				AirDate:      show.AirDate,
				//NumberOfSeasons:  0,
				NumberOfEpisodes: 0,
				ContentType:      uint32(show.ContentType),
				InProduction:     uint32(show.InProduction),
				Franchise:        nil,
			}
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				item.Show.NumberOfEpisodes = uint32(episodeCount)
			}
		}

		resp.List = append(resp.List, item)
	}

	return
}

func (e Entry) AdminAssignDetail(ctx *gin.Context, req *showDto.AdminAssignDetailReq) (resp *showDto.AdminAssignDetailResp, err error) {
	resp = &showDto.AdminAssignDetailResp{}
	resp.AdminAssign = &showDto.AdminAssign{}

	assignModel, err := e.AssignRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("FetchByID failed")
		return nil, err
	}

	resp.AdminAssign.ID = assignModel.ID
	resp.AdminAssign.Status = assignModel.Status

	// 查询channel信息
	if assignModel.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, assignModel.ChannelID)
		if err == nil && channelInfo != nil {
			resp.AdminAssign.Channel = &showDto.ChannelBase{
				ID:   channelInfo.ID,
				Name: channelInfo.Name,
			}
		}
	}

	showEpisodeMap, err := e.getAdminShowEpisodeInfo(ctx, []uint64{assignModel.ShowID})
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("getAdminShowEpisodeInfo failed")
		return nil, err
	}

	base, err := e.toShowBase(ctx, assignModel.ShowID, "")
	if err != nil {
		return nil, err
	}

	resp.AdminAssign.Show = base

	if episodeCount, ok := showEpisodeMap[assignModel.ShowID]; ok {
		resp.AdminAssign.Show.NumberOfEpisodes = uint32(episodeCount)
	}
	return
}

func (e Entry) AdminAssignCreate(ctx *gin.Context, req *showDto.AdminAssignCreateReq) (resp *showDto.AdminAssignCreateResp, err error) {
	resp = &showDto.AdminAssignCreateResp{}

	var (
		assignModel = &assignDao.Assign{
			ShowID:    req.ShowID,
			Status:    uint32(dbs.StatusEnable),
			ChannelID: req.ChannelID,
		}

		assignID uint64
	)

	assignID, err = e.AssignRepo.Create(ctx, assignModel)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("Create failed")
		return nil, err
	}

	resp.ID = assignID

	return
}

func (e Entry) AdminAssignUpdate(ctx *gin.Context, req *showDto.AdminAssignUpdateReq) (resp *showDto.AdminAssignUpdateResp, err error) {
	resp = &showDto.AdminAssignUpdateResp{}

	assignModel, err := e.AssignRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("FetchByID failed")
		return nil, err
	}
	if assignModel == nil || assignModel.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{
		"show_id":    req.ShowID,
		"channel_id": req.ChannelID,
	}

	err = e.AssignRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		return nil, err
	}
	return
}

func (e Entry) AdminAssignUpdatePatch(ctx *gin.Context, req *showDto.AdminAssignUpdatePatchReq) (resp *showDto.AdminAssignUpdatePatchResp, err error) {
	resp = &showDto.AdminAssignUpdatePatchResp{}

	assignModel, err := e.AssignRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("FetchByID failed")
		return nil, err
	}

	if assignModel == nil || assignModel.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{}

	if assignModel.Status == uint32(dbs.StatusDisable) {
		updateMap["status"] = uint32(dbs.StatusEnable)
	} else {
		updateMap["status"] = uint32(dbs.StatusDisable)
	}

	err = e.AssignRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("UpdateMapByID failed")
		return nil, err
	}

	return
}

func (e Entry) AdminAssignDelete(ctx *gin.Context, req *showDto.AdminAssignDeleteReq) (resp *showDto.AdminAssignDeleteResp, err error) {
	resp = &showDto.AdminAssignDeleteResp{}

	assignModel, err := e.AssignRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("FetchByID failed")
		return nil, err
	}
	if assignModel == nil || assignModel.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	}

	err = e.AssignRepo.UpdateMapByID(ctx, assignModel.ID, updateMap)
	if err != nil {
		log.WithContext(ctx).WithError(err).Error("UpdateMapByID failed")
		return nil, err
	}
	return
}
