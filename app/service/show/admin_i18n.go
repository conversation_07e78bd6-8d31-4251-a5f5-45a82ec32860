package show

import (
	"strconv"
	"strings"
	"time"

	"vlab/app/dao"
	i18n "vlab/app/dao/content_i18n"
	showDto "vlab/app/dto/show"
	"vlab/pkg/helper"

	"github.com/gin-gonic/gin"
)

func (e Entry) GenI18nKey(ctx *gin.Context, req *showDto.GenI18nKeyReq) (resp *showDto.GenI18nKeyResp, err error) {
	resp = &showDto.GenI18nKeyResp{}

	// req.List 转换为json, 计算md5
	j, err := json.Marshal(req.List)
	if err != nil {
		return nil, err
	}
	str := helper.EncodeMd5(string(j))

	// 获取当前毫秒时间戳

	// 拼接生成key
	key := strings.Join([]string{strconv.Itoa(int(time.Now().UnixNano())), str}, "-")

	resp.Key = key

	return
}

func (e Entry) AdminI18nCreate(ctx *gin.Context, req *showDto.AdminI18nCreateReq) (resp *showDto.AdminI18nCreateResp, err error) {
	resp = &showDto.AdminI18nCreateResp{}

	var (
		i18nList = make(i18n.I18nList, 0)
	)

	// req.List 转换为json, 计算md5
	j, err := json.Marshal(req.List)
	if err != nil {
		return nil, err
	}
	str := helper.EncodeMd5(string(j))

	// 获取当前毫秒时间戳

	// 拼接生成key
	key := strings.Join([]string{strconv.Itoa(int(time.Now().UnixNano())), str}, "-")

	for _, n := range req.List {
		item := &i18n.I18n{
			Key:       key,
			ISO_639_1: n.ISO_639_1,
			Value:     n.Value,
		}
		i18nList = append(i18nList, item)
	}

	err = e.I18nRepo.BatchCreate(ctx, i18nList)
	if err != nil {
		return nil, err
	}

	resp.Key = key

	return
}

func (e Entry) AdminI18nDetail(ctx *gin.Context, req *showDto.AdminI18nDetailReq) (resp *showDto.AdminI18nDetailResp, err error) {
	resp = &showDto.AdminI18nDetailResp{}
	resp.List = make([]*dao.I18n, 0)

	list, err := e.I18nRepo.FindByFilter(ctx, &i18n.Filter{Key: req.Key})
	if err != nil {
		return nil, err
	}

	for _, n := range list {
		item := &dao.I18n{
			ISO_639_1: n.ISO_639_1,
			Value:     n.Value,
		}

		resp.List = append(resp.List, item)
	}

	return
}
