package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	i18n "vlab/app/dao/content_i18n"
	imageDao "vlab/app/dao/content_image"
	personDao "vlab/app/dao/content_person"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func (e Entry) AdminPersonList(ctx *gin.Context, req *showDto.AdminPersonListReq) (resp *showDto.AdminPersonListResp, err error) {
	resp = &showDto.AdminPersonListResp{}
	resp.List = make([]*showDto.AdminPerson, 0)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	var (
		nameKeys = make([]string, 0)
		i18ns    = make(i18n.I18nList, 0)
		i18nMap  = make(map[string][]*i18n.I18n)

		imageIDs = make([]uint64, 0)
		imageMap = make(map[uint64]*imageDao.Image)
	)

	count, list, err := e.PersonRepo.DataPageList(ctx, &personDao.Filter{
		Status:   req.Status,
		NameLike: req.Name,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	nameKeys = append(nameKeys, list.GetNameKeys()...)
	imageIDs = append(imageIDs, list.GetImageIDs()...)

	if len(nameKeys) > 0 {
		i18ns, err = e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
			Keys: nameKeys,
		})
		if err != nil {
			return nil, err
		}
		i18nMap = i18ns.GetI18nKey()

	}

	if len(imageIDs) > 0 {
		images, err := e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
			IDs: imageIDs,
		})
		if err != nil {
			return nil, err
		}

		imageMap = images.GetMap()
	}

	for _, v := range list {
		person := &showDto.AdminPerson{
			ID:             v.ID,
			Name:           v.Name,
			NameKey:        v.NameKey,
			Gender:         v.Gender,
			Status:         v.Status,
			PersonNameI18n: make([]*dao.I18n, 0),
		}

		if img, ok := imageMap[v.ProfileID]; ok {
			person.Profile = showDto.ImageBase{
				AspectRatio: img.AspectRatio,
				Height:      img.Height,
				Iso6391:     img.ISO_639_1,
				FilePath:    img.GetFilePath(channelKeyInfo.OssImageHost),
				Width:       img.Width,
				AspectType:  img.AspectType,
			}
		}

		if v, ok := i18nMap[v.NameKey]; ok {
			for _, n := range v {
				person.PersonNameI18n = append(person.PersonNameI18n, &dao.I18n{
					ISO_639_1: n.ISO_639_1,
					Value:     n.Value,
				})
			}
		}

		resp.List = append(resp.List, person)
	}

	return
}

func (e Entry) AdminPersonDetail(ctx *gin.Context, req *showDto.AdminPersonDetailReq) (resp *showDto.AdminPersonDetailResp, err error) {
	resp = &showDto.AdminPersonDetailResp{}
	resp.AdminPerson = &showDto.AdminPerson{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	var (
		nameKeys = make([]string, 0)
		person   = &personDao.Person{}

		imageIDs = make([]uint64, 0)
	)

	person, err = e.PersonRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	nameKeys = append(nameKeys, person.NameKey)
	imageIDs = append(imageIDs, person.ProfileID)

	i18ns, err := e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
		Keys: nameKeys,
	})
	if err != nil {
		return nil, err
	}

	images, err := e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{
		IDs: imageIDs,
	})
	if err != nil {
		return nil, err
	}

	resp.AdminPerson.ID = person.ID
	resp.AdminPerson.Name = person.Name
	resp.AdminPerson.NameKey = person.NameKey
	resp.AdminPerson.Status = person.Status
	resp.AdminPerson.PersonNameI18n = make([]*dao.I18n, 0)
	resp.AdminPerson.Gender = person.Gender
	resp.AdminPerson.Profile = showDto.ImageBase{
		AspectRatio: images[0].AspectRatio,
		Height:      images[0].Height,
		Iso6391:     images[0].ISO_639_1,
		FilePath:    images[0].GetFilePath(channelKeyInfo.OssImageHost),
		Width:       images[0].Width,
		AspectType:  images[0].AspectType,
	}

	for _, n := range i18ns {
		resp.AdminPerson.PersonNameI18n = append(resp.AdminPerson.PersonNameI18n, &dao.I18n{
			ISO_639_1: n.ISO_639_1,
			Value:     n.Value,
		})
	}

	return
}

func (e Entry) AdminPersonCreate(ctx *gin.Context, req *showDto.AdminPersonCreateReq) (resp *showDto.AdminPersonCreateResp, err error) {
	resp = &showDto.AdminPersonCreateResp{}

	var (
		person = &personDao.Person{
			Status:    uint32(dbs.StatusEnable),
			Name:      req.Name,
			NameKey:   req.NameKey,
			Gender:    req.Gender,
			ProfileID: 0,
		}
		imageID uint64

		tx        = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		i18nModel = &personDao.Person{}
	)

	imageID, err = e.ImageRepo.CreateWithTx(ctx, tx, &imageDao.Image{
		AspectRatio: req.Profile.AspectRatio,
		Height:      req.Profile.Height,
		ISO_639_1:   req.Profile.Iso6391,
		FilePath:    dbs.OssFilePath(req.Profile.FilePath),
		Width:       req.Profile.Width,
		AspectType:  req.Profile.AspectType,
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	person.ProfileID = imageID

	_, err = e.PersonRepo.CreateWithTx(ctx, tx, person)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.I18nRepo.UpdateMapByFilter(ctx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	resp.ID = person.ID
	return
}

func (e Entry) AdminPersonUpdate(ctx *gin.Context, req *showDto.AdminPersonUpdateReq) (resp *showDto.AdminPersonUpdateResp, err error) {
	resp = &showDto.AdminPersonUpdateResp{}

	var (
		i18nModel = &personDao.Person{}
		imageID   uint64
	)

	person, err := e.PersonRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if person.ID == 0 {
		return resp, nil
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()

	imageID, err = e.ImageRepo.CreateWithTx(ctx, tx, &imageDao.Image{
		AspectRatio: req.Profile.AspectRatio,
		Height:      req.Profile.Height,
		ISO_639_1:   req.Profile.Iso6391,
		FilePath:    dbs.OssFilePath(req.Profile.FilePath),
		Width:       req.Profile.Width,
		AspectType:  req.Profile.AspectType,
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.ImageRepo.UpdateMapByIDWithTx(ctx, tx, person.ProfileID, map[string]interface{}{
		string(dbs.SoftDelField): dbs.True,
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.PersonRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		"name":       req.Name,
		"name_key":   req.NameKey,
		"profile_id": imageID,
		"gender":     req.Gender,
	})
	if err != nil {
		tx.Rollback()
		return
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()

		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminPersonUpdatePatch(ctx *gin.Context, req *showDto.AdminPersonUpdatePatchReq) (resp *showDto.AdminPersonUpdatePatchResp, err error) {
	resp = &showDto.AdminPersonUpdatePatchResp{}

	person, err := e.PersonRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if person.ID == 0 {
		return resp, nil
	}

	resp.ID = person.ID

	updateMap := make(map[string]interface{})

	if person.Status == uint32(dbs.StatusEnable) {
		updateMap["status"] = dbs.StatusDisable
	} else {
		updateMap["status"] = dbs.StatusEnable
	}

	err = e.PersonRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		return
	}

	return
}

func (e Entry) AdminPersonDelete(ctx *gin.Context, req *showDto.AdminPersonDeleteReq) (resp *showDto.AdminPersonDeleteResp, err error) {
	resp = &showDto.AdminPersonDeleteResp{}

	person, err := e.PersonRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if person.ID == 0 {
		return
	}

	if person.IsDeleted == dbs.True {
		return
	}

	err = e.PersonRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		string(dbs.SoftDelField): dbs.True,
	})
	if err != nil {
		return nil, err
	}

	return
}
