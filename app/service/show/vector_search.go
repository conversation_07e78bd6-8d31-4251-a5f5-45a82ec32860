package show

import (
	"strings"
	"unicode"

	adminConfigDao "vlab/app/dao/admin_config"
	showDto "vlab/app/dto/show"

	"github.com/gin-gonic/gin"
)

// shouldUseVectorSearch 判断是否应该使用向量搜索 (Admin)
func shouldUseVectorSearch(ctx *gin.Context, req *showDto.AdminShowListReq) bool {
	// 0. 首先检查配置开关
	configValue := adminConfigDao.RedisGetConfig[string](ctx, adminConfigDao.KeyEnableAdminShowListVector)
	if configValue != "1" && configValue != "true" {
		return false
	}

	// 1. 明确指定使用向量搜索
	if req.UseVector {
		return true
	}

	// 2. 检查是否有搜索关键词
	if req.Name == "" {
		return false
	}

	// 3. 关键词太短不使用向量搜索
	if len(req.Name) <= 2 {
		return false
	}

	// 4. 包含中文字符的查询优先使用向量搜索
	if containsChinese(req.Name) {
		return true
	}

	// 5. 长英文查询（超过5个字符）使用向量搜索
	if len(req.Name) > 5 {
		return true
	}

	return false
}

// shouldUseVectorSearchForShow 判断是否应该使用向量搜索 (Show Search)
func shouldUseVectorSearchForShow(ctx *gin.Context, req *showDto.ShowSearchReq) bool {
	// 0. 首先检查配置开关
	configValue := adminConfigDao.RedisGetConfig[string](ctx, adminConfigDao.KeyEnableShowSearchVector)
	if configValue != "1" && configValue != "true" {
		return false
	}

	// 2. 检查是否有搜索关键词
	if req.Keyword == "" {
		return false
	}

	// 3. 关键词太短不使用向量搜索
	trimmedKeyword := strings.TrimSpace(req.Keyword)
	if len(trimmedKeyword) <= 2 {
		return false
	}

	// 4. 包含中文字符的查询优先使用向量搜索
	if containsChinese(trimmedKeyword) {
		// 中文查询，即使只有1个字符也使用向量搜索
		return len(trimmedKeyword) >= 1
	}

	// 5. 长英文查询（超过5个字符）使用向量搜索
	if len(trimmedKeyword) > 5 {
		return true
	}

	return false
}

// containsChinese 检查字符串是否包含中文字符
func containsChinese(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Han, r) {
			return true
		}
	}
	return false
}

// setVectorSearchDefaults 设置向量搜索的默认参数 (Admin)
func setVectorSearchDefaults(req *showDto.AdminShowListReq) {
	// 设置默认权重
	if req.DenseWeight == 0 && req.SparseWeight == 0 {
		req.DenseWeight = 0.5
		req.SparseWeight = 0.5
	}

	// 设置默认的向量搜索候选数量
	if req.VectorTopK == 0 {
		req.VectorTopK = 100
	}
}

// setVectorSearchDefaultsForShow 设置向量搜索的默认参数 (Show Search)
func setVectorSearchDefaultsForShow(req *showDto.ShowSearchReq) {
	// 设置默认权重
	if req.DenseWeight == 0 && req.SparseWeight == 0 {
		req.DenseWeight = 0.5
		req.SparseWeight = 0.5
	}

	// 设置默认的向量搜索候选数量
	if req.VectorTopK == 0 {
		req.VectorTopK = 100
	}
}

// isChineseQuery 判断是否为中文查询
func isChineseQuery(query string) bool {
	if query == "" {
		return false
	}

	// 统计中文字符数量
	chineseCount := 0
	totalCount := 0

	for _, r := range query {
		if !unicode.IsSpace(r) {
			totalCount++
			if unicode.Is(unicode.Han, r) {
				chineseCount++
			}
		}
	}

	// 如果中文字符占比超过30%，认为是中文查询
	if totalCount > 0 && float64(chineseCount)/float64(totalCount) > 0.3 {
		return true
	}

	return false
}

// normalizeQuery 规范化查询字符串
func normalizeQuery(query string) string {
	// 去除首尾空白
	query = strings.TrimSpace(query)

	// 将多个空格替换为单个空格
	query = strings.Join(strings.Fields(query), " ")

	return query
}
