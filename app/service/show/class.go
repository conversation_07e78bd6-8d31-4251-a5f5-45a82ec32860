package show

import (
	"github.com/gin-gonic/gin"
	classFieldDao "vlab/app/dao/content_class_field"
	showDto "vlab/app/dto/show"
)

func (e Entry) ClassFieldList(ctx *gin.Context, req *showDto.ClassFieldListReq) (*showDto.ClassFieldListResp, error) {
	var (
		resp = &showDto.ClassFieldListResp{
			List: make(classFieldDao.ModelList, 0),
		}
		err error
	)

	list, err := e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
		ClassID: req.ClassID,
		ID:      req.FieldID,
		Name:    req.FieldName,
	})
	if err != nil {
		return nil, err
	}

	if len(list) == 0 {
		return resp, nil
	}

	resp.List = list

	return resp, nil
}
