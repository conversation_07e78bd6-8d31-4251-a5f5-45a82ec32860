package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	franchiseDao "vlab/app/dao/content_franchise"
	i18n "vlab/app/dao/content_i18n"
	showDto "vlab/app/dto/show"
)

func (e Entry) AdminFranchiseList(ctx *gin.Context, req *showDto.AdminFranchiseListReq) (resp *showDto.AdminFranchiseListResp, err error) {
	resp = &showDto.AdminFranchiseListResp{}
	resp.List = make([]*showDto.AdminFranchise, 0)

	var (
		nameKeys = make([]string, 0)
		i18ns    = make(i18n.I18nList, 0)
		i18nMap  = make(map[string][]*i18n.I18n)
	)

	count, list, err := e.FranchiseRepo.DataPageList(ctx, &franchiseDao.Filter{
		Name:   req.Name,
		Status: req.Status,
	}, req.<PERSON>, req.Limit)
	if err != nil {
		return nil, err
	}
	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	nameKeys = append(nameKeys, list.GetNameKeys()...)

	i18ns, err = e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
		Keys: nameKeys,
	})
	if err != nil {
		return nil, err
	}

	i18nMap = i18ns.GetI18nKey()

	for _, v := range list {
		franchise := &showDto.AdminFranchise{
			ID:                v.ID,
			Name:              v.Name,
			NameKey:           v.NameKey,
			Status:            v.Status,
			FranchiseNameI18n: make([]*dao.I18n, 0),
		}

		if v, ok := i18nMap[v.NameKey]; ok {
			for _, n := range v {
				franchise.FranchiseNameI18n = append(franchise.FranchiseNameI18n, &dao.I18n{
					ISO_639_1: n.ISO_639_1,
					Value:     n.Value,
				})
			}
		}

		resp.List = append(resp.List, franchise)
	}

	return
}

func (e Entry) AdminFranchiseDetail(ctx *gin.Context, req *showDto.AdminFranchiseDetailReq) (resp *showDto.AdminFranchiseDetailResp, err error) {
	resp = &showDto.AdminFranchiseDetailResp{}
	resp.AdminFranchise = &showDto.AdminFranchise{}

	var (
		nameKeys  = make([]string, 0)
		franchise = &franchiseDao.Franchise{}
	)

	franchise, err = e.FranchiseRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	nameKeys = append(nameKeys, franchise.NameKey)

	i18ns, err := e.I18nRepo.FindByFilter(ctx, &i18n.Filter{
		Keys: nameKeys,
	})
	if err != nil {
		return nil, err
	}

	resp.AdminFranchise.ID = franchise.ID
	resp.AdminFranchise.Name = franchise.Name
	resp.AdminFranchise.NameKey = franchise.NameKey
	resp.AdminFranchise.Status = franchise.Status
	resp.AdminFranchise.FranchiseNameI18n = make([]*dao.I18n, 0)

	for _, n := range i18ns {
		resp.AdminFranchise.FranchiseNameI18n = append(resp.AdminFranchise.FranchiseNameI18n, &dao.I18n{
			ISO_639_1: n.ISO_639_1,
			Value:     n.Value,
		})
	}

	return
}

func (e Entry) AdminFranchiseCreate(ctx *gin.Context, req *showDto.AdminFranchiseCreateReq) (resp *showDto.AdminFranchiseCreateResp, err error) {
	resp = &showDto.AdminFranchiseCreateResp{}

	var (
		franchise = &franchiseDao.Franchise{
			Name:    req.Name,
			NameKey: req.NameKey,
			Status:  uint32(dbs.StatusEnable),
		}

		tx        = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		i18nModel = &franchiseDao.Franchise{}
	)

	_, err = e.FranchiseRepo.CreateWithTx(ctx, tx, franchise)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()

		return nil, err
	}

	tx.Commit()
	resp.ID = franchise.ID
	return
}

func (e Entry) AdminFranchiseUpdate(ctx *gin.Context, req *showDto.AdminFranchiseUpdateReq) (resp *showDto.AdminFranchiseUpdateResp, err error) {
	resp = &showDto.AdminFranchiseUpdateResp{}

	var (
		i18nModel = &franchiseDao.Franchise{}
	)

	franchise, err := e.FranchiseRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if franchise.ID == 0 {
		return resp, nil
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()

	err = e.FranchiseRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		"name":     req.Name,
		"name_key": req.NameKey,
	})
	if err != nil {
		tx.Rollback()
		return
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18n.Filter{
		Key: req.NameKey,
	}, map[string]interface{}{
		"table":  i18nModel.TableName(),
		"column": "name",
	})
	if err != nil {
		tx.Rollback()

		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminFranchiseUpdatePatch(ctx *gin.Context, req *showDto.AdminFranchiseUpdatePatchReq) (resp *showDto.AdminFranchiseUpdatePatchResp, err error) {
	resp = &showDto.AdminFranchiseUpdatePatchResp{}

	franchise, err := e.FranchiseRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if franchise.ID == 0 {
		return resp, nil
	}

	updateMap := make(map[string]interface{})
	if franchise.Status == uint32(dbs.StatusEnable) {
		updateMap["status"] = dbs.StatusDisable
	} else {
		updateMap["status"] = dbs.StatusEnable
	}

	err = e.FranchiseRepo.UpdateMapByID(ctx, req.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}

func (e Entry) AdminFranchiseDelete(ctx *gin.Context, req *showDto.AdminFranchiseDeleteReq) (resp *showDto.AdminFranchiseDeleteResp, err error) {
	resp = &showDto.AdminFranchiseDeleteResp{}

	franchise, err := e.FranchiseRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if franchise.ID == 0 {
		return
	}

	if franchise.IsDeleted == dbs.True {
		return
	}

	err = e.FranchiseRepo.UpdateMapByID(ctx, req.ID, map[string]interface{}{
		string(dbs.SoftDelField): dbs.True,
	})
	if err != nil {
		return nil, err
	}

	return

}
