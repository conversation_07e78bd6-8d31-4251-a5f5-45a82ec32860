package show

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	classFieldDao "vlab/app/dao/content_class_field"
	i18nDao "vlab/app/dao/content_i18n"
	showDao "vlab/app/dao/content_show"
	showDto "vlab/app/dto/show"
	"vlab/config"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
)

// VectorEmbeddingResult 向量化结果
type VectorEmbeddingResult struct {
	DenseVector  []float32         // 稠密向量
	SparseVector map[int64]float64 // 稀疏向量
}

// AdminShowVectorUpload 单个剧集向量上传
func (e Entry) AdminShowVectorUpload(ctx *gin.Context, req *showDto.AdminShowVectorUploadReq) (*showDto.AdminShowVectorUploadResp, error) {
	resp := &showDto.AdminShowVectorUploadResp{
		ShowID: req.ShowID,
	}

	// 1. 获取单个show的数据（复用导出逻辑）
	exportData, err := e.getShowExportData(ctx, req.ShowID)
	if err != nil {
		resp.Status = "failed"
		resp.Message = fmt.Sprintf("获取剧集数据失败: %v", err)
		return resp, ecode.SystemErr
	}

	if exportData == nil {
		resp.Status = "failed"
		resp.Message = "剧集不存在"
		return resp, ecode.NotFoundErr
	}

	// 2. 初始化VikingDB客户端
	cfg := config.VikingDBCfg
	if cfg.Host == "" {
		resp.Status = "failed"
		resp.Message = "VikingDB配置不完整"
		return resp, ecode.SystemErr
	}

	// 3. 创建嵌入客户端

	// 4. 转换数据为VikingDB格式
	dataList, err := e.convertSingleShowToVikingDBData(context.Background(), exportData, cfg, e.SearchApi.Service)
	if err != nil {
		resp.Status = "failed"
		resp.Message = fmt.Sprintf("转换数据失败: %v", err)
		return resp, ecode.SystemErr
	}

	// 5. 上传到VikingDB
	log.Printf("上传剧集 %d 到VikingDB，文档数: %d", req.ShowID, len(dataList))

	var uploadErr error
	if cfg.UseAsyncUpload {
		uploadErr = e.SearchApi.CollectionClient.UpsertData(dataList, vikingdb.WithAsyncUpsert(true))
	} else {
		uploadErr = e.SearchApi.CollectionClient.UpsertData(dataList)
	}

	if uploadErr != nil {
		resp.Status = "failed"
		resp.Message = fmt.Sprintf("上传失败: %v", uploadErr)
		return resp, ecode.SystemErr
	}

	// 6. 返回成功结果
	resp.Status = "success"
	resp.Message = "向量上传成功"
	resp.DocumentCount = len(dataList)

	log.Printf("成功上传剧集 %d 的 %d 个文档到VikingDB", req.ShowID, len(dataList))

	return resp, nil
}

// getShowExportData 获取单个show的导出数据
func (e Entry) getShowExportData(ctx *gin.Context, showID uint64) (*showDto.ShowI18nExport, error) {
	// 查询show记录
	showList, err := e.ShowRepo.FindByFilter(ctx, &showDao.Filter{ID: showID})
	if err != nil {
		return nil, err
	}
	if len(showList) == 0 {
		return nil, nil
	}
	show := showList[0]

	// 构建导出数据
	exportItem := &showDto.ShowI18nExport{
		ID:          show.ID,
		Name:        show.Name,
		Overview:    show.Overview,
		Status:      show.Status,
		Score:       show.Score,
		ContentType: uint32(show.ContentType),
	}

	// 获取多语言数据
	if show.NameKey != "" || show.OverviewKey != "" {
		i18nKeys := make([]string, 0, 2)
		if show.NameKey != "" {
			i18nKeys = append(i18nKeys, show.NameKey)
		}
		if show.OverviewKey != "" {
			i18nKeys = append(i18nKeys, show.OverviewKey)
		}

		// 获取i18n数据
		i18nList, err := e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{Keys: i18nKeys})
		if err == nil && len(i18nList) > 0 {
			for _, i18n := range i18nList {
				if i18n.Key == show.NameKey && i18n.Value != "" {
					exportItem.NameI18n = append(exportItem.NameI18n, i18n.Value)
				} else if i18n.Key == show.OverviewKey && i18n.Value != "" {
					exportItem.OverviewI18n = append(exportItem.OverviewI18n, i18n.Value)
				}
			}
		}
	}

	// 获取类型数据（地区、年份、类型）
	// 查询ShowWithClass关联数据
	showWithClassList, err := e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
		ShowIDs: []uint64{showID},
	})
	if err == nil && len(showWithClassList) > 0 {
		// 收集field IDs
		fieldIDs := showWithClassList.GetFieldIDs()

		// 查询class_field数据
		classFields, err := e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{IDs: fieldIDs})
		if err == nil && len(classFields) > 0 {
			regionNames := make([]string, 0)
			regionIDs := make([]uint64, 0)
			yearNames := make([]string, 0)
			yearIDs := make([]uint64, 0)
			genreNames := make([]string, 0)
			genreIDs := make([]uint64, 0)

			for _, field := range classFields {
				// 根据ShowWithClass关联信息判断类型
				for _, showClass := range showWithClassList {
					if showClass.FieldID == field.ID {
						switch showClass.ClassID {
						case 2: // 地区
							regionNames = append(regionNames, field.Name)
							regionIDs = append(regionIDs, field.ID)
						case 3: // 年份
							yearNames = append(yearNames, field.Name)
							yearIDs = append(yearIDs, field.ID)
						case 5: // 类型
							genreNames = append(genreNames, field.Name)
							genreIDs = append(genreIDs, field.ID)
						}
						break
					}
				}
			}

			if len(regionNames) > 0 {
				exportItem.Region = strings.Join(regionNames, ",")
				exportItem.RegionIDs = regionIDs
			}
			if len(yearNames) > 0 {
				exportItem.Year = strings.Join(yearNames, ",")
				exportItem.YearIDs = yearIDs
			}
			if len(genreNames) > 0 {
				exportItem.Genre = strings.Join(genreNames, ",")
				exportItem.GenreIDs = genreIDs
			}
		}
	}

	return exportItem, nil
}

// convertSingleShowToVikingDBData 将单个show数据转换为VikingDB格式
func (e Entry) convertSingleShowToVikingDBData(ctx context.Context, show *showDto.ShowI18nExport, cfg *config.VikingDB, embeddingClient *vikingdb.VikingDBService) ([]vikingdb.Data, error) {
	// 跳过空数据
	if show.Name == "" && show.Overview == "" && len(show.NameI18n) == 0 && len(show.OverviewI18n) == 0 {
		return nil, nil
	}

	// 合并文本内容
	combinedText := e.mergeShowTextContent(show, cfg)

	// 创建文档字段
	fields := map[string]interface{}{
		"id":            show.ID,
		"show_id":       show.ID,
		"name":          show.Name,
		"name_i18n":     show.NameI18n,
		"overview":      show.Overview,
		"overview_i18n": show.OverviewI18n,
		"text":          combinedText,
		"document_type": "merged",
		"created_at":    time.Now().Format(time.RFC3339),
		// 基础字段
		"status":       show.Status,
		"score":        show.Score,
		"content_type": show.ContentType,
		// 标量字段及其ID列表
		"region":     append(show.RegionI18n, show.Region),
		"region_ids": show.RegionIDs,
		"year":       append(show.YearI18n, show.Year),
		"year_ids":   show.YearIDs,
		"genre":      append(show.GenreI18n, show.Genre),
		"genre_ids":  show.GenreIDs,
	}

	// 获取向量（如果需要）
	if cfg.NeedEmbed && embeddingClient != nil {
		log.Printf("正在获取剧集 %d 的向量...", show.ID)

		// 获取向量
		result, err := e.getEmbeddingForText(ctx, combinedText, embeddingClient, cfg)
		if err != nil {
			log.Printf("警告：获取剧集 %d 的向量失败: %v，将使用测试向量", show.ID, err)
			// 使用测试向量
			fields["text_vector"] = generateTestVector(1024)
		} else {
			// 使用真实向量
			if result.DenseVector != nil {
				fields["text_vector"] = convertToBase64Vector(result.DenseVector)
			} else {
				fields["text_vector"] = generateTestVector(1024)
			}

			// 添加稀疏向量（如果有）
			if cfg.UseSparse && result.SparseVector != nil {
				fields["text_sparse_vector"] = convertSparseVectorToStringKeys(result.SparseVector)
				log.Printf("剧集 %d: 使用 API 返回的稀疏向量 (%d 个元素)", show.ID, len(result.SparseVector))
			}
		}
	}

	// 如果没有从API获取到稀疏向量，但需要稀疏向量，则生成
	if cfg.UseSparse && fields["text_sparse_vector"] == nil {
		fields["text_sparse_vector"] = e.generateShowSparseVector(show)
		log.Printf("剧集 %d: 使用生成的稀疏向量", show.ID)
	}

	return []vikingdb.Data{{Fields: fields}}, nil
}

// mergeShowTextContent 合并show的文本内容
func (e Entry) mergeShowTextContent(show *showDto.ShowI18nExport, cfg *config.VikingDB) string {
	var parts []string

	// 根据配置的策略合并文本
	switch cfg.TextMergeStrategy {
	case "weighted":
		// 标题重复多次以增加权重
		titleWeight := int(cfg.TitleWeight)
		if titleWeight <= 0 {
			titleWeight = 2
		}
		for i := 0; i < titleWeight; i++ {
			if show.Name != "" {
				parts = append(parts, show.Name)
			}
		}
		// 描述添加一次
		if show.Overview != "" {
			parts = append(parts, show.Overview)
		}
		// 多语言内容
		if len(show.NameI18n) > 0 {
			parts = append(parts, strings.Join(show.NameI18n, " | "))
		}
		if len(show.OverviewI18n) > 0 {
			parts = append(parts, strings.Join(show.OverviewI18n, " | "))
		}

	case "structured":
		if show.Name != "" {
			parts = append(parts, "[TITLE] "+show.Name)
		}
		if show.Overview != "" {
			parts = append(parts, "[DESCRIPTION] "+show.Overview)
		}
		if len(show.NameI18n) > 0 {
			parts = append(parts, "[I18N_TITLE] "+strings.Join(show.NameI18n, " | "))
		}
		if len(show.OverviewI18n) > 0 {
			parts = append(parts, "[I18N_DESCRIPTION] "+strings.Join(show.OverviewI18n, " | "))
		}

	default: // simple
		if show.Name != "" {
			parts = append(parts, show.Name)
		}
		if show.Overview != "" {
			parts = append(parts, show.Overview)
		}
		if len(show.NameI18n) > 0 {
			parts = append(parts, strings.Join(show.NameI18n, " | "))
		}
		if len(show.OverviewI18n) > 0 {
			parts = append(parts, strings.Join(show.OverviewI18n, " | "))
		}
	}

	return strings.Join(parts, " ")
}

// getEmbeddingForText 获取文本的向量表示
func (e Entry) getEmbeddingForText(ctx context.Context, text string, client *vikingdb.VikingDBService, cfg *config.VikingDB) (*VectorEmbeddingResult, error) {
	// 构建请求数据
	rawData := vikingdb.RawData{
		DataType: "text",
		Text:     text,
	}

	// 设置模型参数
	params := map[string]interface{}{
		"return_token_usage": true,
	}
	if cfg.UseSparse {
		params["return_sparse"] = true
	}

	embModel := vikingdb.EmbModel{
		ModelName: cfg.ModelName,
		Params:    params,
	}

	// 调用 EmbeddingV2 API
	results, err := client.EmbeddingV2(embModel, []vikingdb.RawData{rawData})
	if err != nil {
		return nil, fmt.Errorf("调用 EmbeddingV2 失败: %v", err)
	}

	// 解析结果
	if results == nil {
		return nil, fmt.Errorf("EmbeddingV2 返回空结果")
	}

	// 提取 data 字段
	dataInterface, ok := results["data"]
	if !ok {
		return nil, fmt.Errorf("EmbeddingV2 响应中缺少 data 字段")
	}

	dataList, ok := dataInterface.([]interface{})
	if !ok || len(dataList) == 0 {
		return nil, fmt.Errorf("data 字段格式错误或为空")
	}

	// 提取第一个结果
	itemMap, ok := dataList[0].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("结果格式错误")
	}

	result := &VectorEmbeddingResult{}

	// 提取稠密向量
	if denseVectorInterface, ok := itemMap["dense_vector"]; ok {
		denseVector, err := convertInterfaceToFloat32Array(denseVectorInterface)
		if err != nil {
			return nil, fmt.Errorf("向量格式错误: %v", err)
		}
		result.DenseVector = denseVector
	}

	// 提取稀疏向量（如果存在）
	if cfg.UseSparse {
		if sparseVectorInterface, ok := itemMap["sparse_vector"]; ok {
			sparseVector, err := convertInterfaceToSparseVector(sparseVectorInterface)
			if err != nil {
				log.Printf("警告：稀疏向量解析失败: %v", err)
			} else {
				result.SparseVector = sparseVector
			}
		}
	}

	return result, nil
}

// generateShowSparseVector 生成show的稀疏向量
func (e Entry) generateShowSparseVector(show *showDto.ShowI18nExport) map[string]float64 {
	sparseVector := make(map[string]float64)

	// 基于内容生成关键词权重
	if show.Name != "" {
		sparseVector["title"] = 0.9
		sparseVector["name"] = 0.8
	}
	if show.Overview != "" {
		sparseVector["description"] = 0.7
		sparseVector["overview"] = 0.6
	}
	if len(show.NameI18n) > 0 {
		sparseVector["i18n"] = 0.5
		sparseVector["multilingual"] = 0.4
	}

	// 内容类型相关
	switch show.ContentType {
	case 1: // Movie
		sparseVector["movie"] = 0.8
		sparseVector["film"] = 0.7
	case 2: // TV
		sparseVector["tv"] = 0.8
		sparseVector["series"] = 0.7
	case 3: // Comic
		sparseVector["comic"] = 0.8
		sparseVector["anime"] = 0.7
	}

	// 状态相关
	switch show.Status {
	case 0: // 禁用
		sparseVector["disabled"] = 0.6
	case 1: // 启用
		sparseVector["active"] = 0.6
	}

	// 通用权重
	sparseVector["show"] = 0.8
	sparseVector["content"] = 0.6

	return sparseVector
}

// 辅助函数

func generateTestVector(dimension int) string {
	floatArray := make([]float32, dimension)
	for j := range floatArray {
		floatArray[j] = 0.124135132531424
	}

	packedData := make([]byte, dimension*4)
	for j, v := range floatArray {
		binary.LittleEndian.PutUint32(packedData[j*4:], math.Float32bits(v))
	}
	return base64.StdEncoding.EncodeToString(packedData)
}

func convertToBase64Vector(embedding []float32) string {
	packedData := make([]byte, len(embedding)*4)
	for j, v := range embedding {
		binary.LittleEndian.PutUint32(packedData[j*4:], math.Float32bits(v))
	}
	return base64.StdEncoding.EncodeToString(packedData)
}

func convertInterfaceToFloat32Array(data interface{}) ([]float32, error) {
	// 尝试直接转换
	if floatArray, ok := data.([]float32); ok {
		return floatArray, nil
	}

	// 尝试转换为 []interface{} 然后逐个转换
	if interfaceArray, ok := data.([]interface{}); ok {
		result := make([]float32, len(interfaceArray))
		for i, v := range interfaceArray {
			switch val := v.(type) {
			case float32:
				result[i] = val
			case float64:
				result[i] = float32(val)
			case int:
				result[i] = float32(val)
			case int64:
				result[i] = float32(val)
			default:
				return nil, fmt.Errorf("无法转换索引 %d 的值: %T", i, v)
			}
		}
		return result, nil
	}

	// 尝试转换为 []float64
	if float64Array, ok := data.([]float64); ok {
		result := make([]float32, len(float64Array))
		for i, v := range float64Array {
			result[i] = float32(v)
		}
		return result, nil
	}

	return nil, fmt.Errorf("无法将 %T 类型转换为 float32 数组", data)
}

func convertInterfaceToSparseVector(data interface{}) (map[int64]float64, error) {
	// 尝试转换为 map[string]interface{}
	if mapData, ok := data.(map[string]interface{}); ok {
		result := make(map[int64]float64)
		for k, v := range mapData {
			// 将 string 键转换为 int64
			var key int64
			_, err := fmt.Sscanf(k, "%d", &key)
			if err != nil {
				return nil, fmt.Errorf("无法转换键 %s 为 int64: %v", k, err)
			}

			// 转换值为 float64
			switch val := v.(type) {
			case float64:
				result[key] = val
			case float32:
				result[key] = float64(val)
			case int:
				result[key] = float64(val)
			case int64:
				result[key] = float64(val)
			default:
				return nil, fmt.Errorf("无法转换值 %v (类型 %T) 为 float64", v, v)
			}
		}
		return result, nil
	}

	// 如果已经是正确的类型
	if sparseVector, ok := data.(map[int64]float64); ok {
		return sparseVector, nil
	}

	return nil, fmt.Errorf("无法将 %T 类型转换为稀疏向量", data)
}

func convertSparseVectorToStringKeys(sparseVector map[int64]float64) map[string]float64 {
	result := make(map[string]float64)
	for k, v := range sparseVector {
		result[fmt.Sprintf("%d", k)] = v
	}
	return result
}

func generateDocumentID(showID uint64, contentType string) string {
	content := fmt.Sprintf("%d_%s", showID, contentType)
	hash := md5.Sum([]byte(content))
	return hex.EncodeToString(hash[:])
}
