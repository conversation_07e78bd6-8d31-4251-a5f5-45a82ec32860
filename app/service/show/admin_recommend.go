package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	i18nDao "vlab/app/dao/content_i18n"
	imageDao "vlab/app/dao/content_image"
	recommendDao "vlab/app/dao/content_recommend"
	showDao "vlab/app/dao/content_show"
	channelDao "vlab/app/dao/resource_channel"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func (e Entry) AdminRecommendList(ctx *gin.Context, req *showDto.AdminRecommendListReq) (resp *showDto.AdminRecommendListResp, err error) {
	resp = &showDto.AdminRecommendListResp{}
	resp.List = make([]*showDto.AdminRecommend, 0)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	count, list, err := e.RecommendRepo.DataPageList(ctx, &recommendDao.Filter{
		Status:    req.Status,
		ChannelID: req.ChannelID,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return
	}
	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	var (
		showIDs        = list.GetShowIDs()
		showMap        = make(map[uint64]*showDao.Show)
		showEpisodeMap = make(map[uint64]int64)

		imageIDs = list.GetImageIDs()
		images   = make(imageDao.ImageList, 0)
		imageMap = make(map[uint64]*imageDao.Image)

		channelIDs = make([]uint64, 0, len(list))
		channelMap = make(map[uint64]*showDto.ChannelBase)
	)

	// 获取所有的channel_id
	channelIDs = list.GetChannelIDs()

	showList, err := e.ShowRepo.FindByFilter(ctx, &showDao.Filter{IDs: showIDs})
	if err != nil {
		return nil, err
	}

	showMap = showList.GetMap()

	// 批量查询channel信息
	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
			IDS: channelIDs,
		})
		if err == nil && len(channelList) > 0 {
			channelMap = ConvertChannelListToMap(channelList)
		}
	}

	showEpisodeMap, err = e.getAdminShowEpisodeInfo(ctx, showIDs)
	if err != nil {
		return nil, err
	}

	if len(imageIDs) > 0 {
		images, err = e.ImageRepo.FindByFilter(ctx, &imageDao.Filter{IDs: imageIDs})
		if err != nil {
			return nil, err
		}
		imageMap = images.GetMap()
	}

	for _, v := range list {
		item := &showDto.AdminRecommend{
			ID:                v.ID,
			Status:            v.Status,
			Image:             nil,
			Name:              v.Name,
			NameKey:           v.NameKey,
			RecommendNameI18n: make([]*dao.I18n, 0),
			Channel:           GetChannelFromMap(channelMap, v.ChannelID),
		}

		if show, ok := showMap[v.ShowID]; ok {
			item.Show = &showDto.ShowBase{
				ID:           show.ID,
				Posters:      make([]*showDto.ImageBase, 0),
				Name:         show.Name,
				OriginalName: show.Name,
				Overview:     show.Overview,
				Genres:       make([]*showDto.GenreBase, 0),
				Score:        show.Score,
				AirDate:      show.AirDate,
				//NumberOfSeasons:  0,
				NumberOfEpisodes: 0,
				ContentType:      uint32(show.ContentType),
				InProduction:     uint32(show.InProduction),
				Franchise:        &showDto.FranchiseBase{},
			}
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				item.Show.NumberOfEpisodes = uint32(episodeCount)
			}
		}

		if image, ok := imageMap[v.ImageID]; ok {
			item.Image = &showDto.ImageBase{
				ID:          image.ID,
				AspectRatio: image.AspectRatio,
				Width:       image.Width,
				Height:      image.Height,
				FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
				Iso6391:     image.ISO_639_1,
				AspectType:  image.SetAspectType(),
			}
		}

		resp.List = append(resp.List, item)
	}

	return
}

func (e Entry) AdminRecommendDetail(ctx *gin.Context, req *showDto.AdminRecommendDetailReq) (resp *showDto.AdminRecommendDetailResp, err error) {
	resp = &showDto.AdminRecommendDetailResp{}
	resp.AdminRecommend = &showDto.AdminRecommend{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssImageHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	pop, err := e.RecommendRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	resp.AdminRecommend.ID = pop.ID
	resp.AdminRecommend.Name = pop.Name
	resp.AdminRecommend.NameKey = pop.NameKey
	resp.Status = pop.Status

	// 查询channel信息
	if pop.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, pop.ChannelID)
		if err == nil && channelInfo != nil {
			resp.AdminRecommend.Channel = &showDto.ChannelBase{
				ID:   channelInfo.ID,
				Name: channelInfo.Name,
			}
		}
	}

	show, err := e.ShowRepo.FetchByID(ctx, pop.ShowID)
	if err != nil {
		return nil, err
	}

	if pop.ImageID > 0 {
		image, err := e.ImageRepo.FetchByID(ctx, pop.ImageID)
		if err != nil {
			return nil, err
		}
		resp.AdminRecommend.Image = &showDto.ImageBase{
			ID:          image.ID,
			AspectRatio: image.AspectRatio,
			Width:       image.Width,
			Height:      image.Height,
			FilePath:    image.GetFilePath(channelKeyInfo.OssImageHost),
			Iso6391:     image.ISO_639_1,
			AspectType:  image.SetAspectType(),
		}
	}

	// 计算每个show下面有多少episode
	showEpisodeMap, err := e.getAdminShowEpisodeInfo(ctx, []uint64{show.ID})
	if err != nil {
		return nil, err
	}

	resp.AdminRecommend.Show = &showDto.ShowBase{
		ID:           show.ID,
		Posters:      make([]*showDto.ImageBase, 0),
		Name:         show.Name,
		OriginalName: show.Name,
		Overview:     show.Overview,
		Genres:       make([]*showDto.GenreBase, 0),
		Score:        show.Score,
		AirDate:      show.AirDate,
		//NumberOfSeasons:  0,
		NumberOfEpisodes: 0,
		ContentType:      uint32(show.ContentType),
		InProduction:     uint32(show.InProduction),
		Franchise:        &showDto.FranchiseBase{},
	}
	if episodeCount, ok := showEpisodeMap[show.ID]; ok {
		resp.AdminRecommend.Show.NumberOfEpisodes = uint32(episodeCount)
	}

	return
}

func (e Entry) AdminRecommendCreate(ctx *gin.Context, req *showDto.AdminRecommendCreateReq) (resp *showDto.AdminRecommendCreateResp, err error) {
	resp = &showDto.AdminRecommendCreateResp{}

	var (
		pop = &recommendDao.Recommend{
			ShowID:    req.ShowID,
			Name:      req.Name,
			Order:     req.Order,
			NameKey:   req.NameKey,
			Status:    uint32(dbs.StatusEnable),
			ChannelID: req.ChannelID,
		}

		popID uint64

		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	if req.Image != nil && req.Image.FilePath != "" {
		imageID, err := e.ImageRepo.CreateWithTx(ctx, tx, &imageDao.Image{
			AspectRatio: req.Image.AspectRatio,
			Width:       req.Image.Width,
			Height:      req.Image.Height,
			FilePath:    dbs.OssFilePath(req.Image.FilePath),
			ISO_639_1:   req.Image.Iso6391,
			AspectType:  req.Image.AspectType,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		pop.ImageID = imageID
	}

	popID, err = e.RecommendRepo.CreateWithTx(ctx, tx, pop)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{Key: req.NameKey}, map[string]interface{}{
		"table":  "content_recommend",
		"column": "name",
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	resp.ID = popID
	tx.Commit()

	return
}

func (e Entry) AdminRecommendUpdate(ctx *gin.Context, req *showDto.AdminRecommendUpdateReq) (resp *showDto.AdminRecommendUpdateResp, err error) {
	resp = &showDto.AdminRecommendUpdateResp{}

	pop, err := e.RecommendRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var (
		updateMap = map[string]interface{}{
			"show_id": req.ShowID,
		}

		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	if req.Image != nil {
		imageID, err := e.ImageRepo.Create(ctx, &imageDao.Image{
			AspectRatio: req.Image.AspectRatio,
			Width:       req.Image.Width,
			Height:      req.Image.Height,
			FilePath:    dbs.OssFilePath(req.Image.FilePath),
			ISO_639_1:   req.Image.Iso6391,
			AspectType:  req.Image.AspectType,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		updateMap["image_id"] = imageID
	}

	if req.Name != pop.Name {
		updateMap["name"] = req.Name
	}
	if req.NameKey != pop.NameKey {
		updateMap["name_key"] = req.NameKey
	}
	if req.Order != pop.Order {
		updateMap["order"] = req.Order
	}

	updateMap["channel_id"] = req.ChannelID

	err = e.RecommendRepo.UpdateMapByIDWithTx(ctx, tx, pop.ID, updateMap)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	err = e.I18nRepo.UpdateMapByFilterWithTx(ctx, tx, &i18nDao.Filter{Key: req.NameKey}, map[string]interface{}{
		"table":  "content_recommend",
		"column": "name",
	})
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminRecommendUpdatePatch(ctx *gin.Context, req *showDto.AdminRecommendUpdatePatchReq) (resp *showDto.AdminRecommendUpdatePatchResp, err error) {
	resp = &showDto.AdminRecommendUpdatePatchResp{}

	pop, err := e.RecommendRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{}

	if pop.Status == uint32(dbs.StatusDisable) {
		updateMap["status"] = uint32(dbs.StatusEnable)
	} else {
		updateMap["status"] = uint32(dbs.StatusDisable)
	}

	err = e.RecommendRepo.UpdateMapByID(ctx, pop.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return

}

func (e Entry) AdminRecommendDelete(ctx *gin.Context, req *showDto.AdminRecommendDeleteReq) (resp *showDto.AdminRecommendDeleteResp, err error) {
	resp = &showDto.AdminRecommendDeleteResp{}

	pop, err := e.RecommendRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	}

	err = e.RecommendRepo.UpdateMapByID(ctx, pop.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}
