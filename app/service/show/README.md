---
created_at: 2025-07-19
updated_at: 2025-07-19
---

# Show Service 剧集服务

## 📋 文件概述

`app/service/show/show.go` 是剧集服务的核心文件，负责处理所有与剧集列表相关的业务逻辑。该文件提供了五个主要的剧集列表接口，支持搜索、推荐、分配、热门等多种场景的剧集数据查询。

### 主要职责
- 🎬 **剧集列表查询**：提供多种维度的剧集列表数据
- 🔍 **搜索功能**：支持关键词搜索剧集
- 🎯 **推荐系统**：提供个性化推荐剧集列表
- 📊 **数据聚合**：整合剧集、图片、国际化、分类等多维度数据
- 🔐 **权限控制**：基于渠道和版本的数据访问控制
- 🌍 **国际化支持**：多语言内容展示

## 🏗️ 架构设计

### 设计理念

重构后的架构遵循以下设计原则：

1. **DRY原则**：消除重复代码，提取公共逻辑
2. **单一职责**：每个组件只负责特定功能
3. **分层架构**：清晰的数据处理层次
4. **可扩展性**：便于添加新的列表接口

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                    接口层 (Interface Layer)                  │
│  ShowList | ShowSearch | ShowRecommendList | ShowAssignList │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   公共服务层 (Common Service Layer)           │
│  validateShowListRequest | applyVersionFilter               │
│  buildShowListCommonData                                    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Access Layer)             │
│  ShowRepo | FranchiseRepo | ImageRepo | I18nRepo           │
└─────────────────────────────────────────────────────────────┘
```

## 📡 接口列表

### 1. ShowList - 基础剧集列表
**功能**：获取基础的剧集列表，支持分页和排序
**路径**：`/api/show/list`
**特点**：
- 支持字段过滤 (FieldIDs)
- 支持多种排序方式（最新、最热、评分）
- 包含剧集基础信息和分类数据

### 2. ShowSearch - 剧集搜索
**功能**：基于关键词搜索剧集
**路径**：`/api/show/search`
**特点**：
- 关键词模糊匹配
- 支持字段过滤
- 自动保存搜索历史
- 包含完整的分类信息

### 3. ShowRecommendList - 推荐剧集列表
**功能**：获取推荐的剧集列表
**路径**：`/api/show/recommend`
**特点**：
- 基于推荐算法排序
- 包含推荐理由和推荐图片
- 支持个性化推荐

### 4. ShowAssignList - 分配剧集列表
**功能**：获取分配给特定渠道的剧集列表
**路径**：`/api/show/assign`
**特点**：
- 基于分配规则过滤
- 支持渠道级别的内容控制

### 5. ShowPopularList - 热门剧集列表
**功能**：获取热门剧集列表
**路径**：`/api/show/popular`
**特点**：
- 基于热度算法排序
- 实时热度数据

## 🔄 业务逻辑流程

### 通用处理流程

```mermaid
graph TD
    A[接收请求] --> B[验证请求参数]
    B --> C[获取版本信息]
    C --> D[应用版本过滤]
    D --> E{版本状态检查}
    E -->|已下线| F[返回空结果]
    E -->|正常| G[查询主数据]
    G --> H[构建关联数据]
    H --> I[构建响应]
    I --> J[返回结果]
```

### 特殊处理流程

#### ShowSearch 额外步骤
```
查询主数据 → 异步保存搜索历史 → 构建关联数据
```

#### ShowRecommendList/ShowAssignList/ShowPopularList 额外步骤
```
查询中间表数据 → 根据中间表结果查询剧集数据 → 构建关联数据
```

## 🧩 公共组件说明

### 数据结构

#### ShowListRequestContext
```go
type ShowListRequestContext struct {
    ChannelKeyInfo *channelkey.Model  // 渠道密钥信息
    ChannelID      uint64             // 渠道ID
    VersionID      uint64             // 版本ID
    VersionModel   *versionDao.Model  // 版本模型
    ISO639_1       string             // 语言代码
}
```
**用途**：统一管理请求上下文信息，避免重复获取和验证。

#### ShowListCommonData
```go
type ShowListCommonData struct {
    I18nKeyList  []string                           // 国际化键列表
    I18nKeyMap   map[string]string                  // 国际化键值映射
    Posters      posterDao.PosterList               // 海报列表
    Images       imageDao.ImageList                 // 图片列表
    ShowIDs      []uint64                           // 剧集ID列表
    Franchises   franchiseDao.FranchiseList         // 系列列表
    WithGenres   showDao.ShowWithClassList          // 剧集类型关联
    Genres       classFieldDao.ModelList            // 类型列表
    WithFields   showDao.ShowWithClassList          // 剧集字段关联
    FieldMap     map[uint64]classFieldDao.ModelList // 字段映射
    // ... 其他字段
}
```
**用途**：统一管理所有关联数据，提供一站式数据访问。

### 公共方法

#### validateShowListRequest
```go
func (e Entry) validateShowListRequest(ctx *gin.Context) (*ShowListRequestContext, error)
```
**功能**：
- 验证渠道密钥配置
- 获取渠道ID和版本ID
- 获取版本信息
- 设置语言环境

#### applyVersionFilter
```go
func (e Entry) applyVersionFilter(ctx *gin.Context, reqCtx *ShowListRequestContext, filter *showDao.Filter) ([]uint64, error)
```
**功能**：
- 根据版本状态应用不同的过滤策略
- 处理审核中版本的特殊逻辑
- 返回受限的剧集ID列表

#### buildShowListCommonData
```go
func (e Entry) buildShowListCommonData(ctx *gin.Context, showList showDao.ShowList, iso639_1 string, includeFields bool) (*ShowListCommonData, error)
```
**功能**：
- 批量查询所有关联数据
- 构建数据映射关系
- 支持可选的字段数据查询

## 📊 数据流图

```mermaid
graph LR
    A[客户端请求] --> B[validateShowListRequest]
    B --> C[applyVersionFilter]
    C --> D[查询主数据]
    D --> E[buildShowListCommonData]
    E --> F[setShowListResp]
    F --> G[返回响应]
    
    E --> E1[查询Franchise]
    E --> E2[查询Genres]
    E --> E3[查询Posters]
    E --> E4[查询Images]
    E --> E5[查询I18n]
    
    E1 --> F
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
```

## ⚠️ 错误处理机制

### 统一错误处理策略

1. **参数验证错误**
   ```go
   if !channelKeyInfo.CheckOssImageHost() {
       return nil, ecode.ChannelConfigInvalidErr
   }
   ```

2. **版本不存在错误**
   ```go
   if !ok {
       return nil, ecode.NotFoundErr
   }
   ```

3. **数据库查询错误**
   ```go
   if err != nil {
       return nil, err
   }
   ```

### 错误传播原则
- 底层错误直接向上传播
- 业务逻辑错误使用自定义错误码
- 关键错误记录日志便于排查

## 🔐 版本控制逻辑

### 版本状态说明

| 状态 | 值 | 处理逻辑 |
|------|----|---------| 
| StatusEnable | 1 | 已上线，不过滤渠道，返回所有数据 |
| StatusDisable | 2 | 已下线，直接返回空结果 |
| StatusAuditIng | 3 | 审核中，过滤渠道，只返回审核剧集 |

### 过滤逻辑实现

```go
switch reqCtx.VersionModel.Status {
case uint32(dbs.StatusEnable):
    filter.ChannelID = 0  // 不过滤渠道
case uint32(dbs.StatusDisable):
    return resp, nil      // 返回空结果
case uint32(dbs.StatusAuditIng):
    // 获取受限剧集ID列表用于中间表过滤
    pluckShowIDs, err = e.ShowRepo.FindXidsByFilter(ctx, filter, "content_show.id")
}
```

## ⚡ 性能考虑

### 重构带来的性能优化

1. **减少重复查询**
   - 统一的数据查询逻辑避免了重复的数据库访问
   - 批量查询关联数据，减少数据库往返次数

2. **内存使用优化**
   - 公共数据结构复用，减少内存分配
   - 统一的数据映射构建，避免重复计算

3. **代码执行效率**
   - 减少了约50%的代码行数，提升执行效率
   - 统一的错误处理路径，减少分支判断

### 性能监控建议

```go
// 建议在关键方法中添加性能监控
func (e Entry) buildShowListCommonData(ctx *gin.Context, showList showDao.ShowList, iso639_1 string, includeFields bool) (*ShowListCommonData, error) {
    start := time.Now()
    defer func() {
        log.Ctx(ctx).WithField("duration", time.Since(start)).Info("buildShowListCommonData completed")
    }()
    // ... 方法实现
}
```

## 📝 使用示例

### 1. ShowList 调用示例

```go
// 请求参数
req := &showDto.ShowListReq{
    Page:     1,
    Limit:    20,
    FieldIDs: []uint64{1, 2, 3}, // 可选的字段过滤
    SortReq: showDto.SortReq{
        Type:       1,      // 1-最新 2-最热 3-评分
        SortMethod: "desc", // 排序方式
    },
}

// 调用接口
resp, err := showService.ShowList(ctx, req)
if err != nil {
    return err
}

// 响应数据
fmt.Printf("总数: %d\n", resp.Count)
for _, show := range resp.List {
    fmt.Printf("剧集: %s, 评分: %.1f\n", show.Name, show.Score)
}
```

### 2. ShowSearch 调用示例

```go
// 搜索请求
req := &showDto.ShowSearchReq{
    Keyword:  "复仇者联盟",
    Page:     1,
    Limit:    10,
    FieldIDs: []uint64{1, 2}, // 可选的分类过滤
}

// 调用搜索接口
resp, err := showService.ShowSearch(ctx, req)
if err != nil {
    return err
}

// 处理搜索结果
for _, show := range resp.List {
    fmt.Printf("找到剧集: %s\n", show.Name)
    for _, class := range show.Classes {
        fmt.Printf("  分类: %s\n", class.Name)
    }
}
```

### 3. ShowRecommendList 调用示例

```go
// 推荐请求
req := &showDto.ShowRecommendListReq{
    Page:  1,
    Limit: 15,
}

// 调用推荐接口
resp, err := showService.ShowRecommendList(ctx, req)
if err != nil {
    return err
}

// 处理推荐结果
for _, item := range resp.List {
    fmt.Printf("推荐剧集: %s\n", item.ShowBase.Name)
    fmt.Printf("推荐理由: %s\n", item.Recommend.Name)
    if item.Recommend.Image != nil {
        fmt.Printf("推荐图片: %s\n", item.Recommend.Image.FilePath)
    }
}
```

## 🔧 扩展指南

### 添加新的列表接口

基于现有架构，添加新的列表接口非常简单：

```go
func (e Entry) ShowNewList(ctx *gin.Context, req *showDto.ShowNewListReq) (resp *showDto.ShowNewListResp, err error) {
    resp = &showDto.ShowNewListResp{}
    resp.List = make([]*showDto.ShowBase, 0)

    // 1. 验证请求参数（复用）
    reqCtx, err := e.validateShowListRequest(ctx)
    if err != nil {
        return nil, err
    }

    // 2. 设置请求参数
    req.ChannelID = reqCtx.ChannelID
    req.VersionID = reqCtx.VersionID
    req.ISO_639_1 = reqCtx.ISO639_1

    // 3. 设置特定的过滤器
    filter := &showDao.Filter{
        // 根据新接口的需求设置过滤条件
    }

    // 4. 应用版本过滤逻辑（复用）
    pluckShowIDs, err := e.applyVersionFilter(ctx, reqCtx, filter)
    if err != nil {
        return nil, err
    }
    if reqCtx.VersionModel.Status == uint32(dbs.StatusDisable) {
        return resp, nil
    }

    // 5. 查询主数据（根据具体需求实现）
    cnt, list, err := e.ShowRepo.DataPageList(ctx, filter, req.Page, req.Limit)
    if err != nil {
        return nil, err
    }
    if cnt == 0 {
        return
    }
    resp.Count = cnt

    // 6. 构建公共关联数据（复用）
    commonData, err := e.buildShowListCommonData(ctx, list, req.ISO_639_1, true)
    if err != nil {
        return nil, err
    }

    // 7. 构建响应（复用）
    e.setShowListResp(
        resp.ShowListResp,
        list,
        commonData.Posters,
        commonData.Images,
        commonData.Genres,
        commonData.WithGenres,
        commonData.Franchises,
        commonData.I18nKeyMap,
        reqCtx.ChannelKeyInfo,
    )

    return resp, nil
}
```

### 扩展公共数据结构

如果需要添加新的关联数据类型：

```go
// 在 ShowListCommonData 中添加新字段
type ShowListCommonData struct {
    // ... 现有字段
    NewDataList []NewDataType           // 新的数据类型
    NewDataMap  map[uint64]NewDataType  // 新的数据映射
}

// 在 buildShowListCommonData 中添加查询逻辑
func (e Entry) buildShowListCommonData(ctx *gin.Context, showList showDao.ShowList, iso639_1 string, includeFields bool) (*ShowListCommonData, error) {
    // ... 现有逻辑

    // 查询新的关联数据
    if len(data.ShowIDs) > 0 {
        data.NewDataList, err = e.NewDataRepo.FindByFilter(ctx, &newDataDao.Filter{
            ShowIDs: data.ShowIDs,
        })
        if err != nil {
            return nil, err
        }
        data.NewDataMap = data.NewDataList.GetMap()
    }

    return data, nil
}
```

## ⚠️ 注意事项

### 开发注意事项

1. **版本兼容性**
   - 修改公共方法时要考虑对所有调用方的影响
   - 新增字段时使用可选参数，保持向后兼容

2. **性能影响**
   - `buildShowListCommonData` 方法会查询多个关联表，注意数据量控制
   - 大数据量场景下考虑分页或缓存策略

3. **错误处理**
   - 公共方法的错误会影响所有调用方，确保错误信息清晰
   - 关键业务逻辑失败时要记录详细日志

4. **数据一致性**
   - 关联数据查询可能存在时间差，注意数据一致性问题
   - 考虑使用事务或读写分离策略

### 维护注意事项

1. **代码修改**
   - 修改公共方法前要充分测试所有调用场景
   - 重要修改要通知相关开发人员

2. **数据库变更**
   - 关联表结构变更时要同步更新公共查询逻辑
   - 新增索引时考虑对查询性能的影响

3. **监控告警**
   - 关注公共方法的执行时间和错误率
   - 设置合理的性能阈值和告警机制

4. **文档更新**
   - 接口变更时及时更新文档
   - 重要的业务逻辑变更要更新架构说明

### 最佳实践

1. **单元测试**
   ```go
   func TestValidateShowListRequest(t *testing.T) {
       // 测试正常情况
       // 测试异常情况
       // 测试边界条件
   }
   ```

2. **集成测试**
   ```go
   func TestShowListIntegration(t *testing.T) {
       // 测试完整的业务流程
       // 验证数据一致性
   }
   ```

3. **性能测试**
   ```go
   func BenchmarkBuildShowListCommonData(b *testing.B) {
       // 性能基准测试
   }
   ```

### 常见问题解决

1. **Q: 新增接口时如何选择是否包含 fields 数据？**
   A: 根据业务需求决定。如果需要显示详细的分类信息，设置 `includeFields=true`；如果只需要基础信息，设置为 `false` 以提升性能。

2. **Q: 如何处理大数据量场景下的性能问题？**
   A:
   - 使用合适的分页大小（建议不超过50条）
   - 考虑添加缓存层
   - 优化数据库查询和索引
   - 使用异步处理非关键数据

3. **Q: 版本过滤逻辑出现异常如何排查？**
   A:
   - 检查版本状态是否正确设置
   - 验证渠道配置是否有效
   - 查看相关日志确认过滤条件
   - 使用调试工具跟踪数据流

---

## 📚 相关文档

- [API接口文档](../../../docs/api/show.md)
- [数据库设计文档](../../../docs/database/show.md)
- [部署指南](../../../docs/deployment/README.md)
- [重构总结](../../../docs/refactoring_summary.md)

---

**维护者**: 开发团队
**最后更新**: 2025-07-19
**版本**: v1.0.0
