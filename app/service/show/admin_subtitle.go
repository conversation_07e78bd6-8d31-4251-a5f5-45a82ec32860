package show

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"vlab/app/common/dbs"
	subtitleDao "vlab/app/dao/content_subtitle"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
)

func (e Entry) AdminSubtitleList(ctx *gin.Context, req *showDto.AdminSubtitleListReq) (resp *showDto.AdminSubtitleListResp, err error) {
	resp = &showDto.AdminSubtitleListResp{}
	resp.List = make([]*showDto.AdminSubtitle, 0)

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssSubtitleHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	count, list, err := e.SubtitleRepo.DataPageList(ctx, &subtitleDao.Filter{
		EpisodeID: req.EpisodeID,
		VideoID:   req.VideoID,
	}, req.Page, req.Limit) // 视频下的字幕数量不会太多，所以这里直接查出所有的字幕
	if err != nil {
		return nil, err
	}
	resp.Count = count

	if len(list) == 0 {
		return resp, nil
	}

	for _, v := range list {
		subtitle := &showDto.AdminSubtitle{
			ID:        v.ID,
			EpisodeID: v.EpisodeID,
			VideoID:   v.VideoID,
			ISO_639_1: v.ISO_639_1,
			FilePath:  v.GetFilePath(channelKeyInfo.OssSubtitleHost),
		}
		resp.List = append(resp.List, subtitle)
	}

	return
}

func (e Entry) AdminSubtitleDetail(ctx *gin.Context, req *showDto.AdminSubtitleDetailReq) (resp *showDto.AdminSubtitleDetailResp, err error) {
	resp = &showDto.AdminSubtitleDetailResp{}

	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, true)
	if !channelKeyInfo.CheckOssSubtitleHost() {
		return nil, ecode.ChannelConfigInvalidErr
	}

	var (
		subtitle = &subtitleDao.Subtitle{}
	)

	subtitle, err = e.SubtitleRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	resp.ID = subtitle.ID
	resp.EpisodeID = subtitle.EpisodeID
	resp.VideoID = subtitle.VideoID
	resp.ISO_639_1 = subtitle.ISO_639_1
	resp.FilePath = subtitle.GetFilePath(channelKeyInfo.OssSubtitleHost)

	return
}

func (e Entry) AdminSubtitleCreate(ctx *gin.Context, req *showDto.AdminSubtitleCreateReq) (resp *showDto.AdminSubtitleCreateResp, err error) {
	resp = &showDto.AdminSubtitleCreateResp{}

	var (
		subtitle = &subtitleDao.Subtitle{
			EpisodeID: req.EpisodeID,
			VideoID:   req.VideoID,
			ISO_639_1: req.ISO_639_1,
			FilePath:  dbs.OssFilePath(req.FilePath),
		}
		subtitleID uint64

		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	//err = e.SubtitleRepo.UpdateMapByFilterWithTx(ctx, tx, &subtitleDao.Filter{
	//	EpisodeID: req.EpisodeID,
	//	VideoID:   req.VideoID,
	//}, map[string]interface{}{
	//	dbs.SoftDelField.String(): dbs.True,
	//})
	//if err != nil {
	//	tx.Rollback()
	//	return nil, err
	//}

	subtitleID, err = e.SubtitleRepo.CreateOrUpdateWithTx(ctx, tx, subtitle)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	resp.ID = subtitleID

	return
}

func (e Entry) AdminSubtitleCreateBatch(ctx *gin.Context, req *showDto.AdminSubtitleCreateBatchReq) (resp *showDto.AdminSubtitleCreateBatchResp, err error) {
	resp = &showDto.AdminSubtitleCreateBatchResp{}

	var (
		subtitleList = make([]*subtitleDao.Subtitle, 0)
		tx           = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	for _, v := range req.List {
		subtitle := &subtitleDao.Subtitle{
			EpisodeID: req.EpisodeID,
			VideoID:   req.VideoID,
			ISO_639_1: v.ISO_639_1,
			FilePath:  dbs.OssFilePath(v.FilePath),
		}
		subtitleList = append(subtitleList, subtitle)
	}

	//err = e.SubtitleRepo.UpdateMapByFilterWithTx(ctx, tx, &subtitleDao.Filter{
	//	EpisodeID: req.EpisodeID,
	//	VideoID:   req.VideoID,
	//}, map[string]interface{}{
	//	dbs.SoftDelField.String(): dbs.True,
	//})
	//if err != nil {
	//	tx.Rollback()
	//	return nil, err
	//}

	err = e.SubtitleRepo.BatchCreateOrUpdateWithTx(ctx, tx, subtitleList)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminSubtitleBatchImport(ctx *gin.Context, req []*showDto.AdminSubtitleCreateBatchReq, txOption ...*gorm.DB) (err error) {
	var (
		subtitleList = make([]*subtitleDao.Subtitle, 0)
		tx           *gorm.DB
		isNewTx      bool
	)

	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		isNewTx = true
	}

	for _, v := range req {
		for _, item := range v.List {
			subtitle := &subtitleDao.Subtitle{
				EpisodeID: v.EpisodeID,
				VideoID:   v.VideoID,
				ISO_639_1: item.ISO_639_1,
				FilePath:  dbs.OssFilePath(item.FilePath),
			}
			subtitleList = append(subtitleList, subtitle)
		}
	}

	err = e.SubtitleRepo.BatchCreateOrUpdateWithTx(ctx, tx, subtitleList)
	if err != nil {
		tx.Rollback()
		return err
	}

	if isNewTx {
		tx.Commit()
	}

	return nil
}

func (e Entry) AdminSubtitleUpdate(ctx *gin.Context, req *showDto.AdminSubtitleUpdateReq) (resp *showDto.AdminSubtitleUpdateResp, err error) {
	resp = &showDto.AdminSubtitleUpdateResp{}

	var (
		updateMap map[string]interface{}
		model     = &subtitleDao.Subtitle{}
	)

	model, err = e.SubtitleRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if model == nil || model.ID == 0 {
		return nil, ecode.NotFoundErr
	}

	if t := req.EpisodeID; t != 0 && t != model.EpisodeID {
		updateMap["episode_id"] = t
	}
	if t := req.VideoID; t != 0 && t != model.VideoID {
		updateMap["video_id"] = t
	}
	if t := req.ISO_639_1; t != "" && t != model.ISO_639_1 {
		updateMap["iso_639_1"] = t
	}
	if t := req.FilePath; t != "" && t != string(model.FilePath) {
		updateMap["file_path"] = t
	}

	if len(updateMap) == 0 {
		return
	}

	var (
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	err = e.SubtitleRepo.UpdateMapByFilterWithTx(ctx, tx, &subtitleDao.Filter{
		ID: req.ID,
	}, updateMap)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return
}

func (e Entry) AdminSubtitleDelete(ctx *gin.Context, req *showDto.AdminSubtitleDeleteReq) (resp *showDto.AdminSubtitleDeleteResp, err error) {
	resp = &showDto.AdminSubtitleDeleteResp{}

	var (
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	err = e.SubtitleRepo.DeleteByIDWithTx(ctx, tx, req.ID)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	return
}
