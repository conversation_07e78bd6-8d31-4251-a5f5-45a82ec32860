package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	popularDao "vlab/app/dao/content_popular"
	showDao "vlab/app/dao/content_show"
	channelDao "vlab/app/dao/resource_channel"
	showDto "vlab/app/dto/show"
)

func (e Entry) AdminPopularList(ctx *gin.Context, req *showDto.AdminPopularListReq) (resp *showDto.AdminPopularListResp, err error) {
	resp = &showDto.AdminPopularListResp{}
	resp.List = make([]*showDto.AdminPopular, 0)

	count, list, err := e.PopularRepo.DataPageList(ctx, &popularDao.Filter{
		Status:    req.Status,
		ChannelID: req.ChannelID,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if count == 0 {
		return
	}
	resp.Count = count
	if len(list) == 0 {
		return resp, nil
	}

	var (
		showIDs        = list.GetShowIDs()
		showMap        = make(map[uint64]*showDao.Show)
		showEpisodeMap = make(map[uint64]int64)
		channelIDs     = make([]uint64, 0, len(list))
		channelMap     = make(map[uint64]*showDto.ChannelBase)
	)

	// 获取所有的channel_id
	channelIDs = list.GetChannelIDs()

	showList, err := e.ShowRepo.FindByFilter(ctx, &showDao.Filter{IDs: showIDs})
	if err != nil {
		return nil, err
	}

	showMap = showList.GetMap()

	// 批量查询channel信息
	if len(channelIDs) > 0 {
		channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
			IDS: channelIDs,
		})
		if err == nil && len(channelList) > 0 {
			channelMap = ConvertChannelListToMap(channelList)
		}
	}

	// 计算每个show下面有多少episode
	showEpisodeMap, err = e.getAdminShowEpisodeInfo(ctx, showIDs)
	if err != nil {
		return nil, err
	}

	for _, v := range list {
		item := &showDto.AdminPopular{
			ID:      v.ID,
			Heat:    v.Heat,
			Channel: GetChannelFromMap(channelMap, v.ChannelID),
			Status:  v.Status,
		}

		if show, ok := showMap[v.ShowID]; ok {
			item.Show = &showDto.ShowBase{
				ID:           show.ID,
				Posters:      nil,
				Name:         show.Name,
				OriginalName: show.Name,
				Overview:     show.Overview,
				Genres:       nil,
				Score:        show.Score,
				AirDate:      show.AirDate,
				//NumberOfSeasons:  0,
				NumberOfEpisodes: 0,
				ContentType:      uint32(show.ContentType),
				InProduction:     uint32(show.InProduction),
				Franchise:        nil,
			}
			if episodeCount, ok := showEpisodeMap[show.ID]; ok {
				item.Show.NumberOfEpisodes = uint32(episodeCount)
			}
		}
		resp.List = append(resp.List, item)
	}

	return
}

func (e Entry) AdminPopularDetail(ctx *gin.Context, req *showDto.AdminPopularDetailReq) (resp *showDto.AdminPopularDetailResp, err error) {
	resp = &showDto.AdminPopularDetailResp{}
	resp.AdminPopular = &showDto.AdminPopular{}

	pop, err := e.PopularRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	resp.AdminPopular.ID = pop.ID
	resp.AdminPopular.Heat = pop.Heat
	resp.AdminPopular.Status = pop.Status

	// 查询channel信息
	if pop.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, pop.ChannelID)
		if err == nil && channelInfo != nil {
			resp.AdminPopular.Channel = &showDto.ChannelBase{
				ID:   channelInfo.ID,
				Name: channelInfo.Name,
			}
		}
	}

	show, err := e.ShowRepo.FetchByID(ctx, pop.ShowID)
	if err != nil {
		return nil, err
	}

	// 计算每个show下面有多少episode
	showEpisodeMap, err := e.getAdminShowEpisodeInfo(ctx, []uint64{show.ID})
	if err != nil {
		return nil, err
	}

	resp.AdminPopular.Show = &showDto.ShowBase{
		ID:           show.ID,
		Posters:      nil,
		Name:         show.Name,
		OriginalName: show.Name,
		Overview:     show.Overview,
		Genres:       nil,
		Score:        show.Score,
		AirDate:      show.AirDate,
		//NumberOfSeasons:  0,
		NumberOfEpisodes: 0,
		ContentType:      uint32(show.ContentType),
		InProduction:     uint32(show.InProduction),
		Franchise:        nil,
	}
	if episodeCount, ok := showEpisodeMap[show.ID]; ok {
		resp.AdminPopular.Show.NumberOfEpisodes = uint32(episodeCount)
	}

	return
}

func (e Entry) AdminPopularCreate(ctx *gin.Context, req *showDto.AdminPopularCreateReq) (resp *showDto.AdminPopularCreateResp, err error) {
	resp = &showDto.AdminPopularCreateResp{}

	var (
		pop = &popularDao.Popular{
			ShowID:    req.ShowID,
			Heat:      req.Heat,
			Status:    uint32(dbs.StatusEnable),
			ChannelID: req.ChannelID,
		}

		popID uint64
	)

	popID, err = e.PopularRepo.Create(ctx, pop)
	if err != nil {
		return nil, err
	}

	resp.ID = popID

	return
}

func (e Entry) AdminPopularUpdate(ctx *gin.Context, req *showDto.AdminPopularUpdateReq) (resp *showDto.AdminPopularUpdateResp, err error) {
	resp = &showDto.AdminPopularUpdateResp{}

	pop, err := e.PopularRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{
		"show_id":    req.ShowID,
		"heat":       req.Heat,
		"channel_id": req.ChannelID,
	}

	err = e.PopularRepo.UpdateMapByID(ctx, pop.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}

func (e Entry) AdminPopularUpdatePatch(ctx *gin.Context, req *showDto.AdminPopularUpdatePatchReq) (resp *showDto.AdminPopularUpdatePatchResp, err error) {
	resp = &showDto.AdminPopularUpdatePatchResp{}

	pop, err := e.PopularRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{}

	if pop.Status == uint32(dbs.StatusDisable) {
		updateMap["status"] = uint32(dbs.StatusEnable)
	} else {
		updateMap["status"] = uint32(dbs.StatusDisable)
	}

	err = e.PopularRepo.UpdateMapByID(ctx, pop.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return

}

func (e Entry) AdminPopularDelete(ctx *gin.Context, req *showDto.AdminPopularDeleteReq) (resp *showDto.AdminPopularDeleteResp, err error) {
	resp = &showDto.AdminPopularDeleteResp{}

	pop, err := e.PopularRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if pop == nil || pop.ID == 0 {
		return resp, nil
	}

	var updateMap = map[string]interface{}{
		dbs.SoftDelField.String(): dbs.True,
	}

	err = e.PopularRepo.UpdateMapByID(ctx, pop.ID, updateMap)
	if err != nil {
		return nil, err
	}

	return
}
