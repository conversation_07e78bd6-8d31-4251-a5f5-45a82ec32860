package show

import (
	"log"

	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	"vlab/app/dto"
	showDto "vlab/app/dto/show"
)

// EnhancedAdminSearchService 管理后台增强搜索服务
type EnhancedAdminSearchService struct {
	showService *Entry         // 原有的show服务
	searchDao   *dao.SearchDao // 向量搜索DAO
}

// NewEnhancedAdminSearchService 创建管理后台增强搜索服务
func NewEnhancedAdminSearchService(showService *Entry) *EnhancedAdminSearchService {
	return &EnhancedAdminSearchService{
		showService: showService,
		searchDao:   dao.NewSearchDao(),
	}
}

// EnhancedAdminShowSearch 管理后台增强的节目搜索
// 优先使用向量搜索，如果向量搜索不可用，调用者可以回退到 MySQL 查询
// MySQL 查询已经在 showService.ShowRepo.FindByFilter 中实现
func (e *EnhancedAdminSearchService) EnhancedAdminShowSearch(ctx *gin.Context, req *showDto.AdminShowListReq) ([]uint64, string, error) {
	// 如果没有搜索关键词，返回空
	if req.Name == "" {
		return nil, "", nil
	}

	// 构建向量搜索请求
	vectorReq := e.buildVectorSearchRequest(req)

	// 执行向量搜索
	vectorResults, err := e.performVectorSearch(ctx, vectorReq)
	if err != nil {
		log.Printf("Admin向量搜索失败: %v", err)
		return nil, "", err
	}

	// 如果向量搜索没有结果，返回空
	if len(vectorResults.Items) == 0 {
		log.Printf("Admin向量搜索无结果")
		return nil, "", nil
	}

	// 提取show IDs
	showIDs := make([]uint64, 0, len(vectorResults.Items))
	for _, item := range vectorResults.Items {
		showIDs = append(showIDs, item.ID)
	}

	return showIDs, vectorResults.SearchInfo.VectorRequestID, nil
}

// buildVectorSearchRequest 构建向量搜索请求
func (e *EnhancedAdminSearchService) buildVectorSearchRequest(req *showDto.AdminShowListReq) *dto.SearchRequest {
	// 构建筛选条件
	filters := dto.SearchFilters{}

	// 设置状态筛选
	if req.Status > 0 {
		if req.Status == uint32(dbs.StatusEnable) {
			filters.Status = []uint32{uint32(dbs.StatusEnable)}
		} else if req.Status == uint32(dbs.StatusDisable) {
			filters.Status = []uint32{uint32(dbs.StatusDisable)}
		}
	}

	// 设置内容类型筛选
	if req.ContentType > 0 {
		// 根据ContentType映射到相应的类型名称
		if req.ContentType == 1 {
			filters.ContentTypes = []int{1}
		} else if req.ContentType == 2 {
			filters.ContentTypes = []int{2}
		}
	}

	// 设置类型ID筛选
	if req.GenreID > 0 {
		filters.GenreIDs = append(filters.GenreIDs, req.GenreID)
	}

	if req.YearID > 0 {
		filters.YearIDs = append(filters.YearIDs, req.YearID)
	}

	if req.RegionID > 0 {
		filters.RegionIDs = append(filters.RegionIDs, req.RegionID)
	}

	// 设置系列ID筛选
	//if req.FranchiseID > 0 {
	//	// 可以通过Tags或其他方式传递
	//	filters.Tags = append(filters.Tags, e.mapFranchiseIDToTag(req.FranchiseID))
	//}

	// 构建搜索请求
	searchReq := &dto.SearchRequest{
		Query:      req.Name,
		SearchMode: "hybrid", // 使用混合搜索模式
		Language:   req.ISO_639_1,
		Page:       1,              // 向量搜索时获取所有结果，后续分页
		PageSize:   req.VectorTopK, // 使用TopK作为返回数量
		SortBy:     "relevance",
		SortOrder:  "desc",
		Filters:    filters,
		VectorParams: dto.VectorSearchParams{
			DenseWeight:  float64(req.DenseWeight),
			SparseWeight: float64(req.SparseWeight),
			TopK:         req.VectorTopK,
		},
	}

	// 如果没有设置权重，使用默认值
	if searchReq.VectorParams.DenseWeight == 0 && searchReq.VectorParams.SparseWeight == 0 {
		searchReq.VectorParams.DenseWeight = 0.5
		searchReq.VectorParams.SparseWeight = 0.5
	}

	return searchReq
}

// performVectorSearch 执行向量搜索
func (e *EnhancedAdminSearchService) performVectorSearch(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, error) {
	return e.searchDao.Search(ctx, req)
}

// mapGenreIDToName 将GenreID映射到类型名称
func (e *EnhancedAdminSearchService) mapGenreIDToName(genreID uint64) string {
	// TODO: 实际实现需要从数据库查询
	// 建议从 e.showService.GenreRepo 查询
	// genre, err := e.showService.GenreRepo.FindOne(ctx, genreID)
	// if err == nil && genre != nil {
	//     return genre.Name
	// }
	return ""
}

// mapFranchiseIDToTag 将FranchiseID映射到标签
func (e *EnhancedAdminSearchService) mapFranchiseIDToTag(franchiseID uint64) string {
	// TODO: 实际实现需要从数据库查询
	// 建议从 e.showService.FranchiseRepo 查询
	// franchise, err := e.showService.FranchiseRepo.FindOne(ctx, franchiseID)
	// if err == nil && franchise != nil {
	//     return franchise.Name
	// }
	return ""
}
