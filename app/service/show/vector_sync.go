package show

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	classFieldDao "vlab/app/dao/content_class_field"
	i18nDao "vlab/app/dao/content_i18n"
	showDao "vlab/app/dao/content_show"
	showDto "vlab/app/dto/show"
	"vlab/config"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin"
	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
)

// BatchSyncShowsToVector 批量同步show数据到向量数据库
func (e Entry) BatchSyncShowsToVector(ctx *gin.Context, shows showDao.ShowList) error {
	defer func() {
		if r := recover(); r != nil {
			// 这里可以记录更多的错误信息或执行清理操作
			log.Printf("批量同步剧集数据时发生错误: %v", r)
		}
	}()
	if len(shows) == 0 {
		return nil
	}

	// 初始化VikingDB配置
	cfg := config.VikingDBCfg
	if cfg.Host == "" {
		return fmt.Errorf("VikingDB配置不完整")
	}

	// 准备向量数据
	var dataList []vikingdb.Data

	// 逐个处理show
	for _, show := range shows {
		// 获取导出数据（包含多语言和标量字段）
		exportData, err := e.getShowExportDataFromModel(ctx, show)
		if err != nil {
			log.Printf("警告：处理剧集 %d 失败: %v", show.ID, err)
			continue
		}

		if exportData == nil {
			continue
		}

		// 转换为VikingDB格式
		vikingData, err := e.convertSingleShowToVikingDBData(context.Background(), exportData, cfg, e.SearchApi.Service)
		if err != nil {
			log.Printf("警告：转换剧集 %d 数据失败: %v", show.ID, err)
			continue
		}

		dataList = append(dataList, vikingData...)
	}

	if len(dataList) == 0 {
		return nil
	}

	// 批量上传到VikingDB
	log.Printf("批量上传 %d 个文档到VikingDB", len(dataList))

	var uploadErr error
	if cfg.UseAsyncUpload {
		uploadErr = e.SearchApi.CollectionClient.UpsertData(dataList, vikingdb.WithAsyncUpsert(true))
	} else {
		uploadErr = e.SearchApi.CollectionClient.UpsertData(dataList)
	}

	if uploadErr != nil {
		return fmt.Errorf("上传失败: %w", uploadErr)
	}

	log.Printf("成功批量上传 %d 个文档到VikingDB", len(dataList))

	return nil
}

// getShowExportDataFromModel 从show模型获取导出数据
func (e Entry) getShowExportDataFromModel(ctx *gin.Context, show *showDao.Show) (*showDto.ShowI18nExport, error) {
	// 构建导出数据
	exportItem := &showDto.ShowI18nExport{
		ID:          show.ID,
		Name:        show.Name,
		Overview:    show.Overview,
		Status:      show.Status,
		Score:       show.Score,
		ContentType: uint32(show.ContentType),
	}

	// 获取多语言数据
	if show.NameKey != "" || show.OverviewKey != "" {
		i18nKeys := make([]string, 0, 2)
		if show.NameKey != "" {
			i18nKeys = append(i18nKeys, show.NameKey)
		}
		if show.OverviewKey != "" {
			i18nKeys = append(i18nKeys, show.OverviewKey)
		}

		// 获取i18n数据
		i18nList, err := e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{Keys: i18nKeys})
		if err == nil && len(i18nList) > 0 {
			for _, i18n := range i18nList {
				if i18n.Key == show.NameKey && i18n.Value != "" {
					exportItem.NameI18n = append(exportItem.NameI18n, i18n.Value)
				} else if i18n.Key == show.OverviewKey && i18n.Value != "" {
					exportItem.OverviewI18n = append(exportItem.OverviewI18n, i18n.Value)
				}
			}
		}
	}

	// 获取类型数据（地区、年份、类型）
	showWithClassList, err := e.ShowRepo.FindClassByFilter(ctx, &showDao.ClassFilter{
		ShowIDs: []uint64{show.ID},
	})
	if err == nil && len(showWithClassList) > 0 {
		// 收集field IDs
		fieldIDs := showWithClassList.GetFieldIDs()

		// 查询class_field数据
		classFields, err := e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{IDs: fieldIDs})
		if err == nil && len(classFields) > 0 {
			regionNames := make([]string, 0)
			regionIDs := make([]uint64, 0)
			yearNames := make([]string, 0)
			yearIDs := make([]uint64, 0)
			genreNames := make([]string, 0)
			genreIDs := make([]uint64, 0)

			for _, field := range classFields {
				// 根据ShowWithClass关联信息判断类型
				for _, showClass := range showWithClassList {
					if showClass.FieldID == field.ID {
						switch showClass.ClassID {
						case 2: // 地区
							regionNames = append(regionNames, field.Name)
							regionIDs = append(regionIDs, field.ID)
						case 3: // 年份
							yearNames = append(yearNames, field.Name)
							yearIDs = append(yearIDs, field.ID)
						case 5: // 类型
							genreNames = append(genreNames, field.Name)
							genreIDs = append(genreIDs, field.ID)
						}
						break
					}
				}
			}

			if len(regionNames) > 0 {
				exportItem.Region = strings.Join(regionNames, ",")
				exportItem.RegionIDs = regionIDs
			}
			if len(yearNames) > 0 {
				exportItem.Year = strings.Join(yearNames, ",")
				exportItem.YearIDs = yearIDs
			}
			if len(genreNames) > 0 {
				exportItem.Genre = strings.Join(genreNames, ",")
				exportItem.GenreIDs = genreIDs
			}
		}
	}

	return exportItem, nil
}

// SyncRecentUpdatedShowsManual 手动同步最近更新的show数据（供API调用）
func (e Entry) SyncRecentUpdatedShowsManual(ctx *gin.Context, hours int) (*showDto.VectorSyncResp, error) {
	resp := &showDto.VectorSyncResp{
		StartTime: time.Now().Format(time.RFC3339),
	}

	// 获取时间窗口
	syncWindow := time.Duration(hours) * time.Hour
	updatedAfter := time.Now().Add(-syncWindow)

	// 查询最近更新的show数据
	filter := &showDao.Filter{
		UpdatedAfter: &updatedAfter,
		Status:       1, // 只同步启用状态的数据
	}

	shows, err := e.ShowRepo.FindByFilter(ctx, filter)
	if err != nil {
		resp.Status = "failed"
		resp.Message = fmt.Sprintf("查询更新数据失败: %v", err)
		return resp, ecode.SystemErr
	}

	resp.TotalCount = len(shows)

	if len(shows) == 0 {
		resp.Status = "success"
		resp.Message = "没有需要同步的数据"
		resp.EndTime = time.Now().Format(time.RFC3339)
		return resp, nil
	}

	// 批量同步到向量数据库
	err = e.BatchSyncShowsToVector(ctx, shows)
	if err != nil {
		resp.Status = "failed"
		resp.Message = fmt.Sprintf("同步失败: %v", err)
		resp.FailedCount = len(shows)
		return resp, ecode.SystemErr
	}

	resp.Status = "success"
	resp.Message = "同步成功"
	resp.SuccessCount = len(shows)
	resp.EndTime = time.Now().Format(time.RFC3339)

	return resp, nil
}
