package show

import (
	"github.com/gin-gonic/gin"
	"vlab/app/common/dbs"
	classDao "vlab/app/dao/content_class"
	classFieldDao "vlab/app/dao/content_class_field"
	i18nDao "vlab/app/dao/content_i18n"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
)

func (e Entry) AdminClassList(ctx *gin.Context, req *showDto.AdminClassListReq) (*showDto.AdminClassListResp, error) {
	resp := &showDto.AdminClassListResp{}
	resp.List = make([]*showDto.AdminClassListItem, 0)

	cnt, list, err := e.ClassRepo.DataPageList(ctx, &classDao.Filter{
		NotID: 1, // TODO: refactor 排序
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return resp, nil
	}
	resp.Total = uint64(cnt)

	var (
		i18nKeys    = list.GetNameKeys()
		i18nKeyList i18nDao.I18nList
		i18nKeyMap  = make(map[string][]*i18nDao.I18n)
	)

	i18nKeyList, err = e.I18nRepo.FindByFilter(ctx, &i18nDao.Filter{
		Keys: i18nKeys,
	})
	if err != nil {
		return nil, err
	}
	i18nKeyMap = i18nKeyList.GetI18nKey()

	for _, item := range list {
		if item.ID == 1 {
			// TODO: refactor 排序
			continue
		}
		i := &showDto.AdminClassListItem{
			ID:      item.ID,
			Name:    item.Name,
			NameKey: item.NameKey,
			Status:  item.Status,
		}
		if vs, ok := i18nKeyMap[item.Name]; ok {
			for _, v := range vs {
				if v.ISO_639_1 == req.ISO_639_1 {
					i.Name = v.Value
				}
			}
		}

		resp.List = append(resp.List, i)
	}

	return resp, nil
}

func (e Entry) AdminClassDetail(ctx *gin.Context, req *showDto.AdminClassDetailReq) (*showDto.AdminClassDetailResp, error) {
	resp := &showDto.AdminClassDetailResp{}

	class, err := e.ClassRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	resp.AdminClassListItem = &showDto.AdminClassListItem{
		ID:      class.ID,
		Name:    class.Name,
		NameKey: class.NameKey,
		Status:  class.Status,
	}

	fieldList, err := e.ClassFieldRepo.FindByFilter(ctx, &classFieldDao.Filter{
		ClassID: req.ID,
		Status:  dbs.StatusEnable,
	})
	if err != nil {
		return nil, err
	}

	resp.Fields = make([]*showDto.AdminClassFieldItem, 0)
	for _, field := range fieldList {
		resp.Fields = append(resp.Fields, &showDto.AdminClassFieldItem{
			ID:      field.ID,
			ClassID: field.ClassID,
			Name:    field.Name,
			NameKey: field.NameKey,
			Cover:   "",
			Desc:    "",
		})
	}

	return resp, nil
}

func (e Entry) AdminClassCreate(ctx *gin.Context, req *showDto.AdminClassCreateReq) (*showDto.AdminClassCreateResp, error) {
	resp := &showDto.AdminClassCreateResp{}

	var (
		item = &classDao.Model{
			Name:    req.Name,
			NameKey: req.NameKey,
			Status:  dbs.StatusEnable,
		}
	)

	id, err := e.ClassRepo.Create(ctx, item)
	if err != nil {
		return nil, err
	}
	resp.ID = id

	return resp, nil
}

func (e Entry) checkClassExist(ctx *gin.Context, id uint64) (class *classDao.Model, err error) {
	class, err = e.ClassRepo.FetchByID(ctx, id)
	if err != nil {
		return nil, ecode.NotFoundErr
	}
	return class, nil
}

func (e Entry) AdminClassUpdate(ctx *gin.Context, req *showDto.AdminClassUpdateReq) (*showDto.AdminClassUpdateResp, error) {
	resp := &showDto.AdminClassUpdateResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}
	if _, err := e.checkClassExist(ctx, req.ID); err != nil {
		return nil, err
	}

	var (
		updateMap = make(map[string]interface{})
	)

	if t := req.Name; t != "" {
		updateMap["name"] = t
	}
	if t := req.NameKey; t != "" {
		updateMap["name_key"] = t
	}

	if err := e.ClassRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		return nil, err
	}

	resp.ID = req.ID

	return resp, nil
}

func (e Entry) AdminClassUpdatePatch(ctx *gin.Context, req *showDto.AdminClassUpdatePatchReq) (*showDto.AdminClassUpdatePatchResp, error) {
	resp := &showDto.AdminClassUpdatePatchResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}

	var (
		updateMap = make(map[string]interface{})
		class     *classDao.Model
		err       error
	)

	if class, err = e.checkClassExist(ctx, req.ID); err != nil {
		return nil, err
	}

	switch class.Status {
	case dbs.StatusEnable:
		updateMap["status"] = dbs.StatusDisable
	case dbs.StatusDisable:
		updateMap["status"] = dbs.StatusEnable
	default:
		return resp, nil
	}

	if err = e.ClassRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		return nil, err
	}

	resp.ID = req.ID

	return resp, nil
}

func (e Entry) AdminClassDelete(ctx *gin.Context, req *showDto.AdminClassDeleteReq) (*showDto.AdminClassDeleteResp, error) {
	resp := &showDto.AdminClassDeleteResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}
	if _, err := e.checkClassExist(ctx, req.ID); err != nil {
		return nil, err
	}

	if _, err := e.ClassRepo.DeleteByFilter(ctx, &classDao.Filter{ID: req.ID}); err != nil {
		return nil, err
	}

	return resp, nil
}

func (e Entry) AdminClassFieldList(ctx *gin.Context, req *showDto.AdminClassFieldListReq) (*showDto.AdminClassFieldListResp, error) {
	resp := &showDto.AdminClassFieldListResp{}

	cnt, list, err := e.ClassFieldRepo.DataPageList(ctx, &classFieldDao.Filter{
		ClassID: req.ClassID,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return resp, nil
	}
	resp.Total = uint64(cnt)

	resp.List = make([]*showDto.AdminClassFieldItem, 0)
	for _, item := range list {
		resp.List = append(resp.List, &showDto.AdminClassFieldItem{
			ID:      item.ID,
			ClassID: item.ClassID,
			Name:    item.Name,
			NameKey: item.NameKey,
			Status:  item.Status,
		})
	}

	return resp, nil
}

func (e Entry) AdminClassFieldDetail(ctx *gin.Context, req *showDto.AdminClassFieldDetailReq) (*showDto.AdminClassFieldDetailResp, error) {
	resp := &showDto.AdminClassFieldDetailResp{}

	field, err := e.ClassFieldRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if field == nil {
		return nil, ecode.NotFoundErr
	}

	resp.AdminClassFieldItem = &showDto.AdminClassFieldItem{
		ID:      field.ID,
		ClassID: field.ClassID,
		Name:    field.Name,
		NameKey: field.NameKey,
		Status:  field.Status,
	}

	return resp, nil
}

func (e Entry) AdminClassFieldCreate(ctx *gin.Context, req *showDto.AdminClassFieldCreateReq) (*showDto.AdminClassFieldCreateResp, error) {
	resp := &showDto.AdminClassFieldCreateResp{}

	var (
		item = &classFieldDao.Model{
			Name:    req.Name,
			NameKey: req.NameKey,
			ClassID: req.ClassID,
			Status:  dbs.StatusEnable,
		}
	)

	id, err := e.ClassFieldRepo.Create(ctx, item)
	if err != nil {
		return nil, err
	}
	resp.ID = id

	return resp, nil
}

func (e Entry) AdminClassFieldUpdate(ctx *gin.Context, req *showDto.AdminClassFieldUpdateReq) (*showDto.AdminClassFieldUpdateResp, error) {
	resp := &showDto.AdminClassFieldUpdateResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}
	if _, err := e.ClassFieldRepo.FetchByID(ctx, req.ID); err != nil {
		return nil, err
	}

	var (
		updateMap = make(map[string]interface{})
	)

	if t := req.Name; t != "" {
		updateMap["name"] = t
	}
	if t := req.NameKey; t != "" {
		updateMap["name_key"] = t
	}

	if err := e.ClassFieldRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		return nil, err
	}

	resp.ID = req.ID

	return resp, nil
}

func (e Entry) AdminClassFieldUpdatePatch(ctx *gin.Context, req *showDto.AdminClassFieldUpdatePatchReq) (*showDto.AdminClassFieldUpdatePatchResp, error) {
	resp := &showDto.AdminClassFieldUpdatePatchResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}

	var (
		updateMap = make(map[string]interface{})
		field     *classFieldDao.Model
		err       error
	)

	if field, err = e.ClassFieldRepo.FetchByID(ctx, req.ID); err != nil {
		return nil, err
	}

	switch field.Status {
	case dbs.StatusEnable:
		updateMap["status"] = dbs.StatusDisable
	case dbs.StatusDisable:
		updateMap["status"] = dbs.StatusEnable
	default:
		return resp, nil
	}

	if err = e.ClassFieldRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		return nil, err
	}

	resp.ID = req.ID

	return resp, nil
}

func (e Entry) AdminClassFieldDelete(ctx *gin.Context, req *showDto.AdminClassFieldDeleteReq) (*showDto.AdminClassFieldDeleteResp, error) {
	resp := &showDto.AdminClassFieldDeleteResp{}

	if req.ID <= 0 {
		return nil, ecode.NotFoundErr
	}
	if _, err := e.ClassFieldRepo.FetchByID(ctx, req.ID); err != nil {
		return nil, err
	}

	if _, err := e.ClassFieldRepo.DeleteByFilter(ctx, &classFieldDao.Filter{ID: req.ID}); err != nil {
		return nil, err
	}

	return resp, nil
}
