package show

import (
	"sync"

	"vlab/app/api/aliyun/common"
	"vlab/app/dao"
	bannerDao "vlab/app/dao/admin_banner"
	assignDao "vlab/app/dao/content_assign"
	classDao "vlab/app/dao/content_class"
	classFieldDao "vlab/app/dao/content_class_field"
	creditDao "vlab/app/dao/content_credit"
	episodeDao "vlab/app/dao/content_episode"
	franchiseDao "vlab/app/dao/content_franchise"
	genreDao "vlab/app/dao/content_genre"
	i18nDao "vlab/app/dao/content_i18n"
	imageDao "vlab/app/dao/content_image"
	personDao "vlab/app/dao/content_person"
	popularDao "vlab/app/dao/content_popular"
	posterDao "vlab/app/dao/content_poster"
	recommendDao "vlab/app/dao/content_recommend"
	seasonDao "vlab/app/dao/content_season"
	showDao "vlab/app/dao/content_show"
	subtitleDao "vlab/app/dao/content_subtitle"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	videoCpMpsDao "vlab/app/dao/content_video_mps/cp"
	channelDao "vlab/app/dao/resource_channel"
	userDao "vlab/app/dao/user"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	showDto "vlab/app/dto/show"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	Show

	Episode

	AdminShow

	AdminEpisode

	AdminSubtitle

	AdminGenre

	AdminFranchise

	AdminPerson

	AdminI18n

	AdminPopular

	AdminRecommend

	AdminMarketBanner

	MarketBanner

	VideoMpsSrv

	AdminClass

	AdminClassField

	ClassField

	AdminAssign

	AdminRelation
}

type Show interface {
	ClassList(ctx *gin.Context, req *showDto.ClassListReq) (*showDto.ClassListResp, error)

	ShowList(ctx *gin.Context, req *showDto.ShowListReq) (*showDto.ShowListResp, error)

	ShowDetail(ctx *gin.Context, req *showDto.ShowDetailReq) (*showDto.ShowDetailResp, error)

	ShowSearch(ctx *gin.Context, req *showDto.ShowSearchReq) (*showDto.ShowSearchResp, error)

	ShowRecommendList(ctx *gin.Context, req *showDto.ShowRecommendListReq) (*showDto.ShowRecommendListResp, error)

	ShowAssignList(ctx *gin.Context, req *showDto.ShowAssignListReq) (resp *showDto.ShowAssignListResp, err error)

	ShowPopularList(ctx *gin.Context, req *showDto.ShowPopularListReq) (*showDto.ShowPopularListResp, error)

	// 导出功能
	ExportShowsWithI18n(ctx *gin.Context, req *showDto.ShowI18nExportReq) (*showDto.ShowI18nExportResp, error)

	ExportShowsWithI18nToFile(ctx *gin.Context, config *showDto.ShowI18nExportConfig) error
}

type Episode interface {
	EpisodeDetail(ctx *gin.Context, req *showDto.EpisodeDetailReq) (*showDto.EpisodeDetailResp, error)
}

type AdminI18n interface {
	AdminI18nCreate(ctx *gin.Context, req *showDto.AdminI18nCreateReq) (*showDto.AdminI18nCreateResp, error)

	AdminI18nDetail(ctx *gin.Context, req *showDto.AdminI18nDetailReq) (*showDto.AdminI18nDetailResp, error)

	GenI18nKey(ctx *gin.Context, req *showDto.GenI18nKeyReq) (resp *showDto.GenI18nKeyResp, err error)
}

type AdminShow interface {
	AdminShowList(ctx *gin.Context, req *showDto.AdminShowListReq) (*showDto.AdminShowListResp, error)

	AdminShowDetail(ctx *gin.Context, req *showDto.AdminShowDetailReq) (*showDto.AdminShowDetailResp, error)

	AdminShowCreate(ctx *gin.Context, req *showDto.AdminShowCreateReq, txOption ...*gorm.DB) (*showDto.AdminShowCreateResp, error)

	AdminShowUpdate(ctx *gin.Context, req *showDto.AdminShowUpdateReq, txOption ...*gorm.DB) (*showDto.AdminShowUpdateResp, error)

	ImportShowUpdate(ctx *gin.Context, req *showDto.AdminShowUpdateReq, txOption ...*gorm.DB) (res *showDto.AdminShowUpdateResp, err error)

	AdminShowStatusBatch(ctx *gin.Context, req *showDto.AdminShowStatusBatchReq) (res *showDto.AdminShowStatusBatchResp, err error)

	AdminShowUpdatePatch(ctx *gin.Context, req *showDto.AdminShowUpdatePatchReq) (*showDto.AdminShowUpdatePatchResp, error)

	AdminShowDelete(ctx *gin.Context, req *showDto.AdminShowDeleteReq) (*showDto.AdminShowDeleteResp, error)

	AdminShowTmdb(ctx *gin.Context, req *showDto.AdminShowTmdbReq) (*showDto.AdminShowTmdbResp, error)

	AdminShowTmdbDetail(ctx *gin.Context, req *showDto.AdminShowTmdbDetailReq) (*showDto.AdminShowTmdbDetailResp, error)

	AdminShowTmdbUrl(ctx *gin.Context, req *showDto.AdminShowTmdbUrlReq) (*showDto.AdminShowTmdbUrlResp, error)

	AdminShowVectorUpload(ctx *gin.Context, req *showDto.AdminShowVectorUploadReq) (*showDto.AdminShowVectorUploadResp, error)

	// 向量同步相关
	SyncRecentUpdatedShowsManual(ctx *gin.Context, hours int) (*showDto.VectorSyncResp, error)
	BatchSyncShowsToVector(ctx *gin.Context, shows showDao.ShowList) error
}

type AdminEpisode interface {
	AdminEpisodeList(ctx *gin.Context, req *showDto.AdminEpisodeListReq) (*showDto.AdminEpisodeListResp, error)

	AdminEpisodeDetail(ctx *gin.Context, req *showDto.AdminEpisodeDetailReq) (*showDto.AdminEpisodeDetailResp, error)

	AdminEpisodeCreate(ctx *gin.Context, req *showDto.AdminEpisodeCreateReq) (*showDto.AdminEpisodeCreateResp, error)

	AdminEpisodeCreateBatch(ctx *gin.Context, req *showDto.AdminEpisodeCreateBatchReq, txOption ...*gorm.DB) (*showDto.AdminEpisodeCreateBatchResp, error)

	AdminEpisodeImportBatch(ctx *gin.Context, req *showDto.AdminEpisodeCreateBatchReq, txOption ...*gorm.DB) (resp *showDto.AdminEpisodeCreateBatchResp, err error)

	AdminEpisodeVideoImportBatch(ctx *gin.Context, req *showDto.AdminVideoImportBatchReq) (resp *showDto.AdminVideoImportBatchResp, err error)

	AdminEpisodeUpdate(ctx *gin.Context, req *showDto.AdminEpisodeUpdateReq) (resp *showDto.AdminEpisodeUpdateResp, err error)

	AdminEpisodeUpdatePatch(ctx *gin.Context, req *showDto.AdminEpisodeUpdatePatchReq) (resp *showDto.AdminEpisodeUpdatePatchResp, err error)
}

type AdminSubtitle interface {
	AdminSubtitleList(ctx *gin.Context, req *showDto.AdminSubtitleListReq) (*showDto.AdminSubtitleListResp, error)

	AdminSubtitleDetail(ctx *gin.Context, req *showDto.AdminSubtitleDetailReq) (*showDto.AdminSubtitleDetailResp, error)

	AdminSubtitleCreate(ctx *gin.Context, req *showDto.AdminSubtitleCreateReq) (*showDto.AdminSubtitleCreateResp, error)

	AdminSubtitleCreateBatch(ctx *gin.Context, req *showDto.AdminSubtitleCreateBatchReq) (*showDto.AdminSubtitleCreateBatchResp, error)

	AdminSubtitleBatchImport(ctx *gin.Context, req []*showDto.AdminSubtitleCreateBatchReq, txOption ...*gorm.DB) (err error)

	AdminSubtitleUpdate(ctx *gin.Context, req *showDto.AdminSubtitleUpdateReq) (*showDto.AdminSubtitleUpdateResp, error)

	AdminSubtitleDelete(ctx *gin.Context, req *showDto.AdminSubtitleDeleteReq) (*showDto.AdminSubtitleDeleteResp, error)
}

type AdminGenre interface {
	AdminGenreList(ctx *gin.Context, req *showDto.AdminGenreListReq) (*showDto.AdminGenreListResp, error)

	AdminGenreDetail(ctx *gin.Context, req *showDto.AdminGenreDetailReq) (*showDto.AdminGenreDetailResp, error)

	AdminGenreCreate(ctx *gin.Context, req *showDto.AdminGenreCreateReq) (*showDto.AdminGenreCreateResp, error)

	AdminGenreUpdate(ctx *gin.Context, req *showDto.AdminGenreUpdateReq) (*showDto.AdminGenreUpdateResp, error)

	AdminGenreUpdatePatch(ctx *gin.Context, req *showDto.AdminGenreUpdatePatchReq) (*showDto.AdminGenreUpdatePatchResp, error)

	AdminGenreDelete(ctx *gin.Context, req *showDto.AdminGenreDeleteReq) (*showDto.AdminGenreDeleteResp, error)
}

type AdminFranchise interface {
	AdminFranchiseList(ctx *gin.Context, req *showDto.AdminFranchiseListReq) (*showDto.AdminFranchiseListResp, error)

	AdminFranchiseDetail(ctx *gin.Context, req *showDto.AdminFranchiseDetailReq) (*showDto.AdminFranchiseDetailResp, error)

	AdminFranchiseCreate(ctx *gin.Context, req *showDto.AdminFranchiseCreateReq) (*showDto.AdminFranchiseCreateResp, error)

	AdminFranchiseUpdate(ctx *gin.Context, req *showDto.AdminFranchiseUpdateReq) (*showDto.AdminFranchiseUpdateResp, error)

	AdminFranchiseUpdatePatch(ctx *gin.Context, req *showDto.AdminFranchiseUpdatePatchReq) (*showDto.AdminFranchiseUpdatePatchResp, error)

	AdminFranchiseDelete(ctx *gin.Context, req *showDto.AdminFranchiseDeleteReq) (*showDto.AdminFranchiseDeleteResp, error)
}

type AdminPerson interface {
	AdminPersonList(ctx *gin.Context, req *showDto.AdminPersonListReq) (*showDto.AdminPersonListResp, error)

	AdminPersonDetail(ctx *gin.Context, req *showDto.AdminPersonDetailReq) (*showDto.AdminPersonDetailResp, error)

	AdminPersonCreate(ctx *gin.Context, req *showDto.AdminPersonCreateReq) (*showDto.AdminPersonCreateResp, error)

	AdminPersonUpdate(ctx *gin.Context, req *showDto.AdminPersonUpdateReq) (*showDto.AdminPersonUpdateResp, error)

	AdminPersonUpdatePatch(ctx *gin.Context, req *showDto.AdminPersonUpdatePatchReq) (*showDto.AdminPersonUpdatePatchResp, error)

	AdminPersonDelete(ctx *gin.Context, req *showDto.AdminPersonDeleteReq) (*showDto.AdminPersonDeleteResp, error)
}

type AdminPopular interface {
	AdminPopularList(ctx *gin.Context, req *showDto.AdminPopularListReq) (*showDto.AdminPopularListResp, error)

	AdminPopularDetail(ctx *gin.Context, req *showDto.AdminPopularDetailReq) (*showDto.AdminPopularDetailResp, error)

	AdminPopularCreate(ctx *gin.Context, req *showDto.AdminPopularCreateReq) (*showDto.AdminPopularCreateResp, error)

	AdminPopularUpdate(ctx *gin.Context, req *showDto.AdminPopularUpdateReq) (*showDto.AdminPopularUpdateResp, error)

	AdminPopularUpdatePatch(ctx *gin.Context, req *showDto.AdminPopularUpdatePatchReq) (*showDto.AdminPopularUpdatePatchResp, error)

	AdminPopularDelete(ctx *gin.Context, req *showDto.AdminPopularDeleteReq) (*showDto.AdminPopularDeleteResp, error)
}

type AdminAssign interface {
	AdminAssignList(ctx *gin.Context, req *showDto.AdminAssignListReq) (*showDto.AdminAssignListResp, error)

	AdminAssignDetail(ctx *gin.Context, req *showDto.AdminAssignDetailReq) (*showDto.AdminAssignDetailResp, error)

	AdminAssignCreate(ctx *gin.Context, req *showDto.AdminAssignCreateReq) (*showDto.AdminAssignCreateResp, error)

	AdminAssignUpdate(ctx *gin.Context, req *showDto.AdminAssignUpdateReq) (*showDto.AdminAssignUpdateResp, error)

	AdminAssignUpdatePatch(ctx *gin.Context, req *showDto.AdminAssignUpdatePatchReq) (*showDto.AdminAssignUpdatePatchResp, error)

	AdminAssignDelete(ctx *gin.Context, req *showDto.AdminAssignDeleteReq) (*showDto.AdminAssignDeleteResp, error)
}

type AdminRecommend interface {
	AdminRecommendList(ctx *gin.Context, req *showDto.AdminRecommendListReq) (*showDto.AdminRecommendListResp, error)

	AdminRecommendDetail(ctx *gin.Context, req *showDto.AdminRecommendDetailReq) (*showDto.AdminRecommendDetailResp, error)

	AdminRecommendCreate(ctx *gin.Context, req *showDto.AdminRecommendCreateReq) (*showDto.AdminRecommendCreateResp, error)

	AdminRecommendUpdate(ctx *gin.Context, req *showDto.AdminRecommendUpdateReq) (*showDto.AdminRecommendUpdateResp, error)

	AdminRecommendUpdatePatch(ctx *gin.Context, req *showDto.AdminRecommendUpdatePatchReq) (*showDto.AdminRecommendUpdatePatchResp, error)

	AdminRecommendDelete(ctx *gin.Context, req *showDto.AdminRecommendDeleteReq) (*showDto.AdminRecommendDeleteResp, error)
}

type AdminMarketBanner interface {
	AdminMarketBannerList(ctx *gin.Context, req *showDto.AdminMarketBannerListReq) (*showDto.AdminMarketBannerListResp, error)

	AdminMarketBannerDetail(ctx *gin.Context, req *showDto.AdminMarketBannerDetailReq) (*showDto.AdminMarketBannerDetailResp, error)

	AdminMarketBannerCreate(ctx *gin.Context, req *showDto.AdminMarketBannerCreateReq) (*showDto.AdminMarketBannerCreateResp, error)

	AdminMarketBannerUpdate(ctx *gin.Context, req *showDto.AdminMarketBannerUpdateReq) (*showDto.AdminMarketBannerUpdateResp, error)

	AdminMarketBannerUpdatePatch(ctx *gin.Context, req *showDto.AdminMarketBannerUpdatePatchReq) (*showDto.AdminMarketBannerUpdatePatchResp, error)

	AdminMarketBannerDelete(ctx *gin.Context, req *showDto.AdminMarketBannerDeleteReq) (*showDto.AdminMarketBannerDeleteResp, error)
}

type MarketBanner interface {
	MarketBannerList(ctx *gin.Context, req *showDto.MarketBannerListReq) (*showDto.MarketBannerListResp, error)
}

type VideoMpsSrv interface {
	GetVideoSignedUrl(*gin.Context, *videoDao.Video, common.Resolution, watchVideoPv.LogType) (string, common.Resolution, int64, error)
	GetBytesVideoSignedUrl(*gin.Context, *videoDao.Video, common.Resolution, watchVideoPv.LogType) (string, common.Resolution, int64, error)
	UploadAliyunVideoUrlToBytes(*gin.Context, *videoMpsDao.Model, string) error
}

type AdminClass interface {
	AdminClassList(ctx *gin.Context, req *showDto.AdminClassListReq) (*showDto.AdminClassListResp, error)

	AdminClassDetail(ctx *gin.Context, req *showDto.AdminClassDetailReq) (*showDto.AdminClassDetailResp, error)

	AdminClassCreate(ctx *gin.Context, req *showDto.AdminClassCreateReq) (*showDto.AdminClassCreateResp, error)

	AdminClassUpdate(ctx *gin.Context, req *showDto.AdminClassUpdateReq) (*showDto.AdminClassUpdateResp, error)

	AdminClassUpdatePatch(ctx *gin.Context, req *showDto.AdminClassUpdatePatchReq) (*showDto.AdminClassUpdatePatchResp, error)

	AdminClassDelete(ctx *gin.Context, req *showDto.AdminClassDeleteReq) (*showDto.AdminClassDeleteResp, error)
}

type AdminClassField interface {
	AdminClassFieldList(ctx *gin.Context, req *showDto.AdminClassFieldListReq) (*showDto.AdminClassFieldListResp, error)

	AdminClassFieldDetail(ctx *gin.Context, req *showDto.AdminClassFieldDetailReq) (*showDto.AdminClassFieldDetailResp, error)

	AdminClassFieldCreate(ctx *gin.Context, req *showDto.AdminClassFieldCreateReq) (*showDto.AdminClassFieldCreateResp, error)

	AdminClassFieldUpdate(ctx *gin.Context, req *showDto.AdminClassFieldUpdateReq) (*showDto.AdminClassFieldUpdateResp, error)

	AdminClassFieldUpdatePatch(ctx *gin.Context, req *showDto.AdminClassFieldUpdatePatchReq) (*showDto.AdminClassFieldUpdatePatchResp, error)

	AdminClassFieldDelete(ctx *gin.Context, req *showDto.AdminClassFieldDeleteReq) (*showDto.AdminClassFieldDeleteResp, error)
}

type ClassField interface {
	ClassFieldList(ctx *gin.Context, req *showDto.ClassFieldListReq) (*showDto.ClassFieldListResp, error)
}

type AdminRelation interface {
	AdminRelationGroupList(ctx *gin.Context, req *AdminRelationGroupListReq) (*AdminRelationGroupListResp, error)

	AdminRelationGroupCreate(ctx *gin.Context, req *AdminRelationGroupCreateReq) error

	AdminRelationGroupUpdate(ctx *gin.Context, req *AdminRelationGroupUpdateReq) error

	AdminRelationGroupUpdatePatch(ctx *gin.Context, req *AdminRelationGroupUpdatePatchReq) error

	AdminRelationGroupDelete(ctx *gin.Context, id uint64) error

	AdminRelationMemberBatch(ctx *gin.Context, req *AdminRelationMemberBatchReq) error

	AdminShowRelationList(ctx *gin.Context, req *AdminShowRelationListReq) (*AdminShowRelationListResp, error)
}

type Entry struct {
	GenreRepo     genreDao.Repo
	FranchiseRepo franchiseDao.Repo
	ShowRepo      showDao.Repo
	CreditRepo    creditDao.Repo
	SeasonRepo    seasonDao.Repo
	PersonRepo    personDao.Repo
	ImageRepo     imageDao.Repo
	PosterRepo    posterDao.Repo
	EpisodeRepo   episodeDao.Repo
	I18nRepo      i18nDao.Repo

	VideoRepo    videoDao.Repo
	SubtitleRepo subtitleDao.Repo

	PopularRepo    popularDao.Repo
	AssignRepo     assignDao.Repo
	RecommendRepo  recommendDao.Repo
	VideoMpsRepo   videoMpsDao.Repo
	VideoCpMpsRepo *videoCpMpsDao.Entry
	UserRepo       *userDao.Entry

	BannerRepo bannerDao.Repo

	ClassRepo      classDao.Repo
	ClassFieldRepo classFieldDao.Repo

	ChannelRepo channelDao.Repo

	SearchApi *dao.SearchDao     // 向量搜索DAO
	RedisCli  *redis.RedisClient // Redis 客户端，用于缓存
}

var (
	defaultEntry         Server
	defaultEntryInitOnce sync.Once
)

func GetService() Server {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		VideoRepo:     videoDao.GetRepo(),
		SubtitleRepo:  subtitleDao.GetRepo(),
		GenreRepo:     genreDao.GetRepo(),
		FranchiseRepo: franchiseDao.GetRepo(),
		I18nRepo:      i18nDao.GetRepo(),
		EpisodeRepo:   episodeDao.GetRepo(),
		ImageRepo:     imageDao.GetRepo(),
		PersonRepo:    personDao.GetRepo(),
		ShowRepo:      showDao.GetRepo(),
		CreditRepo:    creditDao.GetRepo(),
		SeasonRepo:    seasonDao.GetRepo(),
		PosterRepo:    posterDao.GetRepo(),

		PopularRepo:    popularDao.GetRepo(),
		RecommendRepo:  recommendDao.GetRepo(),
		VideoMpsRepo:   videoMpsDao.GetRepo(),
		VideoCpMpsRepo: videoCpMpsDao.GetRepo(),
		UserRepo:       userDao.GetRepo(),
		BannerRepo:     bannerDao.GetRepo(),
		ClassRepo:      classDao.GetRepo(),
		ClassFieldRepo: classFieldDao.GetRepo(),
		AssignRepo:     assignDao.GetRepo(),
		ChannelRepo:    channelDao.GetRepo(),

		SearchApi: dao.NewSearchDao(),
		RedisCli:  redis.GetRedisClient(), // 初始化 Redis 客户端
	}
}
