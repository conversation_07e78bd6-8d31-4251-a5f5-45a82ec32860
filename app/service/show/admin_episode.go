package show

import (
	"time"

	"vlab/app/api/aliyun/common"
	"vlab/app/common/dbs"
	"vlab/app/dao"
	episodeDao "vlab/app/dao/content_episode"
	videoDao "vlab/app/dao/content_video"
	videoMpsDao "vlab/app/dao/content_video_mps"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

func (e Entry) AdminEpisodeList(ctx *gin.Context, req *showDto.AdminEpisodeListReq) (resp *showDto.AdminEpisodeListResp, err error) {
	resp = &showDto.AdminEpisodeListResp{}
	resp.List = make([]*showDto.AdminEpisode, 0)

	var (
		videos   = make(videoDao.VideoList, 0)
		videoMap = make(map[uint64]videoDao.VideoList)
	)

	count, list, err := e.EpisodeRepo.DataPageList(ctx, &episodeDao.Filter{
		ShowID:        req.ShowID,
		NameLike:      req.Name,
		EpisodeNumber: req.EpisodeNumber,
		Status:        req.Status,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}
	resp.Count = count

	if len(list) == 0 {
		return resp, nil
	}

	videos, err = e.VideoRepo.FindByFilter(ctx, &videoDao.Filter{
		EpisodeIDs: list.GetIDs(),
	})
	if err != nil {
		return nil, err
	}

	videoMap = videos.GetEpisodeIDMap()

	for _, v := range list {
		episode := &showDto.AdminEpisode{
			ID:              v.ID,
			Status:          v.Status,
			ShowID:          v.ShowID,
			Name:            v.Name,
			NameKey:         v.NameKey,
			EpisodeNameI18n: make([]*dao.I18n, 0),
			EpisodeNumber:   v.EpisodeNumber,
		}
		if videos, ok := videoMap[v.ID]; ok && len(videos) > 0 {
			episode.VideoID = videos[0].ID
		}

		resp.List = append(resp.List, episode)
	}

	return
}

func (e Entry) AdminEpisodeDetail(ctx *gin.Context, req *showDto.AdminEpisodeDetailReq) (resp *showDto.AdminEpisodeDetailResp, err error) {
	resp = &showDto.AdminEpisodeDetailResp{}
	resp.AdminEpisode = &showDto.AdminEpisode{}

	var (
		episode   = &episodeDao.Episode{}
		videoList = make([]*videoDao.Video, 0)
	)

	episode, err = e.EpisodeRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	resp.AdminEpisode.ID = episode.ID
	resp.AdminEpisode.ShowID = episode.ShowID
	resp.AdminEpisode.Status = episode.Status
	resp.AdminEpisode.Name = episode.Name
	resp.AdminEpisode.NameKey = episode.NameKey
	resp.AdminEpisode.EpisodeNumber = episode.EpisodeNumber
	resp.AdminEpisode.EpisodeNameI18n = make([]*dao.I18n, 0)

	videoList, err = e.VideoRepo.FindByFilter(ctx, &videoDao.Filter{
		EpisodeID: episode.ID,
	})
	if err != nil {
		return nil, err
	}

	if len(videoList) > 0 {
		var (
			video = &showDto.AdminEpisodeVideo{
				VideoID: videoList[0].ID,
				//VideoName: videoList[0].Name,
				VideoPath: videoList[0].VideoPath,
				VideoMps:  make([]*showDto.AdminVideoMps, 0),
			}
			mps = videoMpsDao.ModelList{}
		)

		// 根据视频ID获取视频码率信息
		mps, err = e.VideoMpsRepo.FindByFilter(ctx, &videoMpsDao.Filter{
			VideoID: video.VideoID,
		})
		if err != nil {
			return nil, err
		}

		for _, mp := range mps {
			if mp.Resolution == 0 {

			}
			tmp := &showDto.AdminVideoMps{
				VideoMpsID: mp.ID,
				Resolution: uint32(mp.Resolution),
				Status:     uint32(mp.Status),
			}
			video.VideoMps = append(video.VideoMps, tmp)
		}

		resp.Video = video
	}

	return
}

func (e Entry) AdminEpisodeCreate(ctx *gin.Context, req *showDto.AdminEpisodeCreateReq) (resp *showDto.AdminEpisodeCreateResp, err error) {
	resp = &showDto.AdminEpisodeCreateResp{}

	var (
		episode = &episodeDao.Episode{
			Status:        uint32(dbs.StatusEnable),
			ShowID:        req.ShowID,
			Name:          req.Name,
			NameKey:       req.NameKey,
			EpisodeNumber: req.EpisodeNumber,
		}
		episodeID uint64
		video     = &videoDao.Video{}
		tx        = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	episodeID, err = e.EpisodeRepo.CreateWithTx(ctx, tx, episode)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	video = &videoDao.Video{
		EpisodeID: episodeID,
		Name:      req.Name,
		VideoPath: req.Video.VideoPath,
		//VideoEtag: req.Video.VideoEtag,
		PathType: req.Video.PathType,
	}

	videoParam, err := json.Marshal(req.Video)
	if err != nil {
		tx.Rollback()
		return nil, err
	}
	video.VideoParam = videoParam

	_, err = e.VideoRepo.CreateWithTx(ctx, tx, video)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	resp.ID = episode.ID

	tx.Commit()

	switch req.Video.PathType {
	case 1:
		urlList := make([]*UploadMediaByUrlItem, 0)
		for _, p := range req.Video.UrlPaths {
			urlList = append(urlList, &UploadMediaByUrlItem{
				Resolution: common.Resolution(p.Resolution),
				Url:        p.Url,
			})
		}
		err = GetVideoMpsEntity().ProducerPullUrl(ctx, video, urlList)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeCreate ProducerPullUrl")
			return nil, err
		}
	case 2:
		err = GetVideoMpsEntity().Producer(ctx, video)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeCreate Producer")
			return nil, err
		}
	}

	return

}

func (e Entry) AdminEpisodeCreateBatch(ctx *gin.Context, req *showDto.AdminEpisodeCreateBatchReq, txOption ...*gorm.DB) (resp *showDto.AdminEpisodeCreateBatchResp, err error) {
	resp = &showDto.AdminEpisodeCreateBatchResp{}

	var (
		episodeList     = make(episodeDao.EpisodeList, 0)
		videoList       = make([]*videoDao.Video, 0)
		tx              *gorm.DB
		isNewTx         bool
		episodeVideoMap = make(map[uint32]*videoDao.Video)
		urlPathMap      = make(map[string][]*showDto.UrlPathItem)
	)

	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		isNewTx = true
	}

	for _, v := range req.List {
		episode := &episodeDao.Episode{
			Status:        uint32(dbs.StatusEnable),
			ShowID:        req.ShowID,
			Name:          v.Name,
			NameKey:       v.NameKey,
			EpisodeNumber: v.EpisodeNumber,
			MappingType:   v.MappingType,
			MappingID:     v.MappingID,
		}
		episodeList = append(episodeList, episode)

		video := &videoDao.Video{
			VideoPath: v.Video.VideoPath,
			PathType:  v.Video.PathType,
			Name:      episode.Name + time.Now().String(),
		}
		urlPathMap[video.Name] = v.Video.UrlPaths

		videoParam, err := json.Marshal(v.Video)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeCreateBatch ProducerPullUrl")
		}
		video.VideoParam = videoParam

		episodeVideoMap[v.EpisodeNumber] = video
	}

	err = e.EpisodeRepo.BatchCreateWithTx(ctx, tx, episodeList)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	for _, episode := range episodeList {

		videoModel := &videoDao.Video{
			EpisodeID: episode.ID,
		}
		if video, ok := episodeVideoMap[episode.EpisodeNumber]; ok {
			videoModel.VideoPath = video.VideoPath
			//videoModel.VideoEtag = video.VideoEtag
			videoModel.Name = video.Name
			videoModel.PathType = video.PathType
		}
		videoList = append(videoList, videoModel)

	}

	err = e.VideoRepo.BatchCreateWithTx(ctx, tx, videoList)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	if isNewTx {
		tx.Commit()
	}

	for _, video := range videoList {
		switch video.PathType {
		case 1:
			urlList := make([]*UploadMediaByUrlItem, 0)
			urlPaths, ok := urlPathMap[video.Name]
			if !ok {
				log.WithContext(ctx).WithFields(logrus.Fields{
					"videoName": video.Name,
				}).Error("AdminEpisodeCreateBatch urlPaths")

				continue
			}
			for _, p := range urlPaths {
				urlList = append(urlList, &UploadMediaByUrlItem{
					Resolution: common.Resolution(p.Resolution),
					Url:        p.Url,
				})
			}

			err = GetVideoMpsEntity().ProducerPullUrl(ctx, video, urlList)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("ProducerPullUrl")
			}
		case 2:
			err = GetVideoMpsEntity().Producer(ctx, video)
			if err != nil {
				log.Ctx(ctx).WithError(err).Error("Producer")
			}
		}
	}

	return resp, nil
}

// 区别于上面的AdminEpisodeCreateBatch, 不会去传入video_mps
func (e Entry) AdminEpisodeImportBatch(ctx *gin.Context, req *showDto.AdminEpisodeCreateBatchReq, txOption ...*gorm.DB) (resp *showDto.AdminEpisodeCreateBatchResp, err error) {
	resp = &showDto.AdminEpisodeCreateBatchResp{}

	var (
		episodeList     = make(episodeDao.EpisodeList, 0)
		videoList       = make([]*videoDao.Video, 0)
		tx              *gorm.DB
		isNewTx         bool
		episodeVideoMap = make(map[uint32]*videoDao.Video)
		urlPathMap      = make(map[string][]*showDto.UrlPathItem)
	)

	if len(txOption) > 0 {
		tx = txOption[0]
	} else {
		tx = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
		isNewTx = true
	}

	for _, v := range req.List {
		episode := &episodeDao.Episode{
			Status:        uint32(dbs.StatusEnable),
			ShowID:        req.ShowID,
			Name:          v.Name,
			NameKey:       v.NameKey,
			EpisodeNumber: v.EpisodeNumber,
			MappingType:   v.MappingType,
			MappingID:     v.MappingID,
		}
		episodeList = append(episodeList, episode)

		video := &videoDao.Video{
			VideoPath: v.Video.VideoPath,
			PathType:  v.Video.PathType,
			Name:      episode.Name + time.Now().String(),
		}
		urlPathMap[video.Name] = v.Video.UrlPaths

		videoParam, err := json.Marshal(v.Video)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeCreateBatch ProducerPullUrl")
		}
		video.VideoParam = videoParam

		episodeVideoMap[v.EpisodeNumber] = video
	}

	err = e.EpisodeRepo.BatchCreateWithTx(ctx, tx, episodeList)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	resp.EpisodeList = episodeList

	for _, episode := range episodeList {

		videoModel := &videoDao.Video{
			EpisodeID: episode.ID,
		}
		if video, ok := episodeVideoMap[episode.EpisodeNumber]; ok {
			videoModel.VideoPath = video.VideoPath
			//videoModel.VideoEtag = video.VideoEtag
			videoModel.Name = video.Name
			videoModel.PathType = video.PathType
		}
		videoList = append(videoList, videoModel)

	}

	err = e.VideoRepo.BatchCreateWithTx(ctx, tx, videoList)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	resp.VideoList = videoList

	if isNewTx {
		tx.Commit()
	}

	return resp, nil
}

func (e Entry) AdminEpisodeVideoImportBatch(ctx *gin.Context, req *showDto.AdminVideoImportBatchReq) (resp *showDto.AdminVideoImportBatchResp, err error) {
	var (
		urlPaths = req.Video.UrlPaths
	)

	video := &videoDao.Video{
		ModelWithDel: dbs.ModelWithDel{
			BaseModel: dbs.BaseModel{
				ID: req.VideoID,
			},
		},
		Runtime:   req.Video.Runtime,
		EpisodeID: req.EpisodeID,
		Name:      req.Name,
		VideoPath: req.Video.VideoPath,
		PathType:  req.Video.PathType,
	}

	if video.Runtime > 0 {
		err = e.VideoRepo.UpdateMapByID(ctx, req.VideoID, map[string]interface{}{
			"runtime": video.Runtime,
		})
		if err != nil {
			log.Ctx(ctx).
				WithFields(logrus.Fields{
					"videoID": video.ID,
				}).
				WithError(err).Error("AdminEpisodeVideoImportBatch UpdateMapByID")
			return nil, err
		}
	}

	switch video.PathType {
	case 1:
		urlList := make([]*UploadMediaByUrlItem, 0)

		for _, p := range urlPaths {
			urlList = append(urlList, &UploadMediaByUrlItem{
				Resolution: common.Resolution(p.Resolution),
				Url:        p.Url,
			})
		}

		err = GetVideoMpsEntity().ProducerPullUrl(ctx, video, urlList)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("ProducerPullUrl")
			return
		}
	case 2:
		err = GetVideoMpsEntity().Producer(ctx, video)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("Producer")
			return
		}
	}

	return
}

func (e Entry) AdminEpisodeUpdate(ctx *gin.Context, req *showDto.AdminEpisodeUpdateReq) (resp *showDto.AdminEpisodeUpdateResp, err error) {
	resp = &showDto.AdminEpisodeUpdateResp{}

	var (
		updates      = make(map[string]interface{})
		episodeModel = &episodeDao.Episode{}
		videoModel   = &videoDao.Video{}
		tx           = dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	)

	episodeModel, err = e.EpisodeRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if episodeModel == nil {
		return nil, ecode.NotFoundErr
	}
	if episodeModel.Status == uint32(dbs.StatusDisable) {
		return nil, ecode.NotFoundErr
	}

	if req.ShowID != 0 {
		updates["show_id"] = req.ShowID
	}

	if req.Name != "" {
		updates["name"] = req.Name
	}

	if req.NameKey != "" {
		updates["name_key"] = req.NameKey
	}

	if req.EpisodeNumber != 0 {
		updates["episode_number"] = req.EpisodeNumber
	}

	if video := req.Video; video != nil && video.VideoPath != "" {

		// 删除原有的视频
		err = e.VideoRepo.UpdateMapByFilterWithTx(ctx, tx, &videoDao.Filter{
			EpisodeID: req.ID,
		}, map[string]interface{}{
			"status":                  uint32(dbs.StatusDisable),
			dbs.SoftDelField.String(): dbs.True,
		})
		if err != nil {
			tx.Rollback()
			return nil, err
		}

		//
		videoModel = &videoDao.Video{
			EpisodeID: req.ID,
			Name:      req.Name,
			VideoPath: video.VideoPath,
			PathType:  video.PathType,
			//VideoEtag: video.VideoEtag,
		}

		videoParam, err := json.Marshal(video)
		if err != nil {
			tx.Rollback()
			return nil, err
		}
		videoModel.VideoParam = videoParam

		_, err = e.VideoRepo.CreateWithTx(ctx, tx, videoModel)
		if err != nil {
			tx.Rollback()
			return nil, err
		}

	}

	err = e.EpisodeRepo.UpdateMapByID(ctx, req.ID, updates)
	if err != nil {
		tx.Rollback()
		return nil, err
	}

	tx.Commit()

	switch req.Video.PathType {
	case 1:
		urlList := make([]*UploadMediaByUrlItem, 0)
		for _, p := range req.Video.UrlPaths {
			urlList = append(urlList, &UploadMediaByUrlItem{
				Resolution: common.Resolution(p.Resolution),
				Url:        p.Url,
			})
		}

		err = GetVideoMpsEntity().ProducerPullUrl(ctx, videoModel, urlList)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeUpdate ProducerPullUrl")
			return nil, err
		}
	case 2:
		err = GetVideoMpsEntity().Producer(ctx, videoModel)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AdminEpisodeUpdate Producer")

			return nil, err
		}
	}

	return
}

func (e Entry) AdminEpisodeUpdatePatch(ctx *gin.Context, req *showDto.AdminEpisodeUpdatePatchReq) (resp *showDto.AdminEpisodeUpdatePatchResp, err error) {
	resp = &showDto.AdminEpisodeUpdatePatchResp{}

	var (
		updates = make(map[string]interface{})
	)

	episodeModel, err := e.EpisodeRepo.FetchByID(ctx, req.ID)
	if err != nil {
		return nil, err
	}

	if episodeModel.Status == uint32(dbs.StatusEnable) {
		updates["status"] = uint32(dbs.StatusDisable)
	} else {
		updates["status"] = uint32(dbs.StatusEnable)
	}

	err = e.EpisodeRepo.UpdateMapByID(ctx, req.ID, updates)
	if err != nil {
		return nil, err
	}

	return
}
