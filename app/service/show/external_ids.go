package show

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"vlab/app/api/imdb"
	genreDao "vlab/app/dao/content_genre"
	showDao "vlab/app/dao/content_show"
	externalIdsDao "vlab/app/dao/content_show_external_ids"
)

// ExternalIDsService 外部ID服务
type ExternalIDsService struct {
	showRepo        showDao.Repo
	externalIDsRepo externalIdsDao.Repo
	imdbService     *imdb.MatchService
}

// NewExternalIDsService 创建外部ID服务
func NewExternalIDsService() *ExternalIDsService {
	return &ExternalIDsService{
		showRepo:        showDao.GetRepo(),
		externalIDsRepo: externalIdsDao.GetRepo(),
		imdbService:     imdb.NewMatchService(nil),
	}
}

// SyncExternalIDs 同步单个剧集的外部IDs
func (s *ExternalIDsService) SyncExternalIDs(ctx *gin.Context, showID uint64) error {
	// 获取剧集信息
	showList, err := s.showRepo.FindByFilter(ctx, &showDao.Filter{
		ID: showID,
	})
	if err != nil {
		return fmt.Errorf("failed to find show: %w", err)
	}
	if len(showList) == 0 {
		return fmt.Errorf("show not found: %d", showID)
	}
	show := showList[0]

	// 准备匹配参数
	contentType := s.determineContentType(show)
	genres := s.extractGenres(ctx, show)
	language := s.extractLanguage(show)
	year := s.extractYear(show)

	// 调用IMDB API进行匹配
	mediaIDs, err := s.imdbService.MatchShow(
		context.Background(),
		show.Name,
		year,
		show.Overview,
		language,
		genres,
		contentType,
	)
	if err != nil {
		return fmt.Errorf("failed to match show %d: %w", showID, err)
	}

	// 没有找到匹配
	if mediaIDs == nil {
		// No match found, save no match record
		return s.saveNoMatchRecord(ctx, showID)
	}

	// 保存匹配结果
	return s.saveExternalIDs(ctx, showID, mediaIDs, contentType)
}

// BatchSyncExternalIDs 批量同步剧集的外部IDs
func (s *ExternalIDsService) BatchSyncExternalIDs(ctx *gin.Context, showIDs []uint64) (map[uint64]error, error) {
	results := make(map[uint64]error)

	for _, showID := range showIDs {
		err := s.SyncExternalIDs(ctx, showID)
		if err != nil {
			results[showID] = err
		}
	}

	return results, nil
}

// GetExternalIDsByShowID 根据ShowID获取外部IDs
func (s *ExternalIDsService) GetExternalIDsByShowID(ctx *gin.Context, showID uint64) (*externalIdsDao.Model, error) {
	return s.externalIDsRepo.FindByShowID(ctx, showID)
}

// GetExternalIDsByShowIDs 批量获取外部IDs
func (s *ExternalIDsService) GetExternalIDsByShowIDs(ctx *gin.Context, showIDs []uint64) (externalIdsDao.ModelList, error) {
	return s.externalIDsRepo.FindByShowIDs(ctx, showIDs)
}

// SearchByIMDBID 根据IMDB ID查找ShowID
func (s *ExternalIDsService) SearchByIMDBID(ctx *gin.Context, imdbID string) (uint64, error) {
	model, err := s.externalIDsRepo.FindByImdbID(ctx, imdbID)
	if err != nil {
		return 0, err
	}
	if model == nil {
		return 0, nil
	}
	return model.ShowID, nil
}

// SearchByTMDBID 根据TMDB ID查找ShowID
func (s *ExternalIDsService) SearchByTMDBID(ctx *gin.Context, tmdbID uint32) (uint64, error) {
	model, err := s.externalIDsRepo.FindByTmdbID(ctx, tmdbID)
	if err != nil {
		return 0, err
	}
	if model == nil {
		return 0, nil
	}
	return model.ShowID, nil
}

// UpdateExternalIDs 更新外部IDs
func (s *ExternalIDsService) UpdateExternalIDs(ctx *gin.Context, showID uint64, updates map[string]interface{}) error {
	// 查找现有记录
	existing, err := s.externalIDsRepo.FindByShowID(ctx, showID)
	if err != nil {
		return err
	}

	if existing == nil {
		// 创建新记录
		model := &externalIdsDao.Model{
			ShowID: showID,
			Source: externalIdsDao.SourceManual.String(),
		}
		s.applyUpdates(model, updates)
		return s.externalIDsRepo.Create(ctx, model)
	}

	// 更新现有记录
	s.applyUpdates(existing, updates)
	return s.externalIDsRepo.Update(ctx, existing)
}

// 辅助方法

// determineContentType 判断内容类型
func (s *ExternalIDsService) determineContentType(show *showDao.Show) string {
	switch show.ContentType {
	case showDao.ContentTypeMovie:
		return "movie"
	case showDao.ContentTypeTV:
		return "show"
	default:
		return imdb.DetermineContentType(show.Name, show.Overview)
	}
}

// extractGenres 提取类型标签
func (s *ExternalIDsService) extractGenres(ctx *gin.Context, show *showDao.Show) []string {
	// 查询content_show_with_genre表获取genre IDs
	showGenreRepo := showDao.GetRepo()
	genreRelations, err := showGenreRepo.FindGenreByFilter(ctx, &showDao.GenreFilter{
		ShowID: show.ID,
	})
	if err != nil {
		// Failed to get genres for show
		return []string{"unknown"}
	}
	
	if len(genreRelations) == 0 {
		return []string{"unknown"}
	}
	
	// 获取genre IDs
	genreIDs := genreRelations.GetGenreIDs()
	if len(genreIDs) == 0 {
		return []string{"unknown"}
	}
	
	// 查询genre详情
	genreRepo := genreDao.GetRepo()
	genres, err := genreRepo.FindByFilter(ctx, &genreDao.Filter{
		IDs: genreIDs,
	})
	if err != nil || len(genres) == 0 {
		// Failed to get genre details
		return []string{"unknown"}
	}
	
	// 提取genre名称并进行标准化
	var genreNames []string
	for _, genre := range genres {
		if genre.Name != "" {
			// 使用IMDB API的标准化方法
			normalized := imdb.NormalizeGenres([]string{genre.Name})
			if len(normalized) > 0 {
				genreNames = append(genreNames, normalized[0])
			}
		}
	}
	
	if len(genreNames) == 0 {
		return []string{"unknown"}
	}
	
	// Successfully extracted genres
	return genreNames
}

// extractLanguage 提取语言
func (s *ExternalIDsService) extractLanguage(show *showDao.Show) string {
	if show.Langs != "" {
		// 取第一个语言代码
		langs := strings.Split(show.Langs, ",")
		if len(langs) > 0 {
			return strings.TrimSpace(langs[0])
		}
	}
	return "en"
}

// extractYear 提取年份
func (s *ExternalIDsService) extractYear(show *showDao.Show) int {
	// 优先使用PresentationTime
	if show.PresentationTime > 0 {
		return int(show.PresentationTime)
	}

	// 从AirDate中提取年份
	if show.AirDate != "" && len(show.AirDate) >= 4 {
		// 尝试解析前4个字符作为年份
		year := 0
		fmt.Sscanf(show.AirDate[:4], "%d", &year)
		if year > 1900 && year < 2100 {
			return year
		}
	}
	
	// 使用AirDateTs时间戳
	if show.AirDateTs != nil {
		year := show.AirDateTs.Year()
		if year > 1900 && year < 2100 {
			return year
		}
	}

	// 返回0让IMDB API自动判断，而不是默认2020
	return 0
}

// saveExternalIDs 保存外部IDs
func (s *ExternalIDsService) saveExternalIDs(ctx *gin.Context, showID uint64, mediaIDs *imdb.MediaIDs, contentType string) error {
	model := &externalIdsDao.Model{
		ShowID:     showID,
		MatchType:  contentType,
		MatchScore: floatPtr(100.0), // 成功匹配默认100分
		IsMatch:    1,
		Source:     externalIdsDao.SourceIMDBAPI.String(),
	}

	// 设置外部IDs
	if mediaIDs != nil {
		if mediaIDs.Trakt > 0 {
			model.TraktID = uint32Ptr(uint32(mediaIDs.Trakt))
		}
		model.Slug = mediaIDs.Slug
		model.ImdbID = mediaIDs.IMDB
		if mediaIDs.TMDB > 0 {
			model.TmdbID = uint32Ptr(uint32(mediaIDs.TMDB))
		}

		// 保存原始响应
		rawData := map[string]interface{}{
			"trakt": mediaIDs.Trakt,
			"slug":  mediaIDs.Slug,
			"imdb":  mediaIDs.IMDB,
			"tmdb":  mediaIDs.TMDB,
		}
		rawResponse := externalIdsDao.RawResponseMap(rawData)
		model.RawResponse = &rawResponse
	}

	// 更新或创建记录
	return s.externalIDsRepo.UpsertByShowID(ctx, model)
}

// saveNoMatchRecord 保存无匹配记录
func (s *ExternalIDsService) saveNoMatchRecord(ctx *gin.Context, showID uint64) error {
	model := &externalIdsDao.Model{
		ShowID:      showID,
		MatchScore:  floatPtr(0),
		IsMatch:     0,
		Source:      externalIdsDao.SourceIMDBAPI.String(),
		MatchReason: "No suitable match found",
	}

	return s.externalIDsRepo.UpsertByShowID(ctx, model)
}

// applyUpdates 应用更新
func (s *ExternalIDsService) applyUpdates(model *externalIdsDao.Model, updates map[string]interface{}) {
	for key, value := range updates {
		switch key {
		case "imdb_id":
			if v, ok := value.(string); ok {
				model.ImdbID = v
			}
		case "tmdb_id":
			if v, ok := value.(uint32); ok {
				model.TmdbID = &v
			} else if v, ok := value.(int); ok {
				tmdbID := uint32(v)
				model.TmdbID = &tmdbID
			}
		case "trakt_id":
			if v, ok := value.(uint32); ok {
				model.TraktID = &v
			} else if v, ok := value.(int); ok {
				traktID := uint32(v)
				model.TraktID = &traktID
			}
		case "slug":
			if v, ok := value.(string); ok {
				model.Slug = v
			}
		case "match_type":
			if v, ok := value.(string); ok {
				model.MatchType = v
			}
		}
	}
}

// 辅助函数
func floatPtr(f float64) *float64 {
	return &f
}

func uint32Ptr(u uint32) *uint32 {
	return &u
}

// GetStatistics 获取外部ID同步统计
func (s *ExternalIDsService) GetStatistics(ctx *gin.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总记录数
	totalFilter := &externalIdsDao.Filter{}
	total, err := s.externalIDsRepo.Count(ctx, totalFilter)
	if err != nil {
		return nil, err
	}
	stats["total"] = total

	// 成功匹配数
	isMatch := uint8(1)
	matchedFilter := &externalIdsDao.Filter{
		IsMatch: &isMatch,
	}
	matched, err := s.externalIDsRepo.Count(ctx, matchedFilter)
	if err != nil {
		return nil, err
	}
	stats["matched"] = matched

	// 未匹配数
	notMatch := uint8(0)
	notMatchedFilter := &externalIdsDao.Filter{
		IsMatch: &notMatch,
	}
	notMatched, err := s.externalIDsRepo.Count(ctx, notMatchedFilter)
	if err != nil {
		return nil, err
	}
	stats["not_matched"] = notMatched

	// 按类型统计
	movieFilter := &externalIdsDao.Filter{
		MatchType: "movie",
	}
	movieCount, _ := s.externalIDsRepo.Count(ctx, movieFilter)

	showFilter := &externalIdsDao.Filter{
		MatchType: "show",
	}
	showCount, _ := s.externalIDsRepo.Count(ctx, showFilter)

	stats["by_type"] = map[string]int64{
		"movie": movieCount,
		"show":  showCount,
	}

	// 按来源统计
	imdbAPIFilter := &externalIdsDao.Filter{
		Source: externalIdsDao.SourceIMDBAPI.String(),
	}
	imdbAPICount, _ := s.externalIDsRepo.Count(ctx, imdbAPIFilter)

	manualFilter := &externalIdsDao.Filter{
		Source: externalIdsDao.SourceManual.String(),
	}
	manualCount, _ := s.externalIDsRepo.Count(ctx, manualFilter)

	stats["by_source"] = map[string]int64{
		"imdb_api": imdbAPICount,
		"manual":   manualCount,
	}

	return stats, nil
}
