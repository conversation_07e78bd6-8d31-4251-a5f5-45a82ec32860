package show

import (
	channelDao "vlab/app/dao/resource_channel"
	showDto "vlab/app/dto/show"
)

// ConvertChannelListToMap 将 channel 列表转换为 ChannelBase map
func ConvertChannelListToMap(channelList channelDao.ModelList) map[uint64]*showDto.ChannelBase {
	channelMap := make(map[uint64]*showDto.ChannelBase, len(channelList))
	for _, channel := range channelList {
		channelMap[channel.ID] = &showDto.ChannelBase{
			ID:   channel.ID,
			Name: channel.Name,
		}
	}
	return channelMap
}

// GetChannelFromMap 安全地从 map 中获取 channel 信息
func GetChannelFromMap(channelMap map[uint64]*showDto.ChannelBase, channelID uint64) *showDto.ChannelBase {
	if channelMap == nil || channelID == 0 {
		return nil
	}
	return channelMap[channelID]
}