package admin

import (
	"vlab/app/common/dbs"
	adminAccount "vlab/app/dao/admin_account"
	adminMenu "vlab/app/dao/admin_menu"
	adminRole "vlab/app/dao/admin_role"
	adminDto "vlab/app/dto/admin"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/util"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
)

// CheckOperationAccountAuth .
func (e *Entry) CheckOperationAccountAuth(ctx *gin.Context, accountID, roleID uint64) error {
	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		return err
	}
	if accountID == ctxAccount.AccountID {
		return nil
	}
	// 仅root可操作root
	if roleID == dbs.RoleRootID && !e.JudgeIsRoot(ctxAccount.RoleID) {
		return ecode.NoAuthErr
	}
	// 仅root or admin可操作admin
	if roleID == dbs.RoleAdminID && !e.EditableFirstAdmin(ctxAccount.AccountID) {
		return ecode.NoAuthErr
	}

	return nil
}

// AdminAccountList .
func (e *Entry) AdminAccountList(ctx *gin.Context, req *adminDto.AdminAccountListReq) (*adminDto.AdminAccountListResp, error) {
	total, list, err := e.AccountRepo.DataPageList(ctx, &adminAccount.Filter{
		ID: req.ID, Account: req.Account, Name: req.Name, Mobile: req.Mobile,
		RoleID: req.RoleID, Status: req.Status,
		NotRids: req.NotRids, NotIds: req.NotAids,
	}, req.Page, req.Limit)
	if err != nil {
		return nil, err
	}

	roleList, err := e.RoleRepo.RedisRoleList(ctx)
	if err != nil {
		return nil, err
	}
	roleMap := roleList.GetIDMap()

	retList := make([]*adminDto.AdminAccountListItem, 0, len(list))
	for _, val := range list {
		item := &adminDto.AdminAccountListItem{
			ID:        val.ID,
			Account:   val.Account,
			Name:      val.Name,
			Mobile:    val.Mobile,
			RoleID:    val.RoleID,
			Status:    val.Status,
			IsAdmin:   e.JudgeIsAdmin(val.RoleID),
			CreatedAt: val.GetCreatedTime(),
		}
		if role, ok := roleMap[val.RoleID]; ok {
			item.RoleName = role.Name
		}
		retList = append(retList, item)
	}

	return &adminDto.AdminAccountListResp{
		List:  retList,
		Total: total,
	}, nil
}

// AdminSetAccount .
func (e *Entry) AdminSetAccount(ctx *gin.Context, req *adminDto.AdminSetAccountReq) error {
	if err := e.CheckOperationAccountAuth(ctx, req.ID, req.RoleID); err != nil {
		return err
	}

	var (
		eg         errgroup.Group
		model      = &adminAccount.Model{}
		num        int64
		m          = adminAccount.SetAccountReqToModel(req)
		clearToken bool
	)
	if req.ID > 0 {
		eg.Go(func() (err error) {
			if model, err = e.AccountRepo.FetchByID(ctx, req.ID); err != nil {
				return
			}
			if model.ID == 0 {
				return ecode.ParamErr
			}
			if err = e.CheckOperationAccountAuth(ctx, model.ID, model.RoleID); err != nil {
				return err
			}
			if req.RoleID != model.RoleID || req.Status == uint32(dbs.StatusDisable) {
				clearToken = true
			}
			return
		})

		eg.Go(func() (err error) {
			if num, err = e.AccountRepo.CountByFilter(ctx, &adminAccount.Filter{Account: req.Account, Name: req.Name, NotID: req.ID, Unique: true}); err != nil {
				return
			}
			if num > 0 {
				return ecode.AccountExistErr
			}
			return
		})
	} else {
		eg.Go(func() (err error) {
			if num, err = e.AccountRepo.CountByFilter(ctx, &adminAccount.Filter{Account: req.Account}); err != nil {
				return
			}
			if num > 0 {
				return ecode.AccountExistErr
			}
			return
		})
	}

	if err := eg.Wait(); err != nil {
		return err
	}
	return e.AccountRepo.CreateOrUpdate(ctx, m, clearToken)
}

// AdminDelAccount .
func (e *Entry) AdminDelAccount(ctx *gin.Context, accountID uint64) error {
	// root & admin 具有删除权限，不可删除
	ctxAccount, _ := helper.GetCtxAccount(ctx)
	if !e.JudgeIsAdmin(ctxAccount.RoleID) || e.JudgeIsAdmin(accountID) {
		return ecode.NoAuthErr
	}

	return e.AccountRepo.DelByID(ctx, accountID)
}

// AdminSetAccountPwd .
func (e *Entry) AdminSetAccountPwd(ctx *gin.Context, id uint64, pwd string) error {
	model, err := e.AccountRepo.FetchByID(ctx, id)
	if err != nil {
		return err
	}
	if model.ID == 0 {
		return ecode.ParamErr
	}
	if err = e.CheckOperationAccountAuth(ctx, id, model.RoleID); err != nil {
		return err
	}

	enPwd, _ := helper.AesCbcEncrypt(pwd, helper.AesKey, helper.AesIV)
	if err = e.AccountRepo.UpdateMapByID(ctx, id, map[string]interface{}{"password": enPwd}, true); err != nil {
		return err
	}
	return nil
}

// AdminSetAccountPwd .
func (e *Entry) AdminGetAccountInfo(ctx *gin.Context, aid, rid uint64) (*adminDto.AdminGetAccountInfoResp, error) {
	var (
		eg           errgroup.Group
		model        = &adminAccount.Model{}
		roleInfo     = &adminRole.Model{}
		roleMenuList = adminMenu.ModelList{}
		menuList     = adminMenu.ModelList{}
		menuIds      = adminRole.RoleAuthMenuIds{}
		feAuth       = []string{}
	)
	eg.Go(func() (err error) {
		if model, err = e.AccountRepo.FetchByID(ctx, aid); err != nil {
			return
		}
		if model.ID == 0 {
			return ecode.ParamErr
		}
		return nil
	})
	eg.Go(func() (err error) {
		if roleInfo, err = e.RoleRepo.FetchByID(ctx, rid); err != nil {
			return err
		}
		return nil
	})

	eg.Go(func() (err error) {
		if menuList, err = e.MenuRepo.RedisMenuList(ctx); err != nil {
			return err
		}
		if !e.JudgeIsAdmin(rid) {
			if menuIds, err = e.RoleRepo.RedisRoleMenuIds(ctx, rid); err != nil {
				return err
			}
			menuMap := menuList.GetIDMap()
			for _, menuId := range menuIds {
				if menu, ok := menuMap[menuId]; ok {
					roleMenuList = append(roleMenuList, menu)
				}
			}
		} else {
			roleMenuList = menuList
		}

		for _, val := range roleMenuList {
			if val.FeAuth != "" {
				feAuth = append(feAuth, val.FeAuth)
			}
		}

		return nil
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}

	return &adminDto.AdminGetAccountInfoResp{
		ID:        model.ID,
		Account:   model.Account,
		Name:      model.Name,
		Mobile:    model.Mobile,
		RoleID:    model.RoleID,
		RoleName:  roleInfo.Name,
		FeAuth:    feAuth,
		CreatedAt: model.GetCreatedTime(),
	}, nil
}

// AdminAccountAll .
func (e *Entry) AdminAccountAll(ctx *gin.Context, req *adminDto.AdminAccountAllReq) ([]*adminDto.AdminAccountAllItem, error) {
	roleList, err := e.RoleRepo.RedisEnableRoleList(ctx)
	if err != nil {
		return nil, err
	}

	// 过滤已经禁用的角色
	list, err := e.AccountRepo.FindByFilter(ctx, &adminAccount.Filter{Status: uint32(dbs.StatusEnable), RoleIds: roleList.GetIds()})
	if err != nil {
		return nil, err
	}

	ctxAccount, err := helper.GetCtxAccount(ctx)
	if err != nil {
		return nil, err
	}

	retList := make([]*adminDto.AdminAccountAllItem, 0, len(list))
	for _, val := range list {
		if !e.JudgeIsRoot(ctxAccount.AccountID) && e.JudgeIsRoot(val.RoleID) {
			continue
		}
		if req.FilterAdmin == dbs.True && e.JudgeIsAdmin(val.RoleID) {
			continue
		}
		item := &adminDto.AdminAccountAllItem{
			ID:   val.ID,
			Name: val.Name,
		}
		retList = append(retList, item)
	}
	return retList, nil
}

// AdminAccountLogin .
func (e *Entry) AdminAccountLogin(ctx *gin.Context, account, pwd string) (string, error) {
	aInfo, err := e.AccountRepo.FeatchByFilterSort(ctx, &adminAccount.Filter{Account: account})
	if err != nil {
		return "", err
	}
	if aInfo.ID == 0 {
		return "", ecode.AdminAccountNotExistErr
	}
	if aInfo.Status != uint32(dbs.StatusEnable) {
		return "", ecode.AdminAccountDisableErr
	}
	if !e.RoleRepo.JudgeRoleIsValid(ctx, aInfo.RoleID) {
		return "", ecode.RoleDisableErr
	}
	dePwd, _ := helper.AesCbcDecrypt(aInfo.Password, helper.AesKey, helper.AesIV)
	if dePwd != pwd {
		return "", ecode.AdminAccountPwdErr
	}

	token, _ := util.GenToken(aInfo.ID, aInfo.RoleID)
	if err = e.AccountRepo.RedisSetAccountToken(ctx, aInfo.ID, token); err != nil {
		return "", err
	}

	return token, nil
}
