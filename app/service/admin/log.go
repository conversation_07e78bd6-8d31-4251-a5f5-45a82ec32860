package admin

import (
	"context"
	"strings"
	"sync"
	"time"
	"vlab/app/common/dbs"
	adminLog "vlab/app/dao/admin_log"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/util/ctxUtil"

	"github.com/gin-gonic/gin"
)

var (
	adminLogInit   sync.Once
	adminLogEntity *AdminLogEntity
	logBatchSize   = 100
	logTickerTime  = time.Second
)

type AdminLogEntity struct {
	logChan  chan *adminLog<PERSON>han
	done     chan bool
	mu       sync.Mutex
	isClosed bool
}

type adminLog<PERSON>han struct {
	ctx *gin.Context
	*adminLog.Model
}

func GetAdminLogEntity() *AdminLogEntity {
	if adminLogEntity == nil {
		adminLogInit.Do(func() {
			adminLogEntity = newAdminLogEntity()
			newCtx := ctxUtil.NewRequestID(context.Background())
			adminLogEntity.Start(newCtx)
			helper.GetAppSrvMgr(newCtx).Register(adminLogEntity)
		})
	}
	return adminLogEntity
}

func newAdminLogEntity() *AdminLogEntity {
	return &AdminLogEntity{
		logChan: make(chan *adminLogChan, 1000),
		done:    make(chan bool),
	}
}

func (l *AdminLogEntity) Start(ctx context.Context) error {
	helper.AsyncDo(func() {
		var (
			ticker  = time.NewTicker(logTickerTime)
			msgList = make(adminLog.ModelList, 0, logBatchSize)
			ginC    *gin.Context
		)
		for {
			select {
			case msg := <-l.logChan:
				msgList = append(msgList, msg.Model)
				ginC = msg.ctx
				if len(msgList) >= logBatchSize {
					adminLog.GetRepo().BatchCreateWithTx(dbs.NewMysqlEngines().UseWithGinCtx(ginC, true), msgList)
					msgList = msgList[:0]
					ticker.Reset(logTickerTime)
				}
			case <-ticker.C:
				if len(msgList) > 0 {
					adminLog.GetRepo().BatchCreateWithTx(dbs.NewMysqlEngines().UseWithGinCtx(ginC, true), msgList)
					msgList = msgList[:0]
				}
			case <-ctx.Done():
				l.mu.Lock()
				defer l.mu.Unlock()
				l.isClosed = true
				close(l.logChan)
				l.done <- true
				ticker.Stop()
				return
			default:
				time.Sleep(200 * time.Millisecond)
			}
		}
	})

	return nil
}

func (l *AdminLogEntity) Close(ctx context.Context) error {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
	return nil
}

func (l *AdminLogEntity) Producer(ctx *gin.Context, headers, body []byte) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()
	if l.isClosed {
		return true, nil
	}

	var (
		rspCode    string
		blackMap   = map[string]struct{}{}
		err        error
		ctxAccount = &helper.CtxAccount{}
		url        = strings.TrimPrefix(ctx.Request.URL.Path, "/")
	)
	if blackMap, err = adminLog.GetRepo().RedisLogBlackMap(ctx); err != nil {
		return false, err
	}
	if _, ok := blackMap[url]; ok {
		return true, nil
	}

	if val, ok := ctx.Get("rspCode"); ok && val != nil {
		rspCode = val.(string)
	}
	if rspCode != "" && rspCode != ecode.OK.Code() {
		return true, nil
	}
	if ctxAccount, err = helper.GetCtxAccount(ctx); err != nil {
		return false, err
	}

	c := ctx.Copy()
	c.Request = c.Request.Clone(context.WithoutCancel(c.Request.Context()))

	logChan := &adminLogChan{
		Model: &adminLog.Model{
			AccountID: ctxAccount.AccountID,
			RoleID:    ctxAccount.RoleID,
			Method:    ctx.Request.Method,
			TraceID:   helper.GetGinRequestID(ctx),
			Url:       strings.TrimPrefix(ctx.Request.URL.Path, "/"),
			ClientIp:  ctx.ClientIP(),
			Header:    string(headers),
			Body:      string(body),
		},
		ctx: c,
	}

	l.logChan <- logChan
	return true, nil
}

func (l *AdminLogEntity) Name() string {
	return "AdminLog"
}
