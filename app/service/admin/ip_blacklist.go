package admin

import (
	"net"
	"strings"
	"vlab/app/common/dbs"
	channelDao "vlab/app/dao/resource_channel"
	ipBlacklistDao "vlab/app/dao/resource_ip_disallow"
	adminDto "vlab/app/dto/admin"
	showDto "vlab/app/dto/show"
	"vlab/pkg/ecode"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// convertChannelListToMap 将渠道列表转换为ChannelBase map
func convertChannelListToMap(channelList channelDao.ModelList) map[uint64]*showDto.ChannelBase {
	channelMap := make(map[uint64]*showDto.ChannelBase, len(channelList))
	for _, channel := range channelList {
		channelMap[channel.ID] = &showDto.ChannelBase{
			ID:   channel.ID,
			Name: channel.Name,
		}
	}
	return channelMap
}

// getChannelFromMap 安全地从map中获取渠道信息
func getChannelFromMap(channelMap map[uint64]*showDto.ChannelBase, channelID uint64) *showDto.ChannelBase {
	if channelMap == nil || channelID == 0 {
		return nil
	}
	return channelMap[channelID]
}

// AdminIPBlacklistList 获取IP黑名单列表
func (e *Entry) AdminIPBlacklistList(ctx *gin.Context, req *adminDto.AdminIPBlacklistListReq) (*adminDto.AdminIPBlacklistListResp, error) {
	filter := &ipBlacklistDao.Filter{
		ID:        req.ID,
		ChannelID: req.ChannelID,
		Status:    req.Status,
	}

	total, list, err := e.IPBlacklistRepo.DataPageList(ctx, filter, req.Page, req.Limit)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Failed to get IP blacklist list")
		return nil, err
	}

	resp := &adminDto.AdminIPBlacklistListResp{
		Total: total,
		List:  make([]*adminDto.AdminIPBlacklistListItem, 0, len(list)),
	}

	// 获取渠道信息
	var channelMap map[uint64]*showDto.ChannelBase
	if len(list) > 0 {
		channelIDs := list.GetChannelIDs()
		if len(channelIDs) > 0 {
			channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
				IDS:    channelIDs,
				Status: uint32(dbs.StatusEnable),
			})
			if err == nil && len(channelList) > 0 {
				channelMap = convertChannelListToMap(channelList)
			}
		}
	}

	for _, item := range list {
		listItem := &adminDto.AdminIPBlacklistListItem{
			ID:        item.ID,
			ChannelID: item.ChannelID,
			IPList:    item.IPList,
			Status:    item.Status,
			CreatedAt: item.GetCreatedTime(),
			UpdatedAt: item.UpdatedAt.Format(dbs.TimeDateFormatFull),
		}

		// 添加渠道信息
		if channel := getChannelFromMap(channelMap, item.ChannelID); channel != nil {
			listItem.Channel = channel
		}

		resp.List = append(resp.List, listItem)
	}

	return resp, nil
}

// AdminIPBlacklistDetail 获取IP黑名单详情
func (e *Entry) AdminIPBlacklistDetail(ctx *gin.Context, req *adminDto.AdminIPBlacklistDetailReq) (*adminDto.AdminIPBlacklistDetailResp, error) {
	item, err := e.IPBlacklistRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("id", req.ID).Error("Failed to get IP blacklist detail")
		return nil, err
	}

	if item.ID == 0 {
		return nil, ecode.NotFoundErr
	}

	resp := &adminDto.AdminIPBlacklistDetailResp{
		ID:        item.ID,
		ChannelID: item.ChannelID,
		IPList:    item.IPList,
		IPCIDRs:   item.GetIPCIDRList(),
		Status:    item.Status,
		CreatedAt: item.GetCreatedTime(),
		UpdatedAt: item.UpdatedAt.Format(dbs.TimeDateFormatFull),
	}

	// 获取渠道信息
	if item.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, item.ChannelID)
		if err == nil && channelInfo.ID > 0 && channelInfo.Status == uint32(dbs.StatusEnable) {
			resp.Channel = &showDto.ChannelBase{
				ID:   channelInfo.ID,
				Name: channelInfo.Name,
			}
		}
	}

	return resp, nil
}

// AdminIPBlacklistCreate 创建IP黑名单
func (e *Entry) AdminIPBlacklistCreate(ctx *gin.Context, req *adminDto.AdminIPBlacklistCreateReq) error {
	// 验证IP格式
	if err := e.validateIPList(req.IPList); err != nil {
		return err
	}

	// 检查渠道是否存在
	if req.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, req.ChannelID)
		if err != nil {
			log.Ctx(ctx).WithError(err).WithField("channel_id", req.ChannelID).Error("Failed to fetch channel for IP blacklist create")
			return err
		}
		if channelInfo.ID == 0 {
			return ecode.NotFoundErr
		}
	}

	// 检查是否已存在该渠道的配置
	existing, err := e.IPBlacklistRepo.FetchByChannelID(ctx, req.ChannelID)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("channel_id", req.ChannelID).Error("Failed to check existing IP blacklist")
		return err
	}

	if existing.ID > 0 {
		// 如果已存在，则更新
		updateMap := map[string]any{
			"ip_list": req.IPList,
			"status":  req.Status,
		}
		if err := e.IPBlacklistRepo.UpdateMapByID(ctx, existing.ID, updateMap); err != nil {
			log.Ctx(ctx).WithError(err).WithField("id", existing.ID).Error("Failed to update existing IP blacklist")
			return err
		}
	} else {
		// 创建新记录
		model := &ipBlacklistDao.Model{
			ChannelID: req.ChannelID,
			IPList:    req.IPList,
			Status:    req.Status,
		}
		if err := e.IPBlacklistRepo.CreateOrUpdate(ctx, model); err != nil {
			log.Ctx(ctx).WithError(err).Error("Failed to create IP blacklist")
			return err
		}
	}

	// 清除缓存
	if err := e.IPBlacklistRepo.RedisClearIPDisallowList(ctx); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Failed to clear IP blacklist cache")
	}

	return nil
}

// AdminIPBlacklistUpdate 更新IP黑名单
func (e *Entry) AdminIPBlacklistUpdate(ctx *gin.Context, req *adminDto.AdminIPBlacklistUpdateReq) error {
	// 验证IP格式
	if err := e.validateIPList(req.IPList); err != nil {
		return err
	}

	// 检查记录是否存在
	existing, err := e.IPBlacklistRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("id", req.ID).Error("Failed to fetch IP blacklist for update")
		return err
	}

	if existing.ID == 0 {
		return ecode.NotFoundErr
	}

	// 检查渠道是否存在
	if req.ChannelID > 0 {
		channelInfo, err := e.ChannelRepo.FetchByID(ctx, req.ChannelID)
		if err != nil {
			log.Ctx(ctx).WithError(err).WithField("channel_id", req.ChannelID).Error("Failed to fetch channel for IP blacklist update")
			return err
		}
		if channelInfo.ID == 0 {
			return ecode.NotFoundErr
		}
	}

	// 更新记录
	updateMap := map[string]any{
		"channel_id": req.ChannelID,
		"ip_list":    req.IPList,
		"status":     req.Status,
	}

	if err := e.IPBlacklistRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		log.Ctx(ctx).WithError(err).WithField("id", req.ID).Error("Failed to update IP blacklist")
		return err
	}

	// 清除缓存
	if err := e.IPBlacklistRepo.RedisClearIPDisallowList(ctx); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Failed to clear IP blacklist cache")
	}

	return nil
}

// AdminIPBlacklistDelete 删除IP黑名单
func (e *Entry) AdminIPBlacklistDelete(ctx *gin.Context, req *adminDto.AdminIPBlacklistDeleteReq) error {
	// 检查记录是否存在
	existing, err := e.IPBlacklistRepo.FetchByID(ctx, req.ID)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithField("id", req.ID).Error("Failed to fetch IP blacklist for delete")
		return err
	}

	if existing.ID == 0 {
		return ecode.NotFoundErr
	}

	// 软删除
	updateMap := map[string]any{
		"is_deleted": 1,
	}

	if err := e.IPBlacklistRepo.UpdateMapByID(ctx, req.ID, updateMap); err != nil {
		log.Ctx(ctx).WithError(err).WithField("id", req.ID).Error("Failed to delete IP blacklist")
		return err
	}

	// 清除缓存
	if err := e.IPBlacklistRepo.RedisClearIPDisallowList(ctx); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Failed to clear IP blacklist cache")
	}

	return nil
}

// validateIPList 验证IP列表格式
func (e *Entry) validateIPList(ipList string) error {
	if strings.TrimSpace(ipList) == "" {
		return ecode.ParamErr
	}

	ips := strings.Split(ipList, ",")
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 检查是否为CIDR格式
		if strings.Contains(ip, "/") {
			_, _, err := net.ParseCIDR(ip)
			if err != nil {
				logrus.WithField("ip", ip).WithError(err).Error("Invalid CIDR format")
				return ecode.ParamErr
			}
		} else {
			// 检查是否为有效IP地址
			if net.ParseIP(ip) == nil {
				logrus.WithField("ip", ip).Error("Invalid IP address")
				return ecode.ParamErr
			}
		}
	}

	return nil
}

// AdminIPBlacklistTest 测试IP是否在黑名单中
func (e *Entry) AdminIPBlacklistTest(ctx *gin.Context, req *adminDto.AdminIPBlacklistTestReq) (*adminDto.AdminIPBlacklistTestResp, error) {
	// 验证IP格式
	if net.ParseIP(req.IP) == nil {
		return nil, ecode.ParamErr
	}

	// 获取指定渠道的IP黑名单配置
	config, err := e.IPBlacklistRepo.FetchByChannelID(ctx, req.ChannelID)
	if err != nil {
		log.Ctx(ctx).WithError(err).WithFields(logrus.Fields{
			"channel_id": req.ChannelID,
			"ip":         req.IP,
		}).Error("Failed to fetch IP blacklist config for test")
		return nil, err
	}

	if config.ID == 0 || config.Status != 1 {
		return &adminDto.AdminIPBlacklistTestResp{
			IsBlacklisted: false,
			Message:       "No active IP blacklist configuration found for this channel",
		}, nil
	}

	// 检查IP是否在黑名单中
	isBlacklisted := config.IsIPInBlacklist(req.IP)

	resp := &adminDto.AdminIPBlacklistTestResp{
		IsBlacklisted: isBlacklisted,
	}

	if isBlacklisted {
		resp.Message = "IP is in blacklist"
		// 找到匹配的规则
		if matchedRule := e.findMatchedRule(config.IPList, req.IP); matchedRule != "" {
			resp.MatchedRule = matchedRule
		}
	} else {
		resp.Message = "IP is not in blacklist"
	}

	return resp, nil
}

// findMatchedRule 找到匹配的IP规则
func (e *Entry) findMatchedRule(ipList, testIP string) string {
	clientIPAddr := net.ParseIP(testIP)
	if clientIPAddr == nil {
		return ""
	}

	ips := strings.Split(ipList, ",")
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip == "" {
			continue
		}

		// 检查是否为CIDR格式
		if strings.Contains(ip, "/") {
			_, ipNet, err := net.ParseCIDR(ip)
			if err != nil {
				continue
			}
			if ipNet.Contains(clientIPAddr) {
				return ip
			}
		} else {
			// 直接IP地址比较
			if ip == testIP {
				return ip
			}
		}
	}

	return ""
}
