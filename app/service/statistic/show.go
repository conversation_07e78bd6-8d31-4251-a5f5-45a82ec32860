package statistic

import (
	showDao "vlab/app/dao/content_show"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"
	statisticDto "vlab/app/dto/statistic"

	"github.com/gin-gonic/gin"
)

// ShowDailyActive 剧日活统计
func (s *Entry) ShowDailyActive(ctx *gin.Context, req statisticDto.GetShowDailyActiveReq) ([]*statisticDto.GetShowDailyActiveResp, error) {
	filter := &watchVideoPv.Filter{
		ShowID:    req.ShowID,
		ChannelID: req.ChannelID,
		Lang:      req.Lang,
		Date:      req.Date,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		LogType:   watchVideoPv.LogTypePlay,
	}

	ret, err := s.WatchVideoPvRepo.ShowDailyActive(ctx, filter)
	if err != nil {
		return nil, err
	}

	retList := make([]*statisticDto.GetShowDailyActiveResp, 0)
	if len(ret) == 0 {
		return retList, nil
	}

	for _, v := range ret {
		retList = append(retList, &statisticDto.GetShowDailyActiveResp{
			Date: v.GetDate(),
			Num:  v.Num,
		})
	}

	return retList, nil
}

// ShowDailyPlaynum 剧 日播放量/日下载量 统计
func (s *Entry) ShowDailyCount(ctx *gin.Context, req statisticDto.GetShowDailyActiveReq) ([]*statisticDto.GetShowDailyActiveResp, error) {
	filter := &watchVideoPv.Filter{
		ShowID:    req.ShowID,
		ChannelID: req.ChannelID,
		Lang:      req.Lang,
		Date:      req.Date,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		LogType:   req.LogType,
	}

	ret, err := s.WatchVideoPvRepo.ShowDailyCountByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}

	retList := make([]*statisticDto.GetShowDailyActiveResp, 0)
	if len(ret) == 0 {
		return retList, nil
	}

	for _, v := range ret {
		retList = append(retList, &statisticDto.GetShowDailyActiveResp{
			Date: v.GetDate(),
			Num:  v.Num,
		})
	}

	return retList, nil
}

// ShowCountTop 剧 播放量/下载量 排行统计
func (s *Entry) ShowCountTop(ctx *gin.Context, req statisticDto.GetShowPlaynumTopReq) ([]*statisticDto.GetShowPlaynumTopResp, error) {
	filter := &watchVideoPv.Filter{
		ShowID:    req.ShowID,
		ChannelID: req.ChannelID,
		Lang:      req.Lang,
		Date:      req.Date,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		LogType:   watchVideoPv.LogType(req.LogType),
		Limit:     req.Limit,
	}

	list, err := s.WatchVideoPvRepo.ShowCountTopByFilter(ctx, filter)
	if err != nil {
		return nil, err
	}
	retList := make([]*statisticDto.GetShowPlaynumTopResp, 0)
	if len(list) == 0 {
		return retList, nil
	}

	showList, err := s.ShowRepo.FindByFilter(ctx, &showDao.Filter{
		IDs: list.GetShowIds(),
	})
	if err != nil {
		return nil, err
	}
	showMap := showList.GetMap()

	for _, v := range list {
		show, ok := showMap[v.ShowID]
		if !ok {
			continue
		}
		retList = append(retList, &statisticDto.GetShowPlaynumTopResp{
			ShowID:   v.ShowID,
			ShowName: show.Name,
			Num:      v.Num,
		})
	}

	return retList, nil
}
