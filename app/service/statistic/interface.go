package statistic

import (
	"sync"

	showDao "vlab/app/dao/content_show"
	watchVideoPv "vlab/app/dao/user/watch_video_pv"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
}

// TODO替换
type Entry struct {
	ShowRepo         showDao.Repo
	WatchVideoPvRepo *watchVideoPv.Entry
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		ShowRepo:         showDao.GetRepo(),
		WatchVideoPvRepo: watchVideoPv.GetRepo(),
	}
}
