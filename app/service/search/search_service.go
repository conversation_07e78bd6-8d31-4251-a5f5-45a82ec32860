package search

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"vlab/app/dao"
	"vlab/app/dto"
	"vlab/pkg/ecode"
	"vlab/pkg/redis"

	goredis "github.com/go-redis/redis/v8"
)

// SearchService 搜索服务
type SearchService struct {
	searchDao *dao.SearchDao
	RedisCli  *redis.RedisClient
}

// NewSearchService 创建搜索服务
func NewSearchService() *SearchService {
	return &SearchService{
		searchDao: dao.NewSearchDao(),
		RedisCli:  redis.GetRedisClient(),
	}
}

// Search 执行搜索
func (s *SearchService) Search(ctx *gin.Context, req *dto.SearchRequest) (*dto.SearchResponse, error) {
	// 参数验证
	if err := s.validateSearchRequest(req); err != nil {
		return nil, ecode.ParamErr
	}

	// 预处理查询文本
	processedQuery := s.preprocessQuery(req.Query, req.Language)
	req.Query = processedQuery

	// 尝试从缓存获取结果
	cacheKey := s.buildCacheKey(req)
	if cachedResult, err := s.getCachedSearchResult(ctx, cacheKey); err == nil && cachedResult != nil {
		log.Printf("搜索缓存命中: %s", cacheKey)
		return cachedResult, nil
	}

	// 执行搜索
	result, err := s.searchDao.Search(ctx, req)
	if err != nil {
		log.Printf("搜索失败: %v", err)
		return nil, ecode.SystemErr
	}

	// 后处理搜索结果
	s.postProcessSearchResults(result, req)

	// 缓存搜索结果（如果结果不为空）
	if len(result.Items) > 0 {
		s.cacheSearchResult(ctx, cacheKey, result, 300) // 缓存5分钟
	}

	return result, nil
}

// GetSearchSuggestions 获取搜索建议
func (s *SearchService) GetSearchSuggestions(ctx context.Context, req *dto.SearchSuggestionRequest) (*dto.SearchSuggestionResponse, error) {
	if req.Query == "" {
		return &dto.SearchSuggestionResponse{Suggestions: []dto.SearchSuggestion{}}, nil
	}

	if req.Limit <= 0 {
		req.Limit = 10
	}

	// 尝试从缓存获取建议
	cacheKey := fmt.Sprintf("search_suggestions:%s:%s:%d", req.Query, req.Language, req.Limit)
	if cachedSuggestions, err := s.getCachedSuggestions(ctx, cacheKey); err == nil && cachedSuggestions != nil {
		return cachedSuggestions, nil
	}

	// 生成搜索建议
	suggestions := s.generateSearchSuggestions(ctx, req)

	// 构建响应
	response := &dto.SearchSuggestionResponse{
		Suggestions: suggestions,
	}

	// 缓存建议结果
	s.cacheSuggestions(ctx, cacheKey, response, 1800) // 缓存30分钟

	return response, nil
}

// GetRecommendations 获取推荐内容
func (s *SearchService) GetRecommendations(ctx *gin.Context, req *dto.RecommendationRequest) (*dto.RecommendationResponse, error) {
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 50 {
		req.Limit = 50
	}

	var searchReq *dto.SearchRequest
	var recommendationType string

	if req.ShowID > 0 {
		// 基于相似内容推荐
		searchReq = s.buildSimilarContentSearchRequest(ctx, req)
		recommendationType = "similar"
	} else if len(req.Categories) > 0 {
		// 基于分类推荐
		searchReq = s.buildCategoryBasedSearchRequest(req)
		recommendationType = "category_based"
	} else {
		// 默认推荐（热门内容）
		searchReq = s.buildDefaultRecommendationRequest(req)
		recommendationType = "personalized"
	}

	// 执行搜索
	searchResult, err := s.searchDao.Search(ctx, searchReq)
	if err != nil {
		return nil, ecode.SystemErr
	}

	// 过滤排除的内容
	items := s.filterExcludedItems(searchResult.Items, req.ExcludeIDs)

	// 限制返回数量
	if len(items) > req.Limit {
		items = items[:req.Limit]
	}

	return &dto.RecommendationResponse{
		Items: items,
		Total: len(items),
		Type:  recommendationType,
	}, nil
}

// validateSearchRequest 验证搜索请求
func (s *SearchService) validateSearchRequest(req *dto.SearchRequest) error {
	if req.SearchMode != "" && !isValidSearchMode(req.SearchMode) {
		return fmt.Errorf("无效的搜索模式: %s", req.SearchMode)
	}

	if req.Page < 1 {
		req.Page = 1
	}
	if req.PageSize < 1 {
		req.PageSize = 20
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	// 验证向量搜索参数
	if req.VectorParams.DenseWeight < 0 || req.VectorParams.DenseWeight > 1 {
		req.VectorParams.DenseWeight = 0.5
	}
	if req.VectorParams.SparseWeight < 0 || req.VectorParams.SparseWeight > 1 {
		req.VectorParams.SparseWeight = 0.5
	}

	return nil
}

// preprocessQuery 预处理查询文本
func (s *SearchService) preprocessQuery(query, language string) string {
	if query == "" {
		return ""
	}

	// 去除多余空格
	query = strings.TrimSpace(query)
	query = strings.Join(strings.Fields(query), " ")

	// 根据语言进行特定处理
	switch language {
	case "zh", "zh-CN":
		// 中文查询处理
		return s.preprocessChineseQuery(query)
	case "en":
		// 英文查询处理
		return s.preprocessEnglishQuery(query)
	default:
		return query
	}
}

// preprocessChineseQuery 预处理中文查询
func (s *SearchService) preprocessChineseQuery(query string) string {
	// 移除常见的无意义词汇
	stopWords := []string{"的", "了", "在", "是", "有", "和", "与", "或", "但是", "然而", "电影", "电视剧", "节目"}
	words := strings.Fields(query)
	var filteredWords []string

	for _, word := range words {
		isStopWord := false
		for _, stopWord := range stopWords {
			if word == stopWord {
				isStopWord = true
				break
			}
		}
		if !isStopWord {
			filteredWords = append(filteredWords, word)
		}
	}

	if len(filteredWords) == 0 {
		return query // 如果过滤后为空，返回原查询
	}

	return strings.Join(filteredWords, " ")
}

// preprocessEnglishQuery 预处理英文查询
func (s *SearchService) preprocessEnglishQuery(query string) string {
	// 转换为小写
	query = strings.ToLower(query)

	// 移除常见的无意义词汇
	stopWords := []string{"the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "movie", "film", "show", "series"}
	words := strings.Fields(query)
	var filteredWords []string

	for _, word := range words {
		isStopWord := false
		for _, stopWord := range stopWords {
			if word == stopWord {
				isStopWord = true
				break
			}
		}
		if !isStopWord {
			filteredWords = append(filteredWords, word)
		}
	}

	if len(filteredWords) == 0 {
		return query // 如果过滤后为空，返回原查询
	}

	return strings.Join(filteredWords, " ")
}

// postProcessSearchResults 后处理搜索结果
func (s *SearchService) postProcessSearchResults(result *dto.SearchResponse, req *dto.SearchRequest) {
	// 排序处理
	// 注意：对于向量搜索模式，当 sortBy 为 "relevance" 时，应保持原始顺序
	// 因为向量搜索返回的结果已经按照相关性分数排序（TopK）
	shouldSort := true
	if (req.SearchMode == "vector" || req.SearchMode == "hybrid") && req.SortBy == "relevance" {
		// 向量搜索结果已经按相关性排序，保持原始顺序
		shouldSort = false
	}

	if shouldSort {
		s.sortSearchResults(result.Items, req.SortBy, req.SortOrder)
	}

	// 添加高亮信息
	if req.Query != "" {
		s.addHighlights(result.Items, req.Query)
	}

	// 多语言处理
	s.processMultiLanguageResults(result.Items, req.Language)
}

// sortSearchResults 排序搜索结果
func (s *SearchService) sortSearchResults(items []dto.SearchResultItem, sortBy, sortOrder string) {
	sort.Slice(items, func(i, j int) bool {
		var less bool

		switch sortBy {
		case "rating":
			less = items[i].Rating < items[j].Rating
		case "year":
			// 比较年份数组的第一个元素（如果存在）
			yearI := 0
			yearJ := 0
			if len(items[i].Year) > 0 {
				fmt.Sscanf(items[i].Year[0], "%d", &yearI)
			}
			if len(items[j].Year) > 0 {
				fmt.Sscanf(items[j].Year[0], "%d", &yearJ)
			}
			less = yearI < yearJ
		case "duration":
			less = items[i].Duration < items[j].Duration
		case "relevance":
			fallthrough
		default:
			less = items[i].Score < items[j].Score
		}

		if sortOrder == "asc" {
			return less
		}
		return !less
	})
}

// addHighlights 添加高亮信息
func (s *SearchService) addHighlights(items []dto.SearchResultItem, query string) {
	queryWords := strings.Fields(strings.ToLower(query))

	for i := range items {
		var highlights []string

		// 检查标题匹配
		if s.containsAnyWord(strings.ToLower(items[i].Name), queryWords) {
			highlights = append(highlights, "title")
		}

		// 检查描述匹配
		if s.containsAnyWord(strings.ToLower(items[i].Overview), queryWords) {
			highlights = append(highlights, "overview")
		}

		// 检查分类匹配
		if s.containsAnyWord(strings.ToLower(items[i].Category), queryWords) {
			highlights = append(highlights, "category")
		}

		// 检查导演匹配
		if s.containsAnyWord(strings.ToLower(items[i].Director), queryWords) {
			highlights = append(highlights, "director")
		}

		// 检查演员匹配
		for _, actor := range items[i].Actors {
			if s.containsAnyWord(strings.ToLower(actor), queryWords) {
				highlights = append(highlights, "actors")
				break
			}
		}

		items[i].Highlights = highlights
	}
}

// containsAnyWord 检查文本是否包含任意一个查询词
func (s *SearchService) containsAnyWord(text string, words []string) bool {
	for _, word := range words {
		if strings.Contains(text, word) {
			return true
		}
	}
	return false
}

// processMultiLanguageResults 处理多语言结果
func (s *SearchService) processMultiLanguageResults(items []dto.SearchResultItem, language string) {
	// 根据用户偏好语言调整显示内容
	for i := range items {
		// 如果有多语言版本，根据语言偏好选择显示内容
		if language == "en" {
			// 优先显示英文内容
			s.prioritizeEnglishContent(&items[i])
		} else if language == "zh" || language == "zh-CN" {
			// 优先显示中文内容
			s.prioritizeChineseContent(&items[i])
		}
	}
}

// prioritizeEnglishContent 优先显示英文内容
func (s *SearchService) prioritizeEnglishContent(item *dto.SearchResultItem) {
	// 从多语言数组中选择英文内容（简单实现）
	for _, name := range item.NameI18n {
		if s.isEnglishText(name) {
			item.Name = name
			break
		}
	}

	for _, overview := range item.OverviewI18n {
		if s.isEnglishText(overview) {
			item.Overview = overview
			break
		}
	}
}

// prioritizeChineseContent 优先显示中文内容
func (s *SearchService) prioritizeChineseContent(item *dto.SearchResultItem) {
	// 从多语言数组中选择中文内容（简单实现）
	for _, name := range item.NameI18n {
		if s.isChineseText(name) {
			item.Name = name
			break
		}
	}

	for _, overview := range item.OverviewI18n {
		if s.isChineseText(overview) {
			item.Overview = overview
			break
		}
	}
}

// generateSearchSuggestions 生成搜索建议
func (s *SearchService) generateSearchSuggestions(ctx context.Context, req *dto.SearchSuggestionRequest) []dto.SearchSuggestion {
	var suggestions []dto.SearchSuggestion

	// 这里可以实现更复杂的建议逻辑，比如：
	// 1. 从热门搜索中匹配
	// 2. 从节目名称中匹配
	// 3. 从分类、导演、演员等维度匹配

	// 简单实现：基于查询文本生成一些基础建议
	query := strings.ToLower(req.Query)

	// 分类建议
	categories := []string{"动作", "喜剧", "科幻", "悬疑", "爱情", "剧情", "动画", "纪录片"}
	for _, category := range categories {
		if strings.Contains(strings.ToLower(category), query) || strings.Contains(query, strings.ToLower(category)) {
			suggestions = append(suggestions, dto.SearchSuggestion{
				Text:  category,
				Type:  "category",
				Score: 0.8,
			})
		}
	}

	// 如果建议数量不够，可以添加通用建议
	if len(suggestions) < req.Limit {
		commonSuggestions := []dto.SearchSuggestion{
			{Text: req.Query + " 电影", Type: "query", Score: 0.7},
			{Text: req.Query + " 电视剧", Type: "query", Score: 0.7},
		}

		suggestions = append(suggestions, commonSuggestions...)
	}

	// 限制返回数量
	if len(suggestions) > req.Limit {
		suggestions = suggestions[:req.Limit]
	}

	return suggestions
}

// buildSimilarContentSearchRequest 构建相似内容搜索请求
func (s *SearchService) buildSimilarContentSearchRequest(ctx context.Context, req *dto.RecommendationRequest) *dto.SearchRequest {
	// 这里可以根据ShowID获取该节目的信息，然后构建相似搜索
	// 简化实现：使用分类信息进行推荐
	searchReq := &dto.SearchRequest{
		SearchMode: "filter_only",
		Page:       1,
		PageSize:   req.Limit,
		SortBy:     "rating",
		SortOrder:  "desc",
		Language:   req.Language,
	}

	return searchReq
}

// buildCategoryBasedSearchRequest 构建基于分类的搜索请求
func (s *SearchService) buildCategoryBasedSearchRequest(req *dto.RecommendationRequest) *dto.SearchRequest {
	return &dto.SearchRequest{
		SearchMode: "filter_only",
		Page:       1,
		PageSize:   req.Limit,
		SortBy:     "rating",
		SortOrder:  "desc",
		Language:   req.Language,
		Filters: dto.SearchFilters{
			Categories: req.Categories,
		},
	}
}

// buildDefaultRecommendationRequest 构建默认推荐请求
func (s *SearchService) buildDefaultRecommendationRequest(req *dto.RecommendationRequest) *dto.SearchRequest {
	return &dto.SearchRequest{
		SearchMode: "filter_only",
		Page:       1,
		PageSize:   req.Limit,
		SortBy:     "rating",
		SortOrder:  "desc",
		Language:   req.Language,
		Filters: dto.SearchFilters{
			RatingFrom: 7.0, // 推荐高评分内容
		},
	}
}

// filterExcludedItems 过滤排除的内容
func (s *SearchService) filterExcludedItems(items []dto.SearchResultItem, excludeIDs []uint64) []dto.SearchResultItem {
	if len(excludeIDs) == 0 {
		return items
	}

	excludeMap := make(map[uint64]bool)
	for _, id := range excludeIDs {
		excludeMap[id] = true
	}

	var filteredItems []dto.SearchResultItem
	for _, item := range items {
		if !excludeMap[item.ShowID] {
			filteredItems = append(filteredItems, item)
		}
	}

	return filteredItems
}

// 缓存相关方法

// buildCacheKey 构建缓存键
func (s *SearchService) buildCacheKey(req *dto.SearchRequest) string {
	// 构建包含主要搜索参数的缓存键
	// 添加版本号以便在缓存策略变化时能够使旧缓存失效
	return fmt.Sprintf("search:v1:%s:%s:%d:%d:%s",
		req.Query, req.SearchMode, req.Page, req.PageSize, req.Language)
}

// getCachedSearchResult 获取缓存的搜索结果
func (s *SearchService) getCachedSearchResult(ctx context.Context, key string) (*dto.SearchResponse, error) {
	cacheData, err := s.RedisCli.Get(ctx, key).Result()
	if err != nil {
		if err == goredis.Nil {
			return nil, fmt.Errorf("缓存未找到")
		}
		return nil, err
	}

	var result dto.SearchResponse
	if err := json.Unmarshal([]byte(cacheData), &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// cacheSearchResult 缓存搜索结果
func (s *SearchService) cacheSearchResult(ctx context.Context, key string, result *dto.SearchResponse, ttl int) {
	cacheData, err := json.Marshal(result)
	if err != nil {
		log.Printf("序列化搜索结果失败: %v", err)
		return
	}

	if err := s.RedisCli.Set(ctx, key, string(cacheData), time.Duration(ttl)*time.Second).Err(); err != nil {
		log.Printf("缓存搜索结果失败: %v", err)
	}
}

// getCachedSuggestions 获取缓存的建议
func (s *SearchService) getCachedSuggestions(ctx context.Context, key string) (*dto.SearchSuggestionResponse, error) {
	cacheData, err := s.RedisCli.Get(ctx, key).Result()
	if err != nil {
		if err == goredis.Nil {
			return nil, fmt.Errorf("缓存未找到")
		}
		return nil, err
	}

	var result dto.SearchSuggestionResponse
	if err := json.Unmarshal([]byte(cacheData), &result); err != nil {
		return nil, err
	}

	return &result, nil
}

// cacheSuggestions 缓存建议
func (s *SearchService) cacheSuggestions(ctx context.Context, key string, suggestions *dto.SearchSuggestionResponse, ttl int) {
	cacheData, err := json.Marshal(suggestions)
	if err != nil {
		log.Printf("序列化搜索建议失败: %v", err)
		return
	}

	if err := s.RedisCli.Set(ctx, key, string(cacheData), time.Duration(ttl)*time.Second).Err(); err != nil {
		log.Printf("缓存搜索建议失败: %v", err)
	}
}

// 辅助方法

// isValidSearchMode 检查搜索模式是否有效
func isValidSearchMode(mode string) bool {
	validModes := []string{"vector", "filter_only", "hybrid"}
	for _, validMode := range validModes {
		if mode == validMode {
			return true
		}
	}
	return false
}

// isEnglishText 简单判断是否为英文文本
func (s *SearchService) isEnglishText(text string) bool {
	// 简单实现：检查是否包含英文字符
	for _, r := range text {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') {
			return true
		}
	}
	return false
}

// isChineseText 简单判断是否为中文文本
func (s *SearchService) isChineseText(text string) bool {
	// 简单实现：检查是否包含中文字符
	for _, r := range text {
		if r >= 0x4e00 && r <= 0x9fff {
			return true
		}
	}
	return false
}
