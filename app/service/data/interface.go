package data

import (
	"sync"

	jsoniter "github.com/json-iterator/go"
	show "vlab/app/dao/content_show"
	"vlab/pkg/redis"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	SearchHistoryRepo show.SearchHistoryRepo
	RedisCli          *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		SearchHistoryRepo: show.GetRepo(),
		RedisCli:          redis.GetRedisClient(),
	}
}
