package user

import (
	"fmt"
	"strings"

	"vlab/app/common/dbs"
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
)

// GetUserAuthMethods 获取用户已绑定的认证方式
func (e *Entry) GetUserAuthMethods(ctx *gin.Context, userID uint64) []*userDto.AdminUserAuthItem {
	var res = make([]*userDto.AdminUserAuthItem, 0)

	authList, err := e.UserRepo.FindAuthsByUserID(ctx, userID, helper.GetCtxPlatformID(ctx))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("获取用户认证方式失败")
		return nil
	}

	for _, auth := range authList {
		item := &userDto.AdminUserAuthItem{
			AuthType: auth.AuthType,
			//AuthUid:  auth.AuthUid,
			//AuthToken: auth.AuthToken,
			AuthEmail: userDao.MaskEmail(auth.AuthEmail, 2, 2),
		}
		res = append(res, item)
	}

	return res
}

// BindEmail 绑定邮箱账号
func (e *Entry) BindEmail(ctx *gin.Context, userID uint64, email, code, password string) error {
	var (
		emailStr     = strings.ToLower(strings.TrimSpace(email))
		platformID   = helper.GetCtxPlatformID(ctx)
		channelID, _ = helper.GetCtxChannelID(ctx)
	)
	code = strings.TrimSpace(code)

	// 验证邮箱验证码
	isValid := e.EmailService.VerifyCode(ctx, emailStr, code)

	if !isValid {
		return fmt.Errorf("code is invalid or expired")
	}

	// 通用绑定验证
	if err := e.validateBind(ctx, userID, uint32(userDao.AtEmail), emailStr, platformID); err != nil {
		return err
	}

	// 获取当前用户信息并更新密码
	userInfo, err := e.UserRepo.FetchByID(ctx, userID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("fetch user info failed")
		return fmt.Errorf("fetch user info failed")
	}

	// 加密密码
	hashedPassword, err := helper.EncodeBcrypt(password)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("password encryption failed")
		return fmt.Errorf("")
	}

	// 开启事务
	tx := dbs.NewMysqlEngines().Use(true).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新用户邮箱和密码
	userInfo.Email = emailStr
	userInfo.Password = hashedPassword
	if err := e.UserRepo.CreateOrUpdateWithTx(ctx, tx, userInfo, true); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("update user info failed")
		return fmt.Errorf("update user info failed")
	}

	// 创建认证记录
	authInfo := &userDao.Auth{
		UserID:     userID,
		ChannelID:  uint32(channelID),
		PlatformID: platformID,
		AuthType:   uint32(userDao.AtEmail),
		AuthUid:    emailStr,
		AuthToken:  "",
		AuthEmail:  emailStr,
	}

	if err := e.UserRepo.CreateAuthWithTx(ctx, tx, authInfo); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("create auth record failed")
		return fmt.Errorf("create auth record failed")
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("transaction commit failed")
		return fmt.Errorf("transaction commit failed")
	}

	log.Ctx(ctx).WithField("userID", userID).WithField("email", emailStr).Info("email bind success")
	return nil
}

// UnbindAuth 解绑认证方式
func (e *Entry) UnbindAuth(ctx *gin.Context, userID uint64, authType uint32, password string) error {
	var (
		platformID = helper.GetCtxPlatformID(ctx)
	)

	// 1. 检查用户认证方式数量，至少要保留一种
	authList, err := e.UserRepo.FindAuthsByUserID(ctx, userID, platformID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("find user auths failed")
		return fmt.Errorf("find user auths failed")
	}

	if len(authList) <= 1 {
		return fmt.Errorf("at least one authentication method is required")
	}

	// 2. 验证密码（如果用户有邮箱认证，需要验证密码）
	userInfo, err := e.UserRepo.FetchByID(ctx, userID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("fetch user info failed")
		return fmt.Errorf("fetch user info failed")
	}

	// 如果用户有密码，需要验证密码
	if userInfo.Password != "" {
		if err := helper.CompareBcrypt(userInfo.Password, password); err != nil {
			return fmt.Errorf("password is incorrect")
		}
	}

	// 3. 查找要解绑的认证记录
	authToDelete, err := e.UserRepo.FetchAuthByUserAndType(ctx, userID, authType, platformID)
	if err != nil || authToDelete.ID == 0 {
		return fmt.Errorf("cannot find the authentication method to unbind")
	}

	// 4. 删除认证记录
	if err := e.UserRepo.DeleteAuth(ctx, authToDelete.ID); err != nil {
		log.Ctx(ctx).WithError(err).Error("delete auth record failed")
		return fmt.Errorf("delete auth record failed")
	}

	log.Ctx(ctx).WithField("userID", userID).WithField("authType", authType).Info("auth unbind success")
	return nil
}

// validateBind 通用绑定验证逻辑
func (e *Entry) validateBind(ctx *gin.Context, userID uint64, authType uint32, authUid string, platformID uint32) error {
	// 1. 检查用户是否已绑定该认证方式
	existingAuth, err := e.UserRepo.FetchAuthByUserAndType(ctx, userID, authType, platformID)
	if err == nil && existingAuth.ID > 0 {
		return fmt.Errorf("this authentication method is already bound to the user")
	}

	// 2. 检查该认证方式是否已被其他用户绑定
	existingUser, err := e.UserRepo.FetchAuthByTypeAndUid(ctx, authType, authUid, platformID)
	if err == nil && existingUser.ID > 0 {
		return fmt.Errorf("this authentication method is already bound to another user")
	}

	return nil
}
