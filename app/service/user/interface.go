package user

import (
	"sync"

	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	userLogDao "vlab/app/dao/user/user_log"
	watchAdLog "vlab/app/dao/user/watch_ad_log"
	"vlab/pkg/email"
	"vlab/pkg/redis"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
	AdminUser
}

type AdminUser interface {
}

// TODO替换
type Entry struct {
	UserRepo    *userDao.Entry
	UserLogRepo userLogDao.Repo
	// DeviceRepo  deviceDao.Repo
	DeviceRepo  *deviceDao.Entry
	WatchAdRepo *watchAdLog.Entry
	RedisCli    *redis.RedisClient

	EmailService *email.EmailService
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:    userDao.GetRepo(),
		UserLogRepo: userLogDao.GetRepo(),
		DeviceRepo:  deviceDao.GetRepo(),
		WatchAdRepo: watchAdLog.GetRepo(),
		RedisCli:    redis.GetRedisClient(),

		EmailService: email.GetEmailService(),
	}
}
