package login

import (
	"fmt"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	userDto "vlab/app/dto/user"
	userSrv "vlab/app/service/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"golang.org/x/sync/errgroup"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type LoginProvider interface {
	Login(ctx *gin.Context, authToken string) (*userDao.UserAuth, error)
	GetAuthType() uint32
}

type LoginManager struct {
	providers map[userDao.AuthType]LoginProvider
}

func NewLoginManager() *LoginManager {
	return &LoginManager{
		providers: map[userDao.AuthType]LoginProvider{
			userDao.AtApple:  &AppleLoginProvider{},
			userDao.AtGoogle: &GoogleLoginProvider{},
			// userDao.AtFacebook: &FacebookLoginProvider{},
		},
	}
}

func (lm *LoginManager) Login(ctx *gin.Context, authType userDao.AuthType, authToken string) (*userDto.UserLoginResp, error) {
	var (
		loginProvider LoginProvider
		exists        bool
		loginAuth     = &userDao.UserAuth{}
		resp          = &userDto.UserLoginResp{}
		err           error
		userAuth      = &userDao.UserAuth{}
		clientType, _ = helper.GetClientType(ctx)
	)
	if loginProvider, exists = lm.providers[authType]; !exists {
		return nil, ecode.UserLoginAuthTypeErr
	}
	if loginAuth, err = loginProvider.Login(ctx, authToken); err != nil {
		return nil, err
	}
	if userAuth, err = lm.FindOrCreateUser(ctx, authType, loginAuth); err != nil {
		return nil, err
	}

	resp.Token, _ = util.GenToken(userAuth.ID, uint64(clientType))
	if err = userDao.GetRepo().RedisSetUserToken(ctx, userAuth.ID, resp.Token); err != nil {
		return nil, err
	}
	resp.UserInfo = &userDto.AdminUserInfoResp{
		AdminUserListItem: userAuth.Model.ToUserInfoResp(ctx),
		Auths:             userSrv.GetService().GetUserAuthMethods(ctx, userAuth.ID),
	}
	userSrv.GetUserLogEntity().ProducerLogin(ctx, userAuth)

	return resp, nil
}

// validateBind 通用绑定验证逻辑
func validateBind(ctx *gin.Context, userID uint64, authType uint32, authUid string, platformID uint32) error {
	// 1. 检查用户是否已绑定该认证方式
	existingAuth, err := userSrv.GetService().UserRepo.FetchAuthByUserAndType(ctx, userID, authType, platformID)
	if err == nil && existingAuth.ID > 0 {
		return fmt.Errorf("this authentication method is already bound to the user")
	}

	// 2. 检查该认证方式是否已被其他用户绑定
	existingUser, err := userSrv.GetService().UserRepo.FetchAuthByTypeAndUid(ctx, authType, authUid, platformID)
	if err == nil && existingUser.ID > 0 {
		return fmt.Errorf("this authentication method is already bound to another user")
	}

	return nil
}

func (lm *LoginManager) bind(ctx *gin.Context, userID uint64, authType userDao.AuthType, authToken string) error {
	var (
		loginProvider LoginProvider
		exists        bool
		loginAuth     = &userDao.UserAuth{}
		err           error
		channelID, _  = helper.GetCtxChannelID(ctx)
		platformID    = helper.GetCtxPlatformID(ctx)
	)
	if loginProvider, exists = lm.providers[authType]; !exists {
		return ecode.UserLoginAuthTypeErr
	}
	if loginAuth, err = loginProvider.Login(ctx, authToken); err != nil {
		return err
	}

	// 通用绑定验证
	if err := validateBind(ctx, userID, loginAuth.AuthType, loginAuth.AuthUid, platformID); err != nil {
		return err
	}

	authInfo := &userDao.Auth{
		UserID:     userID,
		ChannelID:  uint32(channelID),
		PlatformID: platformID,
		AuthType:   uint32(authType),
		AuthUid:    loginAuth.AuthUid,
		AuthToken:  loginAuth.AuthToken,
		AuthEmail:  loginAuth.AuthEmail,
	}

	if err := userSrv.GetService().UserRepo.CreateAuth(ctx, authInfo); err != nil {
		log.Ctx(ctx).WithError(err).Error("创建邮箱认证记录失败")
		return fmt.Errorf("邮箱账号绑定失败")
	}

	return nil
}

// BindApple 绑定苹果账号
func (lm *LoginManager) BindApple(ctx *gin.Context, userID uint64, authToken string) error {

	if err := lm.bind(ctx, userID, userDao.AtApple, authToken); err != nil {
		log.Ctx(ctx).WithError(err).Error("BindApple login error")
		return err
	}

	return nil
}

// BindGoogle 绑定谷歌账号
func (lm *LoginManager) BindGoogle(ctx *gin.Context, userID uint64, authToken string) error {

	if err := lm.bind(ctx, userID, userDao.AtGoogle, authToken); err != nil {
		log.Ctx(ctx).WithError(err).Error("BindGoogle login error")
		return err
	}

	return nil
}

func (lm *LoginManager) FindOrCreateUser(ctx *gin.Context, authType userDao.AuthType, uaInfo *userDao.UserAuth) (*userDao.UserAuth, error) {
	var (
		eg           errgroup.Group
		userRepo     = userDao.GetRepo()
		userAuth     = &userDao.UserAuth{}
		userInfo     = &userDao.Model{}
		deviceInfo   = &deviceDao.Model{}
		deviceRepo   = deviceDao.GetRepo()
		deviceID, _  = helper.GetDeviceNo(ctx)
		timeNow      = timeUtil.GetNowTimestampByZone(ctx)
		channelID, _ = helper.GetCtxChannelID(ctx)
		platformID   = helper.GetCtxPlatformID(ctx)
	)

	eg.Go(func() (err error) {
		if userAuth, err = userRepo.FetchAuthByUdx(ctx, uint32(authType), platformID, uaInfo.AuthUid); err != nil {
			return
		}
		return
	})
	eg.Go(func() (err error) {
		if deviceInfo, err = deviceRepo.RedisDeviceInfo(ctx, deviceID); err != nil {
			return
		}
		return
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	log.Ctx(ctx).WithField("userAuth", userAuth).WithField("deviceInfo", deviceInfo).WithField("timeNow", timeNow).Info("FindOrCreateUser")

	if userAuth.AuthID == dbs.False {
		// if uaInfo.Email != "" {
		// 	if userInfo, err = userRepo.FetchByEmail(ctx, uaInfo.Email); err != nil {
		// 		return nil, err
		// 	}
		// }

		authInfo := &userDao.Auth{
			ChannelID:  uint32(channelID),
			PlatformID: platformID,
			AuthType:   uint32(authType),
			AuthUid:    uaInfo.AuthUid,
			AuthToken:  uaInfo.AuthToken,
			AuthEmail:  uaInfo.AuthEmail,
		}

		noAdTime := adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyNewUserNoAdTime)
		tx := dbs.NewMysqlEngines().Use(true).Begin()
		if err := func() (err error) {
			// if userInfo.ID == dbs.False {
			userInfo = &userDao.Model{
				UUID:          uuid.New().String(),
				Nickname:      uaInfo.Nickname,
				Mobile:        uaInfo.Mobile,
				Email:         uaInfo.Email,
				Avatar:        uaInfo.Avatar,
				Status:        dbs.True,
				ChannelID:     channelID,
				PlatformID:    platformID,
				WatchAdTime:   timeUtil.GetNowTimestampByZone(ctx) + noAdTime,
				LastLoginTime: timeNow,
			}

			if err = userRepo.CreateWithTx(ctx, tx, userInfo); err != nil {
				return
			}
			userAuth.Model = userInfo
			// }
			authInfo.UserID = userInfo.ID
			if err = userRepo.CreateAuthWithTx(ctx, tx, authInfo); err != nil {
				return
			}

			return tx.Commit().Error
		}(); err != nil {
			tx.Rollback()
			log.Ctx(ctx).WithError(err).Error("findOrCreateUser err")
			return nil, err
		}
		userAuth.AuthID = authInfo.ID
		userAuth.AuthType = authInfo.AuthType
		userAuth.AuthUid = authInfo.AuthUid
		userAuth.AuthToken = authInfo.AuthToken
	} else if userAuth.AuthID > dbs.False && userAuth.WatchAdTime < deviceInfo.WatchAdTime && deviceInfo.WatchAdTime > timeNow {
		userAuth.Model.WatchAdTime = deviceInfo.WatchAdTime
		if err := userRepo.CreateOrUpdate(ctx, userAuth.Model, true); err != nil {
			return nil, err
		}
		deviceRepo.UpdateMapByID(ctx, deviceInfo.ID, deviceInfo.DeviceID, map[string]interface{}{"user_id": userAuth.ID, "watch_ad_time": dbs.False})
	}

	return userAuth, nil
}

func GetLoginUserAuth(ctx *gin.Context, authType userDao.AuthType) *userDao.UserAuth {
	randInfo := helper.RandCode(8)
	return &userDao.UserAuth{
		Model: &userDao.Model{
			Nickname: fmt.Sprintf("用户-%v", randInfo),
			Email:    fmt.Sprintf("%<EMAIL>", randInfo),
		},
		AuthType:  uint32(authType),
		AuthUid:   fmt.Sprintf("第三方id-%v", randInfo),
		AuthToken: fmt.Sprintf("token-%v", helper.RandCode(32)),
	}
}
