package login

import (
	userDao "vlab/app/dao/user"

	"github.com/gin-gonic/gin"
)

type FacebookLoginProvider struct{}

// Login 实现 Facebook 登录逻辑
func (a *FacebookLoginProvider) Login(ctx *gin.Context, authToken string) (*userDao.UserAuth, error) {
	return GetLoginUserAuth(ctx, userDao.AtFacebook), nil
	// // TODO: 调用 Facebook Graph API，验证 authToken 并获取用户信息
	// claims, err := VerifyAppleToken(ctx, authToken)
	// if err != nil {
	// 	return nil, err
	// }
	// appleSub := claims["sub"].(string)
	// appleName := claims["name"].(string)
	// email := claims["email"].(string)

	// return &userDao.UserAuth{
	// 	Model: userDao.Model{
	// 		Nickname: appleName,
	// 		Email:    email,
	// 	},
	// 	AuthType:  uint32(userDao.AtApple),
	// 	AuthUid:   appleSub,
	// 	AuthToken: authToken,
	// }, nil
}

func (a *FacebookLoginProvider) GetAuthType() uint32 {
	return uint32(userDao.AtFacebook)
}
