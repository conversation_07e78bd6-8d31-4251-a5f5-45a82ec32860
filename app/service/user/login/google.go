package login

import (
	"errors"
	googlePlayExec "vlab/app/api/googleplay/execute"
	userDao "vlab/app/dao/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v4"
)

type GoogleLoginProvider struct{}

type GoogleClaims struct {
	Sub   string `json:"sub"`   // Google 用户唯一 ID
	Email string `json:"email"` // 用户邮箱
	Name  string `json:"name"`  // 用户姓名
	Aud   string `json:"aud"`   // Google Client ID
	Iss   string `json:"iss"`   // Issuer
	jwt.RegisteredClaims
}

// Login 实现 Facebook 登录逻辑
func (a *GoogleLoginProvider) Login(ctx *gin.Context, authToken string) (*userDao.UserAuth, error) {
	claims, err := VerifyGoogleToken(ctx, authToken)
	if err != nil {
		return nil, err
	}

	uInfo := &userDao.Model{
		Nickname: claims.Name,
		Email:    claims.Email,
	}
	googleSub := claims.Sub

	return &userDao.UserAuth{
		Model:     uInfo,
		AuthType:  a.GetAuthType(),
		AuthUid:   googleSub,
		AuthToken: "",
	}, nil
}

func VerifyGoogleToken(ctx *gin.Context, authToken string) (*GoogleClaims, error) {
	channelKeyInfo, _ := helper.GetCtxChannelKeyInfo(ctx, false)
	if !channelKeyInfo.CheckGoogleLoginConfig() {
		return nil, ecode.ChannelConfigInvalidErr
	}
	// 获取 Google 公钥
	keys, err := googlePlayExec.GetApi().GetGooglePublicKeys(ctx)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("get google publickey error")
		return nil, errors.New("get google publickey error")
	}

	// 解析 token
	token, err := jwt.ParseWithClaims(authToken, &GoogleClaims{}, func(token *jwt.Token) (interface{}, error) {
		log.Ctx(ctx).WithField("token", token).Info("apple token jwt.Parse ret")
		// 获取 keyID
		if keyID, ok := token.Header["kid"].(string); ok {
			if key, found := keys[keyID]; found {
				return key, nil
			}
		}
		return nil, errors.New("publickey match error")
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("jwt.ParseWithClaims google token error")
		return nil, err
	}

	if claims, ok := token.Claims.(*GoogleClaims); ok && token.Valid {
		// 安全性检查
		if claims.Aud != channelKeyInfo.GoogleLoginClientID {
			log.Ctx(ctx).WithError(err).Warn("claims.Aud error")
			return nil, errors.New("google aud invalid")
		}
		if claims.Iss != "https://accounts.google.com" && claims.Iss != "accounts.google.com" {
			log.Ctx(ctx).WithError(err).Warn("claims.Iss error")
			return nil, errors.New("google iss invalid")
		}

		return claims, nil
	}

	log.Ctx(ctx).WithField("authToken", authToken).Warn("invalid token")
	return nil, errors.New("invalid token")
}

func (a *GoogleLoginProvider) GetAuthType() uint32 {
	return uint32(userDao.AtGoogle)
}
