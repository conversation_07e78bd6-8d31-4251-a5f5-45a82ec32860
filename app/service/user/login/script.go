package login

import (
	"vlab/app/common/dbs"
	"vlab/app/dao/common"
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	userSrv "vlab/app/service/user"
	"vlab/pkg/helper"
	"vlab/pkg/util"

	"github.com/gin-gonic/gin"
)

// ScriptUserLogin .
func ScriptUserLogin(ctx *gin.Context, uid uint64) (*userDto.UserLoginResp, error) {
	var (
		err      error
		resp     = &userDto.UserLoginResp{}
		userRepo = userDao.GetRepo()
	)

	uAuth, err := userRepo.FetchAuthByUid(ctx, uid)
	if err != nil {
		return nil, err
	}
	// if uAuth, err = NewLoginManager().FindOrCreateUser(ctx, userDao.AtEmail, uAuth); err != nil {
	// 	return nil, err
	// }

	resp.Token, _ = util.GenToken(uAuth.ID, dbs.False)
	if err = userRepo.RedisSetUserToken(ctx, uAuth.ID, resp.Token); err != nil {
		return nil, err
	}
	resp.UserInfo = &userDto.AdminUserInfoResp{
		AdminUserListItem: uAuth.Model.ToUserInfoResp(ctx),
		Auths:             userSrv.GetService().GetUserAuthMethods(ctx, uAuth.ID),
	}
	userSrv.GetUserLogEntity().ProducerLogin(ctx, uAuth)

	return resp, nil
}

// GetAuditLoginUid .
func GetAuditLoginUid(ctx *gin.Context) uint64 {
	channelInfo, _ := helper.GetCtxChannelInfo(ctx)
	switch channelInfo.PlatformID {
	case uint32(common.PlatformViBox):
		return uint64(userDao.AuditUidVibox)
	case uint32(common.PlatformHeytv):
		return uint64(userDao.AuditUidHeytv)
	default:
		return uint64(userDao.AuditUidSkybox)
	}
}
