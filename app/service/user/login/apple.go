package login

import (
	"crypto/rsa"
	"encoding/base64"
	"errors"
	"math/big"
	"net/http"
	"time"

	userDao "vlab/app/dao/user"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt"
)

// ApplePublicKey 包含 Apple 提供的公钥结构
type ApplePublicKey struct {
	Keys []struct {
		Kty string `json:"kty"`
		Kid string `json:"kid"`
		Use string `json:"use"`
		Alg string `json:"alg"`
		N   string `json:"n"`
		E   string `json:"e"`
	} `json:"keys"`
}

type AppleLoginProvider struct{}

func (a *AppleLoginProvider) Login(ctx *gin.Context, authToken string) (*userDao.UserAuth, error) {
	claims, err := VerifyAppleToken(ctx, authToken)
	if err != nil {
		return nil, err
	}

	var (
		uInfo    = &userDao.Model{}
		emailStr = ""
	)
	appleSub := claims["sub"].(string)
	if appleName, ok := claims["name"]; ok {
		uInfo.Nickname = appleName.(string)
	}
	// 不直接存user上的email，存在auth上
	if email, ok := claims["email"]; ok {
		//uInfo.Email = email.(string)
		emailStr = email.(string)
	}

	return &userDao.UserAuth{
		Model:     uInfo,
		AuthType:  a.GetAuthType(),
		AuthUid:   appleSub,
		AuthToken: "",
		AuthEmail: emailStr,
	}, nil
}

func (a *AppleLoginProvider) GetAuthType() uint32 {
	return uint32(userDao.AtApple)
}

// VerifyAppleToken 验证 Apple ID Token
func VerifyAppleToken(ctx *gin.Context, authToken string) (jwt.MapClaims, error) {
	// 1. 解析 Header，获取 kid 并下载公钥
	token, err := jwt.Parse(authToken, func(token *jwt.Token) (interface{}, error) {
		log.Ctx(ctx).WithField("token", token).Info("apple token jwt.Parse ret")
		// 检查签名方法
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			log.Ctx(ctx).Warn("unexpected signing method")
			return nil, errors.New("unexpected signing method")
		}

		// 获取 kid 字段
		kid, ok := token.Header["kid"].(string)
		if !ok {
			log.Ctx(ctx).Warn("kid not found in token header")
			return nil, errors.New("kid not found in token header")
		}

		// 获取 Apple 公钥列表
		keys, err := getApplePublicKeys(ctx)
		if err != nil {
			return nil, err
		}

		// 根据 kid 获取对应的 RSA 公钥
		return getRSAPublicKey(ctx, kid, keys)
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("jwt.Parse apple token error")
		return nil, err
	}

	// 3. Token 验证成功，获取 claims
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		log.Ctx(ctx).WithField("claims", claims).Info("apple token token.Claims ret")
		// 检查 aud 字段（你的 Client ID / Bundle ID）- ios每个渠道都有唯一的 Bundle ID，多渠道时不便校验
		// if claims["aud"] != adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyAppleClientID) {
		// 	log.Ctx(ctx).Warn("invalid audience")
		// 	return nil, errors.New("invalid audience")
		// }
		// 检查 iss 字段（签发者）
		if claims["iss"] != "https://appleid.apple.com" {
			log.Ctx(ctx).Warn("invalid issuer")
			return nil, errors.New("invalid issuer")
		}
		// 检查 Token 过期时间, TODO 需要注意时区
		exp := int64(claims["exp"].(float64))
		if time.Now().Unix() > exp {
			log.Ctx(ctx).Warn("token expired")
			return nil, errors.New("token expired")
		}
		return claims, nil
	}

	log.Ctx(ctx).WithField("authToken", authToken).Warn("invalid token")
	return nil, errors.New("invalid token")
}

// getApplePublicKeys 从 Apple 获取公钥
func getApplePublicKeys(ctx *gin.Context) (*ApplePublicKey, error) {
	var (
		url  = "https://appleid.apple.com/auth/keys"
		keys ApplePublicKey
	)

	resp, err := http.Get(url)
	if err != nil {
		log.Ctx(ctx).Warn("failed to fetch Apple public keys")
		return nil, errors.New("failed to fetch Apple public keys")
	}
	defer resp.Body.Close()

	if err := json.NewDecoder(resp.Body).Decode(&keys); err != nil {
		log.Ctx(ctx).Warn("failed to decode Apple public keys")
		return nil, errors.New("failed to decode Apple public keys")
	}

	return &keys, nil
}

// getRSAPublicKey 根据 kid 查找并构建 RSA 公钥
func getRSAPublicKey(ctx *gin.Context, kid string, keys *ApplePublicKey) (*rsa.PublicKey, error) {
	for _, key := range keys.Keys {
		if key.Kid == kid {
			// 解码 n (base64 URL) 和 e (base64 URL) 字段
			decodedN, err := base64.RawURLEncoding.DecodeString(key.N)
			if err != nil {
				log.Ctx(ctx).Warn("failed to decode modulus (n)")
				return nil, errors.New("failed to decode modulus (n)")
			}
			decodedE, err := base64.RawURLEncoding.DecodeString(key.E)
			if err != nil {
				log.Ctx(ctx).Warn("failed to decode exponent (e)")
				return nil, errors.New("failed to decode exponent (e)")
			}

			// 构建 RSA 公钥
			pubKey := &rsa.PublicKey{
				N: new(big.Int).SetBytes(decodedN),              // N 转为 big.Int
				E: int(new(big.Int).SetBytes(decodedE).Int64()), // E 转为整数
			}
			return pubKey, nil
		}
	}
	log.Ctx(ctx).Warn("no matching key found for kid")
	return nil, errors.New("no matching key found for kid")
}
