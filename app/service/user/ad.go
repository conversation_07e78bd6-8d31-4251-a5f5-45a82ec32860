package user

import (
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	userDao "vlab/app/dao/user"
	deviceDao "vlab/app/dao/user/device"
	watchAdLog "vlab/app/dao/user/watch_ad_log"
	userDto "vlab/app/dto/user"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
)

func (e *Entry) CheckWatchAdTime(ctx *gin.Context) bool {
	// TODO 需要走 CheckUser 中间件
	return true

	// var (
	// 	ctxUser, err = helper.GetCtxUser(ctx)
	// 	uInfo        = &userDao.Model{}
	// 	dInfo        = &deviceDao.Model{}
	// 	timeNow      = timeUtil.GetNowTimestampByZone(ctx)
	// )

	// if ctxUser.UID > 0 {
	// 	if uInfo, err = e.getUserInfo(ctx, ctxUser.UID); err != nil {
	// 		log.Ctx(ctx).WithError(err).Error("getUserInfo err")
	// 		return false
	// 	}
	// 	return uInfo.WatchAdTime > timeNow
	// } else {
	// 	deviceID, _ := helper.GetDeviceNo(ctx)
	// 	if dInfo, err = e.DeviceRepo.RedisDeviceInfo(ctx, deviceID); err != nil {
	// 		log.Ctx(ctx).WithError(err).Error("RedisDeviceInfo err")
	// 		return false
	// 	}
	// 	return dInfo.WatchAdTime > timeNow
	// }
}

func (e *Entry) UserWatchAd(ctx *gin.Context, uid uint64, deviceID string) (*userDto.WatchAdResp, error) {
	var (
		limitNum      = adminConfig.RedisGetConfig[uint32](ctx, adminConfig.KeyUserWatchAdNumDay)
		userAddTime   = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyUserWatchAdAddTime)
		uInfo         = &userDao.Model{}
		dInfo         = &deviceDao.Model{}
		timeNow       = timeUtil.GetNowTimestampByZone(ctx)
		endOfDay      = timeUtil.GetEndOfDayTimestampByZone(ctx)
		uWatchAdTime  int64
		redisNum, err = e.WatchAdRepo.RedisGetWatchAd(ctx, uid, userDao.EtUser)
		resp          = &userDto.WatchAdResp{}
		clientType, _ = helper.GetClientType(ctx)
		versisonID, _ = helper.GetCtxVersionID(ctx)
		channelID, _  = helper.GetCtxChannelID(ctx)
		headers, _    = json.Marshal(ctx.Request.Header)
	)
	if uInfo, err = e.UserRepo.FetchByID(ctx, uid); err != nil {
		return nil, err
	}
	if dInfo, err = e.DeviceRepo.RedisDeviceInfo(ctx, deviceID); err != nil {
		return nil, err
	}

	if redisNum > limitNum {
		resp.WatchAdTime = uInfo.WatchAdTime
		return resp, nil
	}
	uWatchAdTime = uInfo.WatchAdTime

	if redisNum == limitNum-1 {
		uInfo.WatchAdTime = endOfDay
	} else if redisNum != limitNum && uWatchAdTime < timeNow {
		uInfo.WatchAdTime = timeNow + userAddTime
	} else {
		uInfo.WatchAdTime += userAddTime
	}

	watchLog := &watchAdLog.Model{
		EntityID:   uInfo.ID,
		EntityType: uint32(userDao.EtUser),
		BeforeTime: uWatchAdTime,
		AddTime:    userAddTime,
		AfterTime:  uInfo.WatchAdTime,
		ClientType: uint32(clientType),
		VersionID:  versisonID,
		ChannelID:  channelID,
		TraceID:    helper.GetGinRequestID(ctx),
		Header:     string(headers),
	}

	tx := dbs.NewMysqlEngines().Use(true).Begin()
	if err := func() (err error) {
		if err = e.WatchAdRepo.CreateWithTx(ctx, tx, watchLog); err != nil {
			return
		}
		if err = e.UserRepo.CreateOrUpdateWithTx(ctx, tx, uInfo, true); err != nil {
			return
		}
		if dInfo.UserID == 0 {
			if err = e.DeviceRepo.UpdateMapByIDWithTx(ctx, tx, dInfo.ID, dInfo.DeviceID, map[string]interface{}{"user_id": uInfo.ID}); err != nil {
				return
			}
		}
		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("VisitorWatchAd err")
		return nil, err
	}
	e.WatchAdRepo.RedisSetWatchAd(ctx, uid, userDao.EtUser)

	resp.WatchAdTime = uInfo.WatchAdTime
	return resp, nil
}

func (e *Entry) VisitorWatchAd(ctx *gin.Context, deviceID string) (*userDto.WatchAdResp, error) {
	var (
		visitorAddTime = adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyVisitorWatchAdAddTime)
		dInfo          = &deviceDao.Model{}
		timeNow        = timeUtil.GetNowTimestampByZone(ctx)
		endOfDay       = timeUtil.GetEndOfDayTimestampByZone(ctx)
		err            error
		dWatchAdTime   int64
		resp           = &userDto.WatchAdResp{}
		clientType, _  = helper.GetClientType(ctx)
		versisonID, _  = helper.GetCtxVersionID(ctx)
		channelID, _   = helper.GetCtxChannelID(ctx)
		headers, _     = json.Marshal(ctx.Request.Header)
	)

	if dInfo, err = e.DeviceRepo.RedisDeviceInfo(ctx, deviceID); err != nil {
		return nil, err
	}
	if dInfo.ID == 0 {
		return resp, nil
	}
	dWatchAdTime = dInfo.WatchAdTime

	if dWatchAdTime < timeNow {
		dInfo.WatchAdTime = timeNow + visitorAddTime
	} else {
		dInfo.WatchAdTime += visitorAddTime
	}
	if dInfo.WatchAdTime > endOfDay {
		dInfo.WatchAdTime = endOfDay
	}
	watchLog := &watchAdLog.Model{
		EntityID:   dInfo.ID,
		EntityType: uint32(userDao.EtVisitor),
		BeforeTime: dWatchAdTime,
		AddTime:    visitorAddTime,
		AfterTime:  dInfo.WatchAdTime,
		ClientType: uint32(clientType),
		VersionID:  versisonID,
		ChannelID:  channelID,
		TraceID:    helper.GetGinRequestID(ctx),
		Header:     string(headers),
	}

	tx := dbs.NewMysqlEngines().Use(true).Begin()
	if err := func() (err error) {
		if err = e.WatchAdRepo.CreateWithTx(ctx, tx, watchLog); err != nil {
			return
		}
		if err = e.DeviceRepo.UpdateMapByIDWithTx(ctx, tx, dInfo.ID, dInfo.DeviceID, map[string]interface{}{"watch_ad_time": dInfo.WatchAdTime}); err != nil {
			return
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("VisitorWatchAd err")
		return nil, err
	}

	resp.WatchAdTime = dInfo.WatchAdTime
	return resp, nil
}
