package user

import (
	"vlab/app/common/dbs"
	deviceDao "vlab/app/dao/user/device"

	"github.com/gin-gonic/gin"
)

// CreateOrUpdateDevice 方法用于创建或更新设备信息
func (e *Entry) CreateOrUpdateDevice(ctx *gin.Context, deviceNo string) error {
	dInfo, err := e.DeviceRepo.RedisDeviceInfo(ctx, deviceNo)
	if err != nil {
		return err
	}
	if dInfo.ID == 0 {
		// if err := e.RedisCli.Lock(ctx, redis.GetCreateOrUpdateDevice(deviceNo), redis.LockTimeHalfMinute); err != nil {
		// 	return nil
		// }
		// defer e.RedisCli.Unlock(ctx, redis.GetCreateOrUpdateDevice(deviceNo))

		model := &deviceDao.Model{
			// DeviceID: dbs.Generate(),
			DeviceID: deviceNo,
			Status:   dbs.True,
		}
		return e.DeviceRepo.CreateOrUpdate(ctx, model)
	}
	if dInfo.Status != dbs.True {
		return e.DeviceRepo.UpdateMapByID(ctx, dInfo.ID, deviceNo, map[string]interface{}{"status": dbs.True})
	}

	return nil
}
