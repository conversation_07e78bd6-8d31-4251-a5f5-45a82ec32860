package user

import (
	"fmt"
	"strings"
	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SendVerificationCode 发送邮箱验证码
func (e *Entry) SendVerificationCode(ctx *gin.Context, req *userDto.EmailSendCodeReq) error {
	// 验证邮箱格式（基本验证已在binding中完成）
	emailStr := strings.ToLower(strings.TrimSpace(req.Email))

	// 发送验证码
	if err := e.EmailService.SendVerificationCode(ctx, emailStr); err != nil {
		log.Ctx(ctx).WithError(err).WithField("email", emailStr).Error("SendVerificationCode error")
		return err
	}

	log.Ctx(ctx).WithField("email", emailStr).Info("SendVerificationCode success")
	return nil
}

// VerifyCode 验证邮箱验证码
func (e *Entry) VerifyCode(ctx *gin.Context, req *userDto.EmailVerifyCodeReq) (*userDto.EmailVerifyCodeResp, error) {
	emailStr := strings.ToLower(strings.TrimSpace(req.Email))
	code := strings.TrimSpace(req.Code)

	isValid := e.EmailService.VerifyCode(ctx, emailStr, code)

	resp := &userDto.EmailVerifyCodeResp{
		IsValid: isValid,
	}

	if isValid {
		resp.Message = "Code Verified Successfully"
		log.Ctx(ctx).WithField("email", emailStr).Info("Successfully verified email code")
	} else {
		resp.Message = "Code Verification Failed"
		log.Ctx(ctx).WithField("email", emailStr).Warn("Email code verification failed")
	}

	return resp, nil
}

// Register 邮箱注册 TODO 若与原登录方式有冲突，需考虑合并登录方式
func (e *Entry) Register(ctx *gin.Context, req *userDto.EmailRegisterReq) (*userDto.UserLoginResp, error) {
	emailStr := strings.ToLower(strings.TrimSpace(req.Email))
	code := strings.TrimSpace(req.Code)
	noAdTime := adminConfig.RedisGetConfig[int64](ctx, adminConfig.KeyNewUserNoAdTime)

	// 验证验证码
	isValid := e.EmailService.VerifyCode(ctx, emailStr, code)

	if !isValid {
		return nil, fmt.Errorf("code is invalid or has expired")
	}

	// 检查邮箱是否已注册
	existingUser, err := e.UserRepo.FeatchByFilterSort(ctx, &userDao.Filter{
		Email:      emailStr,
		PlatformID: helper.GetCtxPlatformID(ctx),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Fetching existing user failed")
		return nil, ecode.SystemErr
	}
	if existingUser.ID > 0 {
		return nil, fmt.Errorf("email already registered")
	}

	// 加密密码
	hashedPassword, err := helper.EncodeBcrypt(req.Password)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Password encryption failed")
		return nil, ecode.SystemErr
	}

	// 创建用户
	channelID, _ := helper.GetCtxChannelID(ctx)
	platformID := helper.GetCtxPlatformID(ctx)
	timeNow := timeUtil.GetNowTimestampByZone(ctx)
	clientType, _ := helper.GetClientType(ctx)

	nickname := req.Nickname
	if nickname == "" {
		nickname = fmt.Sprintf("User_%s", helper.RandCode(6))
	}

	userInfo := &userDao.Model{
		UUID:          uuid.New().String(),
		Nickname:      nickname,
		Email:         emailStr,
		Password:      hashedPassword,
		Status:        dbs.True,
		ChannelID:     channelID,
		PlatformID:    platformID,
		WatchAdTime:   timeUtil.GetNowTimestampByZone(ctx) + noAdTime,
		LastLoginTime: timeNow,
	}

	// 创建认证记录
	authInfo := &userDao.Auth{
		ChannelID:  uint32(channelID),
		PlatformID: platformID,
		AuthType:   uint32(userDao.AtEmail),
		AuthUid:    emailStr, // 使用邮箱作为AuthUid
		AuthToken:  "",       // 邮箱登录不需要外部token
		AuthEmail:  emailStr,
	}

	// 开启事务
	tx := dbs.NewMysqlEngines().Use(true).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建用户
	if err := e.UserRepo.CreateWithTx(ctx, tx, userInfo); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("Creating user failed")
		return nil, ecode.SystemErr
	}

	// 创建认证记录
	authInfo.UserID = userInfo.ID
	if err := e.UserRepo.CreateOrUpdateAuthWithTx(ctx, tx, authInfo); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("Creating auth record failed")
		return nil, ecode.SystemErr
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Ctx(ctx).WithError(err).Error("Transaction commit failed")
		return nil, ecode.SystemErr
	}

	// 生成JWT token
	token, err := util.GenToken(userInfo.ID, uint64(clientType))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Generating token failed")
		return nil, ecode.SystemErr
	}

	// 保存token到Redis
	if err := e.UserRepo.RedisSetUserToken(ctx, userInfo.ID, token); err != nil {
		log.Ctx(ctx).WithError(err).Error("Saving token to Redis failed")
		return nil, ecode.SystemErr
	}

	resp := &userDto.UserLoginResp{
		Token: token,
		UserInfo: &userDto.AdminUserInfoResp{
			AdminUserListItem: userInfo.ToUserInfoResp(ctx),
			Auths:             e.GetUserAuthMethods(ctx, userInfo.ID),
		},
	}

	GetUserLogEntity().ProducerLogin(ctx, &userDao.UserAuth{
		Model:     userInfo,
		AuthID:    authInfo.ID,
		AuthType:  uint32(userDao.AtEmail),
		AuthUid:   emailStr,
		AuthToken: token,
		AuthEmail: emailStr,
	})

	log.Ctx(ctx).WithField("email", emailStr).WithField("userID", userInfo.ID).Info("Email registration successful")
	return resp, nil
}

// Login 邮箱登录
func (e *Entry) Login(ctx *gin.Context, req *userDto.EmailLoginReq) (*userDto.UserLoginResp, error) {
	var (
		emailStr   = strings.ToLower(strings.TrimSpace(req.Email))
		platformID = helper.GetCtxPlatformID(ctx)
	)

	// 查找用户
	userInfo, err := e.UserRepo.FeatchByFilterSort(ctx, &userDao.Filter{
		Email:      emailStr,
		PlatformID: platformID,
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Fetching user by email failed")
		return nil, ecode.SystemErr
	}
	if userInfo.ID == 0 {
		return nil, fmt.Errorf("email not registered")
	}

	// 检查用户状态
	if userInfo.Status != uint32(dbs.StatusEnable) {
		return nil, ecode.UserDisableErr
	}

	// 验证密码
	if err := helper.CompareBcrypt(userInfo.Password, req.Password); err != nil {
		log.Ctx(ctx).WithField("email", emailStr).Warn("Password verification failed")
		return nil, fmt.Errorf("invalid email or password")
	}

	// 更新最后登录时间
	timeNow := timeUtil.GetNowTimestampByZone(ctx)
	userInfo.LastLoginTime = timeNow
	if err := e.UserRepo.CreateOrUpdate(ctx, userInfo, true); err != nil {
		log.Ctx(ctx).WithError(err).Warn("Updating user last login time failed")
	}

	// 生成JWT token
	clientType, _ := helper.GetClientType(ctx)
	token, err := util.GenToken(userInfo.ID, uint64(clientType))
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Generating token failed")
		return nil, ecode.SystemErr
	}

	// 保存token到Redis
	if err := e.UserRepo.RedisSetUserToken(ctx, userInfo.ID, token); err != nil {
		log.Ctx(ctx).WithError(err).Error("Saving token to Redis failed")
		return nil, ecode.SystemErr
	}

	resp := &userDto.UserLoginResp{
		Token: token,
		UserInfo: &userDto.AdminUserInfoResp{
			AdminUserListItem: userInfo.ToUserInfoResp(ctx),
			Auths:             e.GetUserAuthMethods(ctx, userInfo.ID),
		},
	}

	authInfo, err := e.UserRepo.FetchAuthByUserAndType(ctx, userInfo.ID, uint32(userDao.AtEmail), platformID)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Fetching email auth info failed")
		return nil, ecode.SystemErr
	}

	GetUserLogEntity().ProducerLogin(ctx, &userDao.UserAuth{
		Model:     userInfo,
		AuthID:    authInfo.ID,
		AuthType:  uint32(userDao.AtEmail),
		AuthUid:   authInfo.AuthUid,
		AuthToken: authInfo.AuthToken,
		AuthEmail: authInfo.AuthEmail,
	})

	log.Ctx(ctx).WithField("email", emailStr).WithField("userID", userInfo.ID).Info("Email login successful")
	return resp, nil
}

// ResetPassword 重置密码
func (e *Entry) ResetPassword(ctx *gin.Context, req *userDto.EmailResetPasswordReq) error {
	emailStr := strings.ToLower(strings.TrimSpace(req.Email))
	code := strings.TrimSpace(req.Code)

	// 验证验证码
	isValid := e.EmailService.VerifyCode(ctx, emailStr, code)

	if !isValid {
		return fmt.Errorf("code is invalid or has expired")
	}

	// 查找用户
	userInfo, err := e.UserRepo.FeatchByFilterSort(ctx, &userDao.Filter{
		Email:      emailStr,
		PlatformID: helper.GetCtxPlatformID(ctx),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Fetching user by email failed")
		return ecode.SystemErr
	}
	if userInfo.ID == 0 {
		return fmt.Errorf("email not registered")
	}

	// 加密新密码
	hashedPassword, err := helper.EncodeBcrypt(req.Password)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Password encryption failed")
		return ecode.SystemErr
	}

	// 更新密码
	userInfo.Password = hashedPassword
	if err := e.UserRepo.CreateOrUpdate(ctx, userInfo, true); err != nil {
		log.Ctx(ctx).WithError(err).Error("Updating user password failed")
		return ecode.SystemErr
	}

	log.Ctx(ctx).WithField("email", emailStr).WithField("userID", userInfo.ID).Info("Password reset successful")
	return nil
}

func (e *Entry) CheckEmailExists(ctx *gin.Context, req *userDto.EmailCheckReq) (*userDto.EmailCheckResp, error) {
	emailStr := strings.ToLower(strings.TrimSpace(req.Email))

	// 查找用户
	userInfo, err := e.UserRepo.FeatchByFilterSort(ctx, &userDao.Filter{
		Email:      emailStr,
		PlatformID: helper.GetCtxPlatformID(ctx),
	})
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("Fetching user by email failed")
		return nil, ecode.SystemErr
	}

	resp := &userDto.EmailCheckResp{
		IsValid: !(userInfo.ID > 0),
	}

	if resp.IsValid {
		resp.Message = "current email is not registered" // 当前邮箱未注册账号
		log.Ctx(ctx).WithField("email", emailStr).Debug("email available")
	} else {
		resp.Message = "email already registered"
		log.Ctx(ctx).WithField("email", emailStr).Warn("email already registered")
	}
	return resp, nil
}
