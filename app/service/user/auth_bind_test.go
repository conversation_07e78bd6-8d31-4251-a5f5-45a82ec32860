package user

import (
	"testing"
	userDao "vlab/app/dao/user"
)

func TestMaskEmail(t *testing.T) {
	type args struct {
		addr     string
		keepHead int
		keepTail int
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				addr:     "<EMAIL>",
				keepHead: 2,
				keepTail: 2,
			},
			want: "",
		},
		{
			name: "test2",
			args: args{
				addr:     "<EMAIL>",
				keepHead: 2,
				keepTail: 2,
			},
			want: "",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := userDao.MaskEmail(tt.args.addr, tt.args.keepHead, tt.args.keepTail); got != tt.want {
				t.<PERSON>rf("MaskEmail() = %v, want %v", got, tt.want)
			}
		})
	}
}
