package vip

import (
	"io"
	"os"

	"vlab/app/common/dbs"
	"vlab/app/dao/common"
	"vlab/app/dao/user"
	vipBill "vlab/app/dao/user/vip_bill"
	payLog "vlab/app/dao/vip/pay_log"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/stripe/stripe-go/v82"
	"github.com/stripe/stripe-go/v82/checkout/session"
	"github.com/stripe/stripe-go/v82/price"
	"github.com/stripe/stripe-go/v82/webhook"
)

/*
sandbox
production
*/

func getStripeEnv(liveMode bool) string {
	if liveMode {
		return "production"
	}
	return "sandbox"
}

func (e *Entry) StripeTransactionCreate(ctx *gin.Context, uid uint64, req userDto.ThirdTransactionCreateReq) (*userDto.StripeTransactionCreateResp, error) {

	var (
		priceID = "" // TODO: product ID 到 price ID
		resp    = &userDto.StripeTransactionCreateResp{
			Session: &userDto.StripeSession{},
		}

		params = &stripe.CheckoutSessionParams{
			SuccessURL: stripe.String(req.SuccessURL + "?session_id={CHECKOUT_SESSION_ID}"),
			CancelURL:  stripe.String(req.CancelURL),
			LineItems: []*stripe.CheckoutSessionLineItemParams{
				{
					Price:    stripe.String(priceID),
					Quantity: stripe.Int64(1),
				},
			},
			AutomaticTax: &stripe.CheckoutSessionAutomaticTaxParams{Enabled: stripe.Bool(true)},
		}

		updateUser = true
		logStatus  = uint32(payLog.StatusPaid)
	)

	priceModel, err := price.Get(priceID, nil)
	if err != nil {
		return nil, err
	} // Ensure the price exists

	if model := priceModel.Type; model == stripe.PriceTypeRecurring {
		params.Mode = stripe.String(string(stripe.CheckoutSessionModeSubscription))
	}

	sess, err := session.New(params)
	if err != nil {
		return nil, err
	}

	resp.Session.SessionID = sess.ID
	resp.Session.URL = sess.URL

	var (
		channelID, _ = helper.GetCtxChannelID(ctx)
		headers, _   = json.Marshal(ctx.Request.Header)
		reqData, _   = json.Marshal(map[string]string{"price_id": priceID})
		respData, _  = json.Marshal(sess)
		date         = timeUtil.NowToDateStringByZone(ctx)

		pLog      = &payLog.Model{}
		vBill     = &vipBill.Model{}
		productID = priceModel.Nickname
	)
	uInfo, err := e.UserRepo.FetchByID(ctx, uid)
	if err != nil {
		return nil, err
	}
	expireTime, gracePeriod, err := e.getStripeProductExpireTime(ctx, uInfo.VipTime, productID)
	if err != nil {
		return nil, err
	}

	pLog = &payLog.Model{
		UserID:                uInfo.ID,
		Platform:              uint32(common.VipPlatStripe),
		LogType:               uint32(payLog.LogRequest),
		Status:                logStatus,
		ProductID:             productID,
		TransactionID:         sess.ID, // TODO: 交易ID?
		OriginalTransactionID: sess.ID,
		PurchaseToken:         "",
		Amount:                sess.AmountTotal, //TODO: 单位是分?
		Currency:              string(sess.Currency),
		NotifyID:              "",
		NotifyType:            "client",
		SubType:               "client",
		ExpiresTime:           sess.Subscription.EndedAt / 1000, //TODO: 秒 or 毫秒
		Env:                   getStripeEnv(sess.Livemode),
		ChannelID:             channelID,
		TraceID:               helper.GetGinRequestID(ctx),
		Func:                  "GetTransactionInfo",
		Date:                  date,
		Header:                string(headers),
		ReqData:               string(reqData),
		RespData:              string(respData),
	}

	vBill = &vipBill.Model{
		UserID:     uInfo.ID,
		Platform:   uint32(common.VipPlatStripe),
		Amount:     sess.AmountTotal, //TODO: 单位是分?
		Currency:   string(sess.Currency),
		Env:        getStripeEnv(sess.Livemode),
		BillType:   vipBill.BtStripePay,
		Date:       date,
		BeforeTime: uInfo.VipTime,
		AfterTime:  expireTime,
		ChannelID:  channelID,
	}

	tx := dbs.NewMysqlEngines().UseWithGinCtx(ctx, true).Begin()
	if err := func() (err error) {
		if err = e.PayLogRepo.CreateWithTx(ctx, tx, pLog); err != nil {
			return
		}
		vBill.LogID = pLog.ID

		if updateUser {
			if err = e.VipBillRepo.CreateWithTx(ctx, tx, vBill); err != nil {
				return
			}

			if err = e.UserRepo.UpdateMapByIDWithTx(ctx, tx, uInfo.ID, map[string]interface{}{
				"vip_time":     expireTime,
				"grace_period": gracePeriod,
			}); err != nil {
				return
			}
		}

		return tx.Commit().Error
	}(); err != nil {
		tx.Rollback()
		log.Ctx(ctx).WithError(err).Error("StripeTransaction err")
		return nil, err
	}

	uInfo.VipTime = expireTime
	return resp, nil
}

func (e *Entry) StripeTransactionCheck(ctx *gin.Context, uid uint64, req userDto.ThirdTransactionCheckReq) (*userDto.StripeTransactionCheckResp, error) {
	return nil, nil
}

func (e *Entry) StripeNotify(ctx *gin.Context) error {
	var (
		err   error
		event = stripe.Event{}
	)

	b, err := io.ReadAll(ctx.Request.Body)
	if err != nil {
		return err
	}

	signature := ctx.GetHeader("Stripe-Signature")

	event, err = webhook.ConstructEvent(b, signature, os.Getenv("STRIPE_WEBHOOK_SECRET"))
	if err != nil {
		return err
	}

	log.Ctx(ctx).WithFields(logrus.Fields{
		"eventID": event.ID,
		// TODO: add more event fields as needed
	}).Debug("")

	switch event.Type {

	case stripe.EventTypeCheckoutSessionCompleted:

	case stripe.EventTypeInvoicePaid:

	case stripe.EventTypeInvoicePaymentFailed:

	default:

	}

	var (
		uInfo = &user.Model{}
	)

	if uInfo, err = e.UserRepo.FetchByUUID(ctx, event.Data.Object["client_reference_id"].(string)); err != nil {
		// TODO: 查询用户
		log.Ctx(ctx).WithError(err).WithField("client_reference_id", event.Data.Object["client_reference_id"]).Error("FetchByUUID error")
		return err
	}
	if uInfo.ID == 0 {
		log.Ctx(ctx).WithField("client_reference_id", event.Data.Object["client_reference_id"]).Error("UUIDNotFound")
		return ecode.ParamInvalidErr
	}

	return nil
}
