package vip

import (
	"sync"
	appStoreExec "vlab/app/api/appstore/execute"
	googlePlayExec "vlab/app/api/googleplay/execute"
	paypalExec "vlab/app/api/paypal/execute"
	userDao "vlab/app/dao/user"
	vipBill "vlab/app/dao/user/vip_bill"
	payLog "vlab/app/dao/vip/pay_log"
	"vlab/app/dao/vip/product"
	userSrv "vlab/app/service/user"
	"vlab/pkg/redis"

	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Server interface {
}

// TODO替换
type Entry struct {
	UserRepo       *userDao.Entry
	VipBillRepo    *vipBill.Entry
	PayLogRepo     *payLog.Entry
	UserSrv        *userSrv.Entry
	ProductRepo    *product.Entry
	PaypalExec     *paypalExec.Entry
	AppStoreExec   *appStoreExec.Entry
	GooglePlayExec *googlePlayExec.Entry
	RedisCli       *redis.RedisClient
}

// TODO替换
var (
	// defaultEntry         Server
	defaultEntry         *Entry
	defaultEntryInitOnce sync.Once
)

// func GetService() Server {
func GetService() *Entry {
	if defaultEntry == nil {
		defaultEntryInitOnce.Do(func() {
			defaultEntry = newEntry()
		})
	}
	return defaultEntry
}

func newEntry() *Entry {
	return &Entry{
		UserRepo:       userDao.GetRepo(),
		VipBillRepo:    vipBill.GetRepo(),
		PayLogRepo:     payLog.GetRepo(),
		UserSrv:        userSrv.GetService(),
		ProductRepo:    product.GetRepo(),
		PaypalExec:     paypalExec.GetApi(),
		AppStoreExec:   appStoreExec.GetApi(),
		GooglePlayExec: googlePlayExec.GetApi(),
		RedisCli:       redis.GetRedisClient(),
	}
}
