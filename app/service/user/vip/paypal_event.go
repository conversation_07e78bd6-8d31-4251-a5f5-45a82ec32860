package vip

import (
	payLog "vlab/app/dao/vip/pay_log"

	"github.com/gin-gonic/gin"
	"github.com/go-pay/gopay/paypal"
)

// // PaypalNotify 处理 PayPal 通知
// func (e *Entry) PaypalNotify(ctx *gin.Context, event paypal.WebhookEvent) error {

// 	// 记录通知日志
// 	payLog := &payLog.Model{
// 		Platform:   uint32(common.VipPlatPayPal),
// 		LogType:    uint32(payLog.LogNotify),
// 		Status:     uint32(payLog.StatusWaitPay),
// 		NotifyID:   event.Id,
// 		NotifyType: event.EventType,
// 		SubType:    event.ResourceType,
// 		Env:        env,
// 		TraceID:    helper.GetGinRequestID(ctx),
// 		Func:       "PaypalNotify",
// 		Date:       date,
// 		Header:     string(headers),
// 		ReqData:    string(event.Resource),
// 	}

// 	switch event.EventType {

// 	default:
// 		log.Ctx(ctx).WithField("event_type", event.EventType).Info("Unhandled PayPal event type")
// 		payLog.Status = uint32(payLog.StatusIgnore)
// 	}

// 	// 保存通知日志
// 	if err := e.PayLogRepo.Create(ctx, payLog); err != nil {
// 		log.Ctx(ctx).WithError(err).Error("Failed to create PayPal notification log")
// 		return err
// 	}

// 	return nil
// }

/*============================================= Payment ===================================================*/

// 处理支付完成事件
func (e *Entry) handlePaymentCaptureCompleted(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// // 获取支付记录
	// logTransInfo, err := e.PayLogRepo.FeatchByTransId(ctx, resource.Id)
	// if err != nil {
	// 	return err
	// }
	// if logTransInfo.ID == 0 {
	// 	return ecode.UserPaypalTranceErr
	// }

	// // 更新支付状态
	// payLog.Status = uint32(payLog.StatusPaid)
	// payLog.TransactionID = resource.Id
	// payLog.Amount = int64(resource.Amount.Value * 1000) // 转换为分
	// payLog.Currency = resource.Amount.CurrencyCode

	// // 更新用户VIP时间
	// e.updateUserVipTime(ctx, logTransInfo.UserID, logTransInfo.ProductID)
	return nil
}

// 处理支付被拒绝事件
func (e *Entry) handlePaymentCaptureDenied(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentCapture
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusFailed)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理支付待处理事件
func (e *Entry) handlePaymentCapturePending(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentCapture
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusWaitPay)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理支付退款事件
func (e *Entry) handlePaymentCaptureRefunded(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentCapture
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusRefund)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理支付撤销事件
func (e *Entry) handlePaymentCaptureReversed(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentCapture
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusReversed)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理支付部分退款事件
func (e *Entry) handlePaymentCapturePartiallyRefunded(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.PaymentCapture
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusPartiallyRefunded)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理退款完成事件
func (e *Entry) handlePaymentRefundCompleted(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.RefundDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusRefund)
	// payLog.TransactionID = resource.Id
	// payLog.Amount = int64(resource.Amount.Value * 1000)
	// payLog.Currency = resource.Amount.CurrencyCode
	return nil
}

// 处理退款被拒绝事件
func (e *Entry) handlePaymentRefundDenied(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.RefundDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusRefundDenied)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理退款待处理事件
func (e *Entry) handlePaymentRefundPending(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.RefundDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusRefundPending)
	// payLog.TransactionID = resource.Id
	return nil
}

/*============================================= Order ===================================================*/
// 处理订单批准事件
func (e *Entry) handleCheckoutOrderApproved(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.OrderDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusWaitPay)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理订单完成事件
func (e *Entry) handleCheckoutOrderCompleted(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.OrderDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusPaid)
	// payLog.TransactionID = resource.Id
	// if len(resource.PurchaseUnits) > 0 {
	// 	payLog.Amount = int64(resource.PurchaseUnits[0].Amount.Value * 1000)
	// 	payLog.Currency = resource.PurchaseUnits[0].Amount.CurrencyCode
	// }
	return nil
}

// 处理订单处理事件
func (e *Entry) handleCheckoutOrderProcessed(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.OrderDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusProcessed)
	// payLog.TransactionID = resource.Id
	return nil
}

// 处理订单作废事件
func (e *Entry) handleCheckoutOrderVoided(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.OrderDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusVoided)
	// payLog.TransactionID = resource.Id
	return nil
}

/*============================================= Subscription ===================================================*/

// 处理订阅激活事件
func (e *Entry) handleSubscriptionActivated(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusPaid)
	// payLog.TransactionID = resource.ID
	// if resource.BillingInfo.LastPayment != nil {
	// 	payLog.Amount = int64(resource.BillingInfo.LastPayment.Amount.Value * 1000)
	// 	payLog.Currency = resource.BillingInfo.LastPayment.Amount.CurrencyCode
	// }
	return nil
}

// 处理订阅取消事件
func (e *Entry) handleSubscriptionCancelled(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusCancel)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅创建事件
func (e *Entry) handleSubscriptionCreated(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusWaitPay)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅过期事件
func (e *Entry) handleSubscriptionExpired(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusExpired)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅支付失败事件
func (e *Entry) handleSubscriptionPaymentFailed(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusFailed)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅暂停事件
func (e *Entry) handleSubscriptionSuspended(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusSuspend)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅更新事件
func (e *Entry) handleSubscriptionUpdated(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusUpdate)
	// payLog.TransactionID = resource.ID
	return nil
}

// 处理订阅续订事件
func (e *Entry) handleSubscriptionRenewed(ctx *gin.Context, event paypal.WebhookEvent, payLog *payLog.Model) error {
	// var resource paypal.SubscriptionDetail
	// if err := json.Unmarshal(event.Resource, &resource); err != nil {
	// 	return err
	// }

	// payLog.Status = uint32(payLog.StatusRenewed)
	// payLog.TransactionID = resource.ID
	// if resource.BillingInfo.LastPayment != nil {
	// 	payLog.Amount = int64(resource.BillingInfo.LastPayment.Amount.Value * 1000)
	// 	payLog.Currency = resource.BillingInfo.LastPayment.Amount.CurrencyCode
	// }
	return nil
}
