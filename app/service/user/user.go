package user

import (
	"fmt"
	"vlab/app/common/dbs"
	userDao "vlab/app/dao/user"
	userDto "vlab/app/dto/user"
	"vlab/pkg/ecode"
	"vlab/pkg/helper"
	"vlab/pkg/util"

	"github.com/gin-gonic/gin"
)

// UserLogin .
func (e *Entry) UserLogin(ctx *gin.Context, account, pwd string) (*userDto.UserLoginResp, error) {
	var (
		uInfo = &userDao.Model{}
		err   error
		resp  = &userDto.UserLoginResp{}
	)
	if uInfo, err = e.UserRepo.FeatchByFilterSort(ctx, &userDao.Filter{Email: account}); err != nil {
		return nil, err
	}
	if uInfo.ID == 0 {
		return nil, fmt.Errorf("用户不存在")
	}
	if uInfo.Status != uint32(dbs.StatusEnable) {
		return nil, ecode.UserDisableErr
	}
	// dePwd, _ := helper.AesCbcDecrypt(uInfo.Password, helper.AesKey)
	// if dePwd != pwd {
	// 	return nil, fmt.Errorf("密码不正确")
	// }
	// if uInfo.LastLoginTime == dbs.False {
	// 	resp.IsFirst = dbs.True
	// }

	resp.Token, _ = util.GenToken(uInfo.ID, dbs.False)
	if err = e.UserRepo.RedisSetUserToken(ctx, uInfo.ID, resp.Token); err != nil {
		return nil, err
	}

	return resp, nil
}

// UserEdit .
func (e *Entry) UserEdit(ctx *gin.Context, UID uint64, req userDto.UserEditReq) error {
	uInfo, err := e.GetUserInfo(ctx, UID)
	if err != nil {
		return err
	}
	if req.Name != "" {
		uInfo.Nickname = req.Name
	}
	if req.Mobile != "" {
		uInfo.Mobile = req.Mobile
	}

	if err = e.UserRepo.CreateOrUpdate(ctx, uInfo, true); err != nil {
		return err
	}

	return nil
}

// UserEditPwd .
func (e *Entry) UserEditPwd(ctx *gin.Context, UID uint64, oldPwd, newPwd string) error {
	uInfo, err := e.UserRepo.FetchByID(ctx, UID)
	if err != nil {
		return err
	}
	if uInfo.ID == 0 {
		return ecode.ParamErr
	}
	// dePwd, _ := helper.AesCbcDecrypt(uInfo.Password, helper.AesKey)
	// if dePwd != oldPwd {
	// 	return fmt.Errorf("原密码不正确")
	// }

	enPwd, _ := helper.AesCbcEncrypt(newPwd, helper.AesKey, helper.AesIV)
	if err = e.UserRepo.UpdateMapByID(ctx, UID, map[string]interface{}{"password": enPwd}); err != nil {
		return err
	}
	return nil
}
