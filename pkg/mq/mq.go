package mq

import (
	"context"
	"fmt"
	"time"

	"vlab/pkg/log"
)

type ProducerType int

var (
	producer Producer
)

const (
	NATS ProducerType = iota + 1
	KAFKA
	REDIS
	RBTMQ
	CHAN
)

func (p ProducerType) String() string {
	switch p {
	case NATS:
		return "nats"
	case KAFKA:
		return "kafka"
	case REDIS:
		return "redis"
	case RBTMQ:
		return "rabbitmq"
	case CHAN:
		return "chan"
	default:
		return "unknown"
	}
}

func Setup() {

	//_ = Configure(KAFKA, config.KafkaCfg.Producer.Address)
	_ = Configure(CHAN, nil)
}

type Producer interface {
	// Push 推送消息
	Push(topic string, message string, key ...string) error
	// Close 关闭生产者
	Close()
}

// Configure 设置
func Configure(mqType ProducerType, address []string) error {
	producer = nil
	switch mqType {
	case KAFKA:
		producer = newKafkaProducer(address)
	case NATS:
		fallthrough
	case REDIS:
		fallthrough
	case RBTMQ:
		fallthrough
	case CHAN:
		producer = newChanProducer()
	default:
		return fmt.Errorf("not implement mq type %d", mqType)
	}
	return nil
}

// Push 推送消息
func Push(topic string, message string, key ...string) error {
	if producer == nil {
		err := fmt.Errorf("producer not available")
		log.WithError(context.Background(), err).Error("push message error")
		return err
	}
	return producer.Push(topic, message, key...)
}

// Close 关闭生产者
func Close() {
	if producer == nil {
		log.WithTime(context.Background(), time.Now()).Error("closing but producer not available")
		return
	}
	producer.Close()
}
