package mq

import (
	"reflect"
	"sync"
)

type EventListenerFunc func(data any)

type eventListener struct {
	async    bool
	Listener EventListenerFunc
}

type EventBus struct {
	mutex     *sync.RWMutex
	listeners map[string][]*eventListener
	async     bool
}

func NewEventBus() *EventBus {
	return &EventBus{
		mutex:     new(sync.RWMutex),
		listeners: map[string][]*eventListener{},
	}
}

func (e *EventBus) Subscribe(event interface{}, listener EventListenerFunc) {
	e.subscribe(event, listener, false)
}

func (e *EventBus) SubscribeAsync(event interface{}, listener EventListenerFunc) {
	e.subscribe(event, listener, true)
}

func (e *EventBus) subscribe(event interface{}, listener EventListenerFunc, async bool) {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	name := e.getEventName(event)
	list, ok := e.listeners[name]
	if !ok {
		list = []*eventListener{}
	}
	list = append(list, &eventListener{
		async,
		listener,
	})
	e.listeners[name] = list
}

func (e *EventBus) Publish(event interface{}) {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	name := e.getEventName(event)
	list, ok := e.listeners[name]
	if !ok {
		return
	}
	for _, each := range list[:] {
		if each.async {
			go each.Listener(event)
		} else {
			each.Listener(event)
		}
	}
}

func (e *EventBus) getEventName(event interface{}) string {
	t := reflect.TypeOf(event)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	return t.Name()
}

var defaultEventBus = NewEventBus()

func Subscribe(event interface{}, listener EventListenerFunc) {
	defaultEventBus.Subscribe(event, listener)
}

func SubscribeAsync(event interface{}, listener EventListenerFunc) {
	defaultEventBus.SubscribeAsync(event, listener)
}

func Publish(event interface{}) {
	defaultEventBus.Publish(event)
}
