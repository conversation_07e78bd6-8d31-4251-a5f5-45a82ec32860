package mq

import (
	"sync"
	"time"

	adminLog "vlab/app/dao/admin_log"
)

var _ Producer = new(ChanProducer)

type ChanProducer struct {
	logChan  chan *adminLog.Model
	done     chan bool
	mu       sync.Mutex
	isClosed bool
}

func newChanProducer() *ChanProducer {
	return &ChanProducer{
		logChan: make(chan *adminLog.Model, 1000),
		done:    make(chan bool),
	}
}

func (l *ChanProducer) Push(topic string, message string, key ...string) error {
	return nil
}

func (l *ChanProducer) Close() {
	select {
	case <-l.done:
		// fmt.Println(l.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(l.Name(), " close Waiting")
	}
}
