package ecode

/***********************************************************
* 100 100 6位错误码(前三位定义项目功能, 后三位定义错误)
* 前3位,如:
* 100:系统 101: 后台管理 102:用户相关 103:商品相关 104:订单相关
* 105:收款单 106:订单
* 107: 营销 1071xx: 公告
* 108: 仓库 1081xx: 出库 1082xx: 入库
* 后期功能各自占位即可
************************************************************/
var (
	OK                = New("100000", "成功")
	CommonErrCode     = "100001"
	SystemErr         = New("100002", "系统异常，请联系客服")
	SystemBusyErr     = New("100003", "系统繁忙，稍后重试")
	ParamErr          = New("100004", "参数错误")
	TokenErr          = New("100005", "token已失效")
	EmptyDataErr      = New("100006", "数据为空")
	ParamInvalidErr   = New("100007", "参数无效")
	ForbiddenErr      = New("100008", "Forbidden")
	VersionInvalidErr = New("100009", "Invalid version, please upgrade to the latest version")
	VersionUpdateErr  = New("100010", "please upgrade to the latest version")
	NameExistErr      = New("100101", "该名称已存在,不可重复")
	VersionExistErr   = New("100102", "该版本号已存在,不可重复")
	NoAuthErr         = New("100103", "无权进行此操作")
	TimeInvalidErr    = New("100104", "时间参数无效")
	NotFoundErr       = New("100105", "未找到相关数据")
	MoreThanMaxErr    = New("100106", "keyword is too long") // 超过最大限制

	// 后台管理相关(101):【0】角色 【1】菜单 【2】用户
	AccountNeedRootAuthErr = New("101001", "仅限超级管理员可进行此操作")
	RoleExistErr           = New("101002", "角色名称不可重复")
	RoleDisableErr         = New("101003", "该角色已禁用")
	RoleHasAccountErr      = New("101004", "该角色下存在账号,不可删除")

	MenuPidSelfErr        = New("101101", "父级菜单不可为自身")
	MenuPidIsSelfChildErr = New("101102", "父级菜单不可为自己的子级菜单")
	MenuPidButtonErr      = New("101103", "父级菜单不可为按钮类型")
	MenuNotExistErr       = New("101104", "当前节点不存在,请先添加")
	MenuHasNotAuthErr     = New("101105", "无此权限,请联系管理员添加")

	MenuDelSystemMenuErr = New("101110", "系统菜单不可删除")
	MenuDelHasChildErr   = New("101111", "该菜单下存在子级菜单,请先删除子级菜单")

	AccountExistErr              = New("101201", "账号不可重复")
	AccountDisableErr            = New("101202", "无法禁用该账号，请先移除管理的客户账号")
	AdminAccountNotExistErr      = New("101203", "该账号不存在")
	AdminAccountDisableErr       = New("101204", "该账号已禁用")
	AdminAccountPwdErr           = New("101205", "该账号密码错误")
	AdminAccountNotOwnerErr      = New("101206", "仅限负责人可进行此操作")
	AdminBatchAccountNotOwnerErr = New("101207", "仅限负责人可进行此操作")
	AccountDelErr                = New("101208", "无法删除该账号，请先移除管理的客户账号")

	// 用户相关(102): 【0】用户管理 【1】前台用户
	UserDisableErr = New("102010", "该用户已禁用")
	UserExistErr   = New("102011", "登录账号名不可重复")

	UserPlatformErr            = New("102101", "此平台未开放")
	UserLoginAuthTypeErr       = New("102102", "the login method is not supported")
	UserAppleTransactionErr    = New("102103", "apple transaction verify err")
	UserAppStoreTraceIDUsedErr = New("102104", "app store transaction used")

	UserPaypalOrderNotFound        = New("102120", "paypal order not found")
	UserPaypalNotifyOrderNotFound  = New("102121", "paypal notify order not found")
	UserPaypalNotifyOrderVerifyErr = New("102122", "paypal notify order verify err")

	UserPaypalSubNotFound        = New("102131", "paypal subscripe not found")
	UserPaypalNotifySubVerifyErr = New("102132", "paypal notify subscripe verify err")

	UserPaypalTranceErr = New("102141", "paypal transaction verify err")

	// vip
	UserGooglePlayTraceIDUsedErr   = New("102201", "Google Play transaction ID has been used")
	UserGooglePlayTransactionErr   = New("102202", "Google Play transaction verification failed")
	UserGooglePlayPurchaseStateErr = New("102203", "Google Play purchase not pay")
	UserGooglePlayOrderStateErr    = New("102204", "Google Play order not success")

	UserPaypalServerBusyErr  = New("102220", "Paypal server busy")
	UserPayPalTransactionErr = New("102221", "PayPal transaction verification failed")

	// vip product
	UserVipRegionProductNotExistErr = New("102301", "region product not exist")
	UserVipProductRegionNotExistErr = New("102302", "product region not exist")
	UserVipProductNotExistErr       = New("102303", "product not exist")

	UserConfigVIpProductNotExistErr = New("102304", "config product not exist")

	// 视频转码相关(103):
	VideoMpsTranscodingTempNotExist = New("103001", "暂不支持此转码方式")

	// 点播相关(104)
	VodUploadMediaByUrlsErr = New("104001", "upload url err")

	// resource相关(110):
	ChannelIDInvalidErr         = New("110101", "channel invalid")
	ChannelConfigInvalidErr     = New("110102", "channel config invalid")
	ChannelKeyChannelIDExistErr = New("110201", "该渠道已设置, 请勿重复设置")
	ChannelNotExistErr          = New("110202", "该渠道不存在")

	VersionIDInvalidErr = New("110151", "version invalid")
)
