package ecode

import (
	"fmt"
	"strconv"
	"sync"

	"github.com/pkg/errors"
)

var (
	_messages sync.Map
	_codes    = map[string]struct{}{}
)

func New(c string, msg string) Code {
	_messages.Store(c, msg)
	if _, ok := _codes[c]; ok {
		panic(fmt.Sprintf("ecode: %s already exist", c))
	}
	_codes[c] = struct{}{}
	return Code(c)
}

type Codes interface {
	Error() string
	Code() string
	Message() string
	FormatMsg(...any) string
}

type Code string

func (c Code) Error() string {
	return string(c)
}

func (c Code) Code() string {
	if _, err := strconv.Atoi(string(c)); err != nil {
		return CommonErrCode
	}
	return string(c)
}

func (c Code) Message() string {
	if msg, ok := _messages.Load(c.Code()); ok {
		return msg.(string)
	}

	return c.Error()
}

func (c Code) FormatMsg(a ...any) string {
	if msg, ok := _messages.Load(c.Code()); ok {
		msg := msg.(string)
		if len(a) > 0 {
			return fmt.Sprintf(msg, a...)
		}
		return msg
	}

	return c.Error()
}

func Cause(e error) Codes {
	if e == nil {
		return OK
	}
	ec, ok := errors.Cause(e).(Codes)
	if ok {
		return ec
	}
	return Code(e.Error())
}
