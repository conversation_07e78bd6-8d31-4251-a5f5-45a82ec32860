package helper

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"math/big"
	"strings"
	"vlab/pkg/log"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/bcrypt"
)

const (
	AesReqKey  = "Z98BwrviSKDCAsXAcInKRjcAiBJcjlWi"
	AesRespKey = "bOzpJ93aag39whQK6bMRkwml9DrYGjvl"
	AesKey     = "196b9a0f7x3c6d9e"
	AesIV      = "196b9a0f7x3c6d9e"

	SignPrivateKey = "XH42LyXFjqH9O62zDSmGWHXX6/ZwOokS1luV2SfaSmM="
	SignPublicKeyX = "yYbHDvRxyO64CsXhfhBQz9NX0j/5gAGIHaBYMhr6UnQ="
	SignPublicKeyY = "Bxe+uifc79y4MEBbA7RFSzAguW3SsOqw7cCixT/TL2U="
)

// EncodeMd5 .
func EncodeMd5(str string) string {
	strByte := []byte(str)
	ret := md5.New()
	ret.Write(strByte)
	return hex.EncodeToString(ret.Sum(nil))
}

// EncodeBcrypt .
func EncodeBcrypt(str string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(str), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedPassword), nil
}

// CompareBcrypt 验证bcrypt密码
func CompareBcrypt(hashedPassword, password string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// AesCbcEncrypt .
func AesCbcEncrypt(str, aesKey, iv string) (string, error) {
	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		return "", err
	}

	ret := PKCS7Padding([]byte(str), block.BlockSize())

	ciphertext := make([]byte, len(ret))
	mode := cipher.NewCBCEncrypter(block, []byte(iv))
	mode.CryptBlocks(ciphertext, ret)

	encodeRet := base64.StdEncoding.EncodeToString(ciphertext)
	return encodeRet, nil
}

// AesCbcEncryptByte .
func AesCbcEncryptByte(data []byte, aesKey, iv string) (string, error) {
	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		return "", err
	}

	ret := PKCS7Padding(data, block.BlockSize())

	ciphertext := make([]byte, len(ret))
	mode := cipher.NewCBCEncrypter(block, []byte(iv))
	mode.CryptBlocks(ciphertext, ret)

	encodeRet := base64.StdEncoding.EncodeToString(ciphertext)
	return encodeRet, nil
}

// AesCbcDecrypt .
func AesCbcDecrypt(str, aesKey, iv string) (string, error) {
	decodeRet, err := base64.StdEncoding.DecodeString(str)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		return "", err
	}

	plainStr := make([]byte, len(decodeRet))
	mode := cipher.NewCBCDecrypter(block, []byte(iv))
	mode.CryptBlocks(plainStr, decodeRet)

	plainStr = PKCS7UnPadding(plainStr)
	return string(plainStr), nil
}

// PKCS7Padding 使用PKCS7填充原始数据
func PKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// PKCS7UnPadding 移除PKCS7填充
func PKCS7UnPadding(data []byte) []byte {
	length := len(data)
	unpadding := int(data[length-1])
	return data[:(length - unpadding)]
}

// GenerateEcdsaKeys 生成密钥对
func GenerateEcdsaKeys(ctx *gin.Context) (*ecdsa.PrivateKey, error) {
	privateKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		return nil, err
	}

	// 获取私钥 D 并编码为 Base64
	privateKeyBytes := privateKey.D.Bytes()
	privateKeyBase64 := base64.StdEncoding.EncodeToString(privateKeyBytes)

	// 获取公钥 X 和 Y 并编码为 Base64
	publicKeyXBase64 := base64.StdEncoding.EncodeToString(privateKey.PublicKey.X.Bytes())
	publicKeyYBase64 := base64.StdEncoding.EncodeToString(privateKey.PublicKey.Y.Bytes())

	log.Ctx(ctx).WithFields(logrus.Fields{
		"PrivateKey": privateKeyBase64,
		"PublicKeyX": publicKeyXBase64,
		"PublicKeyY": publicKeyYBase64,
	}).Info("GenerateEcdsaKeys")

	return privateKey, nil
}

// HmacHashSignMsg 签名消息
func HmacHashSignMsg(ctx *gin.Context, msg, signKey string) string {
	hObj := hmac.New(sha256.New, []byte(signKey))
	hObj.Write([]byte(msg))
	signStr := base64.StdEncoding.EncodeToString(hObj.Sum(nil))

	log.Ctx(ctx).WithFields(logrus.Fields{
		"msg":         msg,
		"HmacHashKey": signKey,
		"signStr":     signStr,
	}).Info("HmacHashSignMsg")

	return signStr
}

// HmacHashVerifySignature 验证签名
func HmacHashVerifySignature(ctx *gin.Context, msg, signStr, signKey string) bool {
	hObj := hmac.New(sha256.New, []byte(signKey))
	hObj.Write([]byte(msg))
	expectedMAC := hObj.Sum(nil)

	clientMAC, err := base64.StdEncoding.DecodeString(signStr)
	log.Ctx(ctx).WithFields(logrus.Fields{
		"msg":         msg,
		"signStr":     signStr,
		"expectedMAC": expectedMAC,
		"clientMAC":   clientMAC,
	}).Info("HmacHashVerifySignature")
	if err != nil {
		log.Ctx(ctx).Warn("HmacHashVerifySignature Failed to decode signature")
		return false
	}

	return hmac.Equal(expectedMAC, clientMAC)
}

// EcdsaSignMsg 签名消息
func EcdsaSignMsg(ctx *gin.Context, msg string) (string, error) {
	// 解码 Base64 私钥 并 创建私钥对象
	privateKeyBytes, err := base64.StdEncoding.DecodeString(SignPrivateKey)
	if err != nil {
		return "", fmt.Errorf("failed to decode private key: %v", err)
	}
	privateKey := new(ecdsa.PrivateKey)
	privateKey.Curve = elliptic.P256() // 使用 P-256 曲线
	privateKey.D = new(big.Int).SetBytes(privateKeyBytes)

	hash := sha256.Sum256([]byte(msg))
	r, s, err := ecdsa.Sign(rand.Reader, privateKey, hash[:])
	if err != nil {
		return "", err
	}
	rbase64, sbase64 := base64.StdEncoding.EncodeToString(r.Bytes()), base64.StdEncoding.EncodeToString(s.Bytes())
	signStr := fmt.Sprintf("%s.%s", rbase64, sbase64)

	log.Ctx(ctx).WithFields(logrus.Fields{
		"msg":     msg,
		"rbase64": rbase64,
		"sbase64": sbase64,
		"signStr": signStr,
	}).Info("EcdsaSignMsg")

	return signStr, nil
}

// EcdsaVerifySignature 验证签名
func EcdsaVerifySignature(ctx *gin.Context, msg, signStr string) bool {
	var (
		x, _   = base64.StdEncoding.DecodeString(SignPublicKeyX)
		y, _   = base64.StdEncoding.DecodeString(SignPublicKeyY)
		pubKey = &ecdsa.PublicKey{
			Curve: elliptic.P256(),
			X:     new(big.Int).SetBytes(x),
			Y:     new(big.Int).SetBytes(y),
		}
		hash  = sha256.Sum256([]byte(msg))
		parts = strings.Split(signStr, ",")
	)
	log.Ctx(ctx).WithFields(logrus.Fields{"msg": msg, "signStr": signStr}).Info("EcdsaVerifySignature")
	if len(parts) != 2 {
		log.Ctx(ctx).Warn("Invalid signature format")
		return false
	}

	// Base64 解码 r 和 s
	rBytes, _ := base64.StdEncoding.DecodeString(parts[0])
	sBytes, _ := base64.StdEncoding.DecodeString(parts[1])

	r := new(big.Int).SetBytes(rBytes)
	s := new(big.Int).SetBytes(sBytes)

	return ecdsa.Verify(pubKey, hash[:], r, s)
}
