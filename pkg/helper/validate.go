package helper

import (
	"fmt"
	"reflect"
	"regexp"
	"strconv"
	"strings"
	"time"

	"vlab/app/dto"
	"vlab/pkg/ecode"

	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zhs "github.com/go-playground/validator/v10/translations/zh"
	"github.com/mattn/go-runewidth"
)

var (
	zhv       = zh.New()
	uni       = ut.New(zhv, zhv)
	trans, _  = uni.GetTranslator("zh")
	errMsgMap = make(map[string]string)
)

const (
	codeValidRegExp = "^[a-zA-Z0-9_-]+$"
)

// LoadAppValidate 注册自定义的验证方法
func LoadAppValidate() {
	vld, _ := binding.Validator.Engine().(*validator.Validate)
	zhs.RegisterDefaultTranslations(vld, trans)
	vld.RegisterValidation("checkPhone", checkPhone)
	vld.RegisterValidation("requiredIF", requiredIF)
	vld.RegisterValidation("checkArrayRequired", checkArrayRequired)
	vld.RegisterValidation("checkStringLength", checkStringLength)
	vld.RegisterValidation("checkCodeValid", checkCodeValid)
	vld.RegisterValidation("checkVersion", checkVersion)
	vld.RegisterValidation("withinOneYear", withinOneYear)

	LoadCustomValidateMsg()
}

// 加载自定义验证信息
func LoadCustomValidateMsg() {
	errMsgMap["Phone.checkPhone"] = "手机号格式不正确"
	errMsgMap["checkStringLength"] = "长度超出限制"
	errMsgMap["Version.checkVersion"] = "版本号格式不正确"
}

var requiredIF validator.Func = func(fl validator.FieldLevel) bool {
	params := strings.Split(fl.Param(), " ")
	confirmField := fl.Parent().FieldByName(params[0])

	switch confirmField.Kind() {
	case reflect.Uint32:
		configval := uint32(confirmField.Uint())
		switch fl.Field().Kind() {
		case reflect.Slice:
			for idx, val := range params {
				if idx == 0 {
					continue
				}
				if val == fmt.Sprint(configval) && fl.Field().IsZero() {
					return false
				}
			}
		case reflect.Uint64:
			for idx, val := range params {
				if idx == 0 {
					continue
				}
				if val == fmt.Sprint(configval) && fl.Field().Uint() == 0 {
					return false
				}
			}
		case reflect.Float64, reflect.Float32:
			if params[1] == fmt.Sprint(configval) && fl.Field().Float() == 0 {
				return false
			}
		}
	case reflect.String:
		configval := confirmField.String()
		switch fl.Field().Kind() {
		case reflect.String:
			if params[1] == configval && fl.Field().String() == "" {
				return false
			}
		}
	}
	return true
}

var checkPhone validator.Func = func(fl validator.FieldLevel) bool {
	var ok bool = true
	if fl.Field().String() != "" {
		ok, _ = regexp.MatchString(`^1[3-9][0-9]{9}$`, fl.Field().String())
	}
	return ok
}

func String2AsciiLen(str string) int {
	reg := regexp.MustCompile(`(\x9B|\x1B\[)[0-?]*[ -/]*[@-~]`)
	return runewidth.StringWidth(reg.ReplaceAllString(str, ""))
}

// checkStringLength 验证该字段字符串长度
var checkStringLength validator.Func = func(fl validator.FieldLevel) bool {
	col := fl.Field().String()

	limitStr := fl.Param()
	limit, _ := strconv.Atoi(limitStr)
	if limit <= 0 {
		return false
	}
	if count := String2AsciiLen(col); count > limit {
		return false
	}

	return true
}

var checkCodeValid validator.Func = func(fl validator.FieldLevel) bool {
	code := fl.Field().String()
	if code == "" {
		return true
	}

	// 验证code是否合法
	if ok, _ := regexp.MatchString(codeValidRegExp, code); !ok {
		return false
	}

	return true
}

var checkVersion validator.Func = func(fl validator.FieldLevel) bool {
	version := fl.Field().String()
	if version == "" {
		return false
	}

	// 验证version是否有效
	versionList := strings.Split(version, ".")
	if len(versionList) != 3 {
		return false
	}

	for _, val := range versionList {
		valInt, err := strconv.Atoi(val)
		if err != nil {
			return false
		}
		if valInt >= 1000 {
			return false
		}
	}

	return true
}

var withinOneYear validator.Func = func(fl validator.FieldLevel) bool {
	req, ok := fl.Parent().Interface().(dto.BaseTimeRangeReq)
	if !ok {
		return false
	}
	startTime := time.Unix(req.StartTime, 0)
	endTime := time.Unix(req.EndTime, 0)

	// 检查时间差是否超过一年
	oneYearLater := startTime.AddDate(1, 0, 0)
	return !endTime.After(oneYearLater)
}

var checkArrayRequired validator.Func = func(fl validator.FieldLevel) bool {
	field := fl.Field()
	if field.Kind() != reflect.Slice {
		return false
	}
	return field.Len() > 0
}

// Translate 翻译错误信息 - 展示单个错误
func Translate(err error) (errMsg string) {
	if errs, ok := err.(validator.ValidationErrors); ok {
		e := errs[0]
		key := fmt.Sprintf("%v.%v", e.Field(), e.Tag())
		if msg, ok := errMsgMap[key]; ok {
			errMsg = msg
		} else {
			errMsg = e.Translate(trans)
		}
		return errMsg
	}
	if err.Error() == "EOF" {
		return ecode.ParamErr.Message()
	}
	return err.Error()
}

// CustomTranslate .
func CustomTranslate(err error, obj interface{}) string {
	getObj := reflect.TypeOf(obj)
	if errs, ok := err.(validator.ValidationErrors); ok {
		e := errs[0]
		key := fmt.Sprintf("%v.%v", e.Field(), e.Tag())
		if msg, ok := errMsgMap[key]; ok {
			return msg
		}
		switch getObj.Elem().Kind() {
		case reflect.Struct:
			if f, exist := getObj.Elem().FieldByName(e.Field()); exist {
				msg := f.Tag.Get("msg")
				if msg != "" {
					return msg
				}
				return e.Translate(trans)
			}

		case reflect.Pointer:
			// 获取指针指向的类型
			if f, exist := getObj.Elem().Elem().FieldByName(e.Field()); exist {
				msg := f.Tag.Get("msg")
				if msg != "" {
					return msg
				}
				return e.Translate(trans)
			}

		default:
		}

		return e.Translate(trans)
	}
	if err.Error() == "EOF" {
		return ecode.ParamErr.Message()
	}
	return err.Error()
}

func TestTranslate(err error, obj interface{}) string {
	if errs, ok := err.(validator.ValidationErrors); ok {
		for _, e := range errs {
			// 首先检查是否在错误消息映射表中
			key := fmt.Sprintf("%v.%v", e.Field(), e.Tag())
			if msg, ok := errMsgMap[key]; ok {
				return msg
			}

			// 递归查找字段的 msg 标签
			msg := findFieldMsg(reflect.ValueOf(obj), e.Field())
			if msg != "" {
				return msg
			}

			// 如果没有找到自定义消息，返回默认的翻译
			return e.Translate(trans)
		}
	}

	if err.Error() == "EOF" {
		return ecode.ParamErr.Message()
	}
	return err.Error()
}

// findFieldMsg 递归查找字段的 msg 标签内容
func findFieldMsg(value reflect.Value, fieldName string) string {
	// 如果是指针，获取其元素
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	// 检查是否为结构体
	if value.Kind() == reflect.Struct {
		// 遍历结构体中的字段
		for i := 0; i < value.NumField(); i++ {
			field := value.Type().Field(i)
			fieldValue := value.Field(i)

			// 如果找到匹配的字段名称，返回 msg 标签
			if field.Name == fieldName {
				return field.Tag.Get("msg")
			}

			// 如果字段是嵌套的结构体或切片，递归查找
			switch fieldValue.Kind() {
			case reflect.Struct:
				if msg := findFieldMsg(fieldValue, fieldName); msg != "" {
					return msg
				}
			case reflect.Slice:
				// 遍历切片的每个元素
				for j := 0; j < fieldValue.Len(); j++ {
					elem := fieldValue.Index(j)
					if elem.Kind() == reflect.Ptr {
						elem = elem.Elem()
					}
					if elem.Kind() == reflect.Struct {
						if msg := findFieldMsg(elem, fieldName); msg != "" {
							return msg
						}
					}
				}
			default:
				continue
			}
		}
	}
	return ""
}

// TranslateBatch 翻译错误信息 - 展示全部错误
func TranslateBatch(err error) map[string][]string {
	var res = make(map[string][]string)
	errors := err.(validator.ValidationErrors)

	for _, err := range errors {
		key := fmt.Sprintf("%v.%v", err.Field(), err.Tag())
		if msg, ok := errMsgMap[key]; ok {
			res[err.Field()] = append(res[err.Field()], msg)
		} else {
			res[err.Field()] = append(res[err.Field()], err.Translate(trans))
		}
	}

	return res
}
