package helper

import (
	"testing"
)

func TestString2AsciiLen(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				str: "BUSHIROAD Creative 盒蛋 灵能百分百 100 III  集换式 方形亚克力立牌 Vol.2 再版 1盒10个(BOX:4571598656284)",
			},
			want: 4,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := String2AsciiLen(tt.args.str); got != tt.want {
				t.Errorf("String2AsciiLen() = %v, want %v", got, tt.want)
			}
		})
	}
}
