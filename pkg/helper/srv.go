package helper

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"
)

var (
	appSrvMgrInit sync.Once
	appSrvMgr     *AppSrvMgr
)

type AppSrvMgr struct {
	Ctx     context.Context
	appSrvs []AppSrv
}

func GetAppSrvMgr(ctx context.Context) *AppSrvMgr {
	if appSrvMgr == nil {
		appSrvMgrInit.Do(func() {
			appSrvMgr = newAppSrvMgr(ctx)
		})
	}
	return appSrvMgr
}

func newAppSrvMgr(ctx context.Context) *AppSrvMgr {
	return &AppSrvMgr{
		Ctx: ctx,
	}
}

func (a *AppSrvMgr) Register(appSrv ...AppSrv) {
	a.appSrvs = append(a.appSrvs, appSrv...)
}

func (a *AppSrvMgr) Start(ctx context.Context) error {
	ctx = ctxUtil.NewRequestID(ctx)
	for _, srv := range a.appSrvs {
		if err := srv.Start(ctx); err != nil {
			msg := fmt.Sprintf("%s start err: %s", srv.Name(), err.Error())
			log.WithError(ctx, err).Error(msg)
			return errors.New(msg)
		}
		log.WithContext(ctx).Info("%s started", srv.Name())
	}
	return nil
}

func (a *AppSrvMgr) Close(ctx context.Context) error {
	ctxExec, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	for _, srv := range a.appSrvs {
		AsyncDo(func() {
			if err := srv.Close(ctxExec); err != nil {
				fmt.Printf("%s close err: %s\n", srv.Name(), err.Error())
				return
			}
			fmt.Println(srv.Name(), " closed")
		})
	}
	return nil
}

type AppSrv interface {
	Start(ctx context.Context) error
	Close(ctx context.Context) error
	Name() string
}
