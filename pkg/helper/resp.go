package helper

import (
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"vlab/pkg/log"
	"vlab/pkg/util/timeUtil"

	"github.com/gin-gonic/gin"
	jsoniter "github.com/json-iterator/go"
)

var (
	json = jsoniter.ConfigCompatibleWithStandardLibrary
)

type Resp struct {
	Code    string      `json:"code"`
	Msg     string      `json:"msg"`
	Data    interface{} `json:"data"`
	Runtime float64     `json:"runtime"`
	Time    int64       `json:"time"`
}

func AppResp(ctx *gin.Context, code, msg string) {
	clientType, _ := GetClientType(ctx)
	channelKeyInfo, _ := GetCtxChannelKeyInfo(ctx, true)
	versionStatus, _ := GetCtxVersionStatus(ctx)
	ctx.Header("Trace-Id", GetGinRequestID(ctx))
	ctx.Header("vstatus", versionStatus)

	resp := Resp{
		Code:    code,
		Msg:     msg,
		Runtime: GetElapsedTime(ctx),
		Time:    timeUtil.GetNowTimestampByZone(ctx),
	}

	if clientType.IsDecryptClientType() && !JudgePassCheck(ctx) {
		respByte, err := json.Marshal(resp)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AppResp marshal err")
		}
		respStr, err := AesCbcEncryptByte(respByte, channelKeyInfo.RespKey, channelKeyInfo.IV)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AppResp AesCbcEncryptByte err")
		}

		ctx.Header("Content-Length", strconv.Itoa(len(respStr)))
		ctx.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(respStr))
	} else {
		ctx.JSON(http.StatusOK, resp)
	}
	ctx.Set("rspCode", code)
}

func AppWithDataResp(ctx *gin.Context, code, msg string, data interface{}) {
	clientType, _ := GetClientType(ctx)
	channelKeyInfo, _ := GetCtxChannelKeyInfo(ctx, true)
	versionStatus, _ := GetCtxVersionStatus(ctx)
	ctx.Header("Trace-Id", GetGinRequestID(ctx))
	ctx.Header("vstatus", versionStatus)

	resp := Resp{
		Code:    code,
		Msg:     msg,
		Data:    data,
		Runtime: GetElapsedTime(ctx),
		Time:    timeUtil.GetNowTimestampByZone(ctx),
	}

	if clientType.IsDecryptClientType() && !JudgePassCheck(ctx) {
		respByte, err := json.Marshal(resp)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AppWithDataResp marshal err")
		}
		respStr, err := AesCbcEncryptByte(respByte, channelKeyInfo.RespKey, channelKeyInfo.IV)
		if err != nil {
			log.Ctx(ctx).WithError(err).Error("AppWithDataResp AesCbcEncryptByte err")
		}

		ctx.Header("Content-Length", strconv.Itoa(len(respStr)))
		ctx.Data(http.StatusOK, "text/plain; charset=utf-8", []byte(respStr))
	} else {
		ctx.JSON(http.StatusOK, resp)
	}
	ctx.Set("rspCode", code)
}

// ExportExcelResp .
func ExportExcelResp(ctx *gin.Context, buf []byte, filename string) {
	ctx.Header("Content-Type", "application/octet-stream")

	encodedFilename := url.QueryEscape(filename) + ".xlsx"
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"; filename*=utf-8''%s", encodedFilename, encodedFilename))
	ctx.Header("Content-Transfer-Encoding", "binary")
	ctx.Header("Cache-Control", "no-cache")
	ctx.Header("Pragma", "no-cache")

	ctx.Writer.WriteHeader(http.StatusOK)
	_, err := ctx.Writer.Write(buf)
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("ExportExcelResp err")
	}
}

func GetElapsedTime(ctx *gin.Context) float64 {
	openTime, _ := ctx.Get("reqTime")
	return float64(time.Since(openTime.(time.Time))) / float64(time.Millisecond)
}

func GetGinRequestID(ctx *gin.Context) string {
	requestId := ctx.Value("request_id")
	return requestId.(string)
}
