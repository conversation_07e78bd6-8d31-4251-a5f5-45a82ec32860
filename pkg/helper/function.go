package helper

import (
	"fmt"
	"math/rand"
	"strings"
	"time"

	"vlab/app/common/dbs"
)

type AppNoType string

const (
	AppNoVipOrder AppNoType = "vip_order"
)

// RandCode .
func RandCode(leng int) string {
	str := "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	//设置随机种子 避免导致设置的随机时间一样
	rand.Seed(time.Now().UnixNano())
	ret := []byte{}
	for i := 0; i < leng; i++ {
		ret = append(ret, byte(str[rand.Intn(len(str))]))
	}
	return string(ret)
}

// GetAppNo .
func GetAppNo(t AppNoType) string {
	noStrs := getAppNo(t)
	if len(noStrs) <= 0 {
		return ""
	}

	randNum := dbs.RandomRangeTime(1000, 9999)
	return fmt.Sprintf("%v-%v-%v%v", noStrs[0], noStrs[1], randNum, noStrs[2])
}

// GetAppNo .
func GetAppNoByNum(t AppNoType, num uint32) string {
	noStrs := getAppNo(t)
	var randStr string
	if num > 99 {
		randNum := dbs.RandomRangeTime(1000, 9999)
		randStr = fmt.Sprintf("%v", randNum)
	} else {
		randNum := dbs.RandomRangeTime(10, 99)
		randStr = fmt.Sprintf("%v%0*d", randNum, 2, num)
	}

	return fmt.Sprintf("%v-%v-%v%v", noStrs[0], noStrs[1], randStr, noStrs[2])
}

func getAppNo(t AppNoType) []string {
	ret := []string{}
	switch t {
	case AppNoVipOrder:
		ret = append(ret, "VO") // 会员订单
	}
	if len(ret) <= 0 {
		return ret
	}
	nowTime := time.Now().Format(dbs.TimeDateFormatAppress)
	nowTimeSlice := strings.Split(nowTime, "-")
	return append(ret, nowTimeSlice...)
}

type MyInt interface {
	int | int8 | int16 | int32 | int64 |
		uint | uint8 | uint16 | uint32 | uint64
}

func Max[T MyInt](a, b T) T {
	if a > b {
		return a
	}
	return b
}

func Min[T MyInt](a, b T) T {
	if a < b {
		return a
	}
	return b
}

func FindMinSlice[T MyInt](slice []T) T {
	if len(slice) == 0 {
		return 0
	}
	min := slice[0]
	for _, val := range slice[1:] {
		if val < min {
			min = val
		}
	}

	return min
}
