package helper

import (
	"testing"
)

func TestEncodeBcrypt(t *testing.T) {
	type args struct {
		str string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				str: "123456",
			},
			want:    "$2a$10$E10sIw/U0sY7GsK3qUMx2uQnVAKedJyto/MpMf1amsH.S2.CgCQ3m",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := EncodeBcrypt(tt.args.str)
			if (err != nil) != tt.wantErr {
				t.Errorf("EncodeBcrypt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("EncodeBcrypt() got = %v, want %v", got, tt.want)
			}
		})
	}
}
