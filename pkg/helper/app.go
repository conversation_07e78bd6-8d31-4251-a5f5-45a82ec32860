package helper

import (
	"context"
	"errors"
	"net/http"

	"vlab/app/common/dbs"
	adminConfig "vlab/app/dao/admin_config"
	"vlab/app/dao/common"
	channelDao "vlab/app/dao/resource_channel"
	channelKeyDao "vlab/app/dao/resource_channel_key"
	versionDao "vlab/app/dao/resource_version"
	"vlab/pkg/ecode"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"
	strUtil "vlab/pkg/util/str_util"

	"github.com/gin-gonic/gin"
	"github.com/gogf/gf/v2/util/gconv"
)

func Setup() {
	LoadAppValidate()
}

type CtxAccount struct {
	AccountID uint64
	RoleID    uint64
}

// GetCtxAccount .
func GetCtxAccount(ctx *gin.Context) (*CtxAccount, error) {
	if val, ok := ctx.Get("ctxAccount"); ok && val != nil {
		if ctxAccount, OK := val.(*CtxAccount); OK {
			return ctxAccount, nil
		}
	}
	return nil, ecode.TokenErr
}

type CtxUser struct {
	UID uint64
}

// GetCtxUser .
func GetCtxUser(ctx *gin.Context) (*CtxUser, error) {
	if val, ok := ctx.Get("ctxUser"); ok && val != nil {
		if ctxUser, OK := val.(*CtxUser); OK {
			return ctxUser, nil
		}
	}
	return &CtxUser{}, ecode.TokenErr
}

// GetCtxChannelID .
func GetCtxChannelID(ctx *gin.Context) (uint64, error) {
	if val, ok := ctx.Get("channelID"); ok && val != nil {
		if channelID, OK := val.(uint64); OK {
			return channelID, nil
		}
	}
	return 0, ecode.ChannelIDInvalidErr
}

// GetCtxPlatformID .
func GetCtxPlatformID(ctx *gin.Context) uint32 {
	if val, ok := ctx.Get("platformID"); ok && val != nil {
		if platformID, OK := val.(uint32); OK {
			return platformID
		}
	}
	return uint32(common.PlatformSkybox)
}

// GetCtxVersionID .
func GetCtxVersionID(ctx *gin.Context) (uint64, error) {
	if val, ok := ctx.Get("versionID"); ok && val != nil {
		if versionID, OK := val.(uint64); OK {
			return versionID, nil
		}
	}
	return 0, ecode.VersionIDInvalidErr
}

// GetCtxVersionStatus .
func GetCtxVersionStatus(ctx *gin.Context) (string, error) {
	if val, ok := ctx.Get("versionStatus"); ok && val != nil {
		if versionStatus, OK := val.(string); OK {
			return versionStatus, nil
		}
	}
	return string(versionDao.StatusStrDisable), ecode.VersionIDInvalidErr
}

// GetCtxChannelInfo .
func GetCtxChannelInfo(ctx *gin.Context) (*channelDao.Model, error) {
	channelID, err := GetCtxChannelID(ctx)
	if err != nil {
		return &channelDao.Model{}, err
	}

	return channelDao.GetRepo().RedisEnableChannelInfo(ctx, channelID)
}

// GetCtxChannelKeyInfo .
func GetCtxChannelKeyInfo(ctx *gin.Context, useDefault bool) (*channelKeyDao.Model, error) {
	channelID, err := GetCtxChannelID(ctx)
	if err != nil {
		if useDefault {
			channelID = 2 // 默认渠道加密id
		} else {
			log.Ctx(ctx).Warn("ctx channel id invalid")
			return nil, err
		}
	}

	return channelKeyDao.GetRepo().RedisEnableChannelKeyInfo(ctx, channelID)
}

func GetClientType(ctx *gin.Context) (dbs.ClientType, error) {
	val := ctx.GetHeader("Client-Type")
	if _, ok := dbs.ClientTypeMap[gconv.Int(val)]; !ok {
		log.Ctx(ctx).WithField("Client-Type", val).Warn("Client-Type invalid")
		return dbs.False, errors.New("Client-Type invalid")
	}
	return dbs.ClientType(gconv.Int(val)), nil
}

func GetDeviceNo(ctx *gin.Context) (string, error) {
	val := ctx.GetHeader("X-Deviceid")
	if val == "" {
		log.Ctx(ctx).WithField("X-Deviceid", val).Warn("Deviceid invalid")
		return val, errors.New("device invalid")
	}
	return val, nil
}

func GetRegion(ctx *gin.Context) (common.Region, error) {
	return common.RegionUs, nil
	// val := ctx.GetHeader("X-Region")
	// if val == "" {
	// 	log.Ctx(ctx).WithField("X-Region", val).Warn("Region invalid")
	// 	return "", errors.New("region invalid")
	// }

	// switch val {
	// case string(common.RegionCn):
	// 	return common.RegionCn, nil
	// case string(common.RegionUs):
	// 	return common.RegionUs, nil
	// default:
	// 	return common.RegionUs, nil
	// }
}

func CheckVersion(ctx *gin.Context, channelID uint64) (*versionDao.Model, error) {
	val := ctx.GetHeader("Client-Version")
	dataMap, err := versionDao.GetRepo().RedisEnableChannelVersionMap(ctx)
	if err != nil {
		log.Ctx(ctx).WithField("Client-Version", val).Warn("Client-Version version invalid")
		return nil, ecode.VersionIDInvalidErr
	}
	mapKey := versionDao.GetChannelVersionKey(channelID, val)

	dataInfo, ok := dataMap[mapKey]
	if !ok {
		log.Ctx(ctx).WithField("dataMap", dataMap).WithField("Client-Version", val).Warn("Client-Version map invalid")
		return nil, ecode.VersionIDInvalidErr
	}

	ctx.Set("versionID", dataInfo.ID)
	ctx.Set("versionStatus", dataInfo.GetVersionStr())
	return dataInfo, nil
}

func CheckChannel(ctx *gin.Context) (*channelDao.Model, *channelKeyDao.Model, error) {
	val := ctx.GetHeader("X-Channel")
	dataMap, err := channelDao.GetRepo().RedisEnableChannelMap(ctx)
	if err != nil {
		log.Ctx(ctx).WithField("X-Channel", val).Warn("X-Channel channel invalid")
		return nil, nil, ecode.ChannelIDInvalidErr
	}
	channelKeyMap, err := channelKeyDao.GetRepo().RedisEnableChannelKeyMap(ctx)
	if err != nil {
		log.Ctx(ctx).WithField("X-Channel", val).Warn("X-Channel channel key invalid")
		return nil, nil, ecode.ChannelIDInvalidErr
	}
	channel, ok := dataMap[gconv.Uint64(val)]
	channelKeyInfo, okk := channelKeyMap[gconv.Uint64(val)]

	if !ok || !okk {
		log.Ctx(ctx).WithField("dataMap", dataMap).WithField("channelKeyMap", channelKeyMap).WithField("X-Channel", val).Warn("X-Channel map invalid")
		return nil, nil, ecode.ChannelIDInvalidErr
	}

	ctx.Set("channelID", gconv.Uint64(val))
	ctx.Set("platformID", channel.PlatformID)
	return channel, channelKeyInfo, nil
}

func JudgePassCheck(ctx *gin.Context) bool {
	val := ctx.GetHeader("Pass-Check")
	return adminConfig.RedisGetConfig[string](ctx, adminConfig.KeyPassCheck) == val
}

func GenGinCtx() *gin.Context {
	var (
		ginCtx = &gin.Context{
			Request: &http.Request{},
		}
		requestID = ctxUtil.GenRequestID(ctxUtil.ReqTypeS)
		newCtx    = ctxUtil.WithRequestID(context.TODO(), requestID)
	)
	ginCtx.Request = ginCtx.Request.WithContext(newCtx)
	return ginCtx
}

// CheckVersionUpdate configVersion 即为配置的更新版本号, version 为当前请求的版本号
// 当前版本号小于等于配置版本号时, 说明需要更新
// 否则不需要更新
func CheckVersionUpdate(configVersion, version string) bool {
	if t := strUtil.CompareVersion(configVersion, version); t <= 0 {
		return true
	}
	return false
}
