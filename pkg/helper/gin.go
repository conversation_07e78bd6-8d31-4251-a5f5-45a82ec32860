package helper

import (
	"context"
	"net/http"
	"sync"
	"time"
	"vlab/pkg/log"
)

var (
	wg      sync.WaitGroup
	timeout = time.Second * 30
)

type GinSrv struct {
	HttpSrv *http.Server
}

func (g *GinSrv) Start(ctx context.Context) error {
	AsyncDo(func() {
		if err := g.HttpSrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.WithError(ctx, err).Error("ListenAndServe err")
		}
	})
	return nil
}

func (g *GinSrv) Close(ctx context.Context) error {
	if err := g.HttpSrv.Shutdown(ctx); err != nil {
		log.WithError(ctx, err).Error("HttpSrv.Shutdown err")
		return err
	}
	wait()
	return nil
}

func (g *GinSrv) Name() string {
	return "Gin"
}

func AsyncDo(f func()) {
	wg.Add(1)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				log.WithField(context.Background(), "err", err).Error("AsyncDo panic")
			}
			wg.Done()
		}()
		f()
	}()
}

func wait() {
	end := make(chan struct{})
	go func() {
		wg.Wait()
		close(end)
	}()
	select {
	case <-end:
	case <-time.After(timeout):
		log.Error(context.Background(), "process quit but something haven't done yet")
	}
}
