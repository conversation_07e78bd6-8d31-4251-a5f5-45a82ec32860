package shardUtil

/****************************************
*  Mod - 2
*****************************************/

// Mod2Uint64 .
func Mod2Uint64(num uint64) int {
	return int(num % uint64(2))
}

// Mod2Int64 .
func Mod2Int64(num int64) int {
	return int(num % int64(2))
}

// Mod2Int32 .
func Mod2Int32(num int32) int {
	return Mod2Int64(int64(num))
}

// Mod2String .
func Mod2String(str string) int {
	cnt := int32(0)
	for _, n := range str {
		cnt += n
	}

	return Mod2Int32(cnt)
}

/****************************************
*  Mod - 100
*****************************************/

// Mod100Uint64 .
func Mod100Uint64(num uint64) int {
	return int(num % uint64(100))
}

// Mod100Int64 .
func Mod100Int64(num int64) int {
	return int(num % int64(100))
}

// Mod100Int32 .
func Mod100Int32(num int32) int {
	return Mod100Int64(int64(num))
}

// Mod100String .
func Mod100String(str string) int {
	cnt := int32(0)
	for _, n := range str {
		cnt += n
	}

	return Mod100Int32(cnt)
}

/****************************************
*  Mod - 1000
*****************************************/

func Mod1000Uint64(num uint64) int {
	return int(num % uint64(1000))
}

// ModNumUint64 .
func ModNumUint64(id uint64, tableNum ShardNum) int {
	return int(id % uint64(tableNum))
}
