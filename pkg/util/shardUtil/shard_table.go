package shardUtil

import (
	"fmt"
	envUtil "vlab/pkg/util/env_util"
)

type ShardNum int

const (
	ShardNum10  ShardNum = 10
	ShardNum20  ShardNum = 20
	ShardNum50  ShardNum = 50
	ShardNum100 ShardNum = 100
)

func ModShardTableName(namePrefix string, key uint64, sn ShardNum) string {
	mod := key % uint64(sn)
	return fmt.Sprintf("%s_%02d", namePrefix, mod)
}

// BitShardTagleName 高位分表(待定)
func BitShardTagleName(namePrefix string, key uint64, sn ShardNum) string {
	return ModShardTableName(namePrefix, key, sn)
}

/****************************************
*  分表 100
*****************************************/

// ShardTable100Uint64 分表设置 - uint64
func ShardTable100Uint64(prefix string, key uint64) string {
	return fmt.Sprintf("%s_%02d", prefix, Mod100Uint64(key))
}

/****************************************
*  分表 100
*****************************************/

// ShardGroupMod100String .
func ShardGroupMod100String(prefix string, elements []string) map[string][]string {
	result := make(map[string][]string)
	for _, item := range elements {
		table := ShardTable100String(prefix, item)
		list, ok := result[table]
		if !ok {
			list = make([]string, 0)
		}
		list = append(list, item)
		result[table] = list
	}
	return result
}

// ShardMod100String 分表数 - mod
func ShardMod100String(key string) int {
	if envUtil.IsEnvTesting() {
		return Mod2String(key)
	} else {
		return Mod100String(key)
	}
}

// ShardTable100String 分表设置 - string
func ShardTable100String(prefix string, key string) string {
	return fmt.Sprintf("%s_%03d", prefix, ShardMod100String(key))
}

// ShardMod100Int64 分表数 - mod
func ShardMod100Int64(key int64) int {
	if envUtil.IsEnvTesting() {
		return Mod2Int64(key)
	} else {
		return Mod100Int64(key)
	}
}

// ShardGroupMod100Uint64 .
func ShardGroupMod100Uint64(prefix string, elements []uint64) map[string][]uint64 {
	result := make(map[string][]uint64)
	for _, item := range elements {
		table := ShardTable100Uint64(prefix, item)
		list, ok := result[table]
		if !ok {
			list = make([]uint64, 0)
		}
		list = append(list, item)
		result[table] = list
	}
	return result
}

// ShardTable100Int64 分表设置 - int64
func ShardTable100Int64(prefix string, key int64) string {
	return fmt.Sprintf("%s_%03d", prefix, ShardMod100Int64(key))
}

/************************************************************
*  分表 1000
*************************************************************/

func ShardGroupMod1000Uint64(prefix string, elements []uint64) map[string][]uint64 {
	result := make(map[string][]uint64)
	for _, item := range elements {
		table := ShardTable1000Uint64(prefix, item)
		list, ok := result[table]
		if !ok {
			list = make([]uint64, 0)
		}
		list = append(list, item)
		result[table] = list
	}
	return result
}

func ShardMod1000Uint64(key uint64) int {
	if envUtil.IsEnvTesting() {
		return Mod2Uint64(key)
	} else {
		return Mod1000Uint64(key)
	}
}

// ShardTable1000Uint64 1000张 - 分表设置 - uint64
func ShardTable1000Uint64(prefix string, key uint64) string {
	return fmt.Sprintf("%s_%04d", prefix, ShardMod1000Uint64(key))
}
