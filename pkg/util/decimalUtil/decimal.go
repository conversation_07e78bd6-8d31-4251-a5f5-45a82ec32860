package decimalUtil

import (
	"github.com/shopspring/decimal"
)

func Div(a, b uint64) uint64 {
	if b == 0 {
		return 0
	}
	return uint64(decimal.NewFromUint64(a).Div(decimal.NewFromUint64(b)).Round(0).IntPart())
}

func AmountToFloat(amount uint64) float64 {
	var ret float64
	if amount == 0 {
		return ret
	}
	ret, _ = decimal.NewFromUint64(amount).Div(decimal.NewFromFloat(100)).Round(2).Float64()
	return ret
}
