package util

import (
	"errors"
	"time"

	"vlab/app/common/dbs"
	"vlab/config"

	"github.com/golang-jwt/jwt/v4"
)

type AppClaims struct {
	UserID uint64 `json:"user_id"`
	RoleID uint64 `json:"role_id"`
	jwt.RegisteredClaims
}

// 生成token
func GenToken(UID, roleID uint64) (string, error) {
	expireTime := time.Now().Add(30 * 24 * time.Hour)
	if UID == dbs.RootUID {
		expireTime = time.Now().Add(365 * 24 * time.Hour)
	}
	claim := AppClaims{
		UserID: UID,
		RoleID: roleID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expireTime), // 过期时间
			IssuedAt:  jwt.NewNumericDate(time.Now()), // 签发时间
			NotBefore: jwt.NewNumericDate(time.Now()), // 生效时间
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claim)
	return token.SignedString([]byte(config.AppCfg.JwtSecret))
}

// 解析token
func ParseToken(headerToken string) (*AppClaims, error) {
	token, err := jwt.ParseWithClaims(headerToken, &AppClaims{}, func(t *jwt.Token) (interface{}, error) {
		return []byte(config.AppCfg.JwtSecret), nil
	})
	if err != nil {
		if ve, ok := err.(*jwt.ValidationError); ok {
			if ve.Errors&jwt.ValidationErrorMalformed != 0 {
				return nil, errors.New("that is not even a token")
			} else if ve.Errors&jwt.ValidationErrorExpired != 0 {
				return nil, errors.New("token is exired")
			} else if ve.Errors&jwt.ValidationErrorNotValidYet != 0 {
				return nil, errors.New("token not active yet")
			} else {
				return nil, errors.New("could not handle this token")
			}
		}
	}
	if claims, ok := token.Claims.(*AppClaims); ok && token.Valid {
		return claims, nil
	} else {
		return nil, err
	}
}
