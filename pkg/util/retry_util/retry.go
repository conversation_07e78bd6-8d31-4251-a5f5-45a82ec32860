package retry_util

import (
	"errors"
	"fmt"
	"strings"
	"time"
)

// Function signature of retryable function
type RetryableFunc func() error

// Function signature of retryable function with data
type RetryableFuncWithData[T any] func() (T, error)

// Default timer is a wrapper around time.After
type timerImpl struct{}

func (t *timerImpl) After(d time.Duration) <-chan time.Time {
	return time.After(d)
}

func Do(retryableFunc RetryableFunc, opts ...Option) error {
	retryableFuncWithData := func() (any, error) {
		return nil, retryableFunc()
	}

	_, err := DoWithData(retryableFuncWithData, opts...)
	return err
}

func DoWithData[T any](retryableFunc RetryableFuncWithData[T], opts ...Option) (T, error) {
	var (
		n      uint
		emptyT T

		lastErr error
	)

	// default
	config := newDefaultRetryConfig()

	// apply options
	for _, opt := range opts {
		opt(config)
	}

	if err := config.context.Err(); err != nil {
		return emptyT, err
	}

	if config.attempts == 0 {
		for {
			t, err := retryableFunc()
			if err == nil {
				return t, nil
			}

			if !isRecoverable(err) {
				return emptyT, err
			}

			if !config.retryIf(err) {
				return emptyT, err
			}

			lastErr = err

			config.onRetry(n, err)
			n++
			select {
			case <-config.timer.After(delay(config, n, err)):
			case <-config.context.Done():
				if config.wrapContextErrorWithLastError {
					return emptyT, ErrorList{
						config.context.Err(),
						lastErr,
					}
				}
				return emptyT, config.context.Err()
			}
		}
	}
	errorLog := ErrorList{}

	attemptsForError := make(map[error]uint, len(config.attemptsForError))
	for err, attempts := range config.attemptsForError {
		attemptsForError[err] = attempts
	}

	shouldRetry := true
	for shouldRetry {
		t, err := retryableFunc()
		if err == nil {
			return t, nil
		}

		errorLog = append(errorLog, unpackUnrecoverable(err))

		if !config.retryIf(err) {
			break
		}

		config.onRetry(n, err)

		for errToCheck, attempts := range attemptsForError {
			if errors.Is(err, errToCheck) {
				attempts--
				attemptsForError[errToCheck] = attempts
				shouldRetry = shouldRetry && attempts > 0
			}
		}

		if n == config.attempts-1 {
			break
		}

		select {
		case <-config.timer.After(delay(config, n, err)):
		case <-config.context.Done():
			if config.lastErrorOnly {
				return emptyT, config.context.Err()
			}
			return emptyT, append(errorLog, config.context.Err())
		}

		n++
		shouldRetry = shouldRetry && n < config.attempts
	}

	if config.lastErrorOnly {
		return emptyT, errorLog.Unwrap()
	}
	return emptyT, errorLog
}

type unrecoverableError struct {
	error
}

func (e unrecoverableError) Error() string {
	if e.error == nil {
		return "unrecoverable error"
	}
	return e.error.Error()
}

func (e unrecoverableError) Unwrap() error {
	return e.error
}

func Unrecoverable(err error) error {
	return unrecoverableError{error: err}
}

func isRecoverable(err error) bool {
	return !errors.Is(err, unrecoverableError{})
}

func (e unrecoverableError) Is(err error) bool {
	var unrecoverableError unrecoverableError
	isUnrecoverable := errors.As(err, &unrecoverableError)
	return isUnrecoverable
}

func unpackUnrecoverable(err error) error {
	var unrecoverable unrecoverableError
	if errors.As(err, &unrecoverable) {
		return unrecoverable.error
	}
	return err
}

type ErrorList []error

func (el ErrorList) Error() string {
	logWithNum := make([]string, len(el))
	for i, err := range el {
		if err != nil {
			logWithNum[i] = fmt.Sprintf("#%d: %s", i+1, err.Error())
		}
	}

	return fmt.Sprintf("All attempts fail:\n %s", strings.Join(logWithNum, "\n"))
}

func (el ErrorList) Is(target error) bool {
	for _, err := range el {
		if errors.Is(err, target) {
			return true
		}
	}
	return false
}

func (el ErrorList) As(target interface{}) bool {
	for _, err := range el {
		if errors.As(err, &target) {
			return true
		}
	}
	return false
}

func (el ErrorList) Unwrap() error {
	return el[len(el)-1]
}

func (el ErrorList) WrappedErrors() []error {
	return el
}

func delay(config *Config, n uint, err error) time.Duration {
	delayTime := config.delayType(n, err, config)
	if config.maxDelay > 0 && delayTime > config.maxDelay {
		delayTime = config.maxDelay
	}
	return delayTime
}
