package retry_util

import (
	"context"
	"math"
	"math/rand"
	"time"
)

type RetryIfFunc func(error) bool

type OnRetryFunc func(attempt uint, err error)

type DelayTypeFunc func(n uint, err error, config *Config) time.Duration

type Timer interface {
	After(d time.Duration) <-chan time.Time
}

type Config struct {
	attempts                      uint
	delay                         time.Duration
	maxDelay                      time.Duration
	maxJitter                     time.Duration
	context                       context.Context
	timer                         Timer
	retryIf                       RetryIfFunc
	onRetry                       OnRetryFunc
	delayType                     DelayTypeFunc
	wrapContextErrorWithLastError bool
	attemptsForError              map[error]uint
	lastErrorOnly                 bool

	maxBackOffN uint
}

func newDefaultRetryConfig() *Config {
	return &Config{
		attempts:         uint(5),
		delay:            100 * time.Millisecond,
		maxJitter:        100 * time.Millisecond,
		context:          context.Background(),
		timer:            &timerImpl{},
		retryIf:          isRecoverable,
		onRetry:          func(attempt uint, err error) {},
		delayType:        combineDelay(BackOffDelay, RandomDelay),
		attemptsForError: make(map[error]uint),
		lastErrorOnly:    false,
	}
}

// Option is a function that configures a Config.
type Option func(*Config)

func emptyOption(config *Config) {}

func LastErrorOnly(lastErrorOnly bool) Option {
	return func(c *Config) {
		c.lastErrorOnly = lastErrorOnly
	}
}

func Attempts(attempts uint) Option {
	return func(c *Config) {
		c.attempts = attempts
	}
}

func UntilSucceeded() Option {
	return func(c *Config) {
		c.attempts = 0
	}
}

func AttemptsForError(attempts uint, err error) Option {
	return func(c *Config) {
		c.attemptsForError[err] = attempts
	}
}

func combineDelay(delays ...DelayTypeFunc) DelayTypeFunc {
	const maxInt64 = uint64(math.MaxInt64)

	return func(n uint, err error, config *Config) time.Duration {
		var total uint64
		for _, delay := range delays {
			total += uint64(delay(n, err, config))
			if total > maxInt64 {
				return time.Duration(maxInt64)
			}
		}
		return time.Duration(total)
	}
}

func RandomDelay(_ uint, _ error, config *Config) time.Duration {
	return time.Duration(rand.Int63n(int64(config.maxJitter)))
}

func FixedDelay(_ uint, _ error, config *Config) time.Duration {
	return config.delay
}

func BackOffDelay(n uint, _ error, config *Config) time.Duration {
	const maxN uint = 62

	if config.maxBackOffN == 0 {
		if config.delay <= 0 {
			config.delay = 1
		}

		config.maxBackOffN = maxN - uint(math.Floor(math.Log2(float64(config.delay))))
	}

	if n > config.maxBackOffN {
		n = config.maxBackOffN
	}

	return config.delay << n
}

func Delay(delay time.Duration) Option {
	return func(config *Config) {
		config.delay = delay
	}
}

func MaxDelay(maxDelay time.Duration) Option {
	return func(config *Config) {
		config.maxDelay = maxDelay
	}
}

func MaxJitter(maxJitter time.Duration) Option {
	return func(config *Config) {
		config.maxJitter = maxJitter
	}
}

func DelayType(delayType DelayTypeFunc) Option {
	if delayType == nil {
		return emptyOption
	}
	return func(config *Config) {
		config.delayType = delayType
	}
}

func OnRetry(onRetry OnRetryFunc) Option {
	if onRetry == nil {
		return emptyOption
	}
	return func(config *Config) {
		config.onRetry = onRetry
	}
}

func RetryIf(retryIf RetryIfFunc) Option {
	if retryIf == nil {
		return emptyOption
	}
	return func(config *Config) {
		config.retryIf = retryIf
	}
}

func Context(ctx context.Context) Option {
	return func(config *Config) {
		config.context = ctx
	}
}

func WithTimer(timer Timer) Option {
	return func(config *Config) {
		config.timer = timer
	}
}

func WrapContextErrorWithLastError(wrapContextErrorWithLastError bool) Option {
	return func(config *Config) {
		config.wrapContextErrorWithLastError = wrapContextErrorWithLastError
	}
}
