package xlsxUtil

import (
	"github.com/xuri/excelize/v2"
)

func GetNewFile(sheetName string) *excelize.File {
	newFile := excelize.NewFile()
	sheetIdx, _ := newFile.NewSheet(sheetName)
	newFile.DeleteSheet("Sheet1")
	newFile.SetActiveSheet(sheetIdx)
	return newFile
}

func WriteHeader(f *excelize.File, sheetName string, headers []string) {
	for colNum, header := range headers {
		f.SetCellValue(sheetName, Coordinate2axis(colNum+1, 1), header)
	}
}

func WriteRow(f *excelize.File, sheetName string, rowIndex int, rowData []interface{}) {
	for colNum, val := range rowData {
		f.SetCellValue(sheetName, Coordinate2axis(colNum+1, rowIndex), val)
		// cellName := excelize.ToAlphaString(colNum+1) + fmt.Sprint(rowIndex)
		// switch v := value.(type) {
		// case string:
		// 	f.SetCellValue(sheetName, cellName, v)
		// case int:
		// 	f.SetCellValue(sheetName, cellName, fmt.Sprint(v))
		// case float64:
		// 	f.SetCellValue(sheetName, cellName, fmt.Sprintf("%.2f", v)) // 格式化金额
		// // 根据需要添加更多类型处理...
		// default:
		// 	log.Printf("Unsupported data type: %T", v)
		// }
	}
}

func Coordinate2axis(col, row int) string {
	axis, _ := excelize.CoordinatesToCellName(col, row, false)
	return axis
}
