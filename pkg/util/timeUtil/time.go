package timeUtil

import (
	"fmt"
	"time"
	"vlab/app/common/dbs"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

type LadderNum uint32
type OverdueLevel uint32

const (
	InvervalOne   = 60 * 60 * 24
	InvervalThree = 60 * 60 * 24 * 3
	InvervalSeven = 60 * 60 * 24 * 7

	OverdueLevelDefault OverdueLevel = 0
	OverdueLevelOne     OverdueLevel = 1
	OverdueLevelTwo     OverdueLevel = 2
	OverdueLevelThree   OverdueLevel = 3
	OverdueLevelFour    OverdueLevel = 4
)

// GetLadderTime .
func GetLadderTime(n LadderNum) time.Duration {
	if 0 < n && n <= 1000 {
		return time.Millisecond * 10
	} else if 1000 < n && n <= 3000 {
		return time.Millisecond * 20
	} else if 3000 < n && n <= 6000 {
		return time.Millisecond * 30
	} else if 6000 < n && n <= 10000 {
		return time.Millisecond * 50
	} else {
		return time.Millisecond * 100
	}
}

// GetOverdueLevel .
func GetOverdueLevel(ctx *gin.Context, endTime int64) OverdueLevel {
	nowTime := GetNowTimestampByZone(ctx)
	if nowTime <= endTime {
		return OverdueLevelDefault
	}
	inverval := nowTime - endTime
	if inverval <= InvervalOne {
		return OverdueLevelOne
	} else if InvervalOne < inverval && inverval <= InvervalThree {
		return OverdueLevelTwo
	} else if InvervalThree < inverval && inverval <= InvervalSeven {
		return OverdueLevelThree
	} else {
		return OverdueLevelFour
	}
}

// JudgeValidStartEndTime .
func JudgeValidStartEndTime(startTime, endTime int64, inverval dbs.ExportTimeInterval) bool {
	if startTime == dbs.False || endTime == dbs.False || endTime-startTime > int64(inverval) || startTime >= endTime {
		return false
	}
	return true
}

// JudgeValidStartEndDate .
func JudgeValidStartEndDate(startDate, endDate string, inverval dbs.ExportTimeInterval) bool {
	sDate, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		return false
	}
	eDate, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		return false
	}
	if sDate.After(eDate) {
		return false
	}

	// 检查时间差是否超过一个月
	oneMonthLater := sDate.AddDate(0, 1, 0)
	return !eDate.After(oneMonthLater)
}

// GetSlidingWindowTime 获取滑动窗口时间
func GetSlidingWindowTime(ctx *gin.Context, interval time.Duration) string {
	zone := GetCurrentZone(ctx)
	now := carbon.Now(zone).Timestamp()
	windowStart := now / int64(interval.Seconds()) * int64(interval.Seconds())
	return carbon.CreateFromTimestamp(windowStart, zone).Format(dbs.TimeDateFormatSliding)
}

// GetWeekSlidingWindowTime 获取滑动窗口时间
func GetWeekSlidingWindowTime(ctx *gin.Context) string {
	now := carbon.Now(GetCurrentZone(ctx))
	year, week := now.Year(), now.WeekOfYear() // 获取年份 & 周数
	return fmt.Sprintf("%d%02d", year, week)
}
