package timeUtil

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-module/carbon/v2"
)

const (
	ShanghaiZone = "Asia/Shanghai" // 东八区时区，北京时间
	JaKartaZone  = "Asia/Jakarta"  // 东七区时区，雅加达时间
)

func GetCurrentZone(ctx *gin.Context) string {
	return JaKartaZone
}

func GetTomorrowEndOfDayTimestampByZone(ctx *gin.Context) int64 {
	return carbon.Tomorrow(GetCurrentZone(ctx)).EndOfDay().Timestamp()
}

func GetEndOfDayTimestampByZone(ctx *gin.Context) int64 {
	return carbon.Now(GetCurrentZone(ctx)).EndOfDay().Timestamp()
}

func GetNowTimestampByZone(ctx *gin.Context) int64 {
	return carbon.Now(GetCurrentZone(ctx)).Timestamp()
}

// GetRedisEndOfDayExpireTime 今日截止过期时间
func GetNowEndOfDayExpireTime(ctx *gin.Context) time.Duration {
	zone := GetCurrentZone(ctx)
	expireTime := carbon.Now(zone).EndOfDay().Timestamp()
	return time.Second * time.Duration(expireTime-carbon.Now(zone).Timestamp())
}

func ToDateTimeStringByZone(ctx *gin.Context, timestamp int64) string {
	return carbon.CreateFromTimestamp(timestamp, GetCurrentZone(ctx)).ToDateTimeString()
}

func NowToDateTimeStringByZone(ctx *gin.Context) string {
	return carbon.Now(GetCurrentZone(ctx)).ToDateTimeString()
}

func NowToDateStringByZone(ctx *gin.Context) string {
	return carbon.Now(GetCurrentZone(ctx)).ToDateString()
}
