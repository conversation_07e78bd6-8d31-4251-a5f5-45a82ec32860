package sliceUtil

import (
	"strconv"

	"github.com/samber/lo"
)

// SliceNotContainerUint64 计算slice1中不包含slice2中的元素
func SliceNotContainer[T comparable](parentSlice, childSlice []T) []T {
	if len(childSlice) == 0 {
		return parentSlice
	}

	cMap := make(map[T]struct{})
	for _, val := range childSlice {
		cMap[val] = struct{}{}
	}

	ret := []T{}
	for _, val := range parentSlice {
		if _, ok := cMap[val]; !ok {
			ret = append(ret, val)
		}
	}
	return ret
}

// SliceToEmptyMap .
func SliceToEmptyMap[T comparable](slice []T) map[T]struct{} {
	retMap := make(map[T]struct{}, len(slice))
	for _, val := range slice {
		if _, ok := retMap[val]; !ok {
			retMap[val] = struct{}{}
		}
	}
	return retMap
}

// StringsToUint64s .
func StringsToUint64s(strs []string) []uint64 {
	ret := []uint64{}
	for _, str := range strs {
		if str == "" {
			continue
		}
		val, _ := strconv.ParseUint(str, 10, 64)
		ret = append(ret, val)
	}
	return ret
}

// Uint64sToStrings .
func Uint64sToStrings(nums []uint64) []string {
	ret := []string{}
	for _, num := range nums {
		if num == 0 {
			continue
		}
		ret = append(ret, strconv.FormatUint(num, 10))
	}
	return ret
}

// GetUintsIntersect .
func GetUintsIntersect(source, target []uint64) []uint64 {
	if len(target) == 0 {
		return source
	}
	return lo.Intersect[uint64](source, target)
}

// Chunk .
func Chunk[T comparable](slice []T, size int) [][]T {
	var chunks [][]T
	for i := 0; i < len(slice); i += size {
		end := i + size
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func FilterZero(nums []uint64) []uint64 {
	var ret []uint64
	for _, num := range nums {
		if num == 0 {
			continue
		}
		ret = append(ret, num)
	}
	return ret
}
