package ctxUtil

import (
	"context"
	"encoding/hex"
	"fmt"

	uuid "github.com/satori/go.uuid"
)

type (
	requestIDKey struct{}
	ReqType      string
)

const (
	RequestIDKeyName         = "request_id"
	ReqTypeH         ReqType = "h" // http
	ReqTypeS         ReqType = "s" // script
)

// NewRequestID .
func NewRequestID(ctx context.Context) context.Context {
	return WithRequestID(ctx, GenRequestID(ReqTypeH))
}

// NewSRequestID .
func NewSRequestID(ctx context.Context) context.Context {
	return WithRequestID(ctx, GenRequestID(ReqTypeS))
}

// WithRequestID .
func WithRequestID(ctx context.Context, id string) context.Context {
	return context.WithValue(ctx, requestIDKey{}, id)
}

// GenRequestID .
func GenRequestID(t ReqType) string {
	ret := hex.EncodeToString(uuid.NewV4().Bytes())[:15]
	return fmt.Sprintf("%v%s", t, ret)
}

func GetRequestID(ctx context.Context) string {
	reqId, ok := ctx.Value(requestIDKey{}).(string)
	if !ok {
		return "undefined"
	}
	return reqId
}
