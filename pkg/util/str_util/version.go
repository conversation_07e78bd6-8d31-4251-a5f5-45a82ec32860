package strUtil

import (
	"strconv"
	"strings"
)

func CompareVersion(versionBase, version string) int {
	parts1 := strings.Split(versionBase, ".")
	parts2 := strings.Split(version, ".")

	// 统一长度，补 0
	maxLen := max(len(parts1), len(parts2))
	for len(parts1) < maxLen {
		parts1 = append(parts1, "0")
	}
	for len(parts2) < maxLen {
		parts2 = append(parts2, "0")
	}

	for i := 0; i < maxLen; i++ {
		num1, _ := strconv.Atoi(parts1[i])
		num2, _ := strconv.Atoi(parts2[i])
		if num1 < num2 {
			return 1
		} else if num1 > num2 {
			return -1
		}
	}
	return 0
}
