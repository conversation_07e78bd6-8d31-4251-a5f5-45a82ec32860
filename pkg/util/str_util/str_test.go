package strUtil

import (
	"testing"
)

func TestIsValidLength(t *testing.T) {
	type args struct {
		s         string
		maxLength int
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "test1",
			args: args{
				s:         "123456",
				maxLength: 6,
			},
			want: true,
		},
		{
			name: "test2",
			args: args{
				s:         "j",
				maxLength: 500,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsValidLength(tt.args.s, tt.args.maxLength); got != tt.want {
				t.<PERSON>rf("IsValidLength() = %v, want %v", got, tt.want)
			}
		})
	}
}
