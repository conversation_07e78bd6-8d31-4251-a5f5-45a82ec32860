package strUtil

import (
	"testing"
)

func TestCompareVersion(t *testing.T) {
	type args struct {
		versionBase string
		version     string
	}
	tests := []struct {
		name string
		args args
		want int
	}{
		// TODO: Add test cases.
		{"equal", args{"1.0.0", "1.0.0"}, 0},
		{"greater", args{"1.0.0", "1.0.1"}, 1},
		{"less", args{"1.0.1", "1.0.0"}, -1},
		{"different lengths", args{"1.0", "1.0.0"}, 0},
		{"empty base", args{"", "1.0.0"}, -1},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CompareVersion(tt.args.versionBase, tt.args.version); got != tt.want {
				t.Errorf("CompareVersion() = %v, want %v", got, tt.want)
			}
		})
	}
}
