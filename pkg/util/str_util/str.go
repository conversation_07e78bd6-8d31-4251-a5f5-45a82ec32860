package strUtil

import (
	"context"
	"strconv"
	"strings"
	"unicode/utf8"

	"vlab/pkg/log"

	"github.com/sirupsen/logrus"
)

// ToCamelInitCase .
func ToCamelInitCase(s string, initCase bool) string {
	s = strings.TrimSpace(s)
	if s == "" {
		return s
	}
	a, hasAcronym := uppercaseAcronym.Load(s)
	if hasAcronym {
		s = a.(string)
	}

	n := strings.Builder{}
	n.Grow(len(s))
	capNext := initCase
	prevIsCap := false
	for i, v := range []byte(s) {
		vIsCap := v >= 'A' && v <= 'Z'
		vIsLow := v >= 'a' && v <= 'z'
		if capNext {
			if vIsLow {
				v -= 'a' - 'A' // 转换为大写字母
			}
		} else if i == 0 {
			if initCase && (vIsLow || !vIsCap || v == '/') {
				if vIsLow {
					v -= 'a' - 'A' // 转换为大写字母
				}
			}
		} else if prevIsCap && vIsCap && !hasAcronym {
			v += 'a' - 'A' // 转换为小写字母
		}
		prevIsCap = vIsCap

		if v == '/' {
			n.WriteByte(v)
			capNext = false
		} else if vIsCap || vIsLow {
			n.WriteByte(v)
			capNext = false
		} else if vIsNum := v >= '0' && v <= '9'; vIsNum {
			n.WriteByte(v)
			capNext = true
		} else {
			capNext = v == '_' || v == ' ' || v == '-' || v == '.'
		}
	}
	return n.String()
}

// CommaStrToIdSlice 逗号分割的字符串转换为ID切片
func CommaStrToIdSlice(commaStr string) []uint64 {
	var (
		ctx = context.Background()
	)

	if commaStr == "" {
		log.WithContext(ctx).WithField("commaStr", commaStr).Warn("CommaStrToIdSlice empty")
		return make([]uint64, 0)
	}
	slice := strings.Split(commaStr, ",")
	idSlice := make([]uint64, 0, len(slice))
	for _, v := range slice {
		u, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			log.WithContext(ctx).WithError(err).
				WithField("commaStr", commaStr).
				Error("strconv.ParseUint error")
			continue
		}
		if u <= 0 {
			log.WithContext(ctx).WithFields(logrus.Fields{
				"commaStr": commaStr,
				"u":        u,
			}).Warn("CommaStrToIdSlice u <= 0")
			continue
		}
		idSlice = append(idSlice, u)
	}
	return idSlice
}

// IsValidLength 检查字符串是否超过maxLength
func IsValidLength(s string, maxLength int) bool {
	return utf8.RuneCountInString(s) <= maxLength
}
