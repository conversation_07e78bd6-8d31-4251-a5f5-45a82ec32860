package es

import (
	"context"
	"vlab/config"
	"vlab/pkg/log"
	"vlab/pkg/util/ctxUtil"

	es "github.com/elastic/go-elasticsearch/v8"
	info "github.com/elastic/go-elasticsearch/v8/typedapi/core/info"
)

var EsClient *es.TypedClient

func Setup() {
	ctx := ctxUtil.NewSRequestID(context.TODO())
	cfg := es.Config{
		Addresses: config.ElasticSearchCfg.Host,
		Username:  config.ElasticSearchCfg.Username,
		Password:  config.ElasticSearchCfg.Password,
	}
	var err error
	EsClient, err = es.NewTypedClient(cfg)
	if err != nil {
		log.WithError(ctx, err).Error("elasticsearch.NewClient")
	}
	var esInfo *info.Response

	esInfo, err = EsClient.Info().Do(ctx)
	if err != nil {
		log.WithError(ctx, err).Error("EsClient.Info")
		EsClient = nil
		return
	} else if esInfo == nil {
		log.WithError(ctx, err).Error("EsClient.Info esInfo is nil")
		EsClient = nil
		return
	}
	log.WithContext(ctx).WithField("esInfo", esInfo).Info("Elasticsearch up and running!...")
}
