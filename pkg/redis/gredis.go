package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"vlab/config"

	"github.com/gomodule/redigo/redis"
)

const redisRandomScopeMin = 360
const redisRandomScopeMax = 7200

var RedisPool *redis.Pool

func Setup() {
	RedisPool = &redis.Pool{
		MaxIdle:     config.RedisCfg.MaxIdle,
		MaxActive:   config.RedisCfg.MaxActive,
		IdleTimeout: config.RedisCfg.IdleTimeout,
		Dial: func() (redis.Conn, error) {
			c, err := redis.Dial("tcp", config.RedisCfg.Host,
				redis.DialUsername(config.RedisCfg.Username),
				redis.DialPassword(config.RedisCfg.Password),
				redis.DialDatabase(config.RedisCfg.Db),
			)
			if err != nil {
				fmt.Println("redis setup error:", err)
				return nil, err
			}

			return c, nil
		},
		TestOnBorrow: func(c redis.Conn, t time.Time) error {
			_, err := c.Do("PING")
			return err
		},
	}
}

// 设置
func Set(key string, data interface{}, timeout int) error {
	key = getPrefixKey(key)
	conn := RedisPool.Get()
	defer conn.Close()

	val, err := json.Marshal(data)
	if err != nil {
		return err
	}

	if _, err = conn.Do("Set", key, val); err != nil {
		return err
	}

	if _, err = conn.Do("EXPIRE", key, timeout); err != nil {
		return err
	}

	return nil
}

// deprecated
func HSet(id uint64, data map[string]interface{}, timeout int) (err error) {
	key := "dmall:tag:" + strconv.FormatUint(id, 10)
	conn := RedisPool.Get()
	defer conn.Close()

	var list = make([]interface{}, 0)

	for k, v := range data {
		list = append(list, k, v)
	}

	if reply, err := conn.Do("HSET", key, list); err != nil {
		err = fmt.Errorf("redis %v hset error:%w", reply, err)
		return err
	}

	if _, err = conn.Do("EXPIRE", key, timeout); err != nil {
		return err
	}

	return nil
}

// 获取
func Get(key string) ([]byte, error) {
	key = getPrefixKey(key)
	conn := RedisPool.Get()
	defer conn.Close()

	reply, err := redis.Bytes(conn.Do("GET", key))
	if err != nil {
		return nil, err
	}
	return reply, nil
}

// 获取前缀key
func getPrefixKey(key string) string {
	if config.RedisCfg.Prefix == "" {
		return key
	}
	//官方推荐的拼接字符串的方式，少量字符串拼接使用+拼接 方便又高效
	var build strings.Builder
	build.WriteString(config.RedisCfg.Prefix)
	build.WriteString("_")
	build.WriteString(key)
	return build.String()
}

// 删除key
func Delete(key string) (bool, error) {
	key = getPrefixKey(key)
	conn := RedisPool.Get()
	defer conn.Close()

	return redis.Bool(conn.Do("DEL", key))
}

func GetCached[T any](ctx context.Context, key string, fn func(key string) (T, error)) (T, error) {
	var obj T
	res, err := Get(key)

	if res != nil {
		if err := json.Unmarshal(res, &obj); err != nil {
			return obj, err
		}
		return obj, nil
	}

	obj, err = fn(key)
	if err != nil {
		err = fmt.Errorf("GetCached Fn execed error: %w", err)
		return obj, err
	}
	// generate random expire time
	expire := time.Duration(rand.Intn(redisRandomScopeMax-redisRandomScopeMin)+redisRandomScopeMin) * time.Second
	err = Set(key, obj, int(expire.Seconds()))
	if err != nil {
		return obj, err
	}
	return obj, nil
}
