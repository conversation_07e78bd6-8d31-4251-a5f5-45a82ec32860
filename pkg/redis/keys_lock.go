package redis

import "fmt"

var (
	LockUserAppStoreTrance     = "vlab:lock:UserAppStoreTrance:%d_%v"
	LockUserGooglePlayTrance   = "vlab:lock:UserGooglePlayTrance:%d_%v"
	LockUserPaypalTranceCreate = "vlab:lock:UserPaypalTranceCreate:%d_%d"
	LockUserPaypalTranceCheck  = "vlab:lock:UserPaypalTranceCheck:%d_%v"

	LockCreateOrUpdateDevice = "vlab:lock:CreateOrUpdateDevice:%s"

	LockAdminUserRepayCredit = "wsd:lock:adminUserRepayCredit:%v"
	LockAdminOrderPayOrEdit  = "wsd:lock:adminOrderPayOrEdit:%v"
	LockSkuAvaStock          = "wsd:lock:skuAvaStock:%v"
)

func GetUserAppStoreTrance(userID uint64, trancdeId string) string {
	return fmt.Sprintf(LockUserAppStoreTrance, userID, trancdeId)
}

func GetUserGooglePlayTrance(userID uint64, productId string) string {
	return fmt.Sprintf(LockUserGooglePlayTrance, userID, productId)
}

func GetUserPaypalTranceCreate(uid, pid uint64) string {
	return fmt.Sprintf(LockUserPaypalTranceCreate, uid, pid)
}

func GetUserPaypalTranceCheck(uid uint64, thirdId string) string {
	return fmt.Sprintf(LockUserPaypalTranceCheck, uid, thirdId)
}

func GetCreateOrUpdateDevice(deviceNo string) string {
	return fmt.Sprintf(LockCreateOrUpdateDevice, deviceNo)
}

func GetUserRepayCreditLockKey(userID uint64) string {
	return fmt.Sprintf(LockAdminUserRepayCredit, userID)
}

func GetOrderPayOrEditLockKey(orderID uint64) string {
	return fmt.Sprintf(LockAdminOrderPayOrEdit, orderID)
}

func GetSkuAvaStockLockKey(skuID uint64) string {
	return fmt.Sprintf(LockSkuAvaStock, skuID)
}
