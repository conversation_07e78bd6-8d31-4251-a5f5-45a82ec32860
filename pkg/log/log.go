package log

import (
	"context"
	"sync"
	"time"

	"vlab/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// TODO 重构
/*****************************************************
* 写入kafka, 消费至es中
******************************************************/
var (
	asyncLoggerInit sync.Once
	asyncLogger     *AsyncLogger
)

type AsyncLogger struct {
	logger   *logrus.Logger
	logChan  chan LogMsg
	done     chan bool
	mu       sync.Mutex
	isClosed bool
}

type LogMsg struct {
	entry  *logrus.Entry
	level  logrus.Level
	format string
	args   []interface{}
}

func GetAsyncLogger() *AsyncLogger {
	if asyncLogger == nil {
		asyncLoggerInit.Do(func() {
			asyncLogger = newAsyncLogger()
		})
	}
	return asyncLogger
}

func newAsyncLogger() *AsyncLogger {
	log := logrus.New()
	log.SetReportCaller(false)
	log.SetLevel(setLogLevel())
	log.AddHook(NewCustomHook())
	log.SetFormatter(&RrLogFormatter{})
	asyncLogger = &AsyncLogger{
		logger:  log,
		logChan: make(chan LogMsg, 1000),
		done:    make(chan bool),
	}
	return asyncLogger
}

func (a *AsyncLogger) Start(ctx context.Context) error {
	go a.Exec(ctx)
	return nil
}

func (a *AsyncLogger) Exec(ctx context.Context) {
	for {
		select {
		case msg := <-a.logChan:
			msg.entry.Logf(msg.level, msg.format, msg.args...)
		case <-ctx.Done():
			a.mu.Lock()
			defer a.mu.Unlock()
			a.isClosed = true
			close(a.logChan)
			a.done <- true
			return
		default:
			time.Sleep(200 * time.Millisecond)
		}
	}
}

func (a *AsyncLogger) Close(ctx context.Context) error {
	select {
	case <-a.done:
		// fmt.Println(a.Name(), " closed")
	case <-time.After(1 * time.Second):
		// fmt.Println(a.Name(), " close Waiting")
	}
	return nil
}

func (a *AsyncLogger) Name() string {
	return "AsyncLogger"
}

func (a *AsyncLogger) Producer(entry *logrus.Entry, level logrus.Level, format string, args ...interface{}) {
	a.mu.Lock()
	defer a.mu.Unlock()
	if a.isClosed {
		return
	}
	entry.Logger = a.logger
	entry.Data["line"] = findCaller(0)
	a.logChan <- LogMsg{entry, level, format, args}
}

func setLogLevel() logrus.Level {
	lv, err := logrus.ParseLevel(config.AppCfg.LogLevel)
	if err != nil {
		lv = logrus.DebugLevel
	}
	return lv
}

func NewLogMsg(ctx context.Context) *LogMsg {
	return &LogMsg{
		entry: logrus.WithContext(ctx),
	}
}

func (m *LogMsg) Trace(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.TraceLevel, format, args...)
}

func (m *LogMsg) Debug(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.DebugLevel, format, args...)
}

func (m *LogMsg) Info(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.InfoLevel, format, args...)
}

func (m *LogMsg) Warn(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.WarnLevel, format, args...)
}

func (m *LogMsg) Error(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.ErrorLevel, format, args...)
}

func (m *LogMsg) Fatal(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.FatalLevel, format, args...)
}

func (m *LogMsg) Panic(format string, args ...interface{}) {
	GetAsyncLogger().Producer(m.entry, logrus.PanicLevel, format, args...)
}

func (m *LogMsg) WithField(key string, value interface{}) *LogMsg {
	m.entry = m.entry.WithField(key, value)
	return m
}

func (m *LogMsg) WithFields(fields logrus.Fields) *LogMsg {
	m.entry = m.entry.WithFields(fields)
	return m
}

func (m *LogMsg) WithError(err error) *LogMsg {
	m.entry = m.entry.WithError(err)
	return m
}

func (m *LogMsg) WithTime(t time.Time) *LogMsg {
	m.entry = m.entry.WithTime(t)
	return m
}

func (m *LogMsg) WithContext(ctx context.Context) *LogMsg {
	m.entry = m.entry.WithContext(ctx)
	return m
}

// func Trace(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Trace(format, args...)
// }

// func Debug(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Debug(format, args...)
// }

// func Info(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Info(format, args...)
// }

// func Warn(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Warn(format, args...)
// }

func Error(ctx context.Context, format string, args ...interface{}) {
	WithContext(ctx).Error(format, args...)
}

// func Fatal(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Fatal(format, args...)
// }

// func Panic(ctx context.Context, format string, args ...interface{}) {
// 	WithContext(ctx).Panic(format, args...)
// }

func WithField(ctx context.Context, key string, value interface{}) *LogMsg {
	return WithContext(ctx).WithField(key, value)
}

// func WithFields(ctx context.Context, fields logrus.Fields) *LogMsg {
// 	return WithContext(ctx).WithFields(fields)
// }

func WithError(ctx context.Context, err error) *LogMsg {
	return WithContext(ctx).WithError(err)
}

func WithTime(ctx context.Context, t time.Time) *LogMsg {
	return WithContext(ctx).WithTime(t)
}

func WithContext(ctx context.Context) *LogMsg {
	return NewLogMsg(ctx)
}

func Ctx(ctx *gin.Context) *LogMsg {
	return NewLogMsg(ctx.Request.Context())
}
