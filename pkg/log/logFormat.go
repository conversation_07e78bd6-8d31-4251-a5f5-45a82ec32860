package log

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"vlab/pkg/util/ctxUtil"

	"github.com/golang-module/carbon/v2"
	"github.com/sirupsen/logrus"
)

type RrLogFormatter struct {
}

func (f *RrLogFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	data := make(logrus.Fields, len(entry.Data)+2)
	for k, v := range entry.Data {
		switch v := v.(type) {
		case error:
			data[k] = v.Error()
		default:
			data[k] = v
		}
	}

	fileLine, ok := entry.Data["line"].(string)
	if !ok {
		fileLine = "<unknown>"
	}
	delete(data, "line")

	data["msg"] = entry.Message
	data["traceId"] = ctxUtil.GetRequestID(entry.Context)
	timeStr := carbon.Now().ToDateTimeString()

	var b bytes.Buffer
	b.WriteString(fmt.Sprintf("[%s] [%s] [%d] [%s] - ", timeStr, entry.Level.String(), os.Getpid(), fileLine))
	encoder := json.NewEncoder(&b)
	encoder.SetEscapeHTML(false)
	if err := encoder.Encode(data); err != nil {
		return nil, fmt.Errorf("failed to marshal fields to JSON, %w", err)
	}
	return b.Bytes(), nil
}
