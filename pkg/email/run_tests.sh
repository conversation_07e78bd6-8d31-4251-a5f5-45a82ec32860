#!/bin/bash

# VLab 邮件服务测试脚本
# 用于快速运行pkg/email包的各种测试

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        print_error "Go环境未安装或未在PATH中"
        exit 1
    fi
    print_info "Go版本: $(go version)"
}

# 检查依赖
check_dependencies() {
    print_info "检查测试依赖..."
    
    # 检查testify
    if ! go list -m github.com/stretchr/testify &> /dev/null; then
        print_warning "testify未安装，正在安装..."
        go get github.com/stretchr/testify/assert
        go get github.com/stretchr/testify/suite
        go get github.com/stretchr/testify/mock
    fi
    
    # 检查redismock
    if ! go list -m github.com/go-redis/redismock/v8 &> /dev/null; then
        print_warning "redismock未安装，正在安装..."
        go get github.com/go-redis/redismock/v8
    fi
    
    print_success "依赖检查完成"
}

# 运行单元测试
run_unit_tests() {
    print_info "运行单元测试..."
    
    if go test -v . -run "^Test" -count=1; then
        print_success "单元测试通过"
    else
        print_error "单元测试失败"
        exit 1
    fi
}

# 运行基准测试
run_benchmark_tests() {
    print_info "运行基准测试..."
    
    go test -bench=. -benchmem . -run "^$"
    print_success "基准测试完成"
}

# 运行集成测试
run_integration_tests() {
    print_info "检查集成测试环境变量..."
    
    # 检查必需的环境变量
    required_vars=("TEST_SMTP_HOST" "TEST_SMTP_USERNAME" "TEST_SMTP_PASSWORD" "TEST_EMAIL")
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        print_warning "缺少以下环境变量，跳过集成测试:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo
        echo "要运行集成测试，请设置这些环境变量："
        echo "export TEST_SMTP_HOST=smtp.gmail.com"
        echo "export TEST_SMTP_USERNAME=<EMAIL>"
        echo "export TEST_SMTP_PASSWORD=your-app-password"
        echo "export TEST_EMAIL=<EMAIL>"
        return 0
    fi
    
    print_info "运行集成测试..."
    print_warning "注意：集成测试会发送真实邮件到 $TEST_EMAIL"
    
    if go test -tags=integration -v . -run "^TestReal" -count=1; then
        print_success "集成测试通过"
    else
        print_error "集成测试失败"
        exit 1
    fi
}

# 运行覆盖率测试
run_coverage_tests() {
    print_info "运行覆盖率测试..."
    
    go test -coverprofile=coverage.out .
    go tool cover -html=coverage.out -o coverage.html
    
    coverage=$(go tool cover -func=coverage.out | grep total | awk '{print $3}')
    print_info "总覆盖率: $coverage"
    
    if command -v open &> /dev/null; then
        open coverage.html
    elif command -v xdg-open &> /dev/null; then
        xdg-open coverage.html
    else
        print_info "覆盖率报告已生成: coverage.html"
    fi
}

# 运行race检测
run_race_tests() {
    print_info "运行race检测..."
    
    if go test -race -v . -run "^Test" -count=1; then
        print_success "race检测通过"
    else
        print_error "发现race condition"
        exit 1
    fi
}

# 清理测试文件
cleanup() {
    print_info "清理测试文件..."
    rm -f coverage.out coverage.html
    print_success "清理完成"
}

# 显示帮助信息
show_help() {
    cat << EOF
VLab 邮件服务测试脚本

用法: $0 [选项]

选项:
  -h, --help        显示帮助信息
  -u, --unit        只运行单元测试
  -i, --integration 只运行集成测试（需要环境变量）
  -b, --benchmark   只运行基准测试
  -c, --coverage    运行覆盖率测试
  -r, --race        运行race检测
  -a, --all         运行所有测试（默认）
  --cleanup         清理测试文件

示例:
  $0                # 运行所有测试
  $0 -u             # 只运行单元测试
  $0 -i             # 只运行集成测试
  $0 -c             # 运行覆盖率测试

环境变量（集成测试需要）:
  TEST_SMTP_HOST     SMTP服务器地址
  TEST_SMTP_USERNAME 邮箱用户名
  TEST_SMTP_PASSWORD 邮箱密码或授权码
  TEST_EMAIL         测试接收邮箱

EOF
}

# 主函数
main() {
    local unit_only=false
    local integration_only=false
    local benchmark_only=false
    local coverage_only=false
    local race_only=false
    local cleanup_only=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -u|--unit)
                unit_only=true
                shift
                ;;
            -i|--integration)
                integration_only=true
                shift
                ;;
            -b|--benchmark)
                benchmark_only=true
                shift
                ;;
            -c|--coverage)
                coverage_only=true
                shift
                ;;
            -r|--race)
                race_only=true
                shift
                ;;
            -a|--all)
                # 默认行为，不需要特殊处理
                shift
                ;;
            --cleanup)
                cleanup_only=true
                shift
                ;;
            *)
                print_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_info "VLab 邮件服务测试开始..."
    
    # 检查环境
    check_go
    check_dependencies
    
    # 执行清理
    if [[ "$cleanup_only" == true ]]; then
        cleanup
        exit 0
    fi
    
    # 执行特定测试
    if [[ "$unit_only" == true ]]; then
        run_unit_tests
    elif [[ "$integration_only" == true ]]; then
        run_integration_tests
    elif [[ "$benchmark_only" == true ]]; then
        run_benchmark_tests
    elif [[ "$coverage_only" == true ]]; then
        run_coverage_tests
    elif [[ "$race_only" == true ]]; then
        run_race_tests
    else
        # 默认运行所有测试
        run_unit_tests
        echo
        run_benchmark_tests
        echo
        run_race_tests
        echo
        run_integration_tests
        echo
        run_coverage_tests
    fi
    
    print_success "所有测试完成！"
}

# 运行主函数
main "$@" 