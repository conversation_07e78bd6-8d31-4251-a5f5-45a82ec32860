# VLab 邮件服务测试文档

本文档介绍如何运行和使用 VLab 邮件服务的测试套件。

## 测试文件结构

```
pkg/email/
├── email.go              # 邮件服务实现
├── email_test.go         # 单元测试
├── integration_test.go   # 集成测试
├── test_config.go        # 测试配置
├── run_tests.sh          # 测试运行脚本
└── README_TEST.md        # 本文档
```

## 测试类型

### 1. 单元测试 (email_test.go)

使用 Mock 对象测试邮件服务的各个功能，不依赖外部服务。

**测试内容：**
- 验证码生成和格式验证
- 邮件内容构建
- Redis 键名生成
- 验证码存储和验证逻辑
- 发送频率限制
- 错误处理

**运行命令：**
```bash
# 运行所有单元测试
go test -v ./pkg/email/ -run "^Test"

# 或使用脚本
./pkg/email/run_tests.sh -u
```

### 2. 集成测试 (integration_test.go)

测试真实的邮件发送功能，需要配置真实的 SMTP 服务器。

**测试内容：**
- 真实邮件发送
- 邮件服务商配置验证
- Redis 过期机制
- 性能测试
- 错误处理

**环境变量设置：**
```bash
export TEST_SMTP_HOST=smtp.gmail.com
export TEST_SMTP_USERNAME=<EMAIL>
export TEST_SMTP_PASSWORD=your-app-password
export TEST_EMAIL=<EMAIL>
```

**运行命令：**
```bash
# 运行集成测试
go test -tags=integration -v ./pkg/email/ -run "^TestReal"

# 或使用脚本
./pkg/email/run_tests.sh -i
```

### 3. 基准测试

测试邮件服务的性能表现。

**运行命令：**
```bash
# 运行基准测试
go test -bench=. -benchmem ./pkg/email/

# 或使用脚本
./pkg/email/run_tests.sh -b
```

## 快速开始

### 使用测试脚本

测试脚本 `run_tests.sh` 提供了便捷的测试运行方式：

```bash
# 查看帮助
./pkg/email/run_tests.sh -h

# 运行所有测试（默认）
./pkg/email/run_tests.sh

# 只运行单元测试
./pkg/email/run_tests.sh -u

# 只运行集成测试（需要环境变量）
./pkg/email/run_tests.sh -i

# 运行覆盖率测试
./pkg/email/run_tests.sh -c

# 运行 race 检测
./pkg/email/run_tests.sh -r

# 清理测试文件
./pkg/email/run_tests.sh --cleanup
```

### 手动运行测试

```bash
# 单元测试
go test -v ./pkg/email/

# 集成测试（需要环境变量）
go test -tags=integration -v ./pkg/email/

# 覆盖率测试
go test -coverprofile=coverage.out ./pkg/email/
go tool cover -html=coverage.out -o coverage.html

# Race 检测
go test -race -v ./pkg/email/

# 基准测试
go test -bench=. -benchmem ./pkg/email/
```

## 邮件服务商配置

### 支持的邮件服务商

1. **Gmail**
   - SMTP 服务器：smtp.gmail.com:587
   - 需要应用专用密码
   - 参考：[Gmail SMTP 设置](https://support.google.com/mail/answer/7126229)

2. **Outlook/Hotmail**
   - SMTP 服务器：smtp.outlook.com:587
   - 使用 Microsoft 账户密码

3. **QQ 邮箱**
   - SMTP 服务器：smtp.qq.com:587
   - 需要开启 SMTP 服务并获取授权码

4. **163 邮箱**
   - SMTP 服务器：smtp.163.com:25
   - 需要开启 SMTP 服务并获取授权码

5. **腾讯企业邮箱**
   - SMTP 服务器：smtp.exmail.qq.com:587
   - 使用企业邮箱密码

### 配置示例

使用测试配置工具：

```go
import "vlab/pkg/email"

// 设置 Gmail 配置
cfg := email.SetupTestConfig("gmail", "<EMAIL>", "your-app-password")
config.EmailCfg = cfg

// 获取支持的服务商列表
providers := email.GetSupportedProviders()
fmt.Println("支持的服务商:", providers)
```

## 测试最佳实践

### 1. 环境隔离

- 单元测试使用 Mock 对象，不依赖外部服务
- 集成测试使用真实服务，但通过环境变量控制
- 测试数据使用专门的测试邮箱

### 2. 测试数据管理

```bash
# 设置测试专用邮箱
export TEST_EMAIL=<EMAIL>

# 使用不同的 Redis 数据库
export REDIS_DB=1  # 默认使用 0，测试使用 1
```

### 3. 并发测试

测试包含并发场景：
- 并发验证码生成
- 并发邮件发送
- Race condition 检测

### 4. 性能测试

基准测试会评估：
- 验证码生成性能
- 邮件内容构建性能
- Redis 操作性能

## 常见问题

### Q: 集成测试失败，提示认证错误？

A: 检查以下几点：
1. 邮箱用户名和密码是否正确
2. 是否开启了 SMTP 服务
3. Gmail 是否使用应用专用密码
4. QQ/163 邮箱是否使用授权码而非登录密码

### Q: 单元测试通过但集成测试失败？

A: 可能的原因：
1. 网络连接问题
2. SMTP 服务器配置错误
3. 防火墙阻止 SMTP 连接
4. 邮箱服务商限制

### Q: 如何调试邮件发送问题？

A: 启用详细日志：
```bash
# 运行时启用调试模式
export GIN_MODE=debug
go test -tags=integration -v ./pkg/email/ -run TestRealEmailSending
```

### Q: 如何模拟网络错误？

A: 在单元测试中使用 Mock：
```go
// 模拟 Redis 连接错误
suite.redisMock.ExpectGet("key").SetErr(errors.New("connection failed"))
```

## 持续集成

在 CI/CD 环境中运行测试：

```yaml
# GitHub Actions 示例
- name: Run Email Unit Tests
  run: go test -v ./pkg/email/ -run "^Test"

- name: Run Email Integration Tests
  env:
    TEST_SMTP_HOST: ${{ secrets.TEST_SMTP_HOST }}
    TEST_SMTP_USERNAME: ${{ secrets.TEST_SMTP_USERNAME }}
    TEST_SMTP_PASSWORD: ${{ secrets.TEST_SMTP_PASSWORD }}
    TEST_EMAIL: ${{ secrets.TEST_EMAIL }}
  run: go test -tags=integration -v ./pkg/email/
```

## 贡献指南

添加新测试时请遵循：

1. **测试命名**：使用描述性的测试名称
2. **测试隔离**：每个测试应该独立运行
3. **Mock 使用**：单元测试优先使用 Mock
4. **文档更新**：添加新功能时更新相应文档
5. **错误处理**：测试各种错误场景

## 测试覆盖率目标

- 单元测试覆盖率：> 90%
- 关键路径覆盖率：100%
- 错误处理覆盖率：> 85%

运行覆盖率测试查看当前状态：
```bash
./pkg/email/run_tests.sh -c
``` 