package email

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"net/smtp"
	"strings"
	"sync"
	"time"

	"vlab/app/dao/common"
	"vlab/config"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
)

type EmailService struct {
	redisCli *redis.RedisClient
}

var (
	defaultEmailService         *EmailService
	defaultEmailServiceInitOnce sync.Once
)

func Setup() {
	defaultEmailService = &EmailService{
		redisCli: redis.GetRedisClient(),
	}
}

func GetEmailService() *EmailService {

	defaultEmailServiceInitOnce.Do(func() {
		Setup()
	})

	return defaultEmailService
}

// SendVerificationCode 发送邮箱验证码
func (e *EmailService) SendVerificationCode(ctx *gin.Context, email string) error {
	// 检查发送频率限制（1分钟内只能发送一次）
	if e.isCodeSentRecently(ctx, email) {
		return fmt.Errorf("code already sent recently, please try again later")
	}

	// 生成6位数字验证码
	code, err := e.generateVerificationCode()
	if err != nil {
		log.Ctx(ctx).WithError(err).Error("generateVerificationCode error")
		return fmt.Errorf("generate verification code failed")
	}

	// 发送邮件
	if err = e.sendEmail(ctx, email, "Verification Code", e.buildVerificationEmailContent(ctx, code)); err != nil {
		log.Ctx(ctx).WithError(err).Error("sendEmail error")
		return fmt.Errorf("send email failed")
	}

	// 存储验证码到Redis，有效期5分钟
	if err := e.storeVerificationCode(ctx, email, code); err != nil {
		log.Ctx(ctx).WithError(err).Error("storeVerificationCode error")
		return fmt.Errorf("store verification code failed")
	}

	// 记录发送时间，防止频繁发送
	if err := e.recordSendTime(ctx, email); err != nil {
		log.Ctx(ctx).WithError(err).Warn("recordSendTime error")
	}

	log.Ctx(ctx).WithField("email", email).Info("email verification code sent successfully")
	return nil
}

// VerifyCode 验证邮箱验证码
func (e *EmailService) VerifyCode(ctx *gin.Context, email, code string) bool {
	storedCode, err := e.getStoredVerificationCode(ctx, email)
	if err != nil {
		log.Ctx(ctx).WithError(err).Warn("getStoredVerificationCode error")
		return false
	}

	if storedCode == "" {
		return false
	}

	isValid := strings.EqualFold(storedCode, code)
	if isValid {
		// 验证成功后删除验证码
		if err := e.deleteVerificationCode(ctx, email); err != nil {
			log.Ctx(ctx).WithError(err).Warn("deleteVerificationCode error")
		}
		log.Ctx(ctx).WithField("email", email).Debug("email verification code validated successfully")
	} else {
		log.Ctx(ctx).WithField("email", email).Warn("email verification code validation failed")
	}

	return isValid
}

// generateVerificationCode 生成6位数字验证码
func (e *EmailService) generateVerificationCode() (string, error) {
	max := big.NewInt(999999)
	min := big.NewInt(100000)

	n, err := rand.Int(rand.Reader, new(big.Int).Sub(max, min))
	if err != nil {
		return "", err
	}

	code := new(big.Int).Add(n, min)
	return fmt.Sprintf("%06d", code.Int64()), nil
}

// getEmailConfig 根据平台ID获取对应的邮件配置
// 每次新增平台时，需要在此处添加对应的邮件配置逻辑
func (e *EmailService) getEmailConfig(ctx *gin.Context) *config.Email {
	var emailCfg *config.Email

	pID := helper.GetCtxPlatformID(ctx)

	// 根据渠道ID获取对应的邮件配置
	switch pID {
	case uint32(common.PlatformSkybox):
		emailCfg = config.SkyEmailCfg
	case uint32(common.PlatformViBox):
		emailCfg = config.ViEmailCfg
	case uint32(common.PlatformHitv):
		emailCfg = config.HitvEmailCfg
	case uint32(common.PlatformAhatv):
		emailCfg = config.AhatvEmailCfg
	case uint32(common.PlatformLomlom):
		emailCfg = config.LomEmailCfg
	case uint32(common.PlatformPikcube):
		emailCfg = config.PikEmailCfg
	default:
		emailCfg = nil
	}
	return emailCfg
}

// sendEmail 发送邮件
func (e *EmailService) sendEmail(ctx *gin.Context, to, subject, body string) error {

	emailCfg := e.getEmailConfig(ctx)
	if emailCfg == nil {
		log.Ctx(ctx).Error("email configuration not found")
		return fmt.Errorf("email configuration not found")
	}

	// 构建邮件内容
	msg := fmt.Sprintf("From: %s <%s>\r\n", emailCfg.FromName, emailCfg.From)
	msg += fmt.Sprintf("To: %s\r\n", to)
	msg += fmt.Sprintf("Subject: %s\r\n", subject)
	msg += "Content-Type: text/html; charset=UTF-8\r\n"
	msg += "\r\n"
	msg += body

	// SMTP认证
	auth := smtp.PlainAuth("", emailCfg.Username, emailCfg.Password, emailCfg.Host)

	// 发送邮件
	addr := fmt.Sprintf("%s:%d", emailCfg.Host, emailCfg.Port)
	return smtp.SendMail(addr, auth, emailCfg.From, []string{to}, []byte(msg))
}

// buildVerificationEmailContent 构建验证码邮件内容
func (e *EmailService) buildVerificationEmailContent(ctx *gin.Context, code string) string {
	emailCfg := e.getEmailConfig(ctx)

	return fmt.Sprintf(`
<div class="base-layout-root_PIHC_0" style="background-color:#FCFCFC;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width: 100%%;">
<tbody>
<tr>
<td>
<div style="margin:0px auto;max-width:674px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%%;">
<tbody>
<tr>
<td style="direction:ltr;font-size:0px;padding:0;padding-left:16px;padding-right:16px;padding-top:32px;text-align:center;">
<div class="mj-column-per-100_2Prp_1 mj-outlook-group-fix_5ONk_2 card_wJyB_3 card-body-with-logo-top_ykxS_4" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%%;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%%" style="border-collapse: separate;">
<tbody>
<tr>
<td style="background-color:#FFFFFF;border:1px solid #e0e0e0;border-bottom:none;border-radius:12px 12px 0px 0px;vertical-align:top;padding:20px 32px;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%%">
<tbody>
<tr>
<td align="center" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:20px;font-weight:600;line-height:24px;text-align:center;color:#5753C6;">Verification Code</div>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</div>
</td>
</tr>
</tbody>
</table>
</div>
</td>
</tr>
</tbody>
</table>

<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%%;">
<tbody>
<tr>
<td>
<div style="margin:0px auto;max-width:674px;">
<table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%%;">
<tbody>
<tr>
<td style="direction:ltr;font-size:0px;padding:0;padding-bottom:24px;padding-left:16px;padding-right:16px;text-align:center;">
<div class="mj-column-per-100_2Prp_1 mj-outlook-group-fix_5ONk_2 card_wJyB_3 card-body-with-logo-bottom_Qt3c_5" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%%;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%%" style="border-collapse: separate;">
<tbody>
<tr>
<td style="background-color:#FFFFFF;border:1px solid #e0e0e0;border-radius:0px 0px 12px 12px;border-top:none;vertical-align:top;padding:0px 40px 36px;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%%">
<tbody>
<tr>
<td align="left" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:24px;font-weight:600;line-height:30px;text-align:left;color:#202020;">Verify your email</div>
</td>
</tr>
<tr>
<td style="font-size:0px;padding:0;word-break:break-word;">
<div style="height:16px;line-height:16px;">&hairsp;</div>
</td>
</tr>
<tr>
<td align="left" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:16px;line-height:24px;text-align:left;color:#202020;">We need to verify your email address before you can access your account. Enter the code below in your App or open browser window.</div>
</td>
</tr>
<tr>
<td style="font-size:0px;padding:0;word-break:break-word;">
<div style="height:24px;line-height:24px;">&hairsp;</div>
</td>
</tr>
<tr>
<td align="left" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:28px;font-weight:400;letter-spacing:2px;line-height:30px;text-align:left;color:#202020;">%s</div>
</td>
</tr>
<tr>
<td style="font-size:0px;padding:0;word-break:break-word;">
<div style="height:32px;line-height:32px;">&hairsp;</div>
</td>
</tr>
<tr>
<td align="center" style="font-size:0px;padding:0;word-break:break-word;">
<p style="border-top:solid 1px #d9d9d9;font-size:1px;margin:0px auto;width:100%%;"></p>
</td>
</tr>
<tr>
<td style="font-size:0px;padding:0;word-break:break-word;">
<div style="height:32px;line-height:32px;">&hairsp;</div>
</td>
</tr>
<tr>
<td align="left" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:12px;line-height:18px;text-align:left;color:#646464;">This code expires in 5 minutes.</div>
</td>
</tr>
<tr>
<td style="font-size:0px;padding:0;word-break:break-word;">
<div style="height:12px;line-height:12px;">&hairsp;</div>
</td>
</tr>
<tr>
<td align="left" style="font-size:0px;padding:0;word-break:break-word;">
<div style="font-family:-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;font-size:12px;line-height:18px;text-align:left;color:#646464;">If you didn't sign up for %s, you can safely ignore this email. Someone else might have typed your email address by mistake.</div>
</td>
</tr>
</tbody>
</table>
</td>
</tr>
</tbody>
</table>
</div>
</td>
</tr>
</tbody>
</table>
</div>
</td>
</tr>
</tbody>
</table>
</div>
`, code, emailCfg.FromName)
}

// Redis相关方法

// storeVerificationCode 存储验证码到Redis
func (e *EmailService) storeVerificationCode(ctx *gin.Context, email, code string) error {
	key := redis.GetVerificationCodeKey(email)
	return e.redisCli.SetEX(ctx.Request.Context(), key, code, 5*time.Minute).Err()
}

// getStoredVerificationCode 从Redis获取验证码
func (e *EmailService) getStoredVerificationCode(ctx *gin.Context, email string) (string, error) {
	key := redis.GetVerificationCodeKey(email)
	return e.redisCli.Get(ctx.Request.Context(), key).Result()
}

// deleteVerificationCode 删除验证码
func (e *EmailService) deleteVerificationCode(ctx *gin.Context, email string) error {
	key := redis.GetVerificationCodeKey(email)
	return e.redisCli.Del(ctx.Request.Context(), key).Err()
}

// recordSendTime 记录发送时间
func (e *EmailService) recordSendTime(ctx *gin.Context, email string) error {
	key := redis.GetSendTimeKey(email)
	return e.redisCli.SetEX(ctx.Request.Context(), key, time.Now().Unix(), 1*time.Minute).Err()
}

// isCodeSentRecently 检查是否最近发送过验证码
func (e *EmailService) isCodeSentRecently(ctx *gin.Context, email string) bool {
	key := redis.GetSendTimeKey(email)
	_, err := e.redisCli.Get(ctx.Request.Context(), key).Result()
	return err == nil
}
