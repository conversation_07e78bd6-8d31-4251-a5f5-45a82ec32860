//go:build test
// +build test

package email

import (
	"vlab/config"
)

// TestEmailConfigs 测试邮件配置模板
var TestEmailConfigs = map[string]*config.Email{
	"gmail": {
		Host:     "smtp.gmail.com",
		Port:     587,
		Username: "", // 需要设置真实的Gmail地址
		Password: "", // 需要设置应用专用密码
		From:     "", // 与Username相同
		FromName: "VLab Test",
	},
	"outlook": {
		Host:     "smtp.outlook.com",
		Port:     587,
		Username: "", // 需要设置真实的Outlook地址
		Password: "", // 需要设置密码
		From:     "", // 与Username相同
		FromName: "VLab Test",
	},
	"qq": {
		Host:     "smtp.qq.com",
		Port:     587,
		Username: "", // 需要设置真实的QQ邮箱地址
		Password: "", // 需要设置授权码
		From:     "", // 与Username相同
		FromName: "VLab Test",
	},
	"163": {
		Host:     "smtp.163.com",
		Port:     25,
		Username: "", // 需要设置真实的163邮箱地址
		Password: "", // 需要设置授权码
		From:     "", // 与Username相同
		FromName: "VLab Test",
	},
	"tencent_exmail": {
		Host:     "smtp.exmail.qq.com",
		Port:     587,
		Username: "", // 需要设置真实的腾讯企业邮箱地址
		Password: "", // 需要设置密码
		From:     "", // 与Username相同
		FromName: "VLab Test",
	},
}

// SetupTestConfig 设置测试配置
func SetupTestConfig(provider string, username, password string) *config.Email {
	cfg, exists := TestEmailConfigs[provider]
	if !exists {
		return nil
	}

	// 复制配置避免修改原始模板
	testCfg := &config.Email{
		Host:     cfg.Host,
		Port:     cfg.Port,
		Username: username,
		Password: password,
		From:     username,
		FromName: cfg.FromName,
	}

	return testCfg
}

// GetSupportedProviders 获取支持的邮件服务商列表
func GetSupportedProviders() []string {
	providers := make([]string, 0, len(TestEmailConfigs))
	for provider := range TestEmailConfigs {
		providers = append(providers, provider)
	}
	return providers
}

// ValidateTestConfig 验证测试配置是否完整
func ValidateTestConfig(cfg *config.Email) bool {
	return cfg != nil &&
		cfg.Host != "" &&
		cfg.Port > 0 &&
		cfg.Username != "" &&
		cfg.Password != "" &&
		cfg.From != ""
}

// TestConfigExamples 测试配置示例说明
const TestConfigExamples = `
邮件服务测试配置说明
===================

支持的邮件服务商:
1. Gmail - 需要应用专用密码
2. Outlook - 支持Microsoft账户
3. QQ邮箱 - 需要开启SMTP并获取授权码
4. 163邮箱 - 需要开启SMTP并获取授权码
5. 腾讯企业邮箱 - 企业邮箱账户

使用方法:
1. 根据你的邮件服务商获取正确的配置
2. 设置环境变量或直接在代码中配置
3. 运行测试

环境变量设置示例:
export TEST_SMTP_HOST=smtp.gmail.com
export TEST_SMTP_USERNAME=<EMAIL>
export TEST_SMTP_PASSWORD=your-app-password
export TEST_EMAIL=<EMAIL>

注意事项:
- Gmail需要开启"不够安全的应用的访问权限"或使用应用专用密码
- QQ邮箱和163邮箱需要在邮箱设置中开启SMTP服务并获取授权码
- 企业邮箱通常可以直接使用登录密码
- 测试邮件会发送到TEST_EMAIL指定的地址
`
