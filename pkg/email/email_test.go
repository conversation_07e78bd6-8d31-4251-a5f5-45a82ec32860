package email

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"vlab/config"
	"vlab/pkg/helper"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redismock/v8"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

func TestMain(m *testing.M) {
	os.Setenv("config", "../../config/test.ini")
	// 读取配置文件
	config.Setup()

	// Run the tests
	code := m.Run()

	// Exit with the result code from m.Run()
	os.Exit(code)
}

// EmailServiceTestSuite 邮箱服务测试套件
type EmailServiceTestSuite struct {
	suite.Suite
	emailService *EmailService
	ctx          *gin.Context
	redisMock    redismock.ClientMock
}

func (suite *EmailServiceTestSuite) SetupSuite() {
	// 设置测试环境
	gin.SetMode(gin.TestMode)

	// 初始化配置
	config.SkyEmailCfg = &config.Email{
		Host:     "smtpdm-ap-southeast-1.aliyun.com",
		Port:     465,
		Username: "",
		Password: "Lqg4VR4hj6MJeQf",
		From:     "<EMAIL>",
		FromName: "Test VLab",
	}
	config.ViEmailCfg = &config.Email{
		Host:     "smtpdm-ap-southeast-1.aliyun.com",
		Port:     465,
		Username: "",
		Password: "Lqg4VR4hj6MJeQf",
		From:     "<EMAIL>",
		FromName: "Test VLab",
	}
}

func (suite *EmailServiceTestSuite) SetupTest() {
	// 创建Redis mock
	db, mock := redismock.NewClientMock()
	suite.redisMock = mock

	// 创建邮箱服务实例
	suite.emailService = &EmailService{
		redisCli: &redis.RedisClient{Client: db},
	}

	// 创建gin context
	ctx, _ := gin.CreateTestContext(nil)
	ctx.Request = &http.Request{}
	ctx.Request = ctx.Request.WithContext(context.Background())
	suite.ctx = ctx
}

func (suite *EmailServiceTestSuite) TearDownTest() {
	// 验证所有mock调用
	suite.redisMock.ClearExpect()
}

// TestGenerateVerificationCode 测试验证码生成
func (suite *EmailServiceTestSuite) TestGenerateVerificationCode() {
	code, err := suite.emailService.generateVerificationCode()

	suite.NoError(err)
	suite.Len(code, 6, "验证码应为6位数字")
	suite.Regexp(`^\d{6}$`, code, "验证码应只包含数字")

	// 验证验证码在有效范围内
	suite.GreaterOrEqual(code, "100000")
	suite.LessOrEqual(code, "999999")
}

// TestGenerateMultipleVerificationCodes 测试生成多个验证码的唯一性
func (suite *EmailServiceTestSuite) TestGenerateMultipleVerificationCodes() {
	codes := make(map[string]bool)

	// 生成100个验证码，检查是否有重复
	for i := 0; i < 100; i++ {
		code, err := suite.emailService.generateVerificationCode()
		suite.NoError(err)

		// 检查格式
		suite.Len(code, 6)
		suite.Regexp(`^\d{6}$`, code)

		// 记录验证码
		codes[code] = true
	}

	// 虽然理论上可能有重复，但100个验证码重复的概率很低
	suite.Greater(len(codes), 80, "生成的验证码应该有较好的随机性")
}

// TestBuildVerificationEmailContent 测试邮件内容构建
func (suite *EmailServiceTestSuite) TestBuildVerificationEmailContent() {
	code := "123456"
	content := suite.emailService.buildVerificationEmailContent("", code)

	suite.Contains(content, code, "邮件内容应包含验证码")
	suite.Contains(content, "VLab", "邮件内容应包含应用名称")
	suite.Contains(content, "验证码", "邮件内容应包含验证码说明")
	suite.Contains(content, "5分钟", "邮件内容应包含有效期说明")
	suite.Contains(content, "<!DOCTYPE html>", "邮件内容应为HTML格式")
}

// TestGetVerificationCodeKey 测试Redis键生成
func (suite *EmailServiceTestSuite) TestGetVerificationCodeKey() {
	email := "<EMAIL>"
	key := redis.GetVerificationCodeKey(email)

	expected := "email:verify_code:<EMAIL>"
	suite.Equal(expected, key)
}

// TestGetSendTimeKey 测试发送时间键生成
func (suite *EmailServiceTestSuite) TestGetSendTimeKey() {
	email := "<EMAIL>"
	key := redis.GetSendTimeKey(email)

	expected := "email:send_time:<EMAIL>"
	suite.Equal(expected, key)
}

// TestStoreVerificationCode 测试存储验证码
func (suite *EmailServiceTestSuite) TestStoreVerificationCode() {
	email := "<EMAIL>"
	code := "123456"

	// 设置Redis mock期望
	suite.redisMock.ExpectSetEX("email:verify_code:<EMAIL>", code, 5*time.Minute).SetVal("OK")

	err := suite.emailService.storeVerificationCode(suite.ctx, email, code)
	suite.NoError(err)
}

// TestGetStoredVerificationCode 测试获取存储的验证码
func (suite *EmailServiceTestSuite) TestGetStoredVerificationCode() {
	email := "<EMAIL>"
	expectedCode := "123456"

	// 设置Redis mock期望
	suite.redisMock.ExpectGet("email:verify_code:<EMAIL>").SetVal(expectedCode)

	code, err := suite.emailService.getStoredVerificationCode(suite.ctx, email)
	suite.NoError(err)
	suite.Equal(expectedCode, code)
}

// TestDeleteVerificationCode 测试删除验证码
func (suite *EmailServiceTestSuite) TestDeleteVerificationCode() {
	email := "<EMAIL>"

	// 设置Redis mock期望
	suite.redisMock.ExpectDel("email:verify_code:<EMAIL>").SetVal(1)

	err := suite.emailService.deleteVerificationCode(suite.ctx, email)
	suite.NoError(err)
}

// TestRecordSendTime 测试记录发送时间
func (suite *EmailServiceTestSuite) TestRecordSendTime() {
	// 跳过这个测试，因为时间戳动态生成难以mock
	// 我们在集成测试中测试这个功能
	suite.T().Skip("跳过recordSendTime单元测试，在集成测试中验证")
}

// TestIsCodeSentRecently 测试检查是否最近发送过验证码
func (suite *EmailServiceTestSuite) TestIsCodeSentRecently() {
	email := "<EMAIL>"

	// 测试最近发送过的情况
	suite.redisMock.ExpectGet("email:send_time:<EMAIL>").SetVal("1234567890")
	result := suite.emailService.isCodeSentRecently(suite.ctx, email)
	suite.True(result)

	// 测试没有发送记录的情况
	suite.redisMock.ExpectGet("email:send_time:<EMAIL>").RedisNil()
	result = suite.emailService.isCodeSentRecently(suite.ctx, email)
	suite.False(result)
}

// TestVerifyCode 测试验证码验证
func (suite *EmailServiceTestSuite) TestVerifyCode() {
	email := "<EMAIL>"
	code := "123456"

	// 测试验证成功的情况
	suite.redisMock.ExpectGet("email:verify_code:<EMAIL>").SetVal(code)
	suite.redisMock.ExpectDel("email:verify_code:<EMAIL>").SetVal(1)

	isValid, err := suite.emailService.VerifyCode(suite.ctx, email, code)
	suite.NoError(err)
	suite.True(isValid)
}

// TestVerifyCodeFailed 测试验证码验证失败
func (suite *EmailServiceTestSuite) TestVerifyCodeFailed() {
	email := "<EMAIL>"
	storedCode := "123456"
	wrongCode := "654321"

	// 设置Redis mock期望
	suite.redisMock.ExpectGet("email:verify_code:<EMAIL>").SetVal(storedCode)

	isValid, err := suite.emailService.VerifyCode(suite.ctx, email, wrongCode)
	suite.NoError(err)
	suite.False(isValid)
}

// TestVerifyCodeExpired 测试验证码过期
func (suite *EmailServiceTestSuite) TestVerifyCodeExpired() {
	email := "<EMAIL>"
	code := "123456"

	// 设置Redis mock期望 - 模拟key不存在（过期）
	suite.redisMock.ExpectGet("email:verify_code:<EMAIL>").RedisNil()

	isValid, err := suite.emailService.VerifyCode(suite.ctx, email, code)
	suite.Error(err)
	suite.False(isValid)
	suite.Contains(err.Error(), "验证码已过期")
}

// TestVerifyCodeEmpty 测试空验证码
func (suite *EmailServiceTestSuite) TestVerifyCodeEmpty() {
	email := "<EMAIL>"
	code := "123456"

	// 设置Redis mock期望 - 返回空字符串
	suite.redisMock.ExpectGet("email:verify_code:<EMAIL>").SetVal("")

	isValid, err := suite.emailService.VerifyCode(suite.ctx, email, code)
	suite.Error(err)
	suite.False(isValid)
	suite.Contains(err.Error(), "验证码不存在或已过期")
}

// TestSendVerificationCodeWithFrequencyLimit 测试发送频率限制
func (suite *EmailServiceTestSuite) TestSendVerificationCodeWithFrequencyLimit() {
	email := "<EMAIL>"

	// 模拟最近已发送过验证码
	suite.redisMock.ExpectGet("email:send_time:<EMAIL>").SetVal("1234567890")

	err := suite.emailService.SendVerificationCode(suite.ctx, email)
	suite.Error(err)
	suite.Contains(err.Error(), "验证码发送过于频繁")
}

// 运行测试套件
func TestEmailServiceTestSuite(t *testing.T) {
	suite.Run(t, new(EmailServiceTestSuite))
}

// TestEmailServiceSetup 测试服务初始化
func TestEmailServiceSetup(t *testing.T) {
	// 这个测试需要Redis连接，跳过
	t.Skip("跳过Setup测试，因为需要Redis连接")
}

// BenchmarkGenerateVerificationCode 验证码生成性能测试
func BenchmarkGenerateVerificationCode(b *testing.B) {
	service := &EmailService{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = service.generateVerificationCode()
	}
}

// BenchmarkBuildVerificationEmailContent 邮件内容构建性能测试
func BenchmarkBuildVerificationEmailContent(b *testing.B) {
	service := &EmailService{}
	code := "123456"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = service.buildVerificationEmailContent("", code)
	}
}

// TestEmailValidation 测试邮箱格式验证的边界情况
func TestEmailValidation(t *testing.T) {
	testCases := []struct {
		name  string
		email string
		valid bool
	}{
		{"正常邮箱", "<EMAIL>", true},
		{"大写邮箱", "<EMAIL>", true},
		{"混合大小写", "<EMAIL>", true},
		{"带数字", "<EMAIL>", true},
		{"带下划线", "<EMAIL>", true},
		{"带点号", "<EMAIL>", true},
		{"带连字符", "<EMAIL>", true},
		{"空字符串", "", false},
		{"无@符号", "testexample.com", false},
		{"无域名", "test@", false},
		{"无用户名", "@example.com", false},
		{"多个@符号", "test@@example.com", false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 这里可以添加邮箱格式验证的测试逻辑
			// 由于当前代码中邮箱验证主要在binding层，这里主要测试服务层的处理
			assert.True(t, len(tc.email) >= 0) // 占位符测试
		})
	}
}

// TestConcurrentVerificationCodeGeneration 并发验证码生成测试
func TestConcurrentVerificationCodeGeneration(t *testing.T) {
	service := &EmailService{}
	codesChan := make(chan string, 100)

	// 启动100个goroutine并发生成验证码
	for i := 0; i < 100; i++ {
		go func() {
			code, err := service.generateVerificationCode()
			if err == nil {
				codesChan <- code
			}
		}()
	}

	// 收集结果
	codes := make(map[string]int)
	for i := 0; i < 100; i++ {
		code := <-codesChan
		codes[code]++
		// 验证格式
		assert.Len(t, code, 6)
		assert.Regexp(t, `^\d{6}$`, code)
	}

	// 验证没有过多重复（允许少量重复）
	assert.Greater(t, len(codes), 80, "并发生成的验证码应该有较好的随机性")
}

func TestEmailService_SendVerificationCode(t *testing.T) {
	type fields struct {
		redisCli *redis.RedisClient
	}
	type args struct {
		ctx   *gin.Context
		email string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		// TODO: Add test cases.
		{
			name: "发送验证码成功",
			fields: fields{
				redisCli: redis.GetRedisClient(),
			},
			args: args{
				ctx:   helper.GenGinCtx(),
				email: "<EMAIL>",
			},
			wantErr: assert.NoError,
		},
		{
			name: "发送验证码成功",
			fields: fields{
				redisCli: redis.GetRedisClient(),
			},
			args: args{
				ctx:   helper.GenGinCtx(),
				email: "<EMAIL>",
			},
			wantErr: assert.NoError,
		},
		{
			name: "发送验证码成功",
			fields: fields{
				redisCli: redis.GetRedisClient(),
			},
			args: args{
				ctx:   helper.GenGinCtx(),
				email: "<EMAIL>",
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := &EmailService{
				redisCli: tt.fields.redisCli,
			}
			tt.wantErr(t, e.SendVerificationCode(tt.args.ctx, tt.args.email), fmt.Sprintf("SendVerificationCode(%v, %v)", tt.args.ctx, tt.args.email))
		})
	}
}
