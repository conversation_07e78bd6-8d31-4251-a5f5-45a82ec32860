//go:build integration
// +build integration

package email

import (
	"context"
	"net/http"
	"os"
	"testing"
	"time"

	"vlab/config"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// IntegrationTestSuite 集成测试套件
// 运行命令: go test -tags=integration ./pkg/email/
type IntegrationTestSuite struct {
	emailService *EmailService
	ctx          *gin.Context
	testEmail    string
}

// getTestEmailFromEnv 从环境变量获取测试邮箱
func getTestEmailFromEnv() string {
	return os.Getenv("TEST_EMAIL")
}

func setupIntegrationTest(t *testing.T) *IntegrationTestSuite {
	// 从环境变量或配置文件加载配置
	// 这里使用测试配置
	testEmail := "<EMAIL>"

	// 如果设置了真实邮箱进行测试，可以从环境变量读取
	if realEmail := getTestEmailFromEnv(); realEmail != "" {
		testEmail = realEmail
	}

	// 加载测试配置
	err := config.LoadConfig("test")
	if err != nil {
		t.Logf("Warning: 无法加载测试配置，使用默认配置: %v", err)
		// 设置默认测试配置
		config.EmailCfg = &config.Email{
			Host:     "smtp.gmail.com",
			Port:     587,
			Username: "<EMAIL>",
			Password: "test-app-password",
			From:     "<EMAIL>",
			FromName: "VLab Test",
		}
	}

	// 验证邮件配置
	if config.EmailCfg == nil {
		t.Skip("跳过集成测试：未配置邮件服务")
	}

	// 初始化Redis（使用真实的Redis连接）
	redis.Setup()

	// 设置邮件服务
	Setup()

	// 创建gin context
	ctx, _ := gin.CreateTestContext(nil)
	// 修复：正确设置Request
	req, _ := http.NewRequestWithContext(context.Background(), "POST", "/test", nil)
	ctx.Request = req

	return &IntegrationTestSuite{
		emailService: GetEmailService(),
		ctx:          ctx,
		testEmail:    testEmail,
	}
}

// TestRealEmailSending 测试真实邮件发送
func TestRealEmailSending(t *testing.T) {
	suite := setupIntegrationTest(t)

	t.Run("发送验证码邮件", func(t *testing.T) {
		err := suite.emailService.SendVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "发送验证码邮件应该成功")

		// 验证Redis中是否存储了验证码
		code, err := suite.emailService.getStoredVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "应该能够从Redis获取验证码")
		assert.Len(t, code, 6, "验证码应为6位")
		assert.Regexp(t, `^\d{6}$`, code, "验证码应只包含数字")

		t.Logf("验证码已发送到 %s，验证码为: %s", suite.testEmail, code)
	})

	t.Run("发送频率限制", func(t *testing.T) {
		// 第一次发送
		err := suite.emailService.SendVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "第一次发送应该成功")

		// 立即再次发送，应该失败
		err = suite.emailService.SendVerificationCode(suite.ctx, suite.testEmail)
		assert.Error(t, err, "频率限制应该生效")
		assert.Contains(t, err.Error(), "验证码发送过于频繁")
	})

	t.Run("验证码验证", func(t *testing.T) {
		// 等待1分钟让频率限制失效
		time.Sleep(61 * time.Second)

		// 发送新的验证码
		err := suite.emailService.SendVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "发送验证码应该成功")

		// 获取验证码
		code, err := suite.emailService.getStoredVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "应该能够获取验证码")

		// 验证正确的验证码
		isValid, err := suite.emailService.VerifyCode(suite.ctx, suite.testEmail, code)
		assert.NoError(t, err, "验证应该成功")
		assert.True(t, isValid, "验证码应该有效")

		// 再次验证相同验证码，应该失败（已被删除）
		isValid, err = suite.emailService.VerifyCode(suite.ctx, suite.testEmail, code)
		assert.Error(t, err, "验证码应该已被删除")
		assert.False(t, isValid, "验证码应该无效")
	})
}

// TestEmailConfiguration 测试不同邮件服务商配置
func TestEmailConfiguration(t *testing.T) {
	testCases := []struct {
		name     string
		provider string
		host     string
		port     int
		ssl      bool
	}{
		{"Gmail", "gmail", "smtp.gmail.com", 587, false},
		{"Outlook", "outlook", "smtp.outlook.com", 587, false},
		{"Yahoo", "yahoo", "smtp.mail.yahoo.com", 587, false},
		{"QQ", "qq", "smtp.qq.com", 587, false},
		{"163", "163", "smtp.163.com", 25, false},
		{"腾讯企业邮箱", "tencent", "smtp.exmail.qq.com", 587, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 这里主要测试配置的正确性，不实际发送邮件
			config.EmailCfg = &config.Email{
				Host:     tc.host,
				Port:     tc.port,
				Username: "<EMAIL>",
				Password: "test-password",
				From:     "<EMAIL>",
				FromName: "Test VLab",
			}

			// 验证配置是否正确设置
			assert.Equal(t, tc.host, config.EmailCfg.Host)
			assert.Equal(t, tc.port, config.EmailCfg.Port)
		})
	}
}

// TestEmailTemplateRendering 测试邮件模板渲染
func TestEmailTemplateRendering(t *testing.T) {
	service := &EmailService{}

	testCases := []struct {
		name string
		code string
	}{
		{"普通验证码", "123456"},
		{"全0验证码", "000000"},
		{"全9验证码", "999999"},
		{"混合验证码", "135792"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			content := service.buildVerificationEmailContent("", tc.code)

			// 验证模板内容
			assert.Contains(t, content, tc.code, "邮件内容应包含验证码")
			assert.Contains(t, content, "VLab", "邮件内容应包含品牌名")
			assert.Contains(t, content, "验证码", "邮件内容应包含验证码说明")
			assert.Contains(t, content, "5分钟", "邮件内容应包含有效期")
			assert.Contains(t, content, "<!DOCTYPE html>", "邮件内容应为HTML格式")

			// 验证HTML结构
			assert.Contains(t, content, "<html>", "应包含html标签")
			assert.Contains(t, content, "<head>", "应包含head标签")
			assert.Contains(t, content, "<body>", "应包含body标签")
			assert.Contains(t, content, "</html>", "应包含html结束标签")

			// 验证样式
			assert.Contains(t, content, "style=", "应包含样式")
			assert.Contains(t, content, "font-size", "应包含字体大小样式")
			assert.Contains(t, content, "color", "应包含颜色样式")

			t.Logf("验证码 %s 的邮件模板渲染正常", tc.code)
		})
	}
}

// TestRedisKeyExpiration 测试Redis键过期机制
func TestRedisKeyExpiration(t *testing.T) {
	suite := setupIntegrationTest(t)

	t.Run("验证码过期测试", func(t *testing.T) {
		// 手动设置一个很短的过期时间进行测试
		code := "123456"
		key := suite.emailService.getVerificationCodeKey(suite.testEmail)

		// 设置1秒过期
		err := suite.emailService.redisCli.SetEX(suite.ctx, key, code, 1*time.Second).Err()
		assert.NoError(t, err, "设置验证码应该成功")

		// 立即验证，应该成功
		storedCode, err := suite.emailService.getStoredVerificationCode(suite.ctx, suite.testEmail)
		assert.NoError(t, err, "应该能够获取验证码")
		assert.Equal(t, code, storedCode, "验证码应该匹配")

		// 等待2秒，验证码应该过期
		time.Sleep(2 * time.Second)

		_, err = suite.emailService.getStoredVerificationCode(suite.ctx, suite.testEmail)
		assert.Error(t, err, "验证码应该已过期")
	})

	t.Run("发送时间过期测试", func(t *testing.T) {
		// 手动设置发送时间
		key := suite.emailService.getSendTimeKey(suite.testEmail)
		err := suite.emailService.redisCli.SetEX(suite.ctx, key, time.Now().Unix(), 1*time.Second).Err()
		assert.NoError(t, err, "设置发送时间应该成功")

		// 立即检查，应该显示最近发送过
		isSent := suite.emailService.isCodeSentRecently(suite.ctx, suite.testEmail)
		assert.True(t, isSent, "应该显示最近发送过")

		// 等待2秒，记录应该过期
		time.Sleep(2 * time.Second)

		isSent = suite.emailService.isCodeSentRecently(suite.ctx, suite.testEmail)
		assert.False(t, isSent, "发送记录应该已过期")
	})
}

// TestErrorHandling 测试错误处理
func TestErrorHandling(t *testing.T) {
	t.Run("无效SMTP配置", func(t *testing.T) {
		// 设置无效的SMTP配置
		config.EmailCfg = &config.Email{
			Host:     "invalid.smtp.host",
			Port:     999,
			Username: "<EMAIL>",
			Password: "invalid-password",
			From:     "<EMAIL>",
			FromName: "Test VLab",
		}

		service := &EmailService{
			redisCli: redis.GetRedisClient(),
		}

		ctx, _ := gin.CreateTestContext(nil)
		req, _ := http.NewRequestWithContext(context.Background(), "POST", "/test", nil)
		ctx.Request = req

		// 尝试发送邮件，应该失败
		err := service.SendVerificationCode(ctx, "<EMAIL>")
		assert.Error(t, err, "无效配置应该导致发送失败")
	})

	t.Run("Redis连接错误处理", func(t *testing.T) {
		// 这个测试需要模拟Redis连接失败的情况
		// 在实际环境中，可以通过关闭Redis服务来测试
		t.Log("Redis连接错误测试需要手动关闭Redis服务")
	})
}

// TestPerformance 性能测试
func TestPerformance(t *testing.T) {
	suite := setupIntegrationTest(t)

	t.Run("并发验证码生成性能", func(t *testing.T) {
		const goroutines = 100
		const iterations = 10

		start := time.Now()

		// 并发生成验证码
		for i := 0; i < goroutines; i++ {
			go func() {
				for j := 0; j < iterations; j++ {
					_, _ = suite.emailService.generateVerificationCode()
				}
			}()
		}

		duration := time.Since(start)
		t.Logf("生成 %d 个验证码耗时: %v", goroutines*iterations, duration)

		// 验证性能应该在合理范围内（这里设置为1秒）
		assert.Less(t, duration, 1*time.Second, "验证码生成性能应该满足要求")
	})

	t.Run("邮件内容构建性能", func(t *testing.T) {
		const iterations = 1000

		start := time.Now()

		for i := 0; i < iterations; i++ {
			_ = suite.emailService.buildVerificationEmailContent("123456")
		}

		duration := time.Since(start)
		t.Logf("构建 %d 个邮件内容耗时: %v", iterations, duration)

		// 验证性能应该在合理范围内
		assert.Less(t, duration, 100*time.Millisecond, "邮件内容构建性能应该满足要求")
	})
}

// 示例：如何运行集成测试
/*
运行集成测试需要设置以下环境变量：

export TEST_SMTP_HOST=smtp.gmail.com
export TEST_SMTP_USERNAME=<EMAIL>
export TEST_SMTP_PASSWORD=your-app-password
export TEST_EMAIL=<EMAIL>

然后运行：
go test -tags=integration ./pkg/email/ -v

或者运行特定测试：
go test -tags=integration ./pkg/email/ -run TestRealEmailSending -v

性能测试：
go test -tags=integration ./pkg/email/ -run TestPerformance -v
*/
