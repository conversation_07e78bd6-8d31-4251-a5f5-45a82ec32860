package router

import (
	"vlab/app/api/paypal"
	"vlab/app/handler/user"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadUserApi(e *gin.RouterGroup) {
	e.GET("/encrypt/get", user.EncryptGet)
	e.GET("/decrypt/get", user.DecryptGet)
	e.POST("/decrypt/resp", user.DecryptResp)
	e.POST("/encrypt/post", user.EncryptPost)

	e.POST("/user/login/audit", mw.ReplayProtection(), mw.CheckAuditVersion(), user.UserLoginAudit)
	e.POST("/user/login/third", mw.ReplayProtection(), user.UserLoginThird)

	// 邮箱登录相关接口
	ue := e.Group("/user/email")
	{
		ue.POST("/code/send", mw.ReplayProtection(), user.EmailSendCode)           // 发送邮箱验证码
		ue.POST("/code/verify", mw.ReplayProtection(), user.EmailVerifyCode)       // 验证邮箱验证码
		ue.POST("/register", mw.ReplayProtection(), user.EmailRegister)            // 邮箱注册
		ue.POST("/login", mw.ReplayProtection(), user.EmailLogin)                  // 邮箱登录
		ue.POST("/password/reset", mw.ReplayProtection(), user.EmailResetPassword) // 重置密码
		ue.GET("/check", mw.ReplayProtection(), user.EmailCheck)                   // 检查邮箱是否已注册
	}

	// e.POST("/user/login", mw.ReplayProtection(), user.UserLogin)
	u := e.Group("/user", mw.ReplayProtection(), mw.CheckUserLogin(), mw.UserOperationLog())
	{

		u.POST("/logout", user.UserLogout)    // user/logout
		u.GET("/info", user.GetUserInfo)      // user/info
		u.POST("/edit", user.UserEdit)        // user/edit
		u.POST("/edit/pwd", user.UserEditPwd) // user/edit/pwd

		// 账号绑定相关路由
		ub := u.Group("/bind")
		{
			ub.POST("/auth", user.BindAuth) // 统一绑定认证方式（推荐使用）
		}

		// 认证方式管理路由
		ua := u.Group("/auth")
		{
			ua.GET("/methods", user.GetAuthMethods) // 获取已绑定认证方式
			ua.DELETE("/unbind", user.UnbindAuth)   // 解绑认证方式
		}

		// apple
		u.GET("/app/store/transaction", user.AppStoreTransaction) // user/app/store/transaction
		// google
		u.GET("/google/play/transaction", user.GooglePlayTransaction) // user/google/play/transaction
		// paypal / stripe
		u.POST("/third/transaction/create", user.ThirdTransactionCreate) // user/third/transaction/create
		u.POST("/third/transaction/check", user.ThirdTransactionCheck)   // user/third/transaction/check
	}

	e.GET("/device/info", mw.ReplayProtection(), mw.CheckUser(), user.GetDeviceInfo) // device/info
	ad := e.Group("/ad", mw.ReplayProtection(), mw.CheckUser())
	{
		ad.GET("/watch", user.AdWatch) // ad/watch
	}

	e.GET("/vip/product/list", mw.ReplayProtection(), mw.CheckUser(), user.VipProductList) // vip/product/list
}

func loadAdminUserApi(e *gin.RouterGroup) {
	au := e.Group("/admin/user", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		au.GET("/list", user.AdminUserList)               // admin/user/list
		au.GET("/info", user.AdminUserInfo)               // admin/user/info
		au.POST("/operate", user.AdminOperateUser)        // admin/user/operate
		au.POST("/set/viptime", user.AdminSetUserVipTime) // admin/user/set/viptime

		// au.POST("/set", user.AdminSetUser)        // admin/user/set
		// au.POST("/set/pwd", user.AdminSetUserPwd) // admin/user/set/pwd
		// au.GET("/type/list", user.AdminUserTypeList)        // admin/user/type/list
		// au.POST("/type/set", user.AdminSetUserType)         // admin/user/type/set
		// au.POST("/type/operate", user.AdminOperateUserType) // admin/user/type/operate
	}

	// aull := e.Group("/admin/user/login/log", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	// {
	// 	aull.GET("/list", user.AdminUserLoginLogList) // admin/user/login/log/list
	// }

	// 用户搜索
	// e.GET("/admin/user/search", mw.CheckAccountLogin(), user.AdminUserSearch)
	// 全部用户类型
	// e.GET("/admin/user/type/all", mw.CheckAccountLogin(), user.AdminUserTypeAll)
}

func loadPayPalApi(e *gin.RouterGroup) {
	p := e.Group("/paypal", mw.CheckAccountLogin())
	{
		p.GET("/order/info", paypal.PaypalOrderInfo) // paypal/order/info

		p.GET("/subscripe/transaction/list", paypal.PaypalSubscriptionTransactionList) // paypal/subscripe/transaction/list
		p.GET("/subscripe/info", paypal.PaypalSubscripeInfo)                           // paypal/subscripe/info

		p.GET("/product/list", paypal.PaypalProductList)      // paypal/product/list
		p.POST("/product/create", paypal.PaypalProductCreate) // paypal/product/create
		p.GET("/product/info", paypal.PaypalProductInfo)      // paypal/product/info

		p.GET("/plan/list", paypal.PaypalPlanList)      // paypal/plan/list
		p.POST("/plan/create", paypal.PaypalPlanCreate) // paypal/plan/create
		p.GET("/plan/info", paypal.PaypalPlanInfo)      // paypal/plan/info

	}
}
