package router

import (
	"vlab/app/handler/search"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

// loadSearchApi 加载搜索相关API路由
func loadSearchApi(e *gin.RouterGroup) {
	// 搜索相关接口组
	searchGroup := e.Group("/search", mw.ReplayProtection())
	{
		// 基础搜索接口
		searchGroup.POST("/", search.Search)                 // POST /search 完整搜索接口
		searchGroup.GET("/quick", search.QuickSearch)        // GET /search/quick 快速搜索
		searchGroup.POST("/vector", search.VectorSearch)     // POST /search/vector 纯向量搜索
		searchGroup.POST("/filter", search.FilterSearch)     // POST /search/filter 纯筛选搜索
		searchGroup.POST("/advanced", search.AdvancedSearch) // POST /search/advanced 高级搜索

		// 搜索建议接口
		searchGroup.GET("/suggestions", search.SearchSuggestions) // GET /search/suggestions 搜索建议

		// 推荐相关接口
		searchGroup.POST("/recommendations", search.Recommendations) // POST /search/recommendations 推荐内容
		searchGroup.GET("/similar/:id", search.SimilarShows)         // GET /search/similar/:id 相似节目推荐
		searchGroup.GET("/popular", search.PopularShows)             // GET /search/popular 热门节目
		searchGroup.GET("/category/:category", search.CategoryShows) // GET /search/category/:category 分类节目
	}

	// 公开搜索接口（不需要用户认证）
	publicSearchGroup := e.Group("/public/search", mw.ReplayProtection())
	{
		publicSearchGroup.GET("/quick", search.QuickSearch)                // GET /public/search/quick 公开快速搜索
		publicSearchGroup.GET("/suggestions", search.SearchSuggestions)    // GET /public/search/suggestions 公开搜索建议
		publicSearchGroup.GET("/popular", search.PopularShows)             // GET /public/search/popular 公开热门节目
		publicSearchGroup.GET("/category/:category", search.CategoryShows) // GET /public/search/category/:category 公开分类节目
	}

	// 需要用户认证的搜索接口
	userSearchGroup := e.Group("/user/search", mw.ReplayProtection(), mw.CheckUser())
	{
		userSearchGroup.POST("/", search.Search)                         // POST /user/search 用户搜索（带个性化）
		userSearchGroup.POST("/recommendations", search.Recommendations) // POST /user/search/recommendations 个性化推荐
		userSearchGroup.GET("/similar/:id", search.SimilarShows)         // GET /user/search/similar/:id 基于用户的相似推荐
	}
}
