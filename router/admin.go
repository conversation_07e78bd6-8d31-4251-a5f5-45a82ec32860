package router

import (
	"vlab/app/api/aliyun"
	"vlab/app/api/bytes"
	"vlab/app/api/googleplay"

	"vlab/app/handler/admin"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

/*
* 纯后台管理路由,与业务无关
* 业务后台的路由建议写到对应的业务路由文件中
 */
func loadAdminApi(e *gin.RouterGroup) {
	e.POST("/admin/account/login", admin.AdminAccountLogin)
	e.POST("/admin/account/logout", mw.CheckAccountLogin(), admin.AdminAccountLogout)
	e.GET("/admin/account/basic", mw.CheckAccountLogin(), admin.AdminAccountInfo)
	e.GET("/admin/judge", mw.CheckAccountLogin(), admin.AdminJudge)
	e.POST("/admin/account/edit/pwd", mw.CheckAccountLogin(), admin.AdminAccountEditPwd)

	e.GET("/admin/role/all", mw.CheckAccountLogin(), admin.AdminRoleAll)
	e.GET("/admin/account/all", mw.CheckAccountLogin(), admin.AdminAccountAll)
	e.GET("/admin/menu/option", mw.CheckAccountLogin(), admin.AdminMenuOption)
	e.GET("/admin/menu/router", mw.CheckAccountLogin(), admin.AdminMenuRouter)
	e.GET("/admin/area/list", mw.CheckAccountLogin(), admin.AdminAreaList)

	aRole := e.Group("/admin/role", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aRole.GET("/list", admin.AdminRoleList)         // admin/role/list
		aRole.POST("/set", admin.AdminSetRole)          // admin/role/set
		aRole.POST("/set/auth", admin.AdminSetRoleAuth) // admin/role/set/auth
		aRole.POST("/del", admin.AdminDelRole)          // admin/role/del

	}

	aAcount := e.Group("/admin/account", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aAcount.GET("/list", admin.AdminAccountList)       // admin/account/list
		aAcount.POST("/set", admin.AdminSetAccount)        // admin/account/set
		aAcount.POST("/set/pwd", admin.AdminSetAccountPwd) // admin/account/set/pwd
		aAcount.POST("/del", admin.AdminDelAccount)        // admin/account/set

	}

	aMenu := e.Group("/admin/menu", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aMenu.GET("/list", admin.AdminMenuList) // admin/menu/list
		aMenu.POST("/set", admin.AdminSetMenu)  // admin/menu/set
		aMenu.POST("/del", admin.AdminDelMenu)  // admin/menu/del
		aMenu.GET("/init", admin.AdminMenuInit) // admin/menu/init
	}

	bp := e.Group("/byteplus", mw.CheckUserLogin())
	{
		bp.GET("/oss/token", bytes.BytePlusOssToken) // /byteplus/oss/token
	}
	aBP := e.Group("/admin/byteplus", mw.CheckAccountLogin())
	{
		aBP.GET("/oss/token", bytes.BytePlusOssToken) // /admin/byteplus/oss/token
	}

	ali := e.Group("/aliyun", mw.CheckUserLogin())
	{
		ali.GET("/oss/token", aliyun.AliyunOssToken)
	}
	aAliyun := e.Group("/admin/aliyun", mw.CheckAccountLogin())
	{
		aAliyun.GET("/oss/token", aliyun.AliyunOssToken)
		// vod
		aAliyun.POST("/vod/upload/info", aliyun.VodUploadInfo)           // admin/aliyun/vod/upload/info
		aAliyun.POST("/vod/upload/by/url", aliyun.UploadMediaByUrl)      // admin/aliyun/vod/upload/by/url
		aAliyun.POST("/vod/upload/url/infos", aliyun.GetURLUploadInfos)  // admin/aliyun/vod/upload/url/infos
		aAliyun.POST("/vod/get/mezzanine/info", aliyun.GetMezzanineInfo) // admin/aliyun/vod/get/mezzanine/info

		// TODO 移除以下 - 阿里云
		aAliyun.GET("/oss/play/url", aliyun.AliyunOssGenSignedUrl)
		aAliyun.GET("/mps/submit/transcode/job", aliyun.SubmitTranscodeJob)
		// TODO 移除以下 - 系统
		aAliyun.GET("/mps/submit/transcode/job/chan", aliyun.SubmitTranscodeJobChan)
		aAliyun.GET("/oss/transcode/job/signed/url", aliyun.GetTranscodeJobSignedUrl)
		aAliyun.GET("/mps/resubmit/transcode/failed/job", aliyun.ResubmitTranscodeFailedJob)
	}

	aByte := e.Group("/admin/bytes", mw.CheckAccountLogin())
	{
		aByte.GET("/tos/token", bytes.ByteTosToken)
		// vod
		aByte.POST("/vod/upload/by/url", bytes.UploadMediaByUrl)             // admin/bytes/vod/upload/by/url
		aByte.POST("/vod/query/upload/task/info", bytes.QueryUploadTaskInfo) // admin/bytes/vod/query/upload/task/info
		aByte.POST("/vod/get/play/info", bytes.GetPlayInfo)                  // admin/bytes/vod/get/play/info

		aByte.POST("/vod/get/media/info", bytes.GetMediaInfo) // admin/bytes/vod/get/media/info
	}

	aGoogle := e.Group("/admin/google", mw.CheckAccountLogin(), mw.ReplayProtectionBackup())
	{
		// 一次性购买
		aGoogle.GET("/purchase/product/info", googleplay.PurchaseProductInfo)        // admin/google/purchase/product/info
		aGoogle.GET("/purchase/product/ack", googleplay.PurchasedProductAcknowledge) // admin/google/purchase/product/ack
		aGoogle.GET("/purchase/product/consume", googleplay.PurchasedProductConsume) // admin/google/purchase/product/consume

		// 订阅
		aGoogle.GET("/purchase/subscription/info", googleplay.PurchaseSubscriptionInfo)       // admin/google/purchase/subscription/info
		aGoogle.GET("/purchase/subscription/ack", googleplay.PurchaseSubscriptionAcknowledge) // admin/google/purchase/subscription/ack

		// 订单
		aGoogle.GET("/order/info", googleplay.OrderInfo) // admin/google/order/info
	}

	// IP黑名单管理
	aIPBlacklist := e.Group("/admin/ip-blacklist", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		aIPBlacklist.GET("/list", admin.AdminIPBlacklistList)      // admin/ip-blacklist/list
		aIPBlacklist.GET("/detail", admin.AdminIPBlacklistDetail)  // admin/ip-blacklist/detail
		aIPBlacklist.POST("/create", admin.AdminIPBlacklistCreate) // admin/ip-blacklist/create
		aIPBlacklist.POST("/update", admin.AdminIPBlacklistUpdate) // admin/ip-blacklist/update
		aIPBlacklist.POST("/delete", admin.AdminIPBlacklistDelete) // admin/ip-blacklist/delete
		aIPBlacklist.GET("/test", admin.AdminIPBlacklistTest)      // admin/ip-blacklist/test
	}
}
