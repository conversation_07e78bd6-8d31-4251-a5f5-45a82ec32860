package router

import (
	"vlab/app/handler/user"
	mw "vlab/app/middleware"
	"vlab/app/script"

	"github.com/gin-gonic/gin"
)

func loadScriptApi(e *gin.RouterGroup) {
	su := e.Group("/script/user", mw.CheckAccountLogin(), mw.ReplayProtectionBackup(), mw.CheckScriptAuth())
	{
		su.POST("/login", user.ScriptUserLogin)
		su.POST("/flush/uuid", script.FlushUserUUID)
	}

	svm := e.Group("/script/video/mps", mw.CheckAccountLogin(), mw.CheckScriptAuth())
	{
		svm.GET("/flush/aliyun/to/bytes", script.FlushVideoMapAliyunToBytes)            // script/video/mps/flush/aliyun/to/bytes
		svm.GET("/flush/aliyun/to/bytes/retry", script.FlushVideoMapAliyunToBytesRetry) // script/video/mps/flush/aliyun/to/bytes/retry

		svm.GET("/flush/bytes/runtime", script.FlushVideoMapBytesRuntime) // script/video/mps/flush/bytes/runtime
		svm.GET("/flush/bytes/cp", script.FlushVideoMapBytesCp)           // script/video/mps/flush/bytes/cp
	}
}
