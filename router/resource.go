package router

import (
	"vlab/app/handler/resource"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadResourceApi(e *gin.RouterGroup) {
	e.GET("/config", mw.ReplayProtection(), resource.GetConfig)           // 获取配置
	e.GET("/version/check", mw.ReplayProtection(), resource.VersionCheck) // version/check 版本检查

	e.POST("/error/reporte", mw.ReplayProtection(), mw.CheckUser(), resource.ErrorReport)
}

func loadAdminResourceApi(e *gin.RouterGroup) {
	e.GET("/admin/channel/all", mw.CheckAccountLogin(), resource.AdminChannelAll)
	// e.GET("/admin/channel/key/all", mw.CheckAccountLogin(), resource.AdminChannelKeyAll)
	e.GET("/admin/version/all", mw.CheckAccountLogin(), resource.AdminVersionAll)

	c := e.Group("/admin/channel", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		c.GET("/list", resource.AdminChannelList)        // admin/channel/list
		c.POST("/set", resource.AdminChannelSet)         // admin/channel/set
		c.POST("/operate", resource.AdminChannelOperate) // admin/channel/operate
	}
	// ck := e.Group("/admin/channel/key", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	// {
	// 	ck.GET("/list", resource.AdminChannelKeyList)        // admin/channel/key/list
	// 	ck.POST("/set", resource.AdminChannelKeySet)         // admin/channel/key/set
	// 	ck.POST("/operate", resource.AdminChannelKeyOperate) // admin/channel/key/operate
	// }
	v := e.Group("/admin/version", mw.CheckAccountLogin(), mw.CheckAccountAuth(), mw.AdminOperationLog())
	{
		v.GET("/list", resource.AdminVersionList) // admin/version/list
		v.POST("/set", resource.AdminVersionSet)  // admin/version/set
		// v.POST("/operate", resource.AdminVersionOperate) // admin/version/operate
	}
}
