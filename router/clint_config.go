package router

import (
	"github.com/gin-gonic/gin"
	"vlab/app/handler/client_config"
	mw "vlab/app/middleware"
)

func loadClientConfigApi(e *gin.RouterGroup) {
	// 客户端配置接口需要通过 ReplayProtection 中间件来解析 X-Channel header
	cf := e.Group("/abtest/config", mw.ReplayProtection())
	{
		cf.GET("", client_config.ClientConfig) // /config/page
	}
}

func loadAdminClientConfigApi(e *gin.RouterGroup) {

	cf := e.Group("/admin/abtest/config")
	{
		cf.GET("", client_config.AdminClientConfig)               // /admin/config/page
		cf.POST("", client_config.AdminUpdateClientConfig)        // /admin/config/page
		cf.POST("/delete", client_config.AdminDeleteClientConfig) // /admin/config/delete
	}
}
