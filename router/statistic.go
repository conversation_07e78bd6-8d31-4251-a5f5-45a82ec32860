package router

import (
	"vlab/app/handler/statistic"
	mw "vlab/app/middleware"

	"github.com/gin-gonic/gin"
)

func loadAdminStatisticApi(e *gin.RouterGroup) {

	// 统计相关接口
	ss := e.Group("/admin/statistic", mw.CheckAccountLogin())
	{
		ss.GET("/show/daily/active", statistic.AdminShowDailyActive)            // admin/statistic/show/daily/active
		ss.GET("/show/daily/play/num", statistic.AdminShowDailyPlayNum)         // admin/statistic/show/daily/playnum
		ss.GET("/show/play/num/top", statistic.AdminShowPlayNumTop)             // admin/statistic/show/playnum/top
		ss.GET("/show/daily/download/num", statistic.AdminShowDailyDownloadNum) // admin/statistic/show/daily/download
		ss.GET("/show/download/num/top", statistic.AdminShowDownloadNumTop)     // admin/statistic/show/download/top
	}

}
