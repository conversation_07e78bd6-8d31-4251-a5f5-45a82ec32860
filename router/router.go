package router

import (
	"os"
	"time"

	"vlab/app/middleware"
	"vlab/config"

	"github.com/gin-contrib/pprof"
	"github.com/gin-gonic/gin"
)

type routerFun func(*gin.RouterGroup)

var routerList = []routerFun{}

var (
	buildTime string
	gitCommit string
)

func init() {
	regRouter(
		loadAdminApi,
		loadResourceApi, loadAdminResourceApi,
		loadUserApi, loadAdminUserApi,
		loadAdminStatisticApi,
		loadVersionApi,
		loadProfileApi,

		loadShowApi, loadAdminShowApi, loadAdminClassApi,
		loadSearchApi, // 添加搜索API路由
		loadScriptApi,
		loadAdminDataApi, loadDataApi,
		loadExternalApi, loadPayPalApi,
		loadClientConfigApi, loadAdminClientConfigApi, // 添加客户端配置API路由
	)
}

func regRouter(routerFun ...routerFun) {
	routerList = append(routerList, routerFun...)
}

func LoadRouter(r *gin.Engine) *gin.RouterGroup {
	r.Use(middleware.CommonMiddleware())
	ginRouter := r.Group(config.AppCfg.AppName)
	ginRouter.Use(middleware.RecoveryWithWriter())

	for _, routerF := range routerList {
		routerF(ginRouter)
	}

	return ginRouter
}

func loadProfileApi(e *gin.RouterGroup) {
	pprof.Register(e, "/admin/pprof")
}

// getBuildInfo 获取构建信息，支持运行时环境变量覆盖
func getBuildInfo() (string, string) {
	// 优先使用环境变量，如果没有则使用构建时注入的值
	envBuildTime := os.Getenv("BUILD_TIME")
	envGitCommit := os.Getenv("GIT_COMMIT")

	finalBuildTime := buildTime
	finalGitCommit := gitCommit

	if envBuildTime != "" {
		finalBuildTime = envBuildTime
	}
	if envGitCommit != "" {
		finalGitCommit = envGitCommit
	}

	// 如果都为空，设置默认值
	if finalBuildTime == "" {
		finalBuildTime = "unknown"
	}
	if finalGitCommit == "" {
		finalGitCommit = "unknown"
	}

	return finalBuildTime, finalGitCommit
}

func loadVersionApi(e *gin.RouterGroup) {
	e.GET("/version", func(c *gin.Context) {
		buildTime, gitCommit := getBuildInfo()
		c.JSON(200, gin.H{
			"buildTime": buildTime,
			"gitCommit": gitCommit,
		})
	})

	// 健康检查端点
	e.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "ok",
			"timestamp": time.Now().Unix(),
		})
	})
}
