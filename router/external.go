package router

import (
	"vlab/app/api/appstore"
	"vlab/app/api/googleplay"
	"vlab/app/api/paypal"
	"vlab/app/api/stripe"

	"github.com/gin-gonic/gin"
)

func loadExternalApi(e *gin.RouterGroup) {
	e.POST("/external/app/store/notify", appstore.AppStoreNotify)       // external/app/store/notify
	e.POST("/external/google/play/notify", googleplay.GooglePlayNotify) // external/google/play/notify

	e.POST("/external/vi/app/store/notify", appstore.AppStoreNotifyVi)       // external/vi/app/store/notify
	e.POST("/external/vi/google/play/notify", googleplay.GooglePlayNotifyVi) // external/vi/google/play/notify

	e.POST("/external/hitv/google/play/notify", googleplay.GooglePlayNotifyHitv) // external/hitv/google/play/notify

	e.POST("/external/paypal/notify", paypal.PaypalNotify) // external/paypal/notify
	e.POST("/external/stripe/notify", stripe.StripeNotify) // external/stripe/notify
}
