version: '3.8'

services:
  # VLab 主服务
  vlab:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: vlab-app
    restart: unless-stopped
    ports:
      - "8081:8081"  # 映射应用端口
    environment:
      # 配置文件路径环境变量，从系统环境变量读取
      - config=${config}
      # 版本信息环境变量（可选，用于运行时覆盖构建时注入的值）
      - BUILD_TIME=${BUILD_TIME:-}
      - GIT_COMMIT=${GIT_COMMIT:-}
    volumes:
      # 挂载配置文件目录
      - ./docker/config:/data/tsf/cmd/config:ro
      # 挂载日志目录（可选）
      - ./docker/logs:/data/tsf/cmd/data/logs
    networks:
      - vlab-network
    healthcheck:
      # 使用新添加的健康检查端点，更轻量级
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/vlab/health"]
      # 或者使用原有的version端点：
      # test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/vlab/version"] 
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  vlab-network:
    driver: bridge
    name: vlab-net

volumes:
  # 日志数据卷
  vlab-logs:
    driver: local 