# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

VLab 是一个基于 Go 的流媒体平台后端服务，采用 Gin 框架构建，提供视频内容管理、用户认证、支付集成等功能。

## 目录结构说明

- **./app/api目录**: 作为调用第三方服务的目录
  - `aliyun/`: 阿里云服务集成（OSS、MPS、VOD）
  - `bytes/`: 字节跳动服务集成（TOS、VOD）
  - `appstore/`: App Store 支付验证
  - `googleplay/`: Google Play 支付验证
  - `paypal/`: PayPal 支付集成
  - `stripe/`: Stripe 支付集成
  - `tmdb/`: TMDB 电影数据库API
  - `vikingdb/`: VikingDB 向量数据库集成
- **./cmd目录**: 命令行工具和脚本
  - `vector_uploader/`: 向量数据上传工具
  - `export_shows_i18n/`: 多语言数据导出工具
- **./docs目录**: 技术文档
  - 包含认证绑定、邮箱登录、重构总结等技术文档

## 常用命令

### 构建和运行
```bash
# 构建项目（自动检测内存并设置 GOGC）
make build

# 启动服务（包含构建）
make start

# 安全重启（零停机时间）
make restart

# 停止服务
make stop

# 清理构建产物和日志
make clean

# 查看版本信息
make version

# 直接运行（开发模式）
go run main.go
```

### 测试
```bash
# 运行所有测试
go test ./...

# 运行特定包的测试
go test -v ./app/service/user/...

# 运行单个测试文件
go test -v ./pkg/email/email_test.go
```

### Docker
```bash
# 构建 Docker 镜像
docker build -t vlab .

# 使用 Docker Compose 启动
docker-compose up -d
```

## 架构说明

### 分层架构
- **handler 层** (`app/handler/`): HTTP 请求处理，参数验证，响应格式化
- **service 层** (`app/service/`): 业务逻辑实现，事务处理
- **dao 层** (`app/dao/`): 数据库访问，SQL 查询
- **dto 层** (`app/dto/`): 数据传输对象定义

### 核心组件
- **路由系统** (`router/`): 模块化路由定义
  - `admin.go`: 管理后台路由
  - `user.go`: 用户相关路由
  - `show.go`: 内容展示路由
  - `resource.go`: 资源管理路由
  - `search.go`: 搜索功能路由
  - `clint_config.go`: 客户端配置路由
  - `external.go`: 外部API路由
  - `script.go`: 脚本工具路由
  - `data.go`: 数据导出路由
- **中间件** (`app/middleware/`): JWT 认证、错误恢复、请求日志
- **外部 API** (`app/api/`): 第三方服务集成（支付、云服务、视频处理）
- **定时任务** (`app/job/`): 使用 cron 的后台任务
- **消息消费者** (`app/consumer/`): Kafka 消息处理

### 数据库设计
- 使用 MySQL，支持读写分离
- 通过 `app/common/db/` 管理数据库连接
- 遵循 Repository 模式，所有 SQL 操作在 dao 层

### 配置管理
- 配置文件位于 `config/` 目录（INI 格式）
- 环境配置：`local.ini`（开发）、`test.ini`（测试）、`prd.ini`（生产）
- 通过环境变量 `config` 指定配置文件路径

### 错误处理
- 统一错误码定义在 `pkg/ecode/`
- 使用 `pkg/ecode.Code` 类型返回业务错误
- HTTP 响应格式：`{"code": 0, "message": "success", "data": {}}`

### 数据处理规范
- **批量获取ID规范**：在 DAO 层的 model 文件中，为列表类型（如 `PopularList`、`AssignList` 等）实现类似 `GetIDs()`、`GetChannelIDs()` 等方法
- **避免在 Service 层直接使用 for 循环提取 ID**，应该在 model 层封装相应的方法
- **数据转换规范**：将 DAO 层数据转换为 DTO 层数据时，应该使用辅助函数，避免在业务逻辑中直接 for 循环转换
- **安全的 Map 访问**：从 map 中获取数据时，应该使用安全的辅助函数，避免直接使用 `map[key]` 可能导致的空指针问题
- 示例：
  ```go
  // 推荐做法 - 在 model.go 中
  func (pl PopularList) GetChannelIDs() []uint64 {
      var ids []uint64
      for _, v := range pl {
          if v.ChannelID > 0 {
              ids = append(ids, v.ChannelID)
          }
      }
      return ids
  }
  
  // 推荐做法 - 在 service 层使用辅助函数
  func ConvertChannelListToMap(channelList channelDao.ModelList) map[uint64]*showDto.ChannelBase {
      channelMap := make(map[uint64]*showDto.ChannelBase, len(channelList))
      for _, channel := range channelList {
          channelMap[channel.ID] = &showDto.ChannelBase{
              ID:   channel.ID,
              Name: channel.Name,
          }
      }
      return channelMap
  }
  
  // 然后在 service 层使用
  channelIDs := list.GetChannelIDs()
  if len(channelIDs) > 0 {
      channelList, err := e.ChannelRepo.FindByFilter(ctx, &channelDao.Filter{
          IDS: channelIDs,
      })
      if err == nil && len(channelList) > 0 {
          channelMap = ConvertChannelListToMap(channelList)
      }
  }
  
  // 不推荐做法 - 直接在 service 层 for 循环
  for _, item := range list {
      if item.ChannelID > 0 {
          channelIDs = append(channelIDs, item.ChannelID)
      }
  }
  
  // 不推荐做法 - 直接在业务逻辑中转换数据
  for _, channel := range channelList {
      channelMap[channel.ID] = &showDto.ChannelBase{
          ID:   channel.ID,
          Name: channel.Name,
      }
  }
  
  // 推荐做法 - 使用安全的 map 访问函数
  func GetChannelFromMap(channelMap map[uint64]*showDto.ChannelBase, channelID uint64) *showDto.ChannelBase {
      if channelMap == nil || channelID == 0 {
          return nil
      }
      return channelMap[channelID]
  }
  
  // 使用示例
  item.Channel = GetChannelFromMap(channelMap, v.ChannelID)
  
  // 不推荐做法 - 直接访问 map
  item.Channel = channelMap[v.ChannelID]  // 可能返回 nil

### 认证系统
- JWT token 认证，中间件在 `app/middleware/auth.go`
- 设备管理：支持多设备登录控制
- 权限控制：基于角色的访问控制（RBAC）
- **多认证方式绑定**：支持苹果、谷歌、邮箱多种认证方式绑定到同一账号
  - 相关代码：`app/service/user/auth_bind.go`
  - 数据表：`user_auth` 存储用户认证信息
  - API：提供认证方式列表、绑定、解绑等功能

### 集成服务
- **缓存**: Redis（`pkg/redis/`）
- **搜索**: Elasticsearch（`pkg/es/`）
- **消息队列**: Kafka（`pkg/mq/`）
- **对象存储**: 阿里云 OSS、字节火山引擎 TOS
- **视频处理**: 阿里云 MPS、字节 VOD
- **支付**: PayPal、Stripe、Google Play、App Store
- **向量数据库**: VikingDB（智能搜索和推荐系统）

### 向量搜索系统
- **位置**: `app/dao/search_dao.go` - 向量搜索数据访问层
- **功能**: 支持向量搜索、筛选搜索、混合搜索三种模式
- **分页优化**: 已优化分页逻辑，支持大数据量的高效分页查询
- **性能特点**: 
  - 智能计算数据获取量，避免过度获取
  - 支持最大1000条记录的分页查询
  - 在搜索层完成分页，避免内存切片操作
- **使用方式**: 通过 `SearchService` 调用，支持缓存机制
- **搜索历史**: 支持搜索历史记录功能
  - 相关代码：`app/dao/content_show/model_search_history.go`
  - 测试文件：`app/service/show/search_history_test.go`

### 日志系统
- 结构化日志，按日期轮转
- 日志配置在 `app/common/service/init.go`
- 支持异步写入提高性能

### 性能优化
- 低内存时自动设置 GOGC=5（Makefile 中实现）
- 支持 Pyroscope 性能分析
- Redis 连接池配置优化

## 开发注意事项

1. **新增 API 时**：
   - 在对应模块的 router 文件中添加路由
   - handler 层处理请求验证
   - service 层实现业务逻辑
   - dao 层处理数据库操作

2. **数据库操作**：
   - 使用 GORM 进行 ORM 操作
   - 复杂查询使用原生 SQL
   - 注意使用事务处理关联操作

3. **错误处理**：
   - 使用 `pkg/ecode` 定义的错误码
   - 避免直接返回数据库错误给客户端

4. **测试**：
   - 测试文件与源文件同目录
   - 使用 testify 断言库
   - mock Redis 使用 redismock

5. **并发安全**：
   - service 层方法应该是无状态的
   - 使用 context 传递请求级别的数据
   - 注意 goroutine 的正确使用和资源清理

6. **向量数据库集成**：
   - 使用 `cmd/vector_uploader/` 工具上传数据
   - 支持同步和并发上传模式
   - 配置文件支持多环境

7. **搜索功能开发**：
   - 向量搜索相关代码在 `app/dao/search_dao.go`
   - 搜索服务层在 `app/service/search/`
   - 已修复分页问题，支持高效的大数据量分页
   - 测试文件：`app/dao/search_dao_pagination_test.go`

8. **国际化**：
   - i18n 相关功能在 `app/service/i18n/`
   - 使用 `cmd/export_shows_i18n/` 导出多语言数据

9. **客户端配置系统**：
   - 支持渠道特定配置：每个 channel_id 可设置不同配置
   - 配置继承机制：渠道配置优先，未配置时回退到全局配置
   - API 文档：参考 `CHANNEL_CONFIG_USAGE.md`

## 相关技术文档

### 搜索功能文档
- **搜索API指南**: `SEARCH_API_GUIDE.md` - 完整的搜索接口文档
- **集成搜索指南**: `INTEGRATED_SEARCH_GUIDE.md` - 向量搜索集成使用指南
- **分页修复文档**: `docs/search_pagination_fix.md` - 分页功能修复的详细技术文档

### 向量数据库相关
- **向量上传工具**: `cmd/vector_uploader/` 目录下的相关文档
- **标量字段说明**: `cmd/export_shows_i18n/SCALAR_FIELDS.md`

### 认证系统文档
- **认证绑定指南**: `docs/auth_bind.md` - 多认证方式绑定系统设计文档
- **认证绑定示例**: `docs/auth_bind_examples.md` - 具体使用示例
- **邮箱登录**: `docs/email_login.md` - 邮箱登录功能文档
- **统一绑定API**: `docs/unified_bind_api.md` - 统一绑定接口文档

### 项目管理文档
- **重构总结**: `docs/refactoring_summary.md` - 项目重构总结
- **验证报告**: `docs/refactoring_validation_report.md` - 重构验证报告
- **实现总结**: `docs/implementation_summary.md` - 功能实现总结

### 配置系统文档
- **渠道配置使用指南**: `CHANNEL_CONFIG_USAGE.md` - 详细的渠道配置功能说明

### 技术修复文档
- **VikingDB连接修复**: `docs/vikingdb_connection_fix.md` - 向量数据库连接问题修复