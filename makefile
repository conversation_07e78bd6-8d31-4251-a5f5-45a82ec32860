SHELL:=/bin/sh
.PHONY: build start stop restart clean version

# 服务名
SERVICE_NAME := be_vlab
# 二进制文件名
BINARY_NAME := $(SERVICE_NAME)
# 临时二进制文件名（用于安全重启）
TEMP_BINARY_NAME := $(SERVICE_NAME)_new
# 主程序文件
MAIN_FILE := main.go
# nohup运行日志文件
LOG_FILE := $(SERVICE_NAME)_$$(date +%m%d%H%M%S).log
# PID文件
PID_FILE := $(SERVICE_NAME).pid
# 健康检查URL
# HEALTH_CHECK_URL := http://localhost:8081/vlab/version

# 检查可用内存并设置GOGC
MEM_AVAILABLE := $(shell free -g | awk '/^Mem:/ {print $$7}')

ifeq ($(shell [ $(MEM_AVAILABLE) -lt 2 ] && echo true), true)
    GOGC = 5
else
    GOGC =
endif

version:
	@echo "VERSION_INFO: $$(date +%Y-%m-%dT%H:%M:%S%z)"

# 构建二进制文件
build: version
	@echo "Building $(SERVICE_NAME)..."
	@echo "mem available: $(MEM_AVAILABLE)G"
	@echo "GOGC=$(GOGC)"
	@VERSION_INFO=$$(date +%Y-%m-%dT%H:%M:%S%z) && GIT_COMMIT=$$(git rev-parse --short HEAD) && \
	if [ -n "$(GOGC)" ]; then \
		GOGC=$(GOGC) go build -ldflags "-X vlab/router.buildTime=$$VERSION_INFO -X vlab/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	else \
		go build -ldflags "-X vlab/router.buildTime=$$VERSION_INFO -X vlab/router.gitCommit=$$GIT_COMMIT" -o $(BINARY_NAME) $(MAIN_FILE); \
	fi

# 启动服务
start: build
	@echo "Starting $(SERVICE_NAME)..."
	@nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(PID_FILE)
	@echo "$(SERVICE_NAME) started with PID $$(cat $(PID_FILE))"

# @sleep 20
# @if ! curl -sSf $(HEALTH_CHECK_URL) > /dev/null; then \
# 	echo "$(SERVICE_NAME) health check failed. Stopping service..."; \
# 	$(MAKE) stop; \
# 	exit 1; \
# else \
# 	echo "$(SERVICE_NAME) is active."; \
# fi

# 停止服务
stop:
	@echo "Stopping $(SERVICE_NAME)..."
	@if [ -f $(PID_FILE) ]; then \
		kill -9 `cat $(PID_FILE)`; \
		rm $(PID_FILE); \
		echo "$(SERVICE_NAME) stopped."; \
	else \
		echo "$(SERVICE_NAME) is not running."; \
	fi

# 安全重启服务
restart:
	@echo "=== Safe Restart Started ==="
	
	# 构建新版本
	@echo "Building new version..."
	@VERSION_INFO=$$(date +%Y-%m-%dT%H:%M:%S%z) && GIT_COMMIT=$$(git rev-parse --short HEAD) && \
	if [ -n "$(GOGC)" ]; then \
		GOGC=$(GOGC) go build -ldflags "-X vlab/router.buildTime=$$VERSION_INFO -X vlab/router.gitCommit=$$GIT_COMMIT" -o $(TEMP_BINARY_NAME) $(MAIN_FILE); \
	else \
		go build -ldflags "-X vlab/router.buildTime=$$VERSION_INFO -X vlab/router.gitCommit=$$GIT_COMMIT" -o $(TEMP_BINARY_NAME) $(MAIN_FILE); \
	fi
	
	# 检查构建结果
	@if [ ! -f $(TEMP_BINARY_NAME) ] || [ ! -x $(TEMP_BINARY_NAME) ]; then \
		echo "❌ Build failed or binary not executable!"; \
		rm -f $(TEMP_BINARY_NAME); \
		exit 1; \
	fi
	@echo "✅ Build successful"
	
	# 停止现有服务
	@echo "Stopping current service..."
	@$(MAKE) stop
	
	# 部署新版本
	@echo "Deploying new version..."
	@mv $(TEMP_BINARY_NAME) $(BINARY_NAME)
	
	# 启动新服务
	@echo "Starting new service..."
	@nohup ./$(BINARY_NAME) > $(LOG_FILE) 2>&1 & echo $$! > $(PID_FILE)
	
	# 简单验证
	@sleep 2
	@if [ -f $(PID_FILE) ] && kill -0 `cat $(PID_FILE)` 2>/dev/null; then \
		echo "🎉 Restart completed! PID: $$(cat $(PID_FILE))"; \
	else \
		echo "❌ Service failed to start! Check log: $(LOG_FILE)"; \
		exit 1; \
	fi
	@echo "=== Safe Restart Completed ==="

clean:
	@pwd
	@rm -rf ./data/logs
	@rm -f $(BINARY_NAME) $(TEMP_BINARY_NAME) $(LOG_FILE) $(PID_FILE)
	@echo "Cleanup done."




