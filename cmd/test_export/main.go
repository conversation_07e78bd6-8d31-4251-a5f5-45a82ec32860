package main

import (
	"context"
	"flag"
	"fmt"
	"log"

	"vlab/app/common/dbs"
	showDto "vlab/app/dto/show"
	showService "vlab/app/service/show"
	"vlab/config"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
)

// 测试show多语言导出功能的简单脚本
func main() {
	// 命令行参数
	var (
		configPath = flag.String("config", "", "配置文件路径 (如: config/dev.ini)")
	)
	flag.Parse()

	fmt.Println("=== 测试Show多语言导出功能 ===")
	if *configPath != "" {
		fmt.Printf("配置文件: %s\n", *configPath)
	} else {
		fmt.Println("配置文件: 默认配置")
	}

	// 初始化配置
	if *configPath != "" {
		config.SetupWithPath(*configPath)
	} else {
		config.Setup()
	}
	dbs.Setup()
	redis.Setup()

	// 获取数据库连接
	mysqlEngine := dbs.NewMysqlEngines()
	if mysqlEngine == nil {
		log.Fatalf("Failed to initialize MySQL")
	}

	// 创建show service
	showSvc := showService.GetService()

	// 创建gin context
	ctx := &gin.Context{}
	ctx.Set("context", context.Background())

	fmt.Println("1. 测试内存导出（前10条记录）...")

	// 测试内存导出
	req := &showDto.ShowI18nExportReq{
		Status:       nil,   // 导出所有状态的记录
		IncludeEmpty: false, // 不包含空翻译值
	}

	resp, err := showSvc.ExportShowsWithI18n(ctx, req)
	if err != nil {
		log.Fatalf("内存导出失败: %v", err)
	}

	fmt.Printf("✅ 内存导出成功，共 %d 条记录\n", resp.Total)

	// 显示前几条记录作为示例
	displayCount := min(3, len(resp.List))

	for i := 0; i < displayCount; i++ {
		item := resp.List[i]
		fmt.Printf("\n记录 %d:\n", i+1)
		fmt.Printf("  ID: %d\n", item.ID)
		fmt.Printf("  Name: %s\n", item.Name)
		fmt.Printf("  Name I18n: %v\n", item.NameI18n)
		fmt.Printf("  Overview: %s\n", truncateString(item.Overview, 50))
		fmt.Printf("  Overview I18n: %v\n", item.OverviewI18n)
	}

	fmt.Println("\n2. 测试文件导出（小批次）...")

	// 测试文件导出
	config := &showDto.ShowI18nExportConfig{
		BatchSize:  10, // 小批次测试
		OutputPath: "test_export.json",
		ProgressFunc: func(current, total int64, message string) {
			if total > 0 {
				progress := float64(current) / float64(total) * 100
				fmt.Printf("进度: %.1f%% (%d/%d) - %s\n", progress, current, total, message)
			} else {
				fmt.Printf("%s\n", message)
			}
		},
	}

	err = showSvc.ExportShowsWithI18nToFile(ctx, config)
	if err != nil {
		log.Fatalf("文件导出失败: %v", err)
	}

	fmt.Println("✅ 文件导出测试完成")
	fmt.Println("\n🎉 所有测试通过！")
}

// 截断字符串用于显示
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
