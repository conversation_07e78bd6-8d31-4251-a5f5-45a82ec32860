package main

import (
	"context"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	"log"
	"math"
	"time"

	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
	"vlab/config"
)

// EmbeddingClient VikingDB Embedding 客户端
type EmbeddingClient struct {
	config  *config.VikingDB
	service *vikingdb.VikingDBService
}

// NewEmbeddingClient 创建新的 Embedding 客户端
func NewEmbeddingClient(cfg *config.VikingDB) *EmbeddingClient {
	// 创建 VikingDB 服务实例
	service := vikingdb.NewVikingDBService(
		cfg.Host,
		cfg.Region,
		cfg.AccessKeyID,
		cfg.AccessKeySecret,
		cfg.Scheme,
	)

	return &EmbeddingClient{
		config:  cfg,
		service: service,
	}
}

// GetEmbeddings 获取文本的向量表示
func (c *EmbeddingClient) GetEmbeddings(ctx context.Context, texts []string) ([][]float32, error) {
	results, err := c.GetEmbeddingsWithDetails(ctx, texts)
	if err != nil {
		return nil, err
	}

	// 仅返回稠密向量
	embeddings := make([][]float32, len(results))
	for i, result := range results {
		embeddings[i] = result.DenseVector
	}

	return embeddings, nil
}

// EmbeddingResult 向量化结果
type EmbeddingResult struct {
	DenseVector  []float32         // 稠密向量
	SparseVector map[int64]float64 // 稀疏向量
}

// GetEmbeddingsWithDetails 获取文本的详细向量表示（包括稠密和稀疏向量）
func (c *EmbeddingClient) GetEmbeddingsWithDetails(ctx context.Context, texts []string) ([]*EmbeddingResult, error) {
	if len(texts) == 0 {
		return nil, fmt.Errorf("输入文本不能为空")
	}

	// 构建请求数据
	rawDataList := make([]vikingdb.RawData, len(texts))
	for i, text := range texts {
		rawDataList[i] = vikingdb.RawData{
			DataType: "text",
			Text:     text,
		}
	}

	// 设置模型参数
	params := map[string]interface{}{
		"return_token_usage": true, // 返回 token 使用情况
	}

	// 如果配置了使用稀疏向量，添加参数
	if c.config.UseSparse {
		params["return_sparse"] = true
		log.Printf("已启用稀疏向量返回")
	}

	embModel := vikingdb.EmbModel{
		ModelName: c.config.ModelName,
		Params:    params,
	}

	// 调用 EmbeddingV2 API
	log.Printf("正在调用 VikingDB EmbeddingV2 API，模型: %s", c.config.ModelName)
	startTime := time.Now()

	results, err := c.service.EmbeddingV2(embModel, rawDataList)
	if err != nil {
		return nil, fmt.Errorf("调用 EmbeddingV2 失败: %v", err)
	}

	duration := time.Since(startTime)
	log.Printf("向量化完成，耗时: %v", duration)

	// 解析结果
	if results == nil {
		return nil, fmt.Errorf("EmbeddingV2 返回空结果")
	}

	// 提取 data 字段
	dataInterface, ok := results["data"]
	if !ok {
		return nil, fmt.Errorf("EmbeddingV2 响应中缺少 data 字段")
	}

	dataList, ok := dataInterface.([]interface{})
	if !ok {
		return nil, fmt.Errorf("data 字段格式错误")
	}

	// 提取向量结果
	embeddingResults := make([]*EmbeddingResult, 0, len(dataList))
	for idx, item := range dataList {
		itemMap, ok := item.(map[string]interface{})
		if !ok {
			continue
		}

		result := &EmbeddingResult{}

		// 提取 dense_vector
		denseVectorInterface, ok := itemMap["dense_vector"]
		if !ok {
			return nil, fmt.Errorf("文本 %d 缺少 dense_vector 字段", idx+1)
		}

		// 转换为 float32 数组
		denseVector, err := convertToFloat32Array(denseVectorInterface)
		if err != nil {
			return nil, fmt.Errorf("文本 %d 向量格式错误: %v", idx+1, err)
		}
		result.DenseVector = denseVector

		// 提取 sparse_vector（如果存在）
		if c.config.UseSparse {
			if sparseVectorInterface, ok := itemMap["sparse_vector"]; ok {
				sparseVector, err := convertToSparseVector(sparseVectorInterface)
				if err != nil {
					log.Printf("警告：文本 %d 稀疏向量解析失败: %v", idx+1, err)
				} else {
					result.SparseVector = sparseVector
					log.Printf("文本 %d: 获取到 %d 维稠密向量和 %d 个稀疏向量元素",
						idx+1, len(denseVector), len(sparseVector))
				}
			}
		} else {
			log.Printf("文本 %d: 获取到 %d 维稠密向量", idx+1, len(denseVector))
		}

		embeddingResults = append(embeddingResults, result)
	}

	return embeddingResults, nil
}

// GetEmbedding 获取单个文本的向量表示
func (c *EmbeddingClient) GetEmbedding(ctx context.Context, text string) ([]float32, error) {
	embeddings, err := c.GetEmbeddings(ctx, []string{text})
	if err != nil {
		return nil, err
	}

	if len(embeddings) == 0 {
		return nil, fmt.Errorf("未获取到向量")
	}

	return embeddings[0], nil
}

// BatchGetEmbeddings 批量获取文本的向量表示（带重试）
func (c *EmbeddingClient) BatchGetEmbeddings(ctx context.Context, texts []string, batchSize int) ([][]float32, error) {
	results, err := c.BatchGetEmbeddingsWithDetails(ctx, texts, batchSize)
	if err != nil {
		return nil, err
	}

	// 仅返回稠密向量
	embeddings := make([][]float32, len(results))
	for i, result := range results {
		embeddings[i] = result.DenseVector
	}

	return embeddings, nil
}

// BatchGetEmbeddingsWithDetails 批量获取文本的详细向量表示（包括稠密和稀疏向量）
func (c *EmbeddingClient) BatchGetEmbeddingsWithDetails(ctx context.Context, texts []string, batchSize int) ([]*EmbeddingResult, error) {
	if batchSize <= 0 {
		batchSize = 100 // 默认批次大小
	}

	var allResults []*EmbeddingResult
	totalTexts := len(texts)

	for i := 0; i < totalTexts; i += batchSize {
		end := i + batchSize
		if end > totalTexts {
			end = totalTexts
		}

		batch := texts[i:end]
		log.Printf("处理批次 %d-%d / %d", i+1, end, totalTexts)

		// 重试逻辑
		var results []*EmbeddingResult
		var err error

		for retry := 0; retry < 3; retry++ {
			results, err = c.GetEmbeddingsWithDetails(ctx, batch)
			if err == nil {
				break
			}

			log.Printf("批次 %d-%d 失败（第 %d 次重试）: %v", i+1, end, retry+1, err)
			if retry < 2 {
				time.Sleep(time.Duration(retry+1) * time.Second)
			}
		}

		if err != nil {
			return nil, fmt.Errorf("批次 %d-%d 获取向量失败: %v", i+1, end, err)
		}

		allResults = append(allResults, results...)
	}

	log.Printf("成功获取 %d 个文本的向量", len(allResults))
	return allResults, nil
}

// GetEmbeddingsWithSparse 获取文本的稠密向量和稀疏向量
func (c *EmbeddingClient) GetEmbeddingsWithSparse(ctx context.Context, texts []string) (map[string]interface{}, error) {
	if len(texts) == 0 {
		return nil, fmt.Errorf("输入文本不能为空")
	}

	// 构建请求数据
	rawDataList := make([]vikingdb.RawData, len(texts))
	for i, text := range texts {
		rawDataList[i] = vikingdb.RawData{
			DataType: "text",
			Text:     text,
		}
	}

	// 设置模型参数（使用支持稀疏向量的模型，如 bge-m3）
	embModel := vikingdb.EmbModel{
		ModelName: c.config.ModelName,
		Params: map[string]interface{}{
			"return_token_usage": true,
			"return_sparse":      c.config.UseSparse, // 是否返回稀疏向量
		},
	}

	// 调用 EmbeddingV2 API
	results, err := c.service.EmbeddingV2(embModel, rawDataList)
	if err != nil {
		return nil, fmt.Errorf("调用 EmbeddingV2 失败: %v", err)
	}

	return results, nil
}

// convertToFloat32Array 将 interface{} 转换为 float32 数组
func convertToFloat32Array(data interface{}) ([]float32, error) {
	// 尝试直接转换为 []float32
	if floatArray, ok := data.([]float32); ok {
		return floatArray, nil
	}

	// 尝试转换为 []interface{} 然后逐个转换
	if interfaceArray, ok := data.([]interface{}); ok {
		result := make([]float32, len(interfaceArray))
		for i, v := range interfaceArray {
			// 尝试多种数值类型
			switch val := v.(type) {
			case float32:
				result[i] = val
			case float64:
				result[i] = float32(val)
			case int:
				result[i] = float32(val)
			case int64:
				result[i] = float32(val)
			default:
				return nil, fmt.Errorf("无法转换索引 %d 的值: %T", i, v)
			}
		}
		return result, nil
	}

	// 尝试转换为 []float64
	if float64Array, ok := data.([]float64); ok {
		result := make([]float32, len(float64Array))
		for i, v := range float64Array {
			result[i] = float32(v)
		}
		return result, nil
	}

	return nil, fmt.Errorf("无法将 %T 类型转换为 float32 数组", data)
}

// ConvertToBase64Vector 将float32数组转换为Base64编码的向量字符串
func ConvertToBase64Vector(embedding []float32) string {
	packedData := make([]byte, len(embedding)*4)
	for j, v := range embedding {
		binary.LittleEndian.PutUint32(packedData[j*4:], math.Float32bits(v))
	}
	return base64.StdEncoding.EncodeToString(packedData)
}

// convertToSparseVector 将 interface{} 转换为稀疏向量 map[int64]float64
func convertToSparseVector(data interface{}) (map[int64]float64, error) {
	// 尝试转换为 map[string]interface{}
	if mapData, ok := data.(map[string]interface{}); ok {
		result := make(map[int64]float64)
		for k, v := range mapData {
			// 将 string 键转换为 int64
			var key int64
			_, err := fmt.Sscanf(k, "%d", &key)
			if err != nil {
				return nil, fmt.Errorf("无法转换键 %s 为 int64: %v", k, err)
			}

			// 转换值为 float64
			switch val := v.(type) {
			case float64:
				result[key] = val
			case float32:
				result[key] = float64(val)
			case int:
				result[key] = float64(val)
			case int64:
				result[key] = float64(val)
			default:
				return nil, fmt.Errorf("无法转换值 %v (类型 %T) 为 float64", v, v)
			}
		}
		return result, nil
	}

	// 尝试转换为 map[interface{}]interface{}
	if mapData, ok := data.(map[interface{}]interface{}); ok {
		result := make(map[int64]float64)
		for k, v := range mapData {
			// 转换键为 int64
			var key int64
			switch kval := k.(type) {
			case int64:
				key = kval
			case int:
				key = int64(kval)
			case string:
				_, err := fmt.Sscanf(kval, "%d", &key)
				if err != nil {
					return nil, fmt.Errorf("无法转换键 %s 为 int64: %v", kval, err)
				}
			default:
				return nil, fmt.Errorf("无法转换键 %v (类型 %T) 为 int64", k, k)
			}

			// 转换值为 float64
			switch val := v.(type) {
			case float64:
				result[key] = val
			case float32:
				result[key] = float64(val)
			case int:
				result[key] = float64(val)
			case int64:
				result[key] = float64(val)
			default:
				return nil, fmt.Errorf("无法转换值 %v (类型 %T) 为 float64", v, v)
			}
		}
		return result, nil
	}

	// 如果已经是正确的类型
	if sparseVector, ok := data.(map[int64]float64); ok {
		return sparseVector, nil
	}

	return nil, fmt.Errorf("无法将 %T 类型转换为稀疏向量", data)
}

// ConvertSparseVector 转换稀疏向量格式为字符串键（用于存储）
func ConvertSparseVector(sparseVector map[int64]float64) map[string]float64 {
	// 将 int64 键转换为 string 键（如果需要）
	result := make(map[string]float64)
	for k, v := range sparseVector {
		result[fmt.Sprintf("%d", k)] = v
	}
	return result
}
