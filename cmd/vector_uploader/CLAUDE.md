# VikingDB向量数据库上传工具

## 功能简介

这是一个用于将JSON格式的show数据上传到字节跳动火山引擎VikingDB向量数据库的命令行工具。

## 主要功能

1. **JSON文件读取**: 支持读取现有导出工具生成的JSON文件
2. **数据转换**: 将show数据转换为向量格式，使用VikingDB SDK
3. **批量上传**: 支持批量上传，每批最多100条记录
4. **并发处理**: 支持多线程并发上传，基于生产者-消费者模式
5. **进度显示**: 实时显示上传进度和预计剩余时间
6. **错误处理**: 内置重试机制和错误恢复
7. **配置管理**: 支持多环境配置
8. **向量化**: 支持稠密向量和稀疏向量
9. **文档模式**: 支持合并模式和拆分模式两种文档设计
10. **文本合并策略**: 支持多种文本合并策略（简单、结构化、权重）

## 数据格式

工具支持以下JSON数据格式：

```json
[
  {
    "id": 123,
    "name": "Show Name",
    "name_i18n": ["English Name", "中文名称", "日本語名"],
    "overview": "Show overview text",
    "overview_i18n": ["English overview", "中文概述", "日本語概要"]
  }
]
```

## 配置说明

### 配置文件

在 `config/*.ini` 文件中添加VikingDB配置：

```ini
[vikingdb]
Host=api-vikingdb.volces.com
Region=cn-beijing
AccessKeyID=your-access-key-id
AccessKeySecret=your-access-key-secret
Scheme=https
ConnectionTimeout=30
Collection=vlab-shows
Index=vlab-shows-index
ModelName=doubao-embedding-240715
UseSparse=true
DenseWeight=0.5
BatchSize=50
MaxRetries=3
RetryDelay=1000
# 文档模式配置
DocumentMode=merged
TextMergeStrategy=weighted
TitleWeight=2.0
```

### 配置参数说明

- `Host`: VikingDB服务地址
- `Region`: 服务区域
- `AccessKeyID`: 访问密钥ID
- `AccessKeySecret`: 访问密钥密码
- `Scheme`: 协议 (http/https)
- `ConnectionTimeout`: 连接超时时间（秒）
- `Collection`: 集合名称
- `Index`: 索引名称
- `ModelName`: 向量化模型名称
- `UseSparse`: 是否使用稀疏向量
- `DenseWeight`: 稠密向量权重
- `BatchSize`: 批次大小
- `MaxRetries`: 最大重试次数
- `RetryDelay`: 重试延迟（毫秒）
- `DocumentMode`: 文档模式 ("merged" | "split")
- `TextMergeStrategy`: 文本合并策略 ("simple" | "structured" | "weighted")
- `TitleWeight`: 标题权重倍数（仅weighted策略有效）
- `UseAsyncUpload`: 是否使用异步上传模式 (默认: true)

## 使用方法

### 基本用法

```bash
# 使用默认配置上传当前目录下的所有JSON文件
go run ./cmd/vector_uploader/main.go

# 指定配置文件
go run ./cmd/vector_uploader/main.go -config=config/test.ini

# 指定输入文件
go run ./cmd/vector_uploader/main.go -input=shows_export.json

# 指定多个文件
go run ./cmd/vector_uploader/main.go -input="file1.json,file2.json"

# 使用通配符
go run ./cmd/vector_uploader/main.go -input="exports/*.json"
```

### 命令行参数

- `-config`: 配置文件路径 (默认: 使用环境变量或默认配置)
- `-input`: 输入JSON文件路径或模式，多个用逗号分隔 (默认: "*.json")
- `-batch`: 批次大小，0表示使用配置文件中的值 (默认: 0)
- `-workers`: 并发工作线程数 (默认: 1)
- `-progress`: 是否显示进度 (默认: true)
- `-dry-run`: 仅测试，不实际上传 (默认: false)
- `-list`: 仅列出匹配的文件 (默认: false)

### 使用示例

```bash
# 测试模式，查看会处理哪些文件
go run ./cmd/vector_uploader/main.go -list

# DryRun模式，测试数据转换但不上传
go run ./cmd/vector_uploader/main.go -dry-run

# 指定批次大小和并发数（高性能模式）
go run ./cmd/vector_uploader/main.go -batch=50 -workers=4

# 大批量数据并发上传
go run ./cmd/vector_uploader/main.go -input=large_export.json -batch=100 -workers=8

# 上传特定文件到生产环境
go run ./cmd/vector_uploader/main.go -input=shows_i18n_export.json -config=config/prd.ini -workers=2
```

## 文档模式说明

工具支持两种文档设计模式：

### 1. 合并模式 (merged) - 推荐
- **设计**: 一个show对应一个向量文档
- **优势**: 存储效率高，检索简单，维护成本低
- **适用**: 大多数应用场景，成本敏感的项目

### 2. 拆分模式 (split)
- **设计**: 一个show对应多个向量文档（name + overview）
- **优势**: 匹配精确，语义分离，支持细粒度检索
- **适用**: 需要精确匹配的场景

## 文本合并策略

### 1. Simple策略
```
合并方式: name + overview + i18n_content
特点: 简单直接，性能最好
```

### 2. Structured策略
```
合并方式: [TITLE] name [DESCRIPTION] overview [I18N] i18n_content
特点: 语义清晰，便于理解
```

### 3. Weighted策略 (推荐)
```
合并方式: name(重复N次) + overview + i18n_content
特点: 标题权重增强，检索效果更好
```

## 数据处理逻辑

### 合并模式
1. **文档合并**: 每个show记录合并为一个文档
2. **ID策略**: 直接使用show ID作为文档ID
3. **字段映射**:
    - `id`: show ID (保持原始int类型，作为文档主键)
    - `show_id`: 原始show的ID
    - `name`: 原始名称
    - `name_i18n`: 多语言名称数组
    - `overview`: 原始概述
    - `overview_i18n`: 多语言概述数组
    - `text`: 合并后的完整文本 (VikingDB API要求的字段名)
    - `document_type`: "merged"

### 拆分模式
1. **文档分割**: 每个show记录分割为多个文档
    - name文档：包含show的名称和多语言名称
    - overview文档：包含show的概述和多语言概述
2. **ID策略**: 使用hash生成唯一文档ID
3. **字段映射**:
    - `id`: hash生成的文档ID
    - `show_id`: 原始show的ID
    - `type`: 文档类型 (name/overview)
    - `content`: 原始内容
    - `content_i18n`: 多语言内容
    - `text`: 合并的所有内容 (VikingDB API要求的字段名)
    - `document_type`: "split"

4. **向量化**: 使用VikingDB内置的向量化模型对文本内容进行向量化

## 注意事项

1. **配置安全**: 请妥善保管AccessKey信息，不要提交到版本控制系统
2. **网络环境**: 确保网络可以访问VikingDB服务
3. **数据备份**: 建议在上传前备份原始数据
4. **测试先行**: 建议先使用DryRun模式测试
5. **监控资源**: 大批量上传时注意监控系统资源使用情况

## 错误处理

工具内置了以下错误处理机制：

1. **自动重试**: 网络错误或临时故障会自动重试
2. **进度恢复**: 支持断点续传（需要手动实现）
3. **详细日志**: 提供详细的错误信息和处理建议

## 性能优化建议

1. **批次大小**: 根据网络情况调整批次大小，建议50-200
2. **并发控制**: 适当增加并发数，但不要超过服务端限制
3. **内存管理**: 大文件处理时注意内存使用情况
4. **网络优化**: 在网络条件好的环境下运行

## 故障排除

### 常见问题

1. **配置错误**: 检查配置文件中的VikingDB配置是否正确
2. **网络问题**: 确认网络连接和防火墙设置
3. **权限问题**: 验证AccessKey是否有足够权限
4. **文件格式**: 确认JSON文件格式正确

### 日志分析

工具会输出详细的日志信息，包括：
- 配置加载状态
- 文件读取进度
- 数据转换结果
- 上传进度和错误信息

## 开发说明

### 代码结构

- `main.go`: 主程序入口和命令行处理
- `ShowI18nData`: JSON数据结构定义
- `VectorDocument`: 向量文档结构
- `VectorUploader`: 核心上传逻辑
- `ProgressBar`: 进度显示组件

### 扩展开发

如需扩展功能，可以：
1. 修改数据转换逻辑
2. 添加新的向量化方式
3. 实现更复杂的错误处理
4. 添加数据验证功能

## 异步上传说明

### 功能简介

工具现在支持异步上传模式，可以显著提高大批量数据的上传效率。

### 配置方式

在配置文件中设置 `UseAsyncUpload=true` 启用异步上传（默认启用）：

```ini
[vikingdb]
# ... 其他配置 ...
UseAsyncUpload=true
```

### 工作原理

- **同步上传**：客户端等待每批数据完全上传后再继续
- **异步上传**：客户端提交数据后立即返回，服务端在后台处理向量化和存储

### 适用场景

- **推荐使用异步上传**：
    - 大批量数据导入（>10000条记录）
    - 原始数据上传（服务端向量化）
    - 对上传速度要求较高的场景

- **考虑使用同步上传**：
    - 需要立即确认数据写入状态
    - 小批量数据更新
    - 调试和测试场景

### 性能对比

根据实际测试，异步上传相比同步上传：
- 吞吐量提升 2-3 倍
- 大批量数据导入时间减少 50% 以上
- 更好的并发处理能力

## 版本历史

- v1.1.0: 添加异步上传支持，优化大批量数据导入性能
- v1.0.0: 初始版本，支持基本的JSON文件上传功能
