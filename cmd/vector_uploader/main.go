package main

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"vlab/config"

	"github.com/volcengine/volc-sdk-golang/service/vikingdb"
)

// ShowI18nData 表示从JSON文件读取的show数据结构
type ShowI18nData struct {
	ID           uint64   `json:"id"`
	Name         string   `json:"name"`
	NameI18n     []string `json:"name_i18n"`
	Overview     string   `json:"overview"`
	OverviewI18n []string `json:"overview_i18n"`
	// 新增的基础字段（与 export_shows_i18n 一致）
	Status      uint32 `json:"status"`       // 状态值（0=禁用, 1=启用）
	Score       uint32 `json:"score"`        // 评分值（0-100）
	ContentType uint32 `json:"content_type"` // 内容类型（1=Movie, 2=TV, 3=Comic）
	// 标量字段及其ID列表
	Region     string   `json:"region"`      // 地区名称 (class_id=2)
	RegionI18n []string `json:"region_i18n"` // 地区的多语言版本
	RegionIDs  []uint64 `json:"region_ids"`  // 地区的ID列表
	Year       string   `json:"year"`        // 年份名称 (class_id=3)
	YearI18n   []string `json:"year_i18n"`   // 年份的多语言版本
	YearIDs    []uint64 `json:"year_ids"`    // 年份的ID列表
	Genre      string   `json:"genre"`       // 类型名称 (class_id=5)
	GenreI18n  []string `json:"genre_i18n"`  // 类型的多语言版本
	GenreIDs   []uint64 `json:"genre_ids"`   // 类型的ID列表
	// 保留的兼容字段
	Category     string   `json:"category"`      // 分类（如：动作、喜剧、科幻）
	CategoryI18n []string `json:"category_i18n"` // 多语言分类
	Rating       float64  `json:"rating"`        // 评分（0-10，兼容旧版）
	Duration     int      `json:"duration"`      // 时长（分钟）
	Director     string   `json:"director"`      // 导演
	Actors       []string `json:"actors"`        // 演员列表
	Tags         []string `json:"tags"`          // 标签（如：热门、推荐、独家）
	Language     string   `json:"language"`      // 主要语言
	Season       int      `json:"season"`        // 季数
	Episodes     int      `json:"episodes"`      // 集数
}

// UploadConfig 上传配置
type UploadConfig struct {
	InputFiles   []string
	BatchSize    int
	MaxWorkers   int
	ShowProgress bool
	DryRun       bool
	ProgressFunc func(current, total int64, message string)
}

// VectorUploader 向量上传器
type VectorUploader struct {
	collectionClient *vikingdb.CollectionClient
	config           *config.VikingDB
	ctx              context.Context
	embeddingClient  *EmbeddingClient // 新增：嵌入客户端
}

// ProgressBar 进度条
type ProgressBar struct {
	total     int64
	current   int64
	startTime time.Time
	mu        sync.Mutex
}

func (pb *ProgressBar) Update(current int64, message string) {
	pb.mu.Lock()
	defer pb.mu.Unlock()

	pb.current = current
	if pb.total > 0 {
		progress := float64(current) / float64(pb.total) * 100
		elapsed := time.Since(pb.startTime)

		if current > 0 {
			estimatedTotal := time.Duration(float64(elapsed) / float64(current) * float64(pb.total))
			remaining := estimatedTotal - elapsed

			fmt.Printf("\r进度: %.1f%% (%d/%d) | 耗时: %v | 预计剩余: %v | %s",
				progress, current, pb.total,
				elapsed.Round(time.Second),
				remaining.Round(time.Second),
				message)
		} else {
			fmt.Printf("\r%s", message)
		}
	} else {
		fmt.Printf("\r%s", message)
	}

	if current == pb.total {
		fmt.Printf("\n✅ %s\n", message)
	}
}

// NewVectorUploader 创建新的向量上传器
func NewVectorUploader(ctx context.Context, cfg *config.VikingDB) (*VectorUploader, error) {
	// 创建VikingDB Service并设置超时
	vikingDBService := vikingdb.NewVikingDBService(
		cfg.Host,
		cfg.Region,
		cfg.AccessKeyID,
		cfg.AccessKeySecret,
		cfg.Scheme,
	)

	// 设置连接超时
	if cfg.ConnectionTimeout > 0 {
		vikingDBService.SetConnectionTimeout(int64(cfg.ConnectionTimeout))
		log.Printf("设置VikingDB连接超时: %d秒", cfg.ConnectionTimeout)
	}

	// 创建VikingDB Collection客户端
	collectionClient := vikingdb.NewCollectionClient(
		cfg.Collection,
		cfg.Host,
		cfg.Region,
		cfg.AccessKeyID,
		cfg.AccessKeySecret,
		cfg.Scheme,
	)

	return &VectorUploader{
		collectionClient: collectionClient,
		config:           cfg,
		ctx:              ctx,
		embeddingClient:  NewEmbeddingClient(cfg), // 初始化嵌入客户端
	}, nil
}

// generateDocumentID 生成文档ID
func generateDocumentID(showID uint64, contentType string) string {
	content := fmt.Sprintf("%d_%s", showID, contentType)
	hash := md5.Sum([]byte(content))
	return hex.EncodeToString(hash[:])
}

// convertToText 将多语言数组转换为文本
func convertToText(texts []string) string {
	if len(texts) == 0 {
		return ""
	}
	return strings.Join(texts, " | ")
}

// readJSONFile 读取JSON文件
func readJSONFile(filePath string) ([]*ShowI18nData, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %v", err)
	}
	defer file.Close()

	var data []*ShowI18nData
	decoder := json.NewDecoder(file)
	if err := decoder.Decode(&data); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %v", err)
	}

	return data, nil
}

// generateRandomVector 生成随机向量（用于测试）
func generateRandomVector(dimension int) string {
	floatArray := make([]float32, dimension)
	for j := range floatArray {
		floatArray[j] = 0.124135132531424 // 简单的测试值
	}

	packedData := make([]byte, dimension*4)
	for j, v := range floatArray {
		binary.LittleEndian.PutUint32(packedData[j*4:], math.Float32bits(v))
	}
	return base64.StdEncoding.EncodeToString(packedData)
}

// convertToVikingDBData 将Show数据转换为VikingDB数据格式
func (u *VectorUploader) convertToVikingDBData(shows []*ShowI18nData) []vikingdb.Data {
	// 根据配置选择转换模式
	if u.config.DocumentMode == "merged" {
		return u.convertToMergedDocuments(shows)
	} else {
		return u.convertToSplitDocuments(shows)
	}
}

// convertToMergedDocuments 合并模式：一个show对应一个文档
func (u *VectorUploader) convertToMergedDocuments(shows []*ShowI18nData) []vikingdb.Data {
	var dataList []vikingdb.Data
	var textsToEmbed []string
	var validShows []*ShowI18nData

	// 第一步：收集需要向量化的文本
	for _, show := range shows {
		// 跳过空数据
		if show.Name == "" && show.Overview == "" && len(show.NameI18n) == 0 && len(show.OverviewI18n) == 0 {
			continue
		}

		// 合并所有文本内容
		combinedText := u.mergeTextContent(show)
		textsToEmbed = append(textsToEmbed, combinedText)
		validShows = append(validShows, show)
	}

	// 第二步：批量获取向量（包括稀疏向量）
	var embeddingResults []*EmbeddingResult
	if len(textsToEmbed) > 0 && u.config.NeedEmbed {
		log.Printf("正在获取 %d 个文本的向量...", len(textsToEmbed))
		var err error
		embeddingResults, err = u.embeddingClient.BatchGetEmbeddingsWithDetails(u.ctx, textsToEmbed, 100)
		if err != nil {
			log.Printf("警告：获取向量失败: %v，将使用测试向量", err)
			// 失败时使用测试向量
			for range textsToEmbed {
				embeddingResults = append(embeddingResults, &EmbeddingResult{})
			}
		}
	}

	// 第三步：创建文档
	for i, show := range validShows {
		// 创建文档字段
		fields := map[string]interface{}{
			"id":            show.ID, // 直接使用show ID作为文档主键
			"show_id":       show.ID,
			"name":          show.Name,
			"name_i18n":     show.NameI18n,
			"overview":      show.Overview,
			"overview_i18n": show.OverviewI18n,
			"text":          textsToEmbed[i], // 使用合并的文本
			"document_type": "merged",
			"created_at":    time.Now().Format(time.RFC3339),
			// 新增的基础字段
			"status":       show.Status,      // uint32 状态值
			"score":        show.Score,       // uint32 评分值
			"content_type": show.ContentType, // uint32 内容类型
			// 标量字段及其ID列表
			"region":     append(show.RegionI18n, show.Region),
			"region_ids": show.RegionIDs,
			"year":       append(show.YearI18n, show.Year),
			"year_ids":   show.YearIDs,
			"genre":      append(show.GenreI18n, show.Genre),
			"genre_ids":  show.GenreIDs,
			// 保留的兼容字段
			"category":      show.Category,
			"category_i18n": show.CategoryI18n,
			"rating":        show.Rating, // 兼容旧版评分（0-10）
			"duration":      show.Duration,
			"director":      show.Director,
			"actors":        show.Actors,
			"tags":          show.Tags,
			"language":      show.Language,
			"season":        show.Season,
			"episodes":      show.Episodes,
		}

		// 添加稠密向量（如果成功获取）
		if u.config.NeedEmbed && i < len(embeddingResults) && embeddingResults[i] != nil && embeddingResults[i].DenseVector != nil {
			fields["text_vector"] = ConvertToBase64Vector(embeddingResults[i].DenseVector)
		} else if u.config.NeedEmbed {
			// 使用测试向量作为后备
			fields["text_vector"] = generateRandomVector(1024)
		}

		// 添加稀疏向量（从 API 返回的真实稀疏向量）
		if u.config.UseSparse && i < len(embeddingResults) && embeddingResults[i] != nil && embeddingResults[i].SparseVector != nil {
			// 使用 API 返回的稀疏向量
			fields["text_sparse_vector"] = ConvertSparseVector(embeddingResults[i].SparseVector)
			log.Printf("文档 %d: 使用 API 返回的稀疏向量 (%d 个元素)", show.ID, len(embeddingResults[i].SparseVector))
		} else if u.config.UseSparse {
			// 如果没有从 API 获取到稀疏向量，使用生成的
			fields["text_sparse_vector"] = u.generateSparseVector(show)
			log.Printf("文档 %d: 使用生成的稀疏向量", show.ID)
		}

		dataList = append(dataList, vikingdb.Data{
			Fields: fields,
			//TTL:    100000, // 设置TTL
		})
	}

	return dataList
}

// uploadBatch 批量上传文档到VikingDB
func (u *VectorUploader) uploadBatch(dataList []vikingdb.Data) error {
	if len(dataList) == 0 {
		return nil
	}

	log.Printf("上传 %d 个文档到VikingDB", len(dataList))

	// 使用VikingDB SDK上传数据 (支持异步上传模式，提高上传效率)
	// 原始数据上传后，上游会自动进行向量化处理
	var err error
	if u.config.UseAsyncUpload {
		log.Printf("使用异步上传模式")
		err = u.collectionClient.UpsertData(dataList, vikingdb.WithAsyncUpsert(true))
	} else {
		log.Printf("使用同步上传模式")
		err = u.collectionClient.UpsertData(dataList)
	}

	if err != nil {
		return fmt.Errorf("上传数据失败: %v", err)
	}

	return nil
}

// uploadWithRetry 带重试的上传
func (u *VectorUploader) uploadWithRetry(dataList []vikingdb.Data) error {
	var lastErr error

	for i := 0; i < u.config.MaxRetries; i++ {
		err := u.uploadBatch(dataList)
		if err == nil {
			return nil
		}

		lastErr = err
		log.Printf("上传失败，第 %d 次重试: %v", i+1, err)

		if i < u.config.MaxRetries-1 {
			time.Sleep(time.Duration(u.config.RetryDelay) * time.Millisecond)
		}
	}

	return fmt.Errorf("重试 %d 次后仍然失败: %v", u.config.MaxRetries, lastErr)
}

// processFiles 处理文件上传
func (u *VectorUploader) processFiles(uploadConfig *UploadConfig) error {
	var allData []vikingdb.Data

	// 读取所有文件
	for _, filePath := range uploadConfig.InputFiles {
		log.Printf("读取文件: %s", filePath)

		shows, err := readJSONFile(filePath)
		if err != nil {
			return fmt.Errorf("读取文件 %s 失败: %v", filePath, err)
		}

		dataList := u.convertToVikingDBData(shows)
		allData = append(allData, dataList...)

		log.Printf("从文件 %s 转换了 %d 个文档", filePath, len(dataList))
	}

	if len(allData) == 0 {
		return fmt.Errorf("没有找到任何文档")
	}

	log.Printf("总共需要上传 %d 个文档", len(allData))

	if uploadConfig.DryRun {
		log.Printf("DryRun模式：跳过实际上传")
		return nil
	}

	// 创建进度条
	var progressBar *ProgressBar
	if uploadConfig.ShowProgress {
		progressBar = &ProgressBar{
			total:     int64(len(allData)),
			startTime: time.Now(),
		}
	}

	// 批量上传
	batchSize := uploadConfig.BatchSize
	if batchSize <= 0 {
		batchSize = u.config.BatchSize
	}

	var uploaded int64
	for i := 0; i < len(allData); i += batchSize {
		end := i + batchSize
		if end > len(allData) {
			end = len(allData)
		}

		batch := allData[i:end]

		err := u.uploadWithRetry(batch)
		if err != nil {
			return fmt.Errorf("上传批次失败: %v", err)
		}

		uploaded += int64(len(batch))

		if progressBar != nil {
			progressBar.Update(uploaded, fmt.Sprintf("已上传 %d 个文档", uploaded))
		}
	}

	log.Printf("✅ 成功上传 %d 个文档", uploaded)
	return nil
}

// processFilesConcurrently 并发处理文件上传
func (u *VectorUploader) processFilesConcurrently(uploadConfig *UploadConfig) error {
	var allData []vikingdb.Data

	// 读取所有文件
	for _, filePath := range uploadConfig.InputFiles {
		log.Printf("读取文件: %s", filePath)

		shows, err := readJSONFile(filePath)
		if err != nil {
			return fmt.Errorf("读取文件 %s 失败: %v", filePath, err)
		}

		dataList := u.convertToVikingDBData(shows)
		allData = append(allData, dataList...)

		log.Printf("从文件 %s 转换了 %d 个文档", filePath, len(dataList))
	}

	if len(allData) == 0 {
		return fmt.Errorf("没有找到任何文档")
	}

	log.Printf("总共需要上传 %d 个文档", len(allData))

	if uploadConfig.DryRun {
		log.Printf("DryRun模式：跳过实际上传")
		return nil
	}

	// 使用简化的并发上传逻辑
	numWorkers := uploadConfig.MaxWorkers
	if numWorkers <= 0 {
		numWorkers = 1
	}

	// 创建进度条
	var progressBar *ProgressBar
	if uploadConfig.ShowProgress {
		progressBar = &ProgressBar{
			total:     int64(len(allData)),
			startTime: time.Now(),
		}
	}

	// 使用简单的并发上传
	err := u.uploadDataConcurrently(allData, numWorkers, progressBar)
	if err != nil {
		return fmt.Errorf("并发上传失败: %v", err)
	}

	log.Printf("✅ 成功上传 %d 个文档", len(allData))
	return nil
}

// uploadDataConcurrently 并发上传数据
func (u *VectorUploader) uploadDataConcurrently(allData []vikingdb.Data, numWorkers int, progressBar *ProgressBar) error {
	// 创建数据通道
	dataChan := make(chan []vikingdb.Data, numWorkers*2)
	errorChan := make(chan error, numWorkers)
	var wg sync.WaitGroup
	var uploaded int64
	var mu sync.Mutex

	// 启动工作线程
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			for batch := range dataChan {
				err := u.uploadWithRetry(batch)
				if err != nil {
					log.Printf("Worker %d 上传失败: %v", workerID, err)
					errorChan <- err
					return
				}

				// 更新进度
				mu.Lock()
				uploaded += int64(len(batch))
				if progressBar != nil {
					progressBar.Update(uploaded, fmt.Sprintf("已上传 %d 个文档", uploaded))
				}
				mu.Unlock()

				log.Printf("Worker %d 成功上传 %d 个文档", workerID, len(batch))
			}
		}(i + 1)
	}

	// 分批发送数据
	batchSize := u.config.BatchSize
	go func() {
		defer close(dataChan)
		for i := 0; i < len(allData); i += batchSize {
			end := i + batchSize
			if end > len(allData) {
				end = len(allData)
			}
			batch := allData[i:end]
			dataChan <- batch
		}
	}()

	// 等待所有工作线程完成
	wg.Wait()
	close(errorChan)

	// 检查是否有错误
	if len(errorChan) > 0 {
		return <-errorChan
	}

	return nil
}

// UseConcurrentUpload 使用并发上传模式
func (u *VectorUploader) UseConcurrentUpload(uploadConfig *UploadConfig) error {
	if uploadConfig.MaxWorkers > 1 {
		log.Printf("使用并发上传模式，工作线程数: %d", uploadConfig.MaxWorkers)
		return u.processFilesConcurrently(uploadConfig)
	} else {
		log.Printf("使用单线程上传模式")
		return u.processFiles(uploadConfig)
	}
}

// findJSONFiles 查找JSON文件
func findJSONFiles(patterns []string) ([]string, error) {
	var files []string

	for _, pattern := range patterns {
		if strings.Contains(pattern, "*") {
			// 使用glob模式匹配
			matches, err := filepath.Glob(pattern)
			if err != nil {
				return nil, fmt.Errorf("glob匹配失败 %s: %v", pattern, err)
			}
			files = append(files, matches...)
		} else {
			// 直接文件路径
			if _, err := os.Stat(pattern); err != nil {
				return nil, fmt.Errorf("文件不存在 %s: %v", pattern, err)
			}
			files = append(files, pattern)
		}
	}

	// 过滤JSON文件
	var jsonFiles []string
	for _, file := range files {
		if strings.HasSuffix(strings.ToLower(file), ".json") {
			jsonFiles = append(jsonFiles, file)
		}
	}

	return jsonFiles, nil
}

func main() {
	// 命令行参数
	var (
		configPath   = flag.String("config", "", "配置文件路径 (如: config/local.ini)")
		inputFiles   = flag.String("input", "*.json", "输入JSON文件路径或模式，多个用逗号分隔")
		batchSize    = flag.Int("batch", 0, "批次大小，0表示使用配置文件中的值")
		maxWorkers   = flag.Int("workers", 1, "并发工作线程数")
		showProgress = flag.Bool("progress", true, "是否显示进度")
		dryRun       = flag.Bool("dry-run", false, "仅测试，不实际上传")
		listFiles    = flag.Bool("list", false, "仅列出匹配的文件")
	)
	flag.Parse()

	fmt.Println("=== VikingDB向量数据库上传工具 ===")
	if *configPath != "" {
		fmt.Printf("配置文件: %s\n", *configPath)
	} else {
		fmt.Println("配置文件: 默认配置")
	}

	// 初始化配置
	if *configPath != "" {
		config.SetupWithPath(*configPath)
	} else {
		config.Setup()
	}

	// 验证VikingDB配置
	if config.VikingDBCfg.Host == "" {
		log.Fatalf("❌ VikingDB配置不完整，请检查配置文件")
	}

	fmt.Printf("VikingDB配置:\n")
	fmt.Printf("  Host: %s\n", config.VikingDBCfg.Host)
	fmt.Printf("  Region: %s\n", config.VikingDBCfg.Region)
	fmt.Printf("  Collection: %s\n", config.VikingDBCfg.Collection)
	fmt.Printf("  BatchSize: %d\n", config.VikingDBCfg.BatchSize)

	// 查找输入文件
	inputPatterns := strings.Split(*inputFiles, ",")
	for i, pattern := range inputPatterns {
		inputPatterns[i] = strings.TrimSpace(pattern)
	}

	jsonFiles, err := findJSONFiles(inputPatterns)
	if err != nil {
		log.Fatalf("❌ 查找文件失败: %v", err)
	}

	if len(jsonFiles) == 0 {
		log.Fatalf("❌ 没有找到匹配的JSON文件")
	}

	fmt.Printf("找到 %d 个JSON文件:\n", len(jsonFiles))
	for _, file := range jsonFiles {
		fmt.Printf("  - %s\n", file)
	}

	if *listFiles {
		fmt.Println("✅ 文件列表显示完成")
		return
	}

	// 创建上传器
	ctx := context.Background()
	uploader, err := NewVectorUploader(ctx, config.VikingDBCfg)
	if err != nil {
		log.Fatalf("❌ 创建上传器失败: %v", err)
	}

	// 配置上传参数
	uploadConfig := &UploadConfig{
		InputFiles:   jsonFiles,
		BatchSize:    *batchSize,
		MaxWorkers:   *maxWorkers,
		ShowProgress: *showProgress,
		DryRun:       *dryRun,
	}

	fmt.Printf("上传配置:\n")
	fmt.Printf("  批次大小: %d\n", uploadConfig.BatchSize)
	fmt.Printf("  工作线程: %d\n", uploadConfig.MaxWorkers)
	fmt.Printf("  显示进度: %v\n", uploadConfig.ShowProgress)
	fmt.Printf("  DryRun模式: %v\n", uploadConfig.DryRun)

	// 开始上传
	fmt.Println("\n开始处理文件...")
	startTime := time.Now()

	// 根据工作线程数选择上传模式
	if uploadConfig.MaxWorkers > 1 {
		fmt.Printf("使用并发上传模式，工作线程数: %d\n", uploadConfig.MaxWorkers)
		err = uploader.UseConcurrentUpload(uploadConfig)
	} else {
		fmt.Printf("使用单线程上传模式\n")
		err = uploader.processFiles(uploadConfig)
	}

	if err != nil {
		log.Fatalf("❌ 上传失败: %v", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("\n🎉 处理完成！\n")
	fmt.Printf("⏱️  总耗时: %v\n", duration.Round(time.Second))

	if *dryRun {
		fmt.Println("📝 这是DryRun模式，没有实际上传数据")
	}
}

// convertToSplitDocuments 拆分模式：一个show对应多个文档（原有逻辑）
func (u *VectorUploader) convertToSplitDocuments(shows []*ShowI18nData) []vikingdb.Data {
	var dataList []vikingdb.Data
	var textsToEmbed []string
	var textIndexMap []int // 记录每个文本对应的文档索引

	// 第一步：收集所有需要向量化的文本
	for _, show := range shows {
		if show.Name != "" || len(show.NameI18n) > 0 {
			nameContent := show.Name + " " + convertToText(show.NameI18n)
			textsToEmbed = append(textsToEmbed, nameContent)
			textIndexMap = append(textIndexMap, len(dataList))

			// 创建 name 文档（不包含向量）
			nameField := map[string]interface{}{
				"id":            generateDocumentID(show.ID, "name"),
				"show_id":       show.ID,
				"type":          "name",
				"content":       show.Name,
				"content_i18n":  convertToText(show.NameI18n),
				"text":          nameContent,
				"document_type": "split",
				"created_at":    time.Now().Format(time.RFC3339),
				// 添加标量字段
				"category":      show.Category,
				"category_i18n": show.CategoryI18n,
				"genre":         show.Genre,
				"genre_i18n":    show.GenreI18n,
				"year":          show.Year,
				"rating":        show.Rating,
				"duration":      show.Duration,
				"director":      show.Director,
				"actors":        show.Actors,
				"tags":          show.Tags,
				"region":        show.Region,
				"language":      show.Language,
				"season":        show.Season,
				"episodes":      show.Episodes,
				"status":        show.Status,
			}

			dataList = append(dataList, vikingdb.Data{
				Fields: nameField,
				TTL:    100000,
			})
		}

		if show.Overview != "" || len(show.OverviewI18n) > 0 {
			overviewContent := show.Overview + " " + convertToText(show.OverviewI18n)
			textsToEmbed = append(textsToEmbed, overviewContent)
			textIndexMap = append(textIndexMap, len(dataList))

			// 创建 overview 文档（不包含向量）
			overviewField := map[string]interface{}{
				"id":            generateDocumentID(show.ID, "overview"),
				"show_id":       show.ID,
				"type":          "overview",
				"content":       show.Overview,
				"content_i18n":  convertToText(show.OverviewI18n),
				"text":          overviewContent,
				"document_type": "split",
				"created_at":    time.Now().Format(time.RFC3339),
				// 添加标量字段
				"category":      show.Category,
				"category_i18n": show.CategoryI18n,
				"genre":         show.Genre,
				"genre_i18n":    show.GenreI18n,
				"year":          show.Year,
				"rating":        show.Rating,
				"duration":      show.Duration,
				"director":      show.Director,
				"actors":        show.Actors,
				"tags":          show.Tags,
				"region":        show.Region,
				"language":      show.Language,
				"season":        show.Season,
				"episodes":      show.Episodes,
				"status":        show.Status,
			}

			dataList = append(dataList, vikingdb.Data{
				Fields: overviewField,
				TTL:    100000,
			})
		}
	}

	// 第二步：批量获取向量（包括稀疏向量）
	if len(textsToEmbed) > 0 {
		log.Printf("正在获取 %d 个文本的向量...", len(textsToEmbed))
		embeddingResults, err := u.embeddingClient.BatchGetEmbeddingsWithDetails(u.ctx, textsToEmbed, 100)
		if err != nil {
			log.Printf("警告：获取向量失败: %v，将使用测试向量", err)
			// 失败时使用测试向量和生成的稀疏向量
			for _, docIdx := range textIndexMap {
				dataList[docIdx].Fields["text_vector"] = generateRandomVector(1024)

				// 生成稀疏向量
				if u.config.UseSparse {
					docType := dataList[docIdx].Fields["type"].(string)
					if docType == "name" {
						dataList[docIdx].Fields["text_sparse_vector"] = map[string]float64{
							"name":  0.8,
							"show":  0.6,
							"title": 0.7,
						}
					} else if docType == "overview" {
						dataList[docIdx].Fields["text_sparse_vector"] = map[string]float64{
							"overview":    0.9,
							"description": 0.8,
							"summary":     0.7,
						}
					}
				}
			}
		} else {
			// 成功时使用真实向量
			for i, docIdx := range textIndexMap {
				if i < len(embeddingResults) && embeddingResults[i] != nil {
					// 添加稠密向量
					if embeddingResults[i].DenseVector != nil {
						dataList[docIdx].Fields["text_vector"] = ConvertToBase64Vector(embeddingResults[i].DenseVector)
					} else {
						dataList[docIdx].Fields["text_vector"] = generateRandomVector(1024)
					}

					// 添加稀疏向量
					if u.config.UseSparse && embeddingResults[i].SparseVector != nil {
						dataList[docIdx].Fields["text_sparse_vector"] = ConvertSparseVector(embeddingResults[i].SparseVector)
						log.Printf("文档 %s: 使用 API 返回的稀疏向量 (%d 个元素)",
							dataList[docIdx].Fields["id"], len(embeddingResults[i].SparseVector))
					} else if u.config.UseSparse {
						// 如果没有从 API 获取到稀疏向量，使用生成的
						docType := dataList[docIdx].Fields["type"].(string)
						if docType == "name" {
							dataList[docIdx].Fields["text_sparse_vector"] = map[string]float64{
								"name":  0.8,
								"show":  0.6,
								"title": 0.7,
							}
						} else if docType == "overview" {
							dataList[docIdx].Fields["text_sparse_vector"] = map[string]float64{
								"overview":    0.9,
								"description": 0.8,
								"summary":     0.7,
							}
						}
						log.Printf("文档 %s: 使用生成的稀疏向量", dataList[docIdx].Fields["id"])
					}
				} else {
					dataList[docIdx].Fields["text_vector"] = generateRandomVector(1024)
				}
			}
		}
	}

	return dataList
}

// mergeTextContent 合并文本内容的不同策略
func (u *VectorUploader) mergeTextContent(show *ShowI18nData) string {
	switch u.config.TextMergeStrategy {
	case "simple":
		return u.mergeTextSimple(show)
	case "structured":
		return u.mergeTextStructured(show)
	case "weighted":
		return u.mergeTextWeighted(show)
	default:
		return u.mergeTextWeighted(show) // 默认使用权重策略
	}
}

// mergeTextSimple 简单合并策略
func (u *VectorUploader) mergeTextSimple(show *ShowI18nData) string {
	var parts []string

	if show.Name != "" {
		parts = append(parts, show.Name)
	}
	if show.Overview != "" {
		parts = append(parts, show.Overview)
	}
	if len(show.NameI18n) > 0 {
		parts = append(parts, convertToText(show.NameI18n))
	}
	if len(show.OverviewI18n) > 0 {
		parts = append(parts, convertToText(show.OverviewI18n))
	}

	return strings.Join(parts, " ")
}

// mergeTextStructured 结构化合并策略
func (u *VectorUploader) mergeTextStructured(show *ShowI18nData) string {
	var parts []string

	if show.Name != "" {
		parts = append(parts, "[TITLE] "+show.Name)
	}
	if show.Overview != "" {
		parts = append(parts, "[DESCRIPTION] "+show.Overview)
	}
	if len(show.NameI18n) > 0 {
		parts = append(parts, "[I18N_TITLE] "+convertToText(show.NameI18n))
	}
	if len(show.OverviewI18n) > 0 {
		parts = append(parts, "[I18N_DESCRIPTION] "+convertToText(show.OverviewI18n))
	}

	return strings.Join(parts, " ")
}

// mergeTextWeighted 权重合并策略
func (u *VectorUploader) mergeTextWeighted(show *ShowI18nData) string {
	var parts []string

	// 标题重复多次以增加权重
	if show.Name != "" {
		titleWeight := int(u.config.TitleWeight)
		if titleWeight <= 0 {
			titleWeight = 2 // 默认权重
		}
		for i := 0; i < titleWeight; i++ {
			parts = append(parts, show.Name)
		}
	}

	// 描述添加一次
	if show.Overview != "" {
		parts = append(parts, show.Overview)
	}

	// 多语言内容
	if len(show.NameI18n) > 0 {
		parts = append(parts, convertToText(show.NameI18n))
	}
	if len(show.OverviewI18n) > 0 {
		parts = append(parts, convertToText(show.OverviewI18n))
	}

	return strings.Join(parts, " ")
}

// generateSparseVector 生成稀疏向量
func (u *VectorUploader) generateSparseVector(show *ShowI18nData) map[string]float64 {
	sparseVector := make(map[string]float64)

	// 基于内容生成关键词权重
	if show.Name != "" {
		sparseVector["title"] = 0.9
		sparseVector["name"] = 0.8
	}
	if show.Overview != "" {
		sparseVector["description"] = 0.7
		sparseVector["overview"] = 0.6
	}
	if len(show.NameI18n) > 0 {
		sparseVector["i18n"] = 0.5
		sparseVector["multilingual"] = 0.4
	}

	// 基于标量数据生成更精确的关键词权重
	if show.Category != "" {
		sparseVector[strings.ToLower(show.Category)] = 0.8
		sparseVector["category"] = 0.7
	}

	// 类型相关
	if show.Genre != "" {
		sparseVector[strings.ToLower(show.Genre)] = 0.8
		sparseVector["genre"] = 0.7
		// 为不同类型添加特定关键词
		switch strings.ToLower(show.Genre) {
		case "movie", "电影":
			sparseVector["film"] = 0.6
			sparseVector["cinema"] = 0.5
		case "series", "电视剧":
			sparseVector["tv"] = 0.6
			sparseVector["television"] = 0.5
		case "documentary", "纪录片":
			sparseVector["docu"] = 0.6
			sparseVector["nonfiction"] = 0.5
		}
	}

	// 年份相关
	if show.Year != "" {
		year, err := strconv.Atoi(show.Year)
		if err == nil && year > 0 {
			sparseVector[show.Year] = 0.6
			if year >= 2020 {
				sparseVector["recent"] = 0.7
				sparseVector["new"] = 0.6
			} else if year >= 2010 {
				sparseVector["modern"] = 0.5
			} else {
				sparseVector["classic"] = 0.6
			}
		}
	}

	// 评分相关（同时支持新旧两种评分方式）
	// 新版：Score (0-100)
	if show.Score >= 90 {
		sparseVector["excellent"] = 0.9
		sparseVector["masterpiece"] = 0.8
	} else if show.Score >= 80 {
		sparseVector["great"] = 0.7
		sparseVector["popular"] = 0.6
	}
	// 兼容旧版：Rating (0-10)
	if show.Rating >= 9.0 {
		sparseVector["excellent"] = 0.9
		sparseVector["masterpiece"] = 0.8
	} else if show.Rating >= 8.0 {
		sparseVector["great"] = 0.7
		sparseVector["popular"] = 0.6
	}

	// 内容类型相关
	switch show.ContentType {
	case 1: // Movie
		sparseVector["movie"] = 0.8
		sparseVector["film"] = 0.7
		sparseVector["cinema"] = 0.6
	case 2: // TV
		sparseVector["tv"] = 0.8
		sparseVector["series"] = 0.7
		sparseVector["television"] = 0.6
	case 3: // Comic
		sparseVector["comic"] = 0.8
		sparseVector["anime"] = 0.7
		sparseVector["animation"] = 0.6
	}

	// 标签相关
	for _, tag := range show.Tags {
		sparseVector[strings.ToLower(tag)] = 0.7
	}

	// 地区和语言
	if show.Region != "" {
		sparseVector[strings.ToLower(show.Region)] = 0.6
	}
	if show.Language != "" {
		sparseVector[strings.ToLower(show.Language)] = 0.5
	}

	// 状态相关（新版使用uint32）
	switch show.Status {
	case 0: // 禁用
		sparseVector["disabled"] = 0.6
		sparseVector["inactive"] = 0.5
	case 1: // 启用
		sparseVector["active"] = 0.6
		sparseVector["enabled"] = 0.5
	}

	// 地区ID列表相关
	if len(show.RegionIDs) > 0 {
		sparseVector["multi_region"] = 0.5
	}
	// 年份ID列表相关
	if len(show.YearIDs) > 0 {
		sparseVector["multi_year"] = 0.5
	}
	// 类型ID列表相关
	if len(show.GenreIDs) > 0 {
		sparseVector["multi_genre"] = 0.5
	}

	// 通用权重
	sparseVector["show"] = 0.8
	sparseVector["content"] = 0.6

	return sparseVector
}
