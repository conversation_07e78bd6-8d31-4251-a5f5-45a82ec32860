# 外部ID同步命令行工具

## 功能概述

外部ID同步工具是一个用于管理剧集与外部平台（IMDB、TMDB、Trakt）ID映射关系的命令行工具。通过调用IMDB匹配API，自动获取并存储各平台的唯一标识符，实现剧集内容的跨平台关联。

## 技术架构

### 核心组件

- **Service层**: 使用 `showService.ExternalIDsService` 处理业务逻辑
- **DAO层**: 使用 `externalIdsDao` 进行数据库操作
- **API集成**: 调用 IMDB 匹配服务 (http://118.196.31.23:5310)
- **数据库**: MySQL 主从架构，支持读写分离

### 数据模型

```go
// 外部ID映射表: content_show_external_ids
type ExternalIDs struct {
    ShowID      uint64  // 剧集ID
    ImdbID      string  // IMDB ID (如: tt0903747)
    TmdbID      *uint64 // TMDB ID (如: 1396)
    TraktID     *uint64 // Trakt ID (如: 1388)
    Slug        string  // URL友好标识符
    MatchType   string  // 类型: movie/show
    MatchScore  float64 // 匹配分数 (0-100)
    IsMatch     uint8   // 是否有效匹配
    Source      string  // 数据来源: imdb_api/manual
}
```

## 主要功能

### 1. 同步功能

#### 单个同步 (`sync-one`)
```bash
./bin/external_ids_sync --cmd sync-one --show-id 123
```
- 根据剧集信息（名称、年份、简介）调用IMDB API
- 自动匹配并保存外部平台ID
- 支持强制更新已存在的记录 (`--force`)

#### 批量同步 (`sync-batch`)
```bash
# 同步指定的剧集
./bin/external_ids_sync --cmd sync-batch --show-ids "123,456,789"

# 同步所有未同步的剧集
./bin/external_ids_sync --cmd sync-batch --all

# 同步所有，限制最多100个
./bin/external_ids_sync --cmd sync-batch --all --limit-batch 100

# 同步所有，自动确认
./bin/external_ids_sync --cmd sync-batch --all --yes
```
- 支持同步指定ID或所有未同步剧集
- `--all`: 同步所有未同步的剧集
- `--limit-batch`: 限制批量同步的最大数量
- `--yes`: 自动确认，跳过交互提示
- `--skip-existing`: 跳过已有记录（默认true）
- 显示成功/失败统计
- 支持详细进度输出 (`--verbose`)

### 2. 查询功能 (`query`)

```bash
# 查询单个剧集
./bin/external_ids_sync --cmd query --show-id 123

# 批量查询
./bin/external_ids_sync --cmd query --show-ids "123,456,789"

# 分页查询所有
./bin/external_ids_sync --cmd query --limit 20 --offset 0
```

### 3. 搜索功能 (`search`)

```bash
# 根据IMDB ID搜索
./bin/external_ids_sync --cmd search --imdb-id tt0903747

# 根据TMDB ID搜索
./bin/external_ids_sync --cmd search --tmdb-id 1396
```

### 4. 统计功能 (`stats`)

```bash
./bin/external_ids_sync --cmd stats
```

输出统计信息：
- 总记录数、有效匹配数、无匹配数
- 按数据来源统计（imdb_api/manual）
- 按类型统计（movie/show）
- 平台覆盖率（IMDB/TMDB/Trakt）

## 实现细节

### 初始化流程

```go
1. 解析命令行参数
2. 加载配置文件 (config.Setup)
3. 初始化数据库连接 (dbs.Setup)
4. 创建服务实例
5. 执行指定命令
```

### API集成

工具集成了IMDB匹配API，调用流程：

1. **准备匹配参数**：
   - 提取剧集名称、年份、简介
   - 判断内容类型（movie/show/auto）
   - 规范化类型标签

2. **调用匹配API**：
   ```json
   POST http://118.196.31.23:5310/match
   {
     "type": "auto",
     "title": "Breaking Bad",
     "year": 2008,
     "overview": "...",
     "language": "en",
     "genres": ["drama", "crime"]
   }
   ```

3. **处理响应**：
   - 验证匹配分数（默认阈值90分）
   - 提取外部ID（IMDB、TMDB、Trakt）
   - 保存到数据库

### 错误处理

- **网络错误**: 自动重试机制（最多2次）
- **API超时**: 默认60秒超时设置
- **数据验证**: 参数校验和数据完整性检查
- **日志记录**: 详细的错误信息输出

## 性能特点

### 优化措施

1. **数据库优化**：
   - 使用唯一索引避免重复
   - 批量查询减少数据库访问
   - 读写分离提高查询性能

2. **内存管理**：
   - 分页查询避免大量数据加载
   - 手动实现统计避免全表扫描

3. **API调用**：
   - 响应时间约6秒，建议批量时控制并发
   - 缓存机制避免重复调用

### 性能指标

- 单个同步：6-8秒（含API调用）
- 批量同步：建议每批20-50个
- 查询性能：毫秒级响应
- 统计计算：根据数据量，通常1-3秒

## 部署和使用

### 编译构建

```bash
# 从项目根目录构建
go build -o ./bin/external_ids_sync ./cmd/external_ids_sync/main.go

# 使用Makefile
cd cmd/external_ids_sync
make build
```

### 配置要求

工具使用项目标准配置文件：
- 开发环境：`config/local.ini`
- 测试环境：`config/test.ini`
- 生产环境：`config/prd.ini`

### 运行示例

```bash
# 开发环境同步
./bin/external_ids_sync --cmd sync-one --show-id 1 --config config/local.ini

# 生产环境批量同步
./bin/external_ids_sync --cmd sync-batch --show-ids "100,101,102" --config config/prd.ini

# 查看统计信息
./bin/external_ids_sync --cmd stats --config config/test.ini
```

## 注意事项

### 使用建议

1. **首次使用**：先用 `--cmd stats` 查看现有数据
2. **批量同步**：控制批次大小，避免API限流
3. **生产环境**：使用 `--force` 参数需谨慎
4. **监控日志**：关注同步失败的记录

### 已知限制

1. **API响应慢**：单次调用约6秒，大批量需要耐心
2. **匹配准确性**：依赖剧集信息完整性
3. **并发限制**：API可能有并发调用限制

### 故障排查

1. **配置错误**：检查配置文件路径和数据库连接
2. **网络问题**：验证API服务可访问性
3. **数据问题**：确保剧集有完整的名称和年份信息
4. **权限问题**：检查数据库用户权限

## 扩展开发

### 添加新平台

如需支持新的外部平台（如豆瓣、烂番茄等）：

1. 修改数据模型，添加新字段
2. 更新API集成逻辑
3. 扩展查询和显示功能

### 性能优化

1. 实现并发同步（使用goroutine池）
2. 添加本地缓存减少API调用
3. 优化数据库查询（添加必要索引）

### 功能增强

1. 添加导出功能（CSV/Excel）
2. 实现增量同步（仅同步新增/更新的剧集）
3. 添加Web界面或API接口

## 相关文档

- [外部ID映射需求文档](../../docs/external_ids_requirements.md)
- [IMDB API集成说明](../../app/api/imdb/README.md)
- [数据库设计文档](../../docs/database_design.md)

## 版本历史

### v1.3.0 (2025-08-13)
- **调试功能**：添加详细的调试日志以诊断匹配失败问题
  - 添加 `--debug` 命令行参数
  - 在 SyncExternalIDs 方法中添加详细日志输出
  - 记录API请求参数和响应内容
  - 显示匹配分数和失败原因
  - 帮助诊断为什么所有匹配都失败的问题

### v1.2.0 (2025-08-13)
- **内存优化**：避免一次性加载所有数据导致内存溢出
  - 限制最大处理剧集数量为10000个
  - 限制最大返回未同步ID为1000个
  - 默认批量同步限制为100个
- **批处理优化**：分批处理避免API超时
  - 每批处理10个剧集（可配置）
  - 批次间添加1秒延迟避免API限流
- **安全机制**：多重保护避免系统过载

### v1.1.0 (2025-08-13)
- 新增同步所有未同步剧集功能
- 添加 `--all` 参数支持批量同步所有
- 添加 `--limit-batch` 限制批量数量
- 添加 `--yes` 自动确认功能
- 添加安全确认机制（超过10个需确认）
- 优化 `GetUnsyncedShowIDs` 方法

### v1.0.0 (2025-08-13)
- 初始版本发布
- 实现基础的同步、查询、搜索、统计功能
- 集成IMDB匹配API
- 支持MySQL主从架构

## 维护说明

- **代码位置**: `/cmd/external_ids_sync/`
- **主要文件**: `main.go` (487行)
- **依赖服务**: IMDB API、MySQL数据库
- **维护周期**: 随业务需求更新