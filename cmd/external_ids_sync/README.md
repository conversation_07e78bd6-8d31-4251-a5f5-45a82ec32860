# 外部ID同步工具

## 功能概述

外部ID同步工具用于管理剧集与外部平台（IMDB、TMDB、Trakt）的ID映射关系。通过调用IMDB API自动匹配剧集信息，获取并存储各平台的唯一标识符。

## 主要功能

- **自动同步**：根据剧名、年份、简介等信息自动匹配外部平台ID
- **批量处理**：支持批量同步多个剧集
- **查询功能**：查询已同步的外部ID信息
- **搜索功能**：根据IMDB或TMDB ID反向查找剧集
- **统计分析**：查看同步状态统计信息

## 构建和安装

```bash
# 进入工具目录
cd cmd/external_ids_sync

# 构建可执行文件
go build -o external_ids_sync

# 或使用Makefile（如果在项目根目录）
make build-external-ids-sync
```

## 使用方法

### 1. 同步单个剧集

```bash
# 同步指定剧集的外部ID
./external_ids_sync --cmd sync-one --show-id 123

# 强制更新已存在的记录
./external_ids_sync --cmd sync-one --show-id 123 --force

# 显示详细信息
./external_ids_sync --cmd sync-one --show-id 123 --verbose
```

### 2. 批量同步

```bash
# 批量同步指定的多个剧集
./external_ids_sync --cmd sync-batch --show-ids "123,456,789"

# 同步所有未同步的剧集
./external_ids_sync --cmd sync-batch --all

# 同步所有，但限制最多100个
./external_ids_sync --cmd sync-batch --all --limit-batch 100

# 同步所有，自动确认（跳过交互确认）
./external_ids_sync --cmd sync-batch --all --yes

# 同步所有，显示详细进度
./external_ids_sync --cmd sync-batch --all --verbose

# 批量同步并显示详细进度
./external_ids_sync --cmd sync-batch --show-ids "123,456,789" --verbose
```

**注意事项**：
- 使用 `--all` 参数时，如果未同步数量超过10个，会要求确认
- 使用 `--yes` 参数可以跳过确认，适合脚本自动化
- 使用 `--limit-batch` 可以限制每次同步的最大数量（建议不超过100）
- 如果不设置 `--limit-batch`，默认限制为100个，避免内存溢出
- `--skip-existing` 默认为true，会跳过已有有效匹配的剧集
- 程序会自动分批处理，每批10个剧集，批次间有1秒延迟

### 3. 查询外部ID

```bash
# 查询单个剧集的外部ID
./external_ids_sync --cmd query --show-id 123

# 批量查询
./external_ids_sync --cmd query --show-ids "123,456,789"

# 查询所有记录（带分页）
./external_ids_sync --cmd query --limit 20 --offset 0

# 显示详细信息（包括匹配理由）
./external_ids_sync --cmd query --show-id 123 --verbose

# 启用调试模式，显示API请求和响应详情
./external_ids_sync --cmd sync-one --show-id 123 --debug
```

### 4. 统计信息

```bash
# 显示同步统计信息
./external_ids_sync --cmd stats
```

输出示例：
```
=== 外部ID映射统计 ===
总记录数: 1500
有效匹配: 1420
无匹配: 80

--- 按来源统计 ---
imdb_api: 1420
manual: 80

--- 按类型统计 ---
movie: 600
show: 900

--- 平台覆盖率 ---
IMDB: 1420 (94.67%)
TMDB: 1380 (92.00%)
Trakt: 1350 (90.00%)
```

### 5. 搜索功能

```bash
# 根据IMDB ID搜索剧集
./external_ids_sync --cmd search --imdb-id tt0903747

# 根据TMDB ID搜索剧集
./external_ids_sync --cmd search --tmdb-id 1396
```

## 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--config` | 配置文件路径 | `./config/local.ini` |
| `--cmd` | 执行命令 | 必填 |
| `--show-id` | 单个剧集ID | - |
| `--show-ids` | 剧集ID列表（逗号分隔） | - |
| `--all` | 同步所有未同步的剧集 | false |
| `--limit-batch` | 限制批量同步的最大数量 | 0（不限制） |
| `--skip-existing` | 跳过已有记录的剧集 | true |
| `--yes` | 自动确认操作 | false |
| `--imdb-id` | IMDB ID（用于搜索） | - |
| `--tmdb-id` | TMDB ID（用于搜索） | - |
| `--limit` | 查询限制 | 100 |
| `--offset` | 查询偏移量 | 0 |
| `--force` | 强制更新已存在的记录 | false |
| `--verbose` | 显示详细信息 | false |
| `--debug` | 启用调试模式，显示API详情 | false |

## 配置文件

工具使用项目的标准配置文件，默认路径为 `./config/local.ini`。

可以通过 `--config` 参数指定不同的配置文件：

```bash
# 使用测试环境配置
./external_ids_sync --cmd stats --config ./config/test.ini

# 使用生产环境配置
./external_ids_sync --cmd stats --config ./config/prd.ini
```

## 数据库表结构

外部ID信息存储在 `content_show_external_ids` 表中：

| 字段 | 类型 | 说明 |
|------|------|------|
| show_id | uint64 | 剧集ID |
| imdb_id | varchar(20) | IMDB ID |
| tmdb_id | uint32 | TMDB ID |
| trakt_id | uint32 | Trakt ID |
| slug | varchar(255) | Slug标识符 |
| match_type | varchar(10) | 匹配类型（movie/show） |
| match_score | decimal(5,2) | 匹配分数（0-100） |
| match_reason | text | 匹配理由 |
| is_match | tinyint | 是否有效匹配 |
| source | varchar(50) | 数据来源 |
| raw_response | json | 原始API响应 |

## 常见问题

### Q: 如何诊断匹配失败的原因？

A: 使用 `--debug` 参数查看详细的API交互：
```bash
# 单个剧集调试
./external_ids_sync --cmd sync-one --show-id 123 --debug

# 批量同步调试
./external_ids_sync --cmd sync-batch --show-ids "123,456" --debug
```

调试信息包括：
- 剧集原始信息（名称、年份、简介等）
- 提取的参数（年份、语言、类型等）
- API请求体
- API响应内容
- 匹配分数和失败原因

### Q: API响应超时怎么办？

A: IMDB API响应可能较慢（约6秒），批量同步时建议：
- 分批处理，每批不超过50个
- 使用 `--verbose` 查看进度
- 考虑在低峰期运行

### Q: 如何处理同步失败的剧集？

A: 可以通过以下步骤处理：
1. 使用 `--verbose` 查看失败原因
2. 检查剧集信息是否完整（名称、年份、简介）
3. 尝试单独同步失败的剧集
4. 必要时使用API端点手动更新

### Q: 同步的数据不准确怎么办？

A: 可以：
1. 使用 `--force` 强制重新同步
2. 通过API端点手动修正
3. 检查剧集的原始信息是否正确

## 性能优化建议

1. **批量同步优化**：
   - 建议每批处理20-50个剧集
   - 避免在高峰期运行大批量同步

2. **缓存利用**：
   - 已同步的剧集默认不会重复调用API
   - 使用 `--force` 参数可强制更新

3. **数据库优化**：
   - 表已建立必要索引
   - 定期清理无效记录

## 扩展开发

### 添加新的外部平台

1. 在 `MediaIDs` 结构体中添加新字段
2. 更新数据库表结构
3. 在匹配服务中集成新平台的API
4. 更新显示和查询逻辑

### 自定义匹配规则

可以修改 `app/api/imdb/match.go` 中的匹配逻辑：
- 调整最小匹配分数（默认90分）
- 自定义内容类型判断规则
- 添加特殊处理逻辑

## 相关文档

- [外部ID映射API文档](../../docs/external_ids_api.md)
- [IMDB API集成说明](../../app/api/imdb/README.md)
- [项目主文档](../../README.md)