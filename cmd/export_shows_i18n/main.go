package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"time"

	"vlab/app/common/dbs"
	showDto "vlab/app/dto/show"
	showService "vlab/app/service/show"
	"vlab/config"
	"vlab/pkg/redis"

	"github.com/gin-gonic/gin"
)

// 导出show记录的多语言JSON数据的高性能脚本
func main() {
	// 命令行参数
	var (
		configPath   = flag.String("config", "", "配置文件路径 (如: config/dev.ini)")
		outputPath   = flag.String("output", "shows_i18n_export.json", "输出文件路径")
		batchSize    = flag.Int("batch", 1000, "批次大小")
		includeEmpty = flag.Bool("empty", false, "是否包含空翻译值")
		showProgress = flag.Bool("progress", true, "是否显示进度")
		status       = flag.Int("status", -1, "过滤状态 (0=禁用, 1=启用, -1=所有)")
	)
	flag.Parse()

	fmt.Println("=== Show多语言数据导出工具 ===")
	if *configPath != "" {
		fmt.Printf("配置文件: %s\n", *configPath)
	} else {
		fmt.Println("配置文件: 默认配置")
	}
	fmt.Printf("输出文件: %s\n", *outputPath)
	fmt.Printf("批次大小: %d\n", *batchSize)
	fmt.Printf("包含空值: %v\n", *includeEmpty)
	if *status >= 0 {
		fmt.Printf("状态过滤: %d\n", *status)
	} else {
		fmt.Println("状态过滤: 所有")
	}
	fmt.Println("开始初始化...")

	// 初始化配置
	if *configPath != "" {
		config.SetupWithPath(*configPath)
	} else {
		config.Setup()
	}
	dbs.Setup()
	redis.Setup()

	// 获取数据库连接
	mysqlEngine := dbs.NewMysqlEngines()
	if mysqlEngine == nil {
		log.Fatalf("Failed to initialize MySQL")
	}

	// 创建show service
	showSvc := showService.GetService()

	// 创建gin context
	ctx := &gin.Context{}
	ctx.Set("context", context.Background())

	// 设置导出配置
	exportConfig := &showDto.ShowI18nExportConfig{
		BatchSize:    *batchSize,
		OutputPath:   *outputPath,
		IncludeEmpty: *includeEmpty,
	}

	// 设置状态过滤
	if *status >= 0 {
		statusValue := uint32(*status)
		exportConfig.Status = &statusValue
	}

	// 设置进度回调函数
	if *showProgress {
		startTime := time.Now()
		exportConfig.ProgressFunc = func(current, total int64, message string) {
			if total > 0 {
				progress := float64(current) / float64(total) * 100
				elapsed := time.Since(startTime)

				if current > 0 {
					estimatedTotal := time.Duration(float64(elapsed) / float64(current) * float64(total))
					remaining := estimatedTotal - elapsed

					fmt.Printf("\r进度: %.1f%% (%d/%d) | 耗时: %v | 预计剩余: %v | %s",
						progress, current, total,
						elapsed.Round(time.Second),
						remaining.Round(time.Second),
						message)
				} else {
					fmt.Printf("\r%s", message)
				}
			} else {
				fmt.Printf("\r%s", message)
			}

			if current == total {
				fmt.Printf("\n✅ %s\n", message)
			}
		}
	}

	// 开始导出
	fmt.Println("开始导出数据...")
	startTime := time.Now()

	err := showSvc.ExportShowsWithI18nToFile(ctx, exportConfig)
	if err != nil {
		log.Fatalf("❌ 导出失败: %v", err)
	}

	duration := time.Since(startTime)
	fmt.Printf("\n🎉 导出完成！\n")
	fmt.Printf("📁 文件路径: %s\n", *outputPath)
	fmt.Printf("⏱️  总耗时: %v\n", duration.Round(time.Second))

	// 显示文件信息
	if fileInfo, err := os.Stat(*outputPath); err == nil {
		fmt.Printf("📊 文件大小: %.2f MB\n", float64(fileInfo.Size())/1024/1024)
	}
}
