{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__mcp-feedback-enhanced__interactive_feedback", "Bash(find:*)", "WebFetch(domain:www.volcengine.com)", "Bash(go list:*)", "<PERSON><PERSON>(chmod:*)", "Bash(go build:*)", "<PERSON><PERSON>(go doc:*)", "Bash(rm:*)", "Bash(ls:*)", "mcp__sequentialthinking__sequentialthinking", "Bash(grep:*)", "WebFetch(domain:pkg.go.dev)", "WebFetch(domain:docs.byteplus.com)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(touch:*)", "mcp__fetch__imageFetch", "<PERSON><PERSON>(mkdir:*)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(echo:*)", "Bash(./bin/export_shows_i18n:*)", "Bash(./bin/vector_uploader:*)", "Bash(nc:*)", "Bash(./bin/external_ids_sync:*)"], "deny": []}}