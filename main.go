package main

import (
	"context"
	"fmt"
	"net/http"
	"time"
	"vlab/app/common/dbs"
	"vlab/app/common/srv"
	"vlab/app/job"
	"vlab/config"
	"vlab/pkg/email"
	"vlab/pkg/helper"
	"vlab/pkg/log"
	"vlab/pkg/redis"
	"vlab/pkg/util/ctxUtil"
	"vlab/router"

	"github.com/gin-gonic/gin"
)

func init() {
	config.Setup()
	dbs.Setup()
	redis.Setup()
	helper.Setup()
	email.Setup()
}

func main() {
	ctx, cancel := context.WithCancel(ctxUtil.NewRequestID(context.Background()))
	defer cancel()

	ginRouter := gin.Default()
	router.LoadRouter(ginRouter)

	ginSrv := &helper.GinSrv{HttpSrv: &http.Server{
		Addr:         fmt.Sprintf(":%v", config.AppCfg.HttpPort),
		Handler:      ginRouter,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
	}}
	logSrv, jobSrv := log.GetAsyncLogger(), job.GetJob()

	appSrvMgr := helper.GetAppSrvMgr(ctx)
	appSrvMgr.Register(logSrv, ginSrv, jobSrv)

	if err := appSrvMgr.Start(ctx); err != nil {
		fmt.Printf("Failed to start services: %v\n", err)
		return
	}

	//es.Setup()
	//mq.Setup()
	//service.EventInit()
	//profile.InitPyroscope("mall")

	srv.GracefullyShutdown(ctx, appSrvMgr)
}
