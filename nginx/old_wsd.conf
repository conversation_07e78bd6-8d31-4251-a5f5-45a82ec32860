
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 4096;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    include /etc/nginx/conf.d/*.conf;

    server {
        listen 80;
        server_name your-domain.com;  # 使用你的域名或服务器的 IP

        root /opt/app/fe_wsd_admin_dist;

        index index.html index.htm;  # 指定默认首页文件

        location / {
            try_files $uri $uri/ /index.html;
        }
    }

    server {
        listen 8888;
        server_name your-domain.com;  # 使用你的域名或服务器的 IP

        root /opt/app/fe_wsd_admin/dist_test;

        index index.html index.htm;  # 指定默认首页文件

        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
