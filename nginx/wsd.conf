server {
    listen       80;
    server_name  test.yuexia-goods.com;

    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /opt/app/fe_wsd_admin_dist;
        index  index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    
    # 后端服务配置
    location /wsd_mall/ {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # CORS 配置（如果前端需要跨域访问后端，可以启用）
        # add_header Access-Control-Allow-Origin *;
        # add_header Access-Control-Allow-Methods GET,POST,PUT,DELETE,OPTIONS;
        # add_header Access-Control-Allow-Headers Content-Type,Authorization;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }


    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}

