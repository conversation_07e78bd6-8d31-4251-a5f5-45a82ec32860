## 一、安装yum-utils
```
sudo yum install -y yum-utils
```

## 二、配置Nginx的Yum源
使用官方提供的 Nginx 仓库配置文件来设置 YUM 源。可以创建一个新的 .repo 文件，或者直接编辑现有的 Nginx 仓库文件
```
sudo vim /etc/yum.repos.d/nginx.repo
```
在文件中添加以下内容（以稳定版为例）

```
[nginx-stable]
name=nginx stable repo
baseurl=http://nginx.org/packages/centos/$releasever/$basearch/
gpgcheck=1
enabled=1
gpgkey=https://nginx.org/keys/nginx_signing.key
module_hotfixes=true 
``` 

 如果希望安装主线版（包含最新功能，但可能不如稳定版稳定），可以将 [nginx-stable] 改为 [nginx-mainline]，并相应地调整 baseurl


## 三、清理缓存并列出可用版本
在配置完仓库后，运行以下命令来清理 YUM 缓存并列出所有可用的 Nginx 版本

```
sudo yum clean all
sudo yum makecache
yum list nginx --showduplicates | sort -r
```

## 四、安装指定版本的Nginx
```
sudo yum install -y nginx-1.26.1-1.el7.ngx.x86_64
```

## 五、启动并配置Nginx
安装完成后，可以使用以下命令来启动 Nginx 服务，并设置它开机自启。
```
sudo systemctl start nginx
sudo systemctl enable nginx
sudo systemctl status nginx
```