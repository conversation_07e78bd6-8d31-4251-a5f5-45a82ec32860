create table if not exists vlab.content_banner
(
    id         bigint auto_increment comment 'ID'
        primary key,
    title      varchar(255)                           not null comment '标题',
    title_key  varchar(255) default ''                not null comment '标题多语言',
    cover      varchar(255)                           not null comment '封面',
    jump_type  tinyint(1)   default 1                 not null comment '跳转类型 1:商品 2:链接',
    jump       varchar(500) default ''                not null comment '跳转地址',
    position   tinyint(1)   default 1                 not null comment '位置 1: 主页 2: 卡片',
    sort       int          default 0                 not null comment '排序值',
    status     tinyint(1)   default 1                 not null comment '状态',
    is_deleted tinyint(1)   default 0                 not null comment '是否删除，0未删除，1已删除',
    created_at datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment 'Banner表';

create index banner_idx_key
    on vlab.content_banner (title_key);

create table if not exists vlab.content_credit
(
    id          int(11) unsigned auto_increment
        primary key,
    person_id   int                                           null comment '关联人物ID',
    show_id     int                                           null comment '关联节目ID',
    job         int                 default 0                 not null,
    department  varchar(64)                                   null comment '部门',
    `character` varchar(64)                                   null comment '角色',
    `order`     int                                           null comment '排序',
    is_deleted  tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at  timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at  timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '工作人员表';

create index credit_idx_person
    on vlab.content_credit (person_id);

create index credit_idx_show
    on vlab.content_credit (show_id);

create table if not exists vlab.content_episode
(
    id             int(11) unsigned auto_increment
        primary key,
    status         tinyint             default 0                 not null,
    name           varchar(64)                                   not null comment '剧集名称',
    name_key       varchar(64)         default ''                not null comment '名称唯一键',
    episode_number int                                           null comment '剧集编号',
    show_id        int                                           null comment '关联节目ID',
    season_id      int                                           null comment '关联季ID',
    mapping_id     int                          default 0                 not null comment '关联映射ID',
    mapping_type   int      default 0                               not null comment '映射类型',
    is_deleted     tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at     timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at     timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '剧集表';

create index episode_idx_name_key
    on vlab.content_episode (name_key);

create index episode_idx_show
    on vlab.content_episode (show_id);

create table if not exists vlab.content_franchise
(
    id         int(11) unsigned auto_increment
        primary key,
    status     tinyint             default 0                 not null,
    name       varchar(64)                                   null comment '系列名称',
    name_key   varchar(64)         default ''                not null comment '名称唯一键',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '系列表';

create index franchise_idx_name_key
    on vlab.content_franchise (name_key);

create table if not exists vlab.content_genre
(
    id         int(11) unsigned auto_increment
        primary key,
    status     tinyint             default 0                 not null,
    name       varchar(64)                                   not null comment '类型名称',
    name_key   varchar(64)         default ''                not null comment '名称唯一键',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '类型表';

create index genre_idx_name_key
    on vlab.content_genre (name_key);

create table if not exists vlab.content_i18n
(
    id         int(11) unsigned auto_increment
        primary key,
    `key`      varchar(255)                                  not null comment '多语言键',
    iso_639_1  varchar(20)                                   null comment '语言代码',
    `table`    varchar(64)                                   not null comment '关联表',
    `column`   varchar(64)                                   null comment '关联列',
    value      varchar(255)                                  null comment '值',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '国际化表';

create index i18n_idx_key
    on vlab.content_i18n (`key`, iso_639_1);

create index i18n_idx_table_column
    on vlab.content_i18n (`table`, `column`);

create table if not exists vlab.content_image
(
    id           int(11) unsigned auto_increment
        primary key,
    aspect_ratio float                                         null comment '图片比例',
    aspect_type  int                                           not null,
    height       int                                           null comment '高度',
    width        int                                           null comment '宽度',
    file_path    varchar(255)                                  null comment '图片路径',
    iso_639_1    varchar(20)                                   null comment '语言',
    is_deleted   tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at   timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '图片表';

create table if not exists vlab.content_person
(
    id         int(11) unsigned auto_increment
        primary key,
    status     tinyint             default 0                 not null,
    name       varchar(64)                                   null comment '人物原名',
    name_key   varchar(64)         default ''                not null comment '唯一键',
    gender     tinyint                                       null comment '性别 1-女性 2-男性 3-未知',
    profile_id int                 default 0                 not null comment '头像图片ID',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '人物表';

create index person_idx_name_key
    on vlab.content_person (name_key);

create index person_idx_profile
    on vlab.content_person (profile_id);

create table if not exists vlab.content_popular
(
    id         int(11) unsigned auto_increment
        primary key,
    status     tinyint             default 0                 not null comment '状态 1-正常 2-已删除',
    show_id    int                 default 0                 not null comment '关联节目ID',
    heat       int                 default 0                 not null comment '关联节目ID',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '热门表';

create index popular_idx_show
    on vlab.content_popular (show_id);

create table if not exists vlab.content_poster
(
    id         int(11) unsigned auto_increment
        primary key,
    show_id    int                 default 0                 not null comment '关联节目ID',
    season_id  int                 default 0                 not null comment '关联季ID',
    image_id   int                 default 0                 not null comment '关联图片ID',
    iso_639_1  varchar(20)         default ''                not null comment '语言代码',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '海报表';

create index poster_idx_image
    on vlab.content_poster (image_id, iso_639_1);

create index poster_idx_show
    on vlab.content_poster (show_id);

create table if not exists vlab.content_recommend
(
    id         int(11) unsigned auto_increment
        primary key,
    status     tinyint             default 0                 not null comment '状态 1-正常 2-已删除',
    name       varchar(64)                                   null comment '名称',
    name_key   varchar(64)         default ''                not null comment '名称唯一键',
    show_id    int                 default 0                 not null comment '关联节目ID',
    image_id   int                 default 0                 not null comment '图片ID',
    `order`    int                                           null comment '排序',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '推荐表';

create index recommend_idx_image
    on vlab.content_recommend (image_id);

create index recommend_idx_name_key
    on vlab.content_recommend (name_key);

create index recommend_idx_show
    on vlab.content_recommend (show_id);

create table if not exists vlab.content_season
(
    id            int(11) unsigned auto_increment
        primary key,
    show_id       int                                           null comment '关联节目ID',
    name          varchar(64)                                   null comment '季名称',
    name_key      varchar(64)                                   null comment '名称唯一键',
    overview      varchar(500)                                  null comment '简介',
    overview_key  varchar(64)                                   null comment '简介唯一键',
    season_number int                                           null comment '季编号',
    is_deleted    tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at    timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '季表';

create table if not exists vlab.content_show
(
    id            int(11) unsigned auto_increment
        primary key,
    status        tinyint             default 0                 not null,
    name          varchar(64)                                   null comment '节目名称',
    name_key      varchar(64)         default ''                not null comment '名称唯一键',
    overview      varchar(500)                                  null comment '简介',
    overview_key  varchar(64)         default ''                null comment '简介唯一键',
    langs         varchar(64)                                   null,
    air_date      varchar(20)                                   null comment '首播日期',
    air_date_key  varchar(64)         default ''                not null comment '首播日期',
    content_type  int                                           null comment '内容类型',
    in_production tinyint                                       null comment '是否在制作',
    score         int                                           null comment '评分',
    franchise_id  int                 default 0                 not null,
    is_deleted    tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at    timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at    timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    homepage      varchar(255)                                  null
)
    comment '节目表';

create index show_idx_date_key
    on vlab.content_show (air_date_key);

create index show_idx_franchise
    on vlab.content_show (franchise_id);

create index show_idx_name_key
    on vlab.content_show (name_key);

create index show_idx_overview_key
    on vlab.content_show (overview_key);

create table if not exists vlab.content_show_with_franchise
(
    id           int(11) unsigned auto_increment
        primary key,
    show_id      int                 default 0                 not null comment '关联节目ID',
    franchise_id int                 default 0                 not null comment '关联系列ID',
    type         int                                           null comment '类型',
    is_deleted   tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at   timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at   timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目与系列关系表';

create index show_franchise_idx_id
    on vlab.content_show_with_franchise (franchise_id);

create index show_franchise_idx_show
    on vlab.content_show_with_franchise (show_id);

create table if not exists vlab.content_show_with_genre
(
    id         int(11) unsigned auto_increment
        primary key,
    show_id    int                 default 0                 not null comment '关联节目ID',
    genre_id   int                 default 0                 not null comment '关联类型ID',
    type       int                                           null comment '类型',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目与类型关系表';

create index show_genre_idx_genre
    on vlab.content_show_with_genre (genre_id);

create index show_genre_idx_show
    on vlab.content_show_with_genre (show_id);

create table if not exists vlab.content_subtitle
(
    id         int(11) unsigned auto_increment
        primary key,
    iso_639_1  varchar(20)         default ''                not null comment '语言代码',
    file_path  varchar(255)                                  null comment '字幕文件路径',
    video_id   int                 default 0                 not null comment '关联视频ID',
    episode_id int                 default 0                 not null comment '关联视频ID',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '字幕表';

create index subtitle_idx_episode
    on vlab.content_subtitle (episode_id, video_id);

create table if not exists vlab.content_video
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(64)                                   null comment '视频名称',
    runtime    int                                           null comment '时长（分钟）',
    image_id   int                 default 0                 not null comment '关联图片ID',
    video_path varchar(255)                                  null comment '视频文件路径',
    video_etag varchar(255)        default ''                not null comment '视频文件tag',
    path_type  tinyint default 0 not null comment '视频路径类型 1-直接的视频地址',
    episode_id int                 default 0                 not null comment '关联剧集ID',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '视频表';

create index video_idx_episode
    on vlab.content_video (episode_id);

create index video_idx_etag
    on vlab.content_video (video_etag);

create index video_idx_image
    on vlab.content_video (image_id);

create table if not exists vlab.content_video_mps
(
    id               bigint unsigned auto_increment
        primary key,
    video_id         bigint              default 0                 not null comment '视频id',
    resolution       tinyint             default 0                 not null comment '分辨率',
    transcode_format tinyint             default 0                 not null comment '转码格式 1m3u8 2mp4',
    status           tinyint             default 0                 not null comment '状态 1进行中 2已完成 失败',
    video_key        varchar(255)        default ''                not null comment 'oss中的视频key',
    mps_video_key    varchar(255)        default ''                not null comment '转码后oss中的视频key',
    job_id           varchar(128)        default ''                not null comment '任务id',
    temp_id          varchar(128)        default ''                not null comment '模版id',
    retry            tinyint             default 0                 not null comment '重试次数',
    is_deleted       tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at       timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at       timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '视频转码表';

create table if not exists vlab.content_show_with_limit
(
    id         int(11) unsigned auto_increment
        primary key,
    show_id    int                 default 0                 not null comment '关联节目ID',
    limit_id int                 default 0                 not null comment '关联频道ID',
    type       int                                           null comment '类型 1-channel 2-version',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目过滤关系表';

create index show_limit_idx_limit
    on vlab.content_show_with_limit (limit_id);

create index show_limit_idx_show
    on vlab.content_show_with_limit (show_id);



create table if not exists vlab.content_class
(
    id         int(11) unsigned auto_increment
        primary key,
    name       varchar(64)         default ''                not null comment '分类名称',
    name_key   varchar(64)         default ''                not null comment '名称唯一键',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '分类表';


create table if not exists vlab.content_class_field
(
    id        int(11) unsigned auto_increment
        primary key,
    class_id   int                                           null comment '关联分类ID',
    name       varchar(64)         default ''                not null comment '字段名称',
    parent_id  int                                           null comment '父字段ID',
    name_key   varchar(64)         default ''                not null comment '名称唯一键',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '分类字段表';


create table if not exists vlab.content_show_with_class
(
    id         int(11) unsigned auto_increment
        primary key,
    show_id    int                 default 0                 not null comment '关联节目ID',
    class_id   int                 default 0                 not null comment '关联分类ID',
    field_id   int                 default 0                 not null comment '关联字段ID',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目分类关系表';

create table if not exists vlab.content_show_with_mapping
(
    id        int(11) unsigned auto_increment
        primary key,
    show_id   int                 default 0                 not null comment '关联节目ID',
    mapping_id int                 default 0                 not null comment '关联映射ID',
    mapping_type int                 default 0                 not null comment '映射类型',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目映射关系表';

create table if not exists vlab.content_show_search_history
(
    id         int(11) unsigned auto_increment
        primary key,
    user_id    int                 default 0                 not null comment '用户ID',
    search_content json                        null comment '搜索关键字',
    is_deleted tinyint(4) unsigned default 0                 not null comment '软删 0未删除 1已删除',
    created_at timestamp           default CURRENT_TIMESTAMP not null comment '创建时间',
    updated_at timestamp           default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '节目搜索历史表';

