
CREATE TABLE `content_credit` (
                          `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                          `person_id` int(11) NULL COMMENT '关联人物ID',
                          `show_id` int(11) NULL COMMENT '关联节目ID',
    `job` int(11) NOT NULL DEFAULT 0 COMMENT '职位',
#                           `job` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '职位',
                          `department` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '部门',
                          `character` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '角色',
                          `order` int(11) NULL COMMENT '排序',
                          `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                          `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                          `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工作人员表';

CREATE TABLE `content_episode` (
                           `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                           `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '剧集名称',
                           `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称唯一键',
                           `episode_number` int(11) NULL COMMENT '剧集编号',
                           `show_id` int(11) NULL COMMENT '关联节目ID',
                           `season_id` int(11) NULL COMMENT '关联季ID',
                           `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                           `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                           `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                           PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '剧集表';

CREATE TABLE `content_franchise` (
                             `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                             `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '系列名称',
                             `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '名称唯一键',
                             `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                             `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                             `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                             PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系列表';

CREATE TABLE `content_genre` (
                         `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                         `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型名称',
                         `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称唯一键',
                         `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                         `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                         `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '类型表';

CREATE TABLE `content_i18n` (
                        `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                        `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '多语言键',
                        `iso_639_1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '语言代码',
                        `table` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '关联表',
                        `column` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '关联列',
                        `value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '值',
                        `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                        `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                        `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国际化表';

CREATE TABLE `content_image` (
                         `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                         `aspect_ratio` float NULL COMMENT '图片比例',
                         `height` int(11) NULL COMMENT '高度',
                         `width` int(11) NULL COMMENT '宽度',
                         `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片路径',
                         `iso_639_1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '语言',
                         `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                         `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                         `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图片表';

CREATE TABLE `content_person` (
                          `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',
                          `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '人物原名',
                          `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '唯一键',
                          `gender` tinyint(4) NULL COMMENT '性别 1-女性 2-男性 3-未知',
                          `image_id` int(11) NULL COMMENT '头像图片ID',
                          `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                          `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                          `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人物表';

CREATE TABLE `content_poster` (
                          `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                          `show_id` int(11) NULL COMMENT '关联节目ID',
                          `season_id` int(11) NULL COMMENT '关联季ID',
                          `image_id` int(11) NULL COMMENT '关联图片ID',
                          `iso_639_1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '语言代码',
                          `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                          `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                          `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '海报表';

CREATE TABLE `content_season` (
                          `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                          `show_id` int(11) NULL COMMENT '关联节目ID',
                          `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '季名称',
                          `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '名称唯一键',
                          `overview` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介',
                          `overview_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介唯一键',
                          `season_number` int(11) NULL COMMENT '季编号',
                          `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                          `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                          `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                          PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '季表';

CREATE TABLE `content_show` (
                        `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                        `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',
                        `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '节目名称',
                        `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '名称唯一键',
                        `overview` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介',
                        `overview_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '简介唯一键',
                        `air_date` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '首播日期',
                        `content_type` int(11) NULL COMMENT '内容类型',
                        `in_production` tinyint(4) NULL COMMENT '是否在制作',
                        `score` int(11) NULL COMMENT '评分',
                        `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                        `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                        `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '节目表';

CREATE TABLE `content_show_with_franchise` (
                                        `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                                        `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',
                                        `show_id` int(11) NULL COMMENT '关联节目ID',
                                        `franchise_id` int(11) NULL COMMENT '关联系列ID',
                                        `type` int(11) NULL COMMENT '类型',
                                        `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                                        `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                        `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '节目与系列关系表';

CREATE TABLE `content_show_with_genre` (
                                    `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                                    `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',
                                    `show_id` int(11) NULL COMMENT '关联节目ID',
                                    `genre_id` int(11) NULL COMMENT '关联类型ID',
                                    `type` int(11) NULL COMMENT '类型',
                                    `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                                    `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                    `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '节目与类型关系表';

CREATE TABLE `content_subtitle` (
                            `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                            `iso_639_1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '语言代码',
                            `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '字幕文件路径',
                            `video_id` int(11) NULL COMMENT '关联视频ID',
                            `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                            `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                            `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                            PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字幕表';

CREATE TABLE `content_video` (
                         `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                         `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '视频名称',
                         `runtime` int(11) NULL COMMENT '时长（分钟）',
                         `image_id` int(11) NULL COMMENT '关联图片ID',
                         `video_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '视频文件路径',
                         `episode_id` int(11) NULL COMMENT '关联剧集ID',
                         `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                         `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                         `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                         PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '视频表';

CREATE TABLE `content_popular` (
                                   `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                                   `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',

                                   `show_id` int(11) NULL COMMENT '关联节目ID',
                                   `heat` int(11) NOT NULL DEFAULT 0 COMMENT '关联节目ID',


                                   `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                                   `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                   `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                   PRIMARY KEY (`id`) USING BTREE

) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '热门表';


CREATE TABLE `content_recommend` (
                                     `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
                                     `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态 1-正常 2-已删除',
                                     `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '名称',
                                     `name_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '名称唯一键',
                                     `show_id` int(11) NULL COMMENT '关联节目ID',
                                     `order` int(11) NULL COMMENT '排序',


                                     `is_deleted` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '软删 0未删除 1已删除',
                                     `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
                                     `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
                                     PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '推荐表';



CREATE TABLE `content_banner` (
                          `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                          `created_at` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL COMMENT '创建时间',
                          `updated_at` datetime DEFAULT CURRENT_TIMESTAMP NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                          `is_deleted` tinyint(1) DEFAULT 0 NOT NULL COMMENT '是否删除，0未删除，1已删除',
                          `title` varchar(255) NOT NULL COMMENT '标题',
    `title_key` varchar(255) DEFAULT '' NOT NULL COMMENT '标题多语言',
                          `cover` varchar(255) NOT NULL COMMENT '封面',
                          `jump_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '跳转类型 1:商品 2:链接',
                          `jump` varchar(500) NOT NULL DEFAULT '' COMMENT '跳转地址',
                          `position` tinyint(1) NOT NULL DEFAULT 1 COMMENT '位置 1: 主页 2: 卡片',
                          `sort` int(11) DEFAULT 0 NOT NULL COMMENT '排序值',
                          `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态',
                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='Banner表';

