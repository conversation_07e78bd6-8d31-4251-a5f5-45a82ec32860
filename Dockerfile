FROM golang:alpine as builder

WORKDIR /build

ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn/,direct

#RUN apk update --no-cache \
#    && apk upgrade \
#    && apk add --no-cache bash \
#            bash-doc \
#            bash-completion \
#    && apk add --no-cache tzdata \
#    && rm -rf /var/cache/apk/*

COPY . .

# 添加git，用于获取commit信息
RUN apk add --no-cache git

# 构建时注入版本信息
RUN BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") && \
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown") && \
    go build -ldflags "-X vlab/router.buildTime=${BUILD_TIME} -X vlab/router.gitCommit=${GIT_COMMIT}" -o main ./main.go

FROM alpine:latest as prod

# TODO(cqf): 优化配置参数
ENV config ./config/test.ini

#RUN apk update --no-cache \
#    && apk upgrade

RUN  mkdir -pv /data/tsf
RUN  mkdir -pv /data/tsf/config
RUN  mkdir -pv /data/tsf/cmd

COPY --from=builder /build/main /data/tsf/cmd
COPY --from=builder /build/config/test.ini  /data/tsf/cmd/config/test.ini

WORKDIR /data/tsf/cmd

CMD  ["./main"]
