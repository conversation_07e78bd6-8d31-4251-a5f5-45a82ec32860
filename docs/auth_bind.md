# VLab 账号绑定机制设计文档

## 概述

VLab 账号绑定系统是一个完整的多认证方式绑定解决方案，允许用户在同一账号下绑定多种登录方式（苹果、谷歌、邮箱），实现一个用户账号多种登录方式的无缝切换。

## 系统设计

### 核心概念

**一个用户在一个 channel + platform 下可以拥有多种认证方式：**
- 苹果登录 (AtApple = 1)
- 谷歌登录 (AtGoogle = 2)  
- 邮箱登录 (AtEmail = 4)

### 数据模型

#### user_auth 表结构
```sql
CREATE TABLE `user_auth` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `channel_id` int(10) unsigned NOT NULL COMMENT '渠道ID',
  `platform_id` int(10) unsigned NOT NULL COMMENT '平台ID',
  `auth_type` int(10) unsigned NOT NULL COMMENT '认证类型 1-苹果 2-谷歌 4-邮箱',
  `auth_uid` varchar(255) NOT NULL COMMENT '认证唯一标识',
  `auth_token` text COMMENT '认证token',
  `auth_email` varchar(255) DEFAULT NULL COMMENT '认证邮箱',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `udx_user_auth` (`auth_type`, `platform_id`, `auth_uid`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## API 接口

### 1. 获取用户认证方式列表
- **路径**: `GET /user/auth/methods`
- **权限**: 需要用户登录
- **响应**: 
  ```json
  {
    "code": "200",
    "message": "success",
    "data": {
      "auth_methods": [
        {
          "auth_type": 1,
          "auth_type_name": "苹果登录",
          "auth_uid": "001234.abc123",
          "auth_email": "<EMAIL>",
          "is_bound": true,
          "bound_at": "2024-01-15 10:30:00"
        },
        {
          "auth_type": 2,
          "auth_type_name": "谷歌登录",
          "auth_uid": "",
          "auth_email": "",
          "is_bound": false,
          "bound_at": ""
        },
        {
          "auth_type": 4,
          "auth_type_name": "邮箱登录",
          "auth_uid": "<EMAIL>",
          "auth_email": "<EMAIL>",
          "is_bound": true,
          "bound_at": "2024-01-20 15:45:00"
        }
      ]
    }
  }
  ```

### 2. 绑定苹果账号
- **路径**: `POST /user/bind/apple`
- **权限**: 需要用户登录
- **参数**: 
  ```json
  {
    "auth_token": "apple_identity_token_here"
  }
  ```
- **功能**: 验证苹果token并绑定到当前用户

### 3. 绑定谷歌账号
- **路径**: `POST /user/bind/google`
- **权限**: 需要用户登录
- **参数**: 
  ```json
  {
    "auth_token": "google_id_token_here"
  }
  ```
- **功能**: 验证谷歌token并绑定到当前用户

### 4. 绑定邮箱账号
- **路径**: `POST /user/bind/email`
- **权限**: 需要用户登录
- **参数**: 
  ```json
  {
    "email": "<EMAIL>",
    "code": "123456",
    "password": "newpassword123"
  }
  ```
- **功能**: 验证邮箱验证码并绑定邮箱，设置或更新密码

### 5. 解绑认证方式
- **路径**: `DELETE /user/auth/unbind/:auth_type`
- **权限**: 需要用户登录
- **参数**: 
  ```json
  {
    "password": "current_password"
  }
  ```
- **功能**: 解绑指定认证方式（用户至少需要保留一种认证方式）

## 流程图

### 绑定流程图
```mermaid
graph TD
    A[用户发起绑定请求] --> B{选择绑定类型}
    
    B -->|苹果账号| C[验证Apple Identity Token]
    B -->|谷歌账号| D[验证Google ID Token]
    B -->|邮箱账号| E[验证邮箱验证码]
    
    C --> F[检查绑定冲突]
    D --> F
    E --> F
    
    F --> G{是否有冲突?}
    G -->|是| H[返回冲突错误]
    G -->|否| I[创建认证记录]
    
    I --> J[更新用户信息]
    J --> K[返回绑定成功]
```

### 解绑流程图
```mermaid
graph TD
    A[用户发起解绑请求] --> B[验证用户密码]
    B --> C{密码正确?}
    C -->|否| D[返回密码错误]
    C -->|是| E[检查认证方式数量]
    E --> F{是否为最后一种?}
    F -->|是| G[返回无法解绑错误]
    F -->|否| H[删除认证记录]
    H --> I[返回解绑成功]
```

### 系统架构图
```mermaid
graph TB
    subgraph "绑定系统架构"
        A1[Handler层<br/>HTTP请求处理]
        A2[Service层<br/>业务逻辑处理]
        A3[DAO层<br/>数据访问]
        A4[第三方认证<br/>Apple/Google API]
        A5[邮箱服务<br/>验证码验证]
        
        A1 --> A2
        A2 --> A3
        A2 --> A4
        A2 --> A5
    end
    
    subgraph "数据存储"
        B1[MySQL<br/>用户认证数据]
        B2[Redis<br/>验证码缓存]
        
        A3 --> B1
        A5 --> B2
    end
    
    subgraph "安全机制"
        C1[冲突检测<br/>防止重复绑定]
        C2[token验证<br/>第三方认证]
        C3[验证码验证<br/>邮箱安全]
        C4[密码验证<br/>解绑保护]
        
        A2 --> C1
        A2 --> C2
        A2 --> C3
        A2 --> C4
    end
```

## 技术实现

### 核心验证逻辑

#### 绑定冲突检测
```go
func (s *AuthBindService) validateBind(ctx *gin.Context, userID uint64, authType uint32, authUid string) error {
    // 1. 检查用户是否已绑定该认证方式
    existingAuth, err := s.userRepo.FetchAuthByUserAndType(ctx, userID, authType)
    if err == nil && existingAuth.ID > 0 {
        return fmt.Errorf("该认证方式已绑定")
    }

    // 2. 检查该认证方式是否被其他用户使用
    conflictAuth, err := s.userRepo.FetchAuthByTypeAndUid(ctx, authType, authUid)
    if err == nil && conflictAuth.UserID != userID && conflictAuth.UserID > 0 {
        return fmt.Errorf("该账号已被其他用户绑定")
    }

    return nil
}
```

#### 第三方认证验证
- **苹果登录**: 复用现有的 `AppleLoginProvider`，验证 Identity Token
- **谷歌登录**: 复用现有的 `GoogleLoginProvider`，验证 ID Token

#### 邮箱绑定验证
- 验证邮箱验证码（调用邮箱服务）
- 更新用户密码（bcrypt加密）
- 事务保护确保数据一致性

### 数据库扩展

#### 新增 DAO 方法
```go
// 根据用户ID和认证类型查找
func (e *Entry) FetchAuthByUserAndType(ctx *gin.Context, userID uint64, authType uint32) (*Auth, error)

// 根据认证类型和认证ID查找
func (e *Entry) FetchAuthByTypeAndUid(ctx *gin.Context, authType uint32, authUid string) (*Auth, error)

// 获取用户所有认证方式
func (e *Entry) FindAuthsByUserID(ctx *gin.Context, userID uint64) (AuthList, error)

// 删除认证方式
func (e *Entry) DeleteAuth(ctx *gin.Context, authID uint64) error
```

### 安全特性

#### 1. 绑定安全
- **冲突检测**: 防止同一认证方式被多个用户绑定
- **重复检测**: 防止用户重复绑定同一认证方式
- **token验证**: 确保第三方认证的有效性
- **验证码验证**: 邮箱绑定需要验证码确认

#### 2. 解绑安全
- **密码验证**: 解绑操作需要用户密码确认
- **最后保护**: 用户至少需要保留一种认证方式
- **日志记录**: 完整的操作日志追踪

#### 3. 数据安全
- **事务保护**: 重要操作使用数据库事务
- **密码加密**: bcrypt 哈希存储
- **敏感信息保护**: token 等敏感信息不记录在日志中

## 业务场景

### 典型使用场景

#### 1. 新用户首次绑定
```
1. 用户通过苹果登录首次注册
2. 进入设置页面绑定邮箱
3. 发送验证码 → 验证码确认 → 设置密码 → 绑定成功
4. 用户现在可以使用苹果登录或邮箱登录
```

#### 2. 多设备使用
```
1. 用户在iPhone上使用苹果登录
2. 在Android设备上无法使用苹果登录
3. 使用已绑定的邮箱登录Android设备
4. 实现跨平台账号同步
```

#### 3. 账号安全管理
```
1. 用户查看当前绑定的认证方式
2. 发现不再使用的谷歌账号
3. 验证密码后解绑谷歌认证
4. 保留苹果和邮箱认证方式
```

### 错误处理

#### 常见错误场景
- **重复绑定**: "该认证方式已绑定"
- **账号冲突**: "该账号已被其他用户绑定"
- **验证失败**: "苹果账号验证失败" / "验证码错误"
- **解绑限制**: "无法解绑最后一种认证方式"
- **权限不足**: "密码错误"

## 监控与日志

### 关键指标
- 绑定成功率（按认证类型）
- 解绑操作频率
- 冲突检测命中率
- 绑定失败原因分析

### 日志记录
```
INFO: 用户绑定成功 - userID=12345, authType=1, authUid=001234.abc123
WARN: 绑定冲突检测 - userID=12345, authType=4, conflictUid=<EMAIL>  
ERROR: 绑定失败 - userID=12345, authType=2, error=token验证失败
```

## 扩展性设计

### 支持新认证方式
1. 在 `AuthType` 枚举中添加新类型
2. 实现对应的 `LoginProvider`
3. 添加新的绑定 Handler
4. 更新前端认证方式列表

### 高级功能扩展
- **主认证方式设置**: 允许用户设置默认登录方式
- **认证方式优先级**: 根据使用频率自动调整顺序
- **安全等级管理**: 不同认证方式设置不同安全等级
- **批量操作**: 支持一次性绑定多个认证方式

## 测试策略

### 单元测试
- 绑定冲突检测逻辑
- 认证方式验证流程
- 数据库事务处理
- 错误处理机制

### 集成测试
- 完整绑定流程测试
- 第三方认证集成
- 邮箱验证码流程
- 解绑安全验证

### 性能测试
- 并发绑定操作
- 大量用户认证方式查询
- 数据库查询性能
- 缓存命中率

通过以上设计，VLab 账号绑定系统提供了完整、安全、可扩展的多认证方式绑定解决方案，满足用户在不同场景下的登录需求。 