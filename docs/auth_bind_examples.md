# VLab 账号绑定 API 使用示例

## 前置条件

所有绑定相关操作都需要用户先登录，并在请求头中携带 JWT token：

```http
Authorization: your_jwt_token_here
```

## 1. 查看当前绑定状态

### 请求示例
```http
GET /user/auth/methods
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例
```json
{
  "code": "200",
  "message": "success",
  "data": {
    "auth_methods": [
      {
        "auth_type": 1,
        "auth_type_name": "苹果登录",
        "auth_uid": "001234.abc123def456",
        "auth_email": "<EMAIL>",
        "is_bound": true,
        "bound_at": "2024-01-15 10:30:00"
      },
      {
        "auth_type": 2,
        "auth_type_name": "谷歌登录",
        "auth_uid": "",
        "auth_email": "",
        "is_bound": false,
        "bound_at": ""
      },
      {
        "auth_type": 4,
        "auth_type_name": "邮箱登录",
        "auth_uid": "",
        "auth_email": "",
        "is_bound": false,
        "bound_at": ""
      }
    ]
  }
}
```

从响应可以看出，用户当前只绑定了苹果登录，谷歌和邮箱登录未绑定。

## 2. 绑定邮箱账号

### 步骤1: 发送验证码
```http
POST /user/email/code/send
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### 步骤2: 绑定邮箱
```http
POST /user/bind/email
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "password": "mySecurePassword123"
}
```

### 成功响应
```json
{
  "code": "200",
  "message": "邮箱账号绑定成功",
  "data": {
    "message": "邮箱账号绑定成功"
  }
}
```

## 3. 绑定谷歌账号

### 请求示例
```http
POST /user/bind/google
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "auth_token": "google_id_token_from_frontend"
}
```

### 成功响应
```json
{
  "code": "200",
  "message": "谷歌账号绑定成功",
  "data": {
    "message": "谷歌账号绑定成功"
  }
}
```

## 4. 绑定苹果账号

### 请求示例
```http
POST /user/bind/apple
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "auth_token": "apple_identity_token_from_frontend"
}
```

### 成功响应
```json
{
  "code": "200",
  "message": "苹果账号绑定成功",
  "data": {
    "message": "苹果账号绑定成功"
  }
}
```

## 5. 解绑认证方式

### 解绑谷歌账号示例
```http
DELETE /user/auth/unbind/2
Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "password": "mySecurePassword123"
}
```

### 成功响应
```json
{
  "code": "200",
  "message": "认证方式解绑成功",
  "data": {
    "message": "认证方式解绑成功"
  }
}
```

## 错误处理示例

### 1. 重复绑定错误
```json
{
  "code": "4000",
  "message": "该认证方式已绑定"
}
```

### 2. 账号冲突错误
```json
{
  "code": "4000", 
  "message": "该账号已被其他用户绑定"
}
```

### 3. 验证码错误
```json
{
  "code": "4000",
  "message": "验证码错误"
}
```

### 4. 无法解绑最后一种认证方式
```json
{
  "code": "4000",
  "message": "无法解绑最后一种认证方式"
}
```

### 5. 密码验证失败
```json
{
  "code": "4000", 
  "message": "密码错误"
}
```

### 6. Token验证失败
```json
{
  "code": "4000",
  "message": "苹果账号验证失败"
}
```

## 认证类型说明

| auth_type | 认证方式 | 说明 |
|-----------|----------|------|
| 1 | 苹果登录 | Apple Sign-In |
| 2 | 谷歌登录 | Google Sign-In |
| 4 | 邮箱登录 | Email + Password |

## 完整流程示例

### 场景：新用户通过苹果登录，然后绑定邮箱

```bash
# 1. 苹果登录（首次注册）
curl -X POST https://api.vlab.com/user/login/third \
  -H "Content-Type: application/json" \
  -d '{
    "auth_type": 1,
    "auth_token": "apple_identity_token"
  }'

# 返回 JWT token，后续请求都需要携带

# 2. 查看当前绑定状态
curl -X GET https://api.vlab.com/user/auth/methods \
  -H "Authorization: your_jwt_token"

# 3. 发送邮箱验证码
curl -X POST https://api.vlab.com/user/email/code/send \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# 4. 绑定邮箱账号
curl -X POST https://api.vlab.com/user/bind/email \
  -H "Authorization: your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "code": "123456", 
    "password": "mySecurePassword123"
  }'

# 5. 再次查看绑定状态，确认邮箱已绑定
curl -X GET https://api.vlab.com/user/auth/methods \
  -H "Authorization: your_jwt_token"
```

### 场景：用户想解绑谷歌账号

```bash
# 1. 先查看当前绑定状态
curl -X GET https://api.vlab.com/user/auth/methods \
  -H "Authorization: your_jwt_token"

# 2. 解绑谷歌账号 (auth_type=2)
curl -X DELETE https://api.vlab.com/user/auth/unbind/2 \
  -H "Authorization: your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"password": "mySecurePassword123"}'

# 3. 再次查看状态，确认谷歌账号已解绑
curl -X GET https://api.vlab.com/user/auth/methods \
  -H "Authorization: your_jwt_token"
```

## 前端集成建议

### 1. 绑定状态页面
```javascript
// 获取用户认证方式列表
const getAuthMethods = async () => {
  const response = await fetch('/user/auth/methods', {
    headers: {
      'Authorization': localStorage.getItem('token')
    }
  });
  return response.json();
};

// 显示绑定状态
const renderAuthMethods = (methods) => {
  methods.forEach(method => {
    if (method.is_bound) {
      showBoundStatus(method);
    } else {
      showBindButton(method);
    }
  });
};
```

### 2. 绑定操作
```javascript
// 绑定邮箱
const bindEmail = async (email, code, password) => {
  const response = await fetch('/user/bind/email', {
    method: 'POST',
    headers: {
      'Authorization': localStorage.getItem('token'),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ email, code, password })
  });
  return response.json();
};

// 绑定第三方账号
const bindThirdParty = async (authType, authToken) => {
  const endpoint = authType === 1 ? '/user/bind/apple' : '/user/bind/google';
  const response = await fetch(endpoint, {
    method: 'POST',
    headers: {
      'Authorization': localStorage.getItem('token'),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ auth_token: authToken })
  });
  return response.json();
};
```

### 3. 解绑操作
```javascript
// 解绑认证方式
const unbindAuth = async (authType, password) => {
  const response = await fetch(`/user/auth/unbind/${authType}`, {
    method: 'DELETE',
    headers: {
      'Authorization': localStorage.getItem('token'),
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ password })
  });
  return response.json();
};
```

通过以上示例，开发者可以快速集成VLab的账号绑定功能到自己的应用中。 