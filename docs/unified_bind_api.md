# 统一认证绑定API文档

## 概述

为了简化账号绑定流程，我们将原来的三个独立绑定接口（`/bind/apple`、`/bind/google`、`/bind/email`）合并为一个统一的绑定接口 `/bind/auth`。

## API接口

### 统一绑定认证方式

**接口地址：** `POST /user/bind/auth`

**请求头：**
```
Authorization: Bearer <user_token>
Content-Type: application/json
```

**请求参数：**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| auth_type | uint32 | 是 | 认证类型：1-苹果，2-谷歌，4-邮箱 |
| auth_token | string | 条件必填 | 第三方认证token（苹果/谷歌绑定时必填） |
| email | string | 条件必填 | 邮箱地址（邮箱绑定时必填） |
| code | string | 条件必填 | 验证码（邮箱绑定时必填，6位数字） |
| password | string | 条件必填 | 密码（邮箱绑定时必填，6-32位字符） |

## 使用示例

### 1. 绑定苹果账号

```json
POST /user/bind/auth
{
    "auth_type": 1,
    "auth_token": "apple_identity_token_here"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "成功",
    "data": {
        "message": "苹果账号绑定成功"
    }
}
```

### 2. 绑定谷歌账号

```json
POST /user/bind/auth
{
    "auth_type": 2,
    "auth_token": "google_id_token_here"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "成功",
    "data": {
        "message": "谷歌账号绑定成功"
    }
}
```

### 3. 绑定邮箱账号

```json
POST /user/bind/auth
{
    "auth_type": 4,
    "email": "<EMAIL>",
    "code": "123456",
    "password": "password123"
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "成功",
    "data": {
        "message": "邮箱账号绑定成功"
    }
}
```

## 错误处理

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误（缺少必填字段、认证类型不支持等） |
| 401 | 用户token无效 |
| 500 | 服务器内部错误 |

### 错误响应示例

```json
{
    "code": 400,
    "message": "该认证方式已绑定"
}
```

## 参数验证规则

### 按认证类型的参数要求

| 认证类型 | auth_token | email | code | password |
|----------|------------|-------|------|----------|
| 苹果(1) | ✅ 必填 | ❌ 不需要 | ❌ 不需要 | ❌ 不需要 |
| 谷歌(2) | ✅ 必填 | ❌ 不需要 | ❌ 不需要 | ❌ 不需要 |
| 邮箱(4) | ❌ 不需要 | ✅ 必填 | ✅ 必填 | ✅ 必填 |

### 字段格式要求

- **email**: 有效的邮箱格式，最大100字符
- **code**: 6位数字验证码
- **password**: 6-32位字符
- **auth_type**: 只能是 1、2、4

## 兼容性说明

### 老接口保留

为了保持向后兼容，原有的分离接口仍然保留：

- `POST /user/bind/apple` - 绑定苹果账号
- `POST /user/bind/google` - 绑定谷歌账号  
- `POST /user/bind/email` - 绑定邮箱账号

### 推荐使用

**建议新开发的客户端使用统一接口 `/user/bind/auth`**，具有以下优势：

1. **代码简化**: 客户端只需要维护一个绑定接口
2. **逻辑统一**: 所有绑定类型使用相同的请求结构
3. **易于扩展**: 未来新增认证类型无需增加新接口
4. **错误处理统一**: 所有类型的绑定错误处理逻辑一致

## curl测试示例

### 绑定苹果账号
```bash
curl -X POST "http://your-domain/user/bind/auth" \
  -H "Authorization: Bearer your_user_token" \
  -H "Content-Type: application/json" \
  -d '{
    "auth_type": 1,
    "auth_token": "apple_token_here"
  }'
```

### 绑定邮箱账号
```bash
curl -X POST "http://your-domain/user/bind/auth" \
  -H "Authorization: Bearer your_user_token" \
  -H "Content-Type: application/json" \
  -d '{
    "auth_type": 4,
    "email": "<EMAIL>",
    "code": "123456",
    "password": "password123"
  }'
``` 