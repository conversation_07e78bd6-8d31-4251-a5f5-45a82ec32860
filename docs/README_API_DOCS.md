# VLab IP黑名单管理API文档

## 📋 文档概览

本目录包含VLab IP黑名单管理系统的完整API文档，提供多种格式和工具支持。

## 📁 文件结构

```
docs/
├── swagger/
│   ├── ip_blacklist_api.yaml          # OpenAPI 3.0 YAML格式
│   └── ip_blacklist_api.json          # OpenAPI 3.0 JSON格式
├── postman/
│   └── ip_blacklist_collection.json   # Postman集合文件
├── api_usage_guide.md                 # API使用指南
├── ip_blacklist_middleware.md         # 中间件详细文档
└── README_API_DOCS.md                 # 本文件
```

## 🚀 快速开始

### 1. 查看API文档

**在线查看（推荐）**
- Swagger UI: `http://localhost:8080/swagger-ui/`
- 或访问：`https://api.vlab.com/swagger-ui/`

**本地查看**
```bash
# 使用swagger-ui-serve
npm install -g swagger-ui-serve
swagger-ui-serve docs/swagger/ip_blacklist_api.yaml

# 或使用在线编辑器
# 访问 https://editor.swagger.io/
# 导入 docs/swagger/ip_blacklist_api.yaml
```

### 2. 导入Postman集合

1. 打开Postman
2. 点击 `Import` 按钮
3. 选择 `docs/postman/ip_blacklist_collection.json`
4. 配置环境变量：
   - `baseUrl`: API基础URL
   - `adminToken`: 管理员认证Token
   - `channelId`: 测试用渠道ID

### 3. 生成客户端代码

```bash
# 安装OpenAPI Generator
npm install @openapitools/openapi-generator-cli -g

# 生成JavaScript客户端
openapi-generator-cli generate \
  -i docs/swagger/ip_blacklist_api.yaml \
  -g javascript \
  -o ./generated/js-client

# 生成Python客户端
openapi-generator-cli generate \
  -i docs/swagger/ip_blacklist_api.yaml \
  -g python \
  -o ./generated/python-client

# 生成Go客户端
openapi-generator-cli generate \
  -i docs/swagger/ip_blacklist_api.yaml \
  -g go \
  -o ./generated/go-client
```

## 📖 API接口概览

### 接口列表

| 方法 | 路径 | 功能 | 权限要求 |
|------|------|------|----------|
| GET | `/admin/ip-blacklist/list` | 获取IP黑名单列表 | 管理员 |
| GET | `/admin/ip-blacklist/detail` | 获取IP黑名单详情 | 管理员 |
| POST | `/admin/ip-blacklist/create` | 创建IP黑名单配置 | 管理员 |
| PUT | `/admin/ip-blacklist/update` | 更新IP黑名单配置 | 管理员 |
| DELETE | `/admin/ip-blacklist/delete` | 删除IP黑名单配置 | 管理员 |
| GET | `/admin/ip-blacklist/test` | 测试IP是否在黑名单中 | 管理员 |

### 认证方式

- **Bearer Token**: `Authorization: Bearer YOUR_TOKEN`
- **Cookie**: `Cookie: admin_session=YOUR_SESSION`

### 响应格式

所有接口都遵循统一的响应格式：

```json
{
  "code": "100000",      // 响应码，100000表示成功
  "msg": "成功",         // 响应消息
  "data": {},           // 响应数据（可选）
  "runtime": 15.5,      // 处理时间（毫秒）
  "time": 1704067200    // 服务器时间戳
}
```

## 🔧 开发工具支持

### 1. IDE插件

**VS Code**
- 安装 `OpenAPI (Swagger) Editor` 插件
- 打开 `docs/swagger/ip_blacklist_api.yaml`

**IntelliJ IDEA**
- 安装 `OpenAPI Specifications` 插件
- 支持语法高亮和自动补全

### 2. API测试工具

**支持的工具**
- ✅ Postman
- ✅ Insomnia
- ✅ Apifox
- ✅ curl
- ✅ HTTPie

**导入方式**
- Postman: 导入 `docs/postman/ip_blacklist_collection.json`
- 其他工具: 导入 `docs/swagger/ip_blacklist_api.yaml`

### 3. 代码生成支持

**支持的语言**
- JavaScript/TypeScript
- Python
- Go
- Java
- C#
- PHP
- Ruby
- Swift
- Kotlin

## 📝 使用示例

### 基础用法

```bash
# 1. 获取IP黑名单列表
curl -X GET "https://api.vlab.com/admin/ip-blacklist/list?page=1&limit=20" \
     -H "Authorization: Bearer YOUR_TOKEN"

# 2. 创建IP黑名单配置
curl -X POST "https://api.vlab.com/admin/ip-blacklist/create" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
       "channel_id": 1,
       "ip_list": "*************,10.0.0.0/8",
       "status": 1
     }'

# 3. 测试IP是否在黑名单中
curl -X GET "https://api.vlab.com/admin/ip-blacklist/test?channel_id=1&ip=*************" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

### JavaScript示例

```javascript
// 使用fetch API
const response = await fetch('/admin/ip-blacklist/list?page=1&limit=20', {
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log(data);
```

### Python示例

```python
import requests

# 创建IP黑名单配置
url = "https://api.vlab.com/admin/ip-blacklist/create"
headers = {
    "Authorization": "Bearer YOUR_TOKEN",
    "Content-Type": "application/json"
}
data = {
    "channel_id": 1,
    "ip_list": "*************,10.0.0.0/8",
    "status": 1
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

## 🔍 错误处理

### 常见错误码

| 错误码 | 错误信息 | HTTP状态码 | 解决方案 |
|--------|----------|------------|----------|
| 100000 | 成功 | 200 | - |
| 100004 | 参数错误 | 400 | 检查请求参数 |
| 100005 | token已失效 | 401 | 重新获取token |
| 100008 | Forbidden | 403 | 检查权限 |
| 100105 | 未找到相关数据 | 404 | 确认资源存在 |
| 100002 | 系统异常 | 500 | 联系技术支持 |

### 错误处理最佳实践

```javascript
async function handleAPICall() {
  try {
    const response = await fetch('/admin/ip-blacklist/list');
    const data = await response.json();
    
    if (data.code === '100000') {
      // 成功处理
      console.log('Success:', data.data);
    } else {
      // 业务错误处理
      console.error('Business Error:', data.msg);
    }
  } catch (error) {
    // 网络错误处理
    console.error('Network Error:', error);
  }
}
```

## 📊 性能和限制

### 请求限制

- **频率限制**: 每分钟最多100次请求
- **并发限制**: 每个token最多10个并发请求
- **数据限制**: IP列表最大2000字符

### 性能优化

- 使用分页查询，避免一次性获取大量数据
- 合理使用过滤条件减少数据传输
- 利用缓存机制，避免频繁查询

### 缓存策略

- **缓存时间**: 5分钟
- **缓存键**: `vlab:cache:list:ipDisallow`
- **自动清除**: 创建/更新/删除操作后自动清除

## 🔒 安全注意事项

1. **认证安全**
   - 使用HTTPS传输
   - 定期更换token
   - 不在日志中记录敏感信息

2. **权限控制**
   - 确保只有授权用户可以访问
   - 记录所有操作日志
   - 定期审计权限

3. **数据验证**
   - 严格验证IP格式
   - 防止SQL注入
   - 限制输入长度

## 📞 技术支持

### 联系方式

- **技术文档**: https://docs.vlab.com
- **API状态**: https://status.vlab.com
- **技术支持**: <EMAIL>
- **问题反馈**: https://github.com/vlab/issues

### 更新日志

- **v1.0.0** (2024-01-01): 初始版本发布
  - 完整的CRUD接口
  - IP测试功能
  - 缓存优化
  - 完整的API文档

### 版本兼容性

- **当前版本**: v1.0.0
- **最低兼容版本**: v1.0.0
- **API版本策略**: 语义化版本控制

---

📚 **更多文档**
- [API使用指南](api_usage_guide.md)
- [中间件文档](ip_blacklist_middleware.md)
- [系统架构文档](../README_IP_BLACKLIST.md)
